<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.4</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.iguokao</groupId>
	<artifactId>supernova</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>supernova</name>
	<description>Supernova project for Spring Boot</description>
	<properties>
		<java.version>17</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<spring-boot.version>3.4.4</spring-boot.version>
		<json.version>20250107</json.version>
	</properties>

	<modules>
		<module>common</module>
		<module>management</module>
		<module>exam</module>
		<module>report</module>
		<module>gateway</module>
		<module>registration</module>
		<module>question</module>
	</modules>

	<packaging>pom</packaging>
	<build>
		<defaultGoal>compile</defaultGoal>
	</build>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.json</groupId>
				<artifactId>json</artifactId>
				<version>${json.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>2.0.57</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
</project>
