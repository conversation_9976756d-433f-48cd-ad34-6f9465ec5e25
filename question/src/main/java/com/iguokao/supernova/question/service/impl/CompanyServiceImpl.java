package com.iguokao.supernova.question.service.impl;

import com.iguokao.supernova.question.repository.CompanyConfigRepository;
import com.iguokao.supernova.question.service.CompanyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyServiceImpl implements CompanyService {
    private final CompanyConfigRepository companyConfigRepository;


}
