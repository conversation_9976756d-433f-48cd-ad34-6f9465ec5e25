package com.iguokao.supernova.question.service.impl;

import com.iguokao.supernova.common.response.ActionRemoteResponse;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ExamRemote;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.question.service.CacheService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CacheServiceImpl extends RedisServiceImpl implements CacheService {

    private final ExamRemote examRemote;

    public CacheServiceImpl(RedisTemplate<String, QuestionJudgeRemoteResponse> questionJudgeRedisTemplate, RedisTemplate<String, ActionRemoteResponse> actionRedisTemplate, StringRedisTemplate redisTemplate, ExamRemote examRemote) {
        super(questionJudgeRedisTemplate, actionRedisTemplate, redisTemplate);
        this.examRemote = examRemote;
    }

    @Override
    public List<QuestionJudgeRemoteResponse> getPeriodQuestion(String periodId) {
        List<QuestionJudgeRemoteResponse> list = super.getPeriodQuestion(periodId);
        if(list.isEmpty()){
            RestResponse<List<QuestionJudgeRemoteResponse>> response = this.examRemote.periodQuestion(periodId);
            if(response.getCode() != 0){
                throw ServiceException.remote(response);
            }
            list = response.getData();
            super.setPeriodQuestion(list);
        }
        return list;
    }

    @Override
    public List<ActionRemoteResponse> getPeriodIllegalList(String periodId) {
        List<ActionRemoteResponse> list = super.getPeriodIllegalList(periodId);
        if(list.isEmpty()){
            RestResponse<List<ActionRemoteResponse>> response = this.examRemote.illegalAction(periodId);
            if(response.getCode() != 0){
                throw ServiceException.remote(response);
            }
            list = response.getData();
            if(!list.isEmpty()){
                super.setPeriodIllegalList(list);
            }
        }
        return list;
    }
}
