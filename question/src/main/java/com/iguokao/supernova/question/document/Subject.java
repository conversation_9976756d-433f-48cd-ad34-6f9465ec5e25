package com.iguokao.supernova.question.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;

@Getter
@Setter
public class Subject extends BaseDocument{
    @JsonSerialize(using = ObjectIdSerializer.class)
    @Indexed(name = "companyId_index")
    private ObjectId companyId; //  时段ID
    private ObjectId occupationId; //  时段ID

    private String name; // 名称
    private String level; // 时长
    private String type; // 时长
}