package com.iguokao.supernova.question.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.servers.Server;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Profile({"dev", "test", "prod-t"})
@Configuration
@OpenAPIDefinition(
        security = {@SecurityRequirement(name = "bearer-key")},
        servers = {@Server(url = "https://supernova-api.igky.cc/report", description = "测试环境"),
                @Server(url = "http://127.0.0.1:8003", description = "本地测试")}
)
@Slf4j
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        log.info("启动 OpenApiConfig");
        return new OpenAPI()
                .components(new Components()
                        .addSecuritySchemes("bearer-key", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("Bearer")
                                .bearerFormat("JWT")))
                .info(new Info().title("Supernova机考系统 - 报告服务")
                        .title("Supernova API")
                        .termsOfService("https://iguokao.com/terms/")
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://iguokao.com"))
                        .description("API Description")
                        .version("1.0"));
    }
}
