package com.iguokao.supernova.question.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;

@Getter
@Setter
public class KnowledgePoint extends BaseDocument {
    @Indexed(name = "occupationId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId occupationId; //  时段ID

    @Indexed(name = "subjectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId subjectId;

    @Indexed(name = "parentId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId parentId;

    private String name;
    private Integer level;
}
