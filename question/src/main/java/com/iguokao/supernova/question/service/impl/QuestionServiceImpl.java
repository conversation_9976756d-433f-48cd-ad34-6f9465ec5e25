package com.iguokao.supernova.question.service.impl;

import com.iguokao.supernova.question.repository.QuestionRepository;
import com.iguokao.supernova.question.service.QuestionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class QuestionServiceImpl implements QuestionService {
    private final QuestionRepository questionRepository;


}
