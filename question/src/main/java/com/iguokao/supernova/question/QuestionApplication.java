package com.iguokao.supernova.question;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@RequiredArgsConstructor
public class QuestionApplication implements CommandLineRunner {


    public static void main(String[] args) {
        SpringApplication.run(QuestionApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        


    }
}
//        List<CandidateReport> list = this.candidateReportRepository.findByPeriodId(new ObjectId("6721b05c081a1f04d15334cb"));
//        for(CandidateReport report : list){
//
//            if(!report.getAnswerList().isEmpty()){
//                double finalScore = report
//                        .getAnswerList()
//                        .stream()
//                        .mapToDouble(Answer::getScore)
//                        .sum();
//                if(finalScore > report.getScore()){
//                    System.out.printf(" %s - %f - %f", report.getFullName(), report.getScore(), finalScore);
//                }
//            }
//
//        }
//        System.out.println("ok");

//        List<String> roomIdList = new ArrayList<>();
//        roomIdList.add("6724a0f4081a1f04d15339c0");
//        roomIdList.add("66f3adaf20d6f17e206f42e5");
//        roomIdList.add("670f2ff6041ee8478c1c46a9");
//        roomIdList.add("666a6ce501e10a4377eb3f35");
//        roomIdList.add("671778c1f470ad4ae156463d");
//        roomIdList.add("671f1434f470ad4ae156f118");
//        roomIdList.add("670dc23b041ee8478c1c458a");
//        roomIdList.add("6724a0f4081a1f04d15339c3");
//        roomIdList.add("670c782a3a7d5d02b91093c4");
//        roomIdList.add("67288489081a1f04d1533e38");
//        roomIdList.add("67232100081a1f04d1533875");
//        roomIdList.add("66628183adc9730a1e61fbb6");
//        roomIdList.add("67232100081a1f04d153385e");
//        roomIdList.add("6662b47cadc9730a1e61fcc8");
//        roomIdList.add("67232101081a1f04d153387f");
//        roomIdList.add("67232101081a1f04d153387f");
//        roomIdList.add("6704dffab8b3cf2973b0ad5f");
//        roomIdList.add("66bc5b46cb691147305a9de0");
//        roomIdList.add("66f668a3defa6d083465aa23");
//        roomIdList.add("66628183adc9730a1e61fbb7");
//        roomIdList.add("6724ab41081a1f04d15339d1");
//        roomIdList.add("666818ebadc9730a1e61fff8");
//        roomIdList.add("670f2ff6041ee8478c1c46a1");
//        roomIdList.add("67247619081a1f04d153396e");
//        roomIdList.add("672a0f392c01e13ecf54cba3");
//        roomIdList.add("671f3ba2f470ad4ae156f137");
//        roomIdList.add("66ed3c95567f39698be7b5c4");
//        roomIdList.add("671f1377f470ad4ae156f10e");
//        roomIdList.add("67287a860bd21c528ffa1b47");
//        roomIdList.add("66628183adc9730a1e61fbb9");
//        roomIdList.add("6667ed1fadc9730a1e61feae");
//        roomIdList.add("66f3adaf20d6f17e206f42df");
//        roomIdList.add("66f3adaf20d6f17e206f42df");
//        roomIdList.add("6719e0eef470ad4ae156adde");
//        roomIdList.add("671f3ba2f470ad4ae156f139");
//        roomIdList.add("6724a0f4081a1f04d15339bc");
//        roomIdList.add("6710a818041ee8478c1c4724");
//        roomIdList.add("6710a818041ee8478c1c4724");
//        roomIdList.add("66616b57adc9730a1e61f9b6");
//        roomIdList.add("6667ed1fadc9730a1e61feb0");
//        roomIdList.add("6710a818041ee8478c1c4725");
//        roomIdList.add("67048eeada300501b8ced1e3");
//
//        List<Integer> candidateNumList = new ArrayList<>();
//        candidateNumList.add(129036999);
//        candidateNumList.add(129054238);
//        candidateNumList.add(129021982);
//        candidateNumList.add(129057604);
//        candidateNumList.add(129034573);
//        candidateNumList.add(129026144);
//        candidateNumList.add(129010950);
//        candidateNumList.add(129037148);
//        candidateNumList.add(129012256);
//        candidateNumList.add(129032574);
//        candidateNumList.add(129052401);
//        candidateNumList.add(129008878);
//        candidateNumList.add(129050140);
//        candidateNumList.add(129010163);
//        candidateNumList.add(129053250);
//        candidateNumList.add(129053331);
//        candidateNumList.add(129019245);
//        candidateNumList.add(129008996);
//        candidateNumList.add(129038954);
//        candidateNumList.add(129060532);
//        candidateNumList.add(129001618);
//        candidateNumList.add(129023258);
//        candidateNumList.add(129000902);
//        candidateNumList.add(129000396);
//        candidateNumList.add(129065837);
//        candidateNumList.add(129011818);
//        candidateNumList.add(129010436);
//        candidateNumList.add(129025272);
//        candidateNumList.add(129001450);
//        candidateNumList.add(129060685);
//        candidateNumList.add(129035101);
//        candidateNumList.add(129053751);
//        candidateNumList.add(129053696);
//        candidateNumList.add(129005428);
//        candidateNumList.add(129011966);
//        candidateNumList.add(129036781);
//        candidateNumList.add(129039732);
//        candidateNumList.add(129039715);
//        candidateNumList.add(129064122);
//        candidateNumList.add(129035255);
//        candidateNumList.add(129039761);
//        candidateNumList.add(129058476);
//
//        List<CandidateReport> list = new ArrayList<>();
//        for(int i=0; i< candidateNumList.size(); i++){
//            CandidateReport candidateReport = this.candidateReportRepository.findByRoomIdAndNum(new ObjectId(roomIdList.get(i)), candidateNumList.get(i)).orElse(null);
//            list.add(candidateReport);
//        }
//
//        String subjectId = "6721b0710bd21c528ffa12db";
//        String groupPartId = "672b39e25a1f0a6621d0f2ad";
//        String selectPartId = "672b39e25a1f0a6621d0f2aa";
//        int  size = 78;
//        String q1 = "672b39e25a1f0a6621d0f2ab";
//
//        ObjectMapper mapper = new ObjectMapper();
//        List<String> v1 = new ArrayList<>();
//        List<String> v11 = new ArrayList<>();
//        List<String> v12 = new ArrayList<>();
//        List<String> v13 = new ArrayList<>();
//        List<String> v14 = new ArrayList<>();
//        List<String> v15 = new ArrayList<>();
//        v11.add("1");
//        v12.add("1");
//        v13.add("2");
//        v14.add("2");
//        v15.add("3");
//
//        v1.add(mapper.writeValueAsString(v11));
//        v1.add(mapper.writeValueAsString(v12));
//        v1.add(mapper.writeValueAsString(v13));
//        v1.add(mapper.writeValueAsString(v14));
//        v1.add(mapper.writeValueAsString(v15));
//
//        List<Double> scoreList = new ArrayList<>();
//        scoreList.add(1.5);
//        scoreList.add(1.5);
//        scoreList.add(0.0);
//        scoreList.add(0.0);
//        scoreList.add(0.0);
//
//        List<String> v2 = new ArrayList<>();
//        List<String> v21 = new ArrayList<>();
//        List<String> v22 = new ArrayList<>();
//        List<String> v23 = new ArrayList<>();
//        List<String> v24 = new ArrayList<>();
//        List<String> v25 = new ArrayList<>();
//        v21.add("1");
//        v22.add("0");
//        v23.add("2");
//        v24.add("3");
//        v25.add("2");
//
//        v2.add(mapper.writeValueAsString(v21));
//        v2.add(mapper.writeValueAsString(v22));
//        v2.add(mapper.writeValueAsString(v23));
//        v2.add(mapper.writeValueAsString(v24));
//        v2.add(mapper.writeValueAsString(v25));

//        String subjectId = "6721b07e0bd21c528ffa12dc";
//        String groupPartId = "672b39e95a1f0a6621d0f30c";
//        String selectPartId = "672b39e95a1f0a6621d0f309";
//        int  size = 83;
//        String q1 = "672b39e95a1f0a6621d0f30a:";
//
//        ObjectMapper mapper = new ObjectMapper();
//        List<String> v1 = new ArrayList<>();
//        List<String> v11 = new ArrayList<>();
//        List<String> v12 = new ArrayList<>();
//        List<String> v13 = new ArrayList<>();
//        List<String> v14 = new ArrayList<>();
//        List<String> v15 = new ArrayList<>();
//        v11.add("1");
//        v12.add("0");
//        v13.add("1");
//        v14.add("3");
//        v15.add("3");
//
//        v1.add(mapper.writeValueAsString(v11));
//        v1.add(mapper.writeValueAsString(v12));
//        v1.add(mapper.writeValueAsString(v13));
//        v1.add(mapper.writeValueAsString(v14));
//        v1.add(mapper.writeValueAsString(v15));
//
//        List<Double> scoreList = new ArrayList<>();
//        scoreList.add(1.5);
//        scoreList.add(1.5);
//        scoreList.add(0.0);
//        scoreList.add(0.0);
//        scoreList.add(0.0);
//
//        List<String> v2 = new ArrayList<>();
//        List<String> v21 = new ArrayList<>();
//        List<String> v22 = new ArrayList<>();
//        List<String> v23 = new ArrayList<>();
//        List<String> v24 = new ArrayList<>();
//        List<String> v25 = new ArrayList<>();
//        v21.add("2");
//        v22.add("1");
//        v23.add("2");
//        v24.add("2");
//        v25.add("0");
//
//        v2.add(mapper.writeValueAsString(v21));
//        v2.add(mapper.writeValueAsString(v22));
//        v2.add(mapper.writeValueAsString(v23));
//        v2.add(mapper.writeValueAsString(v24));
//        v2.add(mapper.writeValueAsString(v25));
//        List<QuestionJudgeRemoteResponse> questionList = this.examRemote.subjectQuestion(subjectId).getData();
//
//        for (CandidateReport candidateReport : list) {
//            if(candidateReport.getSubjectId().toString().equals(subjectId)) {
//                if (!candidateReport.getAnswerList().isEmpty() && candidateReport.getAnswerList().size() != questionList.size()) {
//                    double score = candidateReport.getAnswerList().stream().mapToDouble(Answer::getScore).sum();
//                    System.out.printf("%s - %d - %f%n", candidateReport.getFullName(), candidateReport.getAnswerList().size(), score);
//                    int count = questionList.size() - candidateReport.getAnswerList().size();
//                    if (candidateReport.getAnswerList().size() < size) {
//                        count = Math.abs((int) Math.ceil(count / 2.0));
//                    }
//                    for (QuestionJudgeRemoteResponse q : questionList) {
//                        Answer answer = candidateReport
//                                .getAnswerList()
//                                .stream()
//                                .filter(a -> a.getQuestionId().toString().equals(q.getQuestionId()))
//                                .findFirst()
//                                .orElse(null);
//                        if (answer == null) {
//                            Answer a = new Answer();
//                            a.setAnswerId(new ObjectId());
//                            a.setQuestionId(new ObjectId(q.getQuestionId()));
//                            if (q.getType().equals(QuestionTypeEnum.GROUP_SELECTION.getCode())) {
//                                a.setPartId(new ObjectId(groupPartId));
//                                if (count > 0) {
//                                    a.setJudgeResult(AnswerJudgeResultEnum.CORRECT.getCode());
//                                    a.setScore(q.getQuestionScore());
//                                    for (QuestionGroupOptionRemoteResponse op : q.getGroupOptionList()) {
//                                        a.setGroupScore(new ArrayList<>());
//                                        a.getValue().add(new ObjectMapper().writeValueAsString(op.getCorrectValue()));
//                                        a.getGroupScore().add(Double.parseDouble(op.getCorrectValue().get(0)));
//                                        count--;
//                                    }
//                                } else {
//                                    a.setScore(3.0);
//                                    a.setJudgeResult(AnswerJudgeResultEnum.PART_CORRECT.getCode());
//                                    a.setGroupScore(scoreList);
//                                    if(a.getQuestionId().toString().equals(q1)){
//                                        a.setValue(v1);
//                                    }else {
//                                        a.setValue(v2);
//                                    }
//                                }
//                                candidateReport.getAnswerList().add(a);
//                            } else {
//                                a.setPartId(new ObjectId(selectPartId));
//                                if (count > 0) {
//                                    a.setJudgeResult(AnswerJudgeResultEnum.CORRECT.getCode());
//                                    a.setScore(q.getQuestionScore());
//                                    a.setValue(q.getCorrectValue());
//                                    count --;
//                                } else {
//                                    a.setJudgeResult(AnswerJudgeResultEnum.WRONG.getCode());
//                                    a.setScore(0.0);
//                                    int o = Integer.parseInt( q.getCorrectValue().get(0)) +1;
//                                    if(o==3){
//                                        o = 0;
//                                    }
//                                    List<String> ol = new ArrayList<>();
//                                    ol.add(String.valueOf(o));
//                                    a.setValue(ol);
//                                }
//                                candidateReport.getAnswerList().add(a);
//                            }
//                        }
//                    }
//                    double finalScore = candidateReport.getAnswerList().stream().mapToDouble(Answer::getScore).sum();
//                    System.out.printf("%s - %d - %f  - %f %n", candidateReport.getFullName(), candidateReport.getAnswerList().size(), finalScore, finalScore - score);
//                    candidateReport.setScore(finalScore);
//                    this.candidateReportRepository.save(candidateReport);
//                }
//            }
//        }