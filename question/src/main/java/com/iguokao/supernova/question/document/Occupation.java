package com.iguokao.supernova.question.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class Occupation  extends BaseDocument {
    private ObjectId companyId;
    private String name;
    private String sn;
    private List<String> levelList = new ArrayList<>(); // 开通的职业
}
