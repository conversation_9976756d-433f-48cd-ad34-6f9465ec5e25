package com.iguokao.supernova.question.service.impl;

import com.iguokao.supernova.question.repository.OccupationRepository;
import com.iguokao.supernova.question.service.OccupationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class OccupationServiceImpl implements OccupationService {
    private final OccupationRepository occupationRepository;


}
