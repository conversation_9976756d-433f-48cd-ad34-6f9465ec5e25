package com.iguokao.supernova.question.service.impl;

import com.iguokao.supernova.question.repository.SubjectRepository;
import com.iguokao.supernova.question.service.SubjectService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SubjectServiceImpl implements SubjectService {
    private final SubjectRepository subjectRepository;


}
