package com.iguokao.supernova.question.document;

import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.common.document.QuestionGroupOption;
import com.iguokao.supernova.common.document.QuestionOption;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
public class Question extends BaseDocument {
    @Indexed(name = "companyId_index")
    private ObjectId companyId;
    private ObjectId additionalId;

    @Indexed(name = "type_index")
    private Integer type;
    private String body;
    private Double questionScore;
    private String analysis;
    private Integer scoreType = 1;  // 默认是 严格积分-全对才给分
    private Integer wordLimit;  // 字符限制
    private String note;
    private List<String> correctValue = new ArrayList<>();
    private List<QuestionOption> optionList = new ArrayList<>();
    private List<QuestionGroupOption> groupOptionList = new ArrayList<>();
}
