package com.iguokao.supernova.question.service.impl;

import com.iguokao.supernova.common.response.ActionRemoteResponse;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;
import com.iguokao.supernova.question.service.RedisService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public abstract class RedisServiceImpl implements RedisService {
    public static Integer DURATION_ONE_DAY = 3600 * 24;
    public static String PREFIX_QUESTION_JUDGE = "qj";
    public static String PREFIX_QUESTION_PERIOD = "qp";
    public static String PREFIX_ACTION_PERIOD = "act";
    public static String PREFIX_LOCK = "lk";

    private final RedisTemplate<String, QuestionJudgeRemoteResponse> questionJudgeRedisTemplate;
    private final RedisTemplate<String, ActionRemoteResponse> actionRedisTemplate;
    private final StringRedisTemplate redisTemplate;

    @Override
    public QuestionJudgeRemoteResponse getQuestion(String questionId) {
        String k = String.format("%s:%s", PREFIX_QUESTION_JUDGE, questionId);
        return this.questionJudgeRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setQuestion(QuestionJudgeRemoteResponse question) {
        String k = String.format("%s:%s", PREFIX_QUESTION_JUDGE, question.getQuestionId());
        this.questionJudgeRedisTemplate.opsForValue().set(k, question, DURATION_ONE_DAY, TimeUnit.SECONDS);
    }

    @Override
    public List<QuestionJudgeRemoteResponse> getPeriodQuestion(String periodId) {
        String k = String.format("%s:%s", PREFIX_QUESTION_PERIOD, periodId);
        return this.questionJudgeRedisTemplate.opsForList().range(k, 0, -1);
    }

    @Override
    public void setPeriodQuestion(List<QuestionJudgeRemoteResponse> list) {
        String k = String.format("%s:%s", PREFIX_QUESTION_PERIOD, list.get(0).getQuestionId());
        this.questionJudgeRedisTemplate.opsForList().leftPushAll(k, list);
        this.questionJudgeRedisTemplate.expire(k, DURATION_ONE_DAY, TimeUnit.SECONDS);
    }

    @Override
    public List<ActionRemoteResponse> getPeriodIllegalList(String periodId) {
        String k = String.format("%s:%s", PREFIX_ACTION_PERIOD, periodId);
        return this.actionRedisTemplate.opsForList().range(k, 0, -1);
    }

    @Override
    public void setPeriodIllegalList(List<ActionRemoteResponse> list) {
        String k = String.format("%s:%s", PREFIX_ACTION_PERIOD, list.get(0).getPeriodId());
        this.actionRedisTemplate.opsForList().leftPushAll(k, list);
        this.actionRedisTemplate.expire(k, DURATION_ONE_DAY, TimeUnit.SECONDS);
    }

    @Override
    public String getLock(String key) {
        String k = String.format("%s:%s", PREFIX_LOCK, key);
        return this.redisTemplate.opsForValue().get(k);
    }

    @Override
    public void setLock(String key) {
        String k = String.format("%s:%s", PREFIX_LOCK, key);
        this.redisTemplate.opsForValue().set(k, "lock");
    }

    @Override
    public void deleteLock(String key) {
        String k = String.format("%s:%s", PREFIX_LOCK, key);
        this.redisTemplate.delete(k);
    }
}
