package com.iguokao.supernova.question.service.impl;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.remote.ManagementRemote;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.util.ProfileUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AuditServiceImpl implements AuditService {
    private final ManagementRemote managementRemote;
    private final JwtService jwtService;
    private final Environment environment;

    @Override
    public void add(AuditItem item) {
        String operatorId = this.jwtService.currentOperatorId();
        item.setOperatorId(operatorId);
        if(ProfileUtil.isTestOrDev(environment)){
            return;
        }
        this.managementRemote.audit(item);
    }
}
