package com.iguokao.supernova.question.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@CompoundIndex(name = "questionId_index", def = "{'questionList.questionId' : 1}")
public class Part extends BaseDocument {
    @Indexed(name = "periodId_index")
    private ObjectId periodId;

    @Indexed(name = "subjectId_index")
    private ObjectId subjectId;
    private String name;  // 名称
    private Double partScore = 0D; // 子卷得分
    private Integer sort = 0; // 子卷排序号
    private List<PartQuestion> questionList = new ArrayList<>();
    private Boolean questionRandomized; // 试题乱序
    private Boolean optionRandomized; // 选项乱序
}

