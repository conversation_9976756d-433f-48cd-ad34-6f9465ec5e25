package com.iguokao.supernova.question.config;

import com.iguokao.supernova.common.remote.ExamRemote;
import com.iguokao.supernova.common.remote.ManagementRemote;
import com.iguokao.supernova.common.remote.QwenRemote;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import feign.slf4j.Slf4jLogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RemoteConfig {
    @Bean
    public ExamRemote examRemote(@Value("${app.service.exam}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(ExamRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(ExamRemote.class, service);
    }

    @Bean
    public ManagementRemote managementRemote(@Value("${app.service.management}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(ManagementRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(ManagementRemote.class, service);
    }

}
