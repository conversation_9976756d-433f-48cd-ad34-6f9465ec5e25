package com.iguokao.supernova.question.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document("company_config")
public class CompanyConfig extends BaseDocument {
    private String name;
    private String logo;
    private String banner;
    private String miniBanner;
    private String libPassword;
    private List<ObjectId> occupationIdList = new ArrayList<>(); // 开通的职业
    private List<ObjectId> publicCompanyIdList = new ArrayList<>();
    private List<String> levelList = new ArrayList<>(); // 开通的职业
}
