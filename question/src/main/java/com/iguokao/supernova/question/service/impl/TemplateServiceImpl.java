package com.iguokao.supernova.question.service.impl;

import com.iguokao.supernova.question.repository.TemplateRepository;
import com.iguokao.supernova.question.service.TemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TemplateServiceImpl implements TemplateService {
    private final TemplateRepository templateRepository;


}
