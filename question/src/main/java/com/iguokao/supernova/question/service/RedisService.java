package com.iguokao.supernova.question.service;

import com.iguokao.supernova.common.response.ActionRemoteResponse;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;

import java.util.List;

public interface RedisService {
    QuestionJudgeRemoteResponse getQuestion(String questionId);
    void setQuestion(QuestionJudgeRemoteResponse question);

    List<QuestionJudgeRemoteResponse> getPeriodQuestion(String periodId);
    void setPeriodQuestion(List<QuestionJudgeRemoteResponse> list);

    List<ActionRemoteResponse> getPeriodIllegalList(String periodId);
    void setPeriodIllegalList(List<ActionRemoteResponse> list);

    String getLock(String key);
    void setLock(String key);
    void deleteLock(String key);
}
