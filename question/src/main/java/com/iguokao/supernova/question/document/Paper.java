package com.iguokao.supernova.question.document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
public class Paper extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    @Indexed(name = "occupationId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId occupationId; //  时段ID

    @Indexed(name = "subjectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId subjectId;

    @JsonIgnore
    private List<PaperPart> paperPartList = new ArrayList<>();
}

