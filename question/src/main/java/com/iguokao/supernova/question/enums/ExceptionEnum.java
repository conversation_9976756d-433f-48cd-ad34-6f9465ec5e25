package com.iguokao.supernova.report.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum ExceptionEnum implements BaseEnum {
    CANDIDATE_NOT_FOUND(3001, "DTO 考生不存在"),
    CANDIDATE_SYNCED(3002, "考生已经同步"),
    CANDIDATE_SYNCING(3003, "考生同步中"),

    PERIOD_NOT_FINISHED(3010, "有房间未提交答案，不能同步考生数据"),

    REVIEW_CANDIDATE_OUT_OF_RANGE(3050, "阅卷人数超过了可以添加的人数"),
    EXPORT_SQLITE_FAILED(3060, "导出到 SQLite 失败"),
    CANDIDATE_REPORT_NOT_FOUND(3100, "考生报告不存在"),
    CANDIDATE_ANSWER_NOT_FOUND(3101, "考生答案不存在"),

    AI_JUDGE_TASK_EXIST(3201, "AI检测任务存在"),
    AI_JUDGE_TASK_NOT_FOUND(3201, "AI检测任务存在"),
    AI_JUDGE_RESULT_NOT_EMPTY(3203, "已经有阅卷结果存在，请清空阅卷结果，重新运行"),
    ;

    public static final BaseEnum AUDIO_EXECUTE_FAILED = null;

    ExceptionEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
