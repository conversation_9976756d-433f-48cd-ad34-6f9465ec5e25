server:
  port: 8003
spring:
  data:
    mongodb:
      uri: ***************************************************
      auto-index-creation: true
  jpa:
    show-sql: true
    generate-ddl: true
#logging:
#  level:
#    com:
#      iguokao:
#        supernova:
#          common:
#            remote:
#              CloopenRemote: TRACE
app:
  upload-path: /Users/<USER>/db
  service:
    management: http://127.0.0.1:8001
    exam: http://127.0.0.1:8002
  security:
    jwt:
      secret-key: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
      expiration: 86400000 # a day
      refresh-token:
        expiration: 86400000
    remote-ip: hasIpAddress('************') or hasIpAddress('**********/16')
  redis:
    database: 3
    host: ***********
    password: wVtB7vKkqZggAbyJ
    port: 6379
    max-active: 8
    max-idle: 8
    min-idle: 0
  ali:
    access-key-id: LTAI5tBsx8tiNMdspnQehbDS
    access-key-secret: ******************************
    oss-endpoint: oss-cn-beijing.aliyuncs.com
    oss-endpoint-internal: oss-cn-beijing-internal.aliyuncs.com
    oss-bucket-exam: stone-exam-test
  tencent:
    secret-id: AKID1rRloa5NPnvmNhjW5NO8bolSt51h6F7i
    secret-key: LQEl1zh8H9l7C0YppYz93g27vTlnYS6T
    merchant-id: 0NSJ2203141444073205
    merchant-id-lite: 0NSJ2207111622402921
    callback-password: Q2leXoBh1CTWBcoF
    rtc-app-supernova-id: 1400795330
    rtc-app-supernova-key: 874788148b4d77c0ecc5254a3a54c9da59b03d44a086c4b07565330ba87e3cbe
    vod-sub-app-id: 1500009069