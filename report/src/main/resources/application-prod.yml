server:
  port: 8003
  undertow:
    threads:
      io: 4
      worker: 128
  forward-headers-strategy: framework
springdoc:
  api-docs:
    enabled: false
spring:
  data:
    mongodb:
      uri: mongodb://app:<EMAIL>:3717,dds-2ze3f7fd58d45bb41.mongodb.rds.aliyuncs.com:3717/report?replicaSet=mgset-76207179&slaveOk=true&readPreference=secondaryPreferred&maxPoolSize=200
      auto-index-creation: true
app:
  upload-path: /opt/tmp
  service:
    management: http://127.0.0.1:8001
    exam: http://127.0.0.1:8002
  security:
    jwt:
      secret-key: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
      expiration: 86400000 # a day
      refresh-token:
        expiration: 86400000
    remote-ip: has<PERSON>p<PERSON>ddress('10.0.0.0/8') or has<PERSON><PERSON><PERSON><PERSON><PERSON>('**********/16')
  redis:
    database: 3
    host: r-2zetxv1tyf5hfd778y.redis.rds.aliyuncs.com
    password: av49J1WhTickA8GZ
    port: 6379
    max-active: 8
    max-idle: 8
    min-idle: 0
  ali:
    access-key-id: LTAI5t5sGHZy1GGQjJVUoqfq
    access-key-secret: ******************************
    oss-endpoint: oss-cn-beijing.aliyuncs.com
    oss-endpoint-internal: oss-cn-beijing-internal.aliyuncs.com
    oss-bucket-exam: iguokao-supernova-exam
  tencent:
    secret-id: AKID1rRloa5NPnvmNhjW5NO8bolSt51h6F7i
    secret-key: LQEl1zh8H9l7C0YppYz93g27vTlnYS6T
    merchant-id: 0NSJ2203141444073205
    merchant-id-lite: 0NSJ2207111622402921
    callback-password: Q2leXoBh1CTWBcoF
    rtc-app-supernova-id: **********
    rtc-app-supernova-key: 874788148b4d77c0ecc5254a3a54c9da59b03d44a086c4b07565330ba87e3cbe
    vod-sub-app-id: **********
management:
  endpoint:
    health:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health
# 日志配置
logging:
  file:
    path: /opt/log
  config: classpath:logback-prod.xml