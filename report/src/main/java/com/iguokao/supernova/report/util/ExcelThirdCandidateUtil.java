package com.iguokao.supernova.report.util;

import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.common.util.ExcelUtil;
import com.iguokao.supernova.common.util.ScoreUtil;
import com.iguokao.supernova.report.document.Answer;
import com.iguokao.supernova.report.document.CandidateReport;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

public class ExcelThirdCandidateUtil {

    public static List<List<String>> getExcelHeader() {
        List<List<String>> headerList = new ArrayList<>();
        ExcelUtil.addHeaderItem(headerList, "ZKZH 准考证号");
        ExcelUtil.addHeaderItem(headerList, "KMDM 科目代码");
        ExcelUtil.addHeaderItem(headerList, "BKJB 报考级别");
        ExcelUtil.addHeaderItem(headerList, "BKZY 报考专业");
        ExcelUtil.addHeaderItem(headerList, "QKBJ 缺考记录");
        ExcelUtil.addHeaderItem(headerList, "WJBJ 违纪标记");
        ExcelUtil.addHeaderItem(headerList, "KSXM 考生姓名");
        return headerList;
    }

    public static List<List<Object>> getExcelData(List<CandidateReport> list) {
        list = list
                .stream()
                .sorted(Comparator.comparing(CandidateReport::getState))
                .toList();

        List<List<Object>> dataList = new ArrayList<>();
        for(CandidateReport candidateReport : list){
            List<Object> row = new ArrayList<>();
            row.add(candidateReport.getNum());
            row.add("1");
            row.add("01");
            row.add("01");
            if(candidateReport.getState().equals(CandidateStateEnum.FINISHED.getCode())
                    || !candidateReport.getAnswerList().isEmpty()){
                row.add("");
            } else {
                row.add("Q");
            }
            row.add(candidateReport.getIllegal() != null ? "W" : "");
            row.add(candidateReport.getFullName());
            dataList.add(row);
        }
        return dataList;
    }
}
