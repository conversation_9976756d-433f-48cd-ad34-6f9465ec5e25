package com.iguokao.supernova.report.service;

import com.iguokao.supernova.report.document.TransCandidate;
import com.iguokao.supernova.report.repository.TransCandidateRepository;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class TransCandidateServiceImpl implements TransCandidateService {
    private final TransCandidateRepository exchangeRepository;
    @Override
    public void out(List<TransCandidate> list) {
        exchangeRepository.saveAll(list);
    }

    @Override
    public List<TransCandidate> in(List<String> idList) {
        return this.exchangeRepository.findBy_idIn(idList
                .stream()
                .map(ObjectId::new)
                .toList());
    }
}
