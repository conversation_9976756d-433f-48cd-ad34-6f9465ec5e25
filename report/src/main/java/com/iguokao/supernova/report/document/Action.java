package com.iguokao.supernova.report.document;

import com.iguokao.supernova.common.response.ActionRemoteResponse;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class Action {
    private String actionId;
    private String candidateId;
    private String projectId;
    private String periodId;
    private String subjectId;
    private String siteId;
    private String roomId;

    private Integer type;
    private Integer value;
    private String text;

    public static List<Action> of(List<ActionRemoteResponse> list) {
        List<Action> res = new ArrayList<>();
        for (ActionRemoteResponse a : list){
            Action action = new Action();
            BeanUtils.copyProperties(a, action);
            res.add(action);
        }
        return res;
    }
}
