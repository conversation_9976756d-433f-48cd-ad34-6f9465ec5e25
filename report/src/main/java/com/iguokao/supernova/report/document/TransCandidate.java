package com.iguokao.supernova.report.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document("trans")
public class TransCandidate extends BaseDocument {
    private List<Answer> answerList = new ArrayList<>();
}
