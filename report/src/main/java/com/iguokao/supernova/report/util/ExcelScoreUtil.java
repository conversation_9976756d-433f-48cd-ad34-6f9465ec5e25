package com.iguokao.supernova.report.util;

import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.common.util.ExcelUtil;
import com.iguokao.supernova.common.util.ScoreUtil;
import com.iguokao.supernova.report.document.Answer;
import com.iguokao.supernova.report.document.CandidateReport;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

public class ExcelScoreUtil {

    public static List<List<String>> getExcelHeader(Map<String, String> map) {
        List<List<String>> headerList = new ArrayList<>();
        ExcelUtil.addHeaderItem(headerList, "姓名");
        ExcelUtil.addHeaderItem(headerList, "性别");
        ExcelUtil.addHeaderItem(headerList, "科目");
        ExcelUtil.addHeaderItem(headerList, "考试城市");
        ExcelUtil.addHeaderItem(headerList, "手机");
        ExcelUtil.addHeaderItem(headerList, "Email");
        ExcelUtil.addHeaderItem(headerList, "证件号码");
        ExcelUtil.addHeaderItem(headerList, "准考证号");
        ExcelUtil.addHeaderItem(headerList, "参加状态");
        ExcelUtil.addHeaderItem(headerList, "答题状态");

        ExcelUtil.addHeaderItem(headerList, "违纪信息");

        ExcelUtil.addHeaderItem(headerList, "成绩");

        if(map != null){
            for(String id : map.keySet()){
                ExcelUtil.addHeaderItem(headerList, map.get(id));
            }
        }

        ExcelUtil.addHeaderItem(headerList, "开始时间");
        ExcelUtil.addHeaderItem(headerList, "结束时间");
        ExcelUtil.addHeaderItem(headerList, "考试用时（分钟）");
        ExcelUtil.addHeaderItem(headerList, "答题数量");

        return headerList;
    }

    public static List<List<Object>> getExcelData(List<CandidateReport> list, String subjectName, Map<String, String> map) {
        list = list
                .stream()
                .sorted(Comparator.comparing(CandidateReport::getState))
                .toList();

        List<List<Object>> dataList = new ArrayList<>();
        for(CandidateReport candidateReport : list){
            List<Object> row = new ArrayList<>();
            row.add(candidateReport.getFullName());
            if(candidateReport.getGender() == null){
                row.add("");
            } else {
                row.add(candidateReport.getGender()==1?"男":"女");
            }
            row.add(subjectName);
            row.add(candidateReport.getCity());
            row.add(candidateReport.getMobile());
            row.add(candidateReport.getEmail());
            row.add(candidateReport.getIdCardNum());
            row.add(candidateReport.getNum());
            if(candidateReport.getState().equals(CandidateStateEnum.FINISHED.getCode())
                    || !candidateReport.getAnswerList().isEmpty()){
                row.add("参考");
            } else {
                row.add("缺考");
            }
            row.add(!candidateReport.getAnswerList().isEmpty() ?"已答题":"未答题");
            row.add(candidateReport.getIllegal());

            if(candidateReport.getAnswerList().isEmpty()){
                row.add("");
            } else {
                row.add(ScoreUtil.getFormatScore(candidateReport.getScore()));
            }

            if(map != null){
                for(String id : map.keySet()){
                    double partScore = candidateReport.getAnswerList()
                            .stream()
                            .filter(answer -> id.equals(answer.getPartId().toString()))
                            .mapToDouble(Answer::getScore)
                            .sum();
                    if(candidateReport.getAnswerList().isEmpty()){
                        row.add("");
                    } else {
                        row.add(ScoreUtil.getFormatScore((partScore)));
                    }
                }
            }

            if(candidateReport.getState().equals(CandidateStateEnum.ABSENT.getCode())){
                row.add("");
                row.add("");
            } else {
                row.add(DateUtil.dateToStrStd(candidateReport.getStartedAt()));
                row.add(DateUtil.dateToStrStd(candidateReport.getFinishedAt()));
            }
            if(candidateReport.getStartedAt() == null
                    || candidateReport.getFinishedAt() == null
                    || candidateReport.getState().equals(CandidateStateEnum.ABSENT.getCode())){
                row.add("");
            }else {
                row.add((candidateReport.getFinishedAt().getTime() - candidateReport.getStartedAt().getTime())/1000/60);
            }
            row.add(candidateReport.getAnswerList().size());
            dataList.add(row);
        }
        return dataList;
    }
}
