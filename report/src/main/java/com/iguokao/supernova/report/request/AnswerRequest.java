package com.iguokao.supernova.report.request;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.report.document.Answer;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class AnswerRequest {
    private String candidateId;
    private String questionId;
    private String partId;
    private Integer questionType;
    private List<String> value;

    public static Answer of(AnswerRequest request){
        if(request == null){
            return null;
        }
        Answer res = new Answer();
        res.setAnswerId(new ObjectId());
        res.setQuestionId(new ObjectId(request.getQuestionId()));
        res.setPartId(new ObjectId(request.getPartId()));
        res.setValue(request.getValue());
        res.setQuestionType(request.getQuestionType());
        return res;
    }

    public static List<Answer> of(List<AnswerRequest> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<Answer> res = new ArrayList<>();
        for(AnswerRequest obj : list){
            res.add(of(obj));
        }
        return res;
    }

    public static Map<String, List<Answer>> of(Map<String, List<AnswerRequest>> map){
        if(map.isEmpty()){
            return new HashMap<>();
        }
        Map<String, List<Answer>> res = new HashMap<>();
        for(String candidateId : map.keySet()){
            res.put(candidateId, AnswerRequest.of(map.get(candidateId)));
        }
        return res;
    }
}
