package com.iguokao.supernova.report.response;

import com.iguokao.supernova.report.document.Answer;
import com.iguokao.supernova.report.document.CandidateReport;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ReviewReportResponse {
    private String candidateId;
    private String fullName;
    private List<SimpleAnswerResponse> answerList;
    public static List<ReviewReportResponse> of(List<CandidateReport> list) {
        List<ReviewReportResponse> res = new ArrayList<>();
        for(CandidateReport candidateReport : list){
            ReviewReportResponse r = new ReviewReportResponse();
            r.setCandidateId(candidateReport.get_id().toString());
            r.setFullName(candidateReport.getFullName());
            r.setAnswerList(SimpleAnswerResponse.of(candidateReport.getAnswerList()));
            res.add(r);
        }
        return res;
    }

    @Setter
    @Getter
    public static class SimpleAnswerResponse {
        private String questionId;
        private String answerId;
        private List<String> value = new ArrayList<>();

        public static SimpleAnswerResponse of(Answer answer) {
            SimpleAnswerResponse r = new SimpleAnswerResponse();
            BeanUtils.copyProperties(answer, r);
            r.setQuestionId(answer.getQuestionId().toString());
            r.setAnswerId(answer.getAnswerId().toString());
            return r;
        }

        public static List<SimpleAnswerResponse> of(List<Answer> list) {
            List<SimpleAnswerResponse> res = new ArrayList<>();
            for(Answer answer : list){
                res.add(of(answer));
            }
            return res;
        }
    }
}
