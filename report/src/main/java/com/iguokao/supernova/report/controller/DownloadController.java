package com.iguokao.supernova.report.controller;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ExamRemote;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.report.document.CandidateReport;
import com.iguokao.supernova.report.enums.ExceptionEnum;
import com.iguokao.supernova.report.request.ScoreRequest;
import com.iguokao.supernova.report.service.CandidateReportService;
import com.iguokao.supernova.report.util.ExcelPaperUtil;
import com.iguokao.supernova.report.util.ExcelScoreUtil;
import com.iguokao.supernova.report.util.ExcelThirdAnswerUtil;
import com.iguokao.supernova.report.util.ExcelThirdCandidateUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
@RequestMapping("/api/v1/download")
@RequiredArgsConstructor
public class DownloadController {

    private final CandidateReportService candidateReportService;
    private final ExamRemote examRemote;
    private final AuditService auditService;

    @PostMapping("/excel/score")
    @Operation(summary = "成绩单")
    public void score(@RequestBody ScoreRequest request, HttpServletResponse response) throws IOException {
        List<CandidateReport> list = candidateReportService.getBySubjectIdAndScore(request.getSubjectId());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("成绩单", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 审计
        AuditItem item = new AuditItem(request.getCompanyId(), AuditTypeEnum.REPORT_SCORE.getCode(), request.getSubjectId());
        this.auditService.add(item);

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        EasyExcel.write(response.getOutputStream())
                .sheet("成绩单")
                .head(ExcelScoreUtil.getExcelHeader(request.getPartInfo()))
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(ExcelScoreUtil.getExcelData(list, request.getSubjectName(), request.getPartInfo()));
    }

    @PostMapping("/excel/paper/detail")
    @Operation(summary = "试卷详情")
    public void paper(@RequestBody ScoreRequest request, HttpServletResponse response) throws IOException {
        List<CandidateReport> list = candidateReportService.getBySubjectIdAndScore(request.getSubjectId());
        RestResponse<List<QuestionJudgeRemoteResponse>> restResponse = this.examRemote.subjectQuestion(request.getSubjectId());
        if(restResponse.getCode() != 0){
            throw ServiceException.remote(restResponse);
        }

        // 审计
        AuditItem item = new AuditItem(request.getCompanyId(), AuditTypeEnum.REPORT_ANSWER.getCode(), request.getSubjectId());
        this.auditService.add(item);

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("试卷作答详情_" + request.getSubjectName(), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);


        EasyExcel.write(response.getOutputStream())
                .sheet("试卷作答详情_" + request.getSubjectName())
                .head(ExcelPaperUtil.getExcelHeader(restResponse.getData()))
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(ExcelPaperUtil.getExcelData(list, restResponse.getData()));
    }


    @GetMapping("/third/excel/candidate/{subjectId}")
    @Operation(summary = "试卷详情")
    public void excelCandidate(@PathVariable String subjectId, HttpServletResponse response) throws IOException {
        List<CandidateReport> list = candidateReportService.getBySubjectId(subjectId);

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("sqlite_candidate_" + subjectId, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);


        EasyExcel.write(response.getOutputStream())
                .sheet("candidate")
                .head(ExcelThirdCandidateUtil.getExcelHeader())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(ExcelThirdCandidateUtil.getExcelData(list));
    }

    @GetMapping("/third/excel/answer/{subjectId}")
    @Operation(summary = "试卷详情")
    public void excelAnswer(@PathVariable String subjectId, HttpServletResponse response) throws IOException {
        List<CandidateReport> list = candidateReportService.getBySubjectIdAndScore(subjectId);
        list.removeIf(c -> c.getAnswerList().isEmpty());
        RestResponse<List<QuestionJudgeRemoteResponse>> restResponse;
        try {
            restResponse = this.examRemote.subjectQuestion(subjectId);
            if(restResponse.getCode() != 0){
                throw ServiceException.remote(restResponse);
            }
        } catch (Exception e){
            throw new ServiceException(BaseExceptionEnum.REMOTE_COMMUNICATION_FAILED);
        }

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("sqlite_candidate_" + subjectId, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.GENERAL);

        EasyExcel.write(response.getOutputStream())
                .sheet("sqlite_candidate_" + subjectId)
                .head(ExcelThirdAnswerUtil.getExcelHeader())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(ExcelThirdAnswerUtil.getExcelData(list, restResponse.getData()));
    }


    @GetMapping("/third/sqlite/{subjectId}")
    @Operation(summary = "试卷详情 sqlite")
    public void sqlite(@PathVariable String subjectId, HttpServletResponse response) throws IOException {

        String path = this.candidateReportService.sqliteExport(subjectId);
        // 创建文件对象
        File sqliteFile = new File(path);

        // 判断文件是否存在
        if (!sqliteFile.exists()) {
            throw new ServiceException(ExceptionEnum.EXPORT_SQLITE_FAILED);
        }

        // 设置响应头，提示浏览器下载文件
        response.setContentType("application/x-sqlite3");  // 设置下载文件类型
        response.setHeader("Content-Disposition", "attachment; filename=\"" + sqliteFile.getName() + "\"");

        // 读取文件并写入响应流
        try (InputStream inputStream = new FileInputStream(sqliteFile);
             OutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();  // 确保所有数据都写入响应流
        } catch (IOException e) {
            throw new ServiceException(BaseExceptionEnum.IO_EXCEPTION);
        }
    }
}
