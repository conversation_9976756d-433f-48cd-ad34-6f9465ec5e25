package com.iguokao.supernova.report.response;

import com.iguokao.supernova.report.document.TransCandidate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
public class ExchangeCandidateResponse {

    @NotNull(message = "candidateId 不能为空")
    private String candidateId;

    @Schema(description = "答案")
    private List<AnswerResponse> answerList;

    public static ExchangeCandidateResponse of(TransCandidate obj){
        if(obj == null){
            return null;
        }
        ExchangeCandidateResponse res = new ExchangeCandidateResponse();
        res.setCandidateId(obj.get_id().toString());
        res.setAnswerList(AnswerResponse.of(obj.getAnswerList()));
        return res;
    }

    public static List<ExchangeCandidateResponse> of(List<TransCandidate> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<ExchangeCandidateResponse> res = new ArrayList<>();
        for(TransCandidate obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
