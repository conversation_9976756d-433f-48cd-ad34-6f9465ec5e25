package com.iguokao.supernova.report.repository;

import com.iguokao.supernova.report.document.CandidateReport;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.CountQuery;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface CandidateReportRepository extends MongoRepository<CandidateReport, ObjectId> {
    int countByPeriodId(ObjectId periodId);
    int countBySubjectId(ObjectId subject);

    @CountQuery("{ periodId:?0, fullName: null }")
    int countNotSynced(ObjectId periodId);


    List<CandidateReport> findByPeriodId(ObjectId periodId);
    List<CandidateReport> findBySubjectId(ObjectId subjectId);
    List<CandidateReport> findByRoomId(ObjectId roomId);

    Optional<CandidateReport> findByRoomIdAndNum(ObjectId roomId, Integer num);

}
