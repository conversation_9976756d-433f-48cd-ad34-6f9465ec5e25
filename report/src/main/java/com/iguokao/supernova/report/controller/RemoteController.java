package com.iguokao.supernova.report.controller;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.request.SubjectCandidateAnswerRequest;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.response.ScoreImportResponse;
import com.iguokao.supernova.report.document.Answer;
import com.iguokao.supernova.report.document.CandidateReport;
import com.iguokao.supernova.report.request.*;
import com.iguokao.supernova.report.response.ReviewAnswerResponse;
import com.iguokao.supernova.report.response.ReviewCandidateResponse;
import com.iguokao.supernova.report.response.ReviewReportResponse;
import com.iguokao.supernova.report.service.CandidateReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/remote")
@RequiredArgsConstructor
public class RemoteController {

    private final CandidateReportService candidateReportService;

    @GetMapping("/review/candidate/total/{subjectId}")
    @Operation(summary = "机考可以阅卷考生总数")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<Long> reviewCandidateList(@PathVariable String subjectId) {
        long count = candidateReportService.reviewAvailableCount(subjectId);
        return RestResponse.success(count);
    }

    @GetMapping("/review/candidate/{subjectId}/{limit}")
    @Operation(summary = "阅卷考生")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<ReviewCandidateResponse>> reviewCandidateList(@PathVariable String subjectId, @PathVariable String limit) {
        List<CandidateReport> candidateList = candidateReportService.sortByFinalScoreAndLimit(subjectId,Integer.parseInt(limit));
        return RestResponse.success(ReviewCandidateResponse.of(candidateList));
    }

    @PostMapping("/review/candidate/answer")
    @Operation(summary = "阅卷考生的答案")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = ReviewAnswerResponse.class))))
    public RestResponse<List<ReviewAnswerResponse>> reviewCandidateAnswer(@RequestBody ReviewCandidateAnswerRequest request) {
        List<Answer> answerList = candidateReportService.reviewAnswerList(request.getCandidateId(), request.getQuestionIdList());
        List<ReviewAnswerResponse> reviewAnswerList = new ArrayList<>();
        for(Answer answer : answerList){
            ReviewAnswerResponse a = new ReviewAnswerResponse();
            a.setQuestionId(answer.getQuestionId().toString());
            a.setAnswerId(answer.getAnswerId().toString());
            a.setValue(answer.getValue());
            reviewAnswerList.add(a);
        }
        return RestResponse.success(reviewAnswerList);
    }

    @PostMapping("/review/subject/answer/page")
    @Operation(summary = "阅卷考生的答案")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = ReviewAnswerResponse.class))))
    public RestResponse<PageResponse<ReviewReportResponse>> subjectAnswerPage(@RequestBody SubjectAnswerPageRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<CandidateReport>, Integer> page =  this.candidateReportService.getAnswerPageBySubjectId(request.getSubjectId(), request.getQuestionId(),pageable);
        PageResponse<ReviewReportResponse> res = new PageResponse<>(ReviewReportResponse.of(page.first()), page.second(), pageable);
        return RestResponse.success(res);
    }

    @PostMapping("/review/subject/answer")
    @Operation(summary = "阅卷考生的答案")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = ReviewAnswerResponse.class))))
    public RestResponse<List<ReviewReportResponse>> subjectAnswerList(@RequestBody SubjectAnswerRequest request) {
        List<CandidateReport> list =  this.candidateReportService.getAnswerBySubjectId(request.getSubjectId(), request.getQuestionId(), request.getCandidateId());
        return RestResponse.success(ReviewReportResponse.of(list));
    }

    @PostMapping("/review/subject/candidate/answer")
    public RestResponse<List<ReviewReportResponse>> candidateAnswer(@RequestBody SubjectCandidateAnswerRequest request) {
        List<CandidateReport> list =  this.candidateReportService.getAnswerByCandidateId(request.getSubjectId(), request.getQuestionId(), request.getCandidateIdList());
        return RestResponse.success(ReviewReportResponse.of(list));
    }

    @GetMapping("/review/answered/count/{subjectId}/{questionId}")
    @Operation(summary = "阅卷 科目 某题 答题 答案")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = ReviewAnswerResponse.class))))
    public RestResponse<Integer> subjectAnsweredCount(@PathVariable String subjectId, @PathVariable String questionId) {
        int count = this.candidateReportService.getQuestionAnsweredCount(subjectId, questionId);
        return RestResponse.success(count);
    }

    @PostMapping("/review/async/result")
    @Operation(summary = "同步考生答案")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> asyncResult(@RequestBody ReviewCandidateResultRequest request) {
        this.candidateReportService.asyncResult(request.getCandidateUuid(),request.getReviewResultList());
        return RestResponse.success();
    }

    @GetMapping("/score/{subjectId}")
    @Operation(summary = "导出科目成绩")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<List<ScoreImportResponse>> scoreExport(@PathVariable String subjectId) {
        List<CandidateReport> list = this.candidateReportService.getBySubjectId(subjectId);
        List<ScoreImportResponse> res = new ArrayList<>();
        list.forEach(candidateReport -> {
            ScoreImportResponse item = new ScoreImportResponse();
            item.setScore(candidateReport.getScore());
            item.setIdCardNum(candidateReport.getIdCardNum());
            if(!candidateReport.getState().equals(CandidateStateEnum.FINISHED.getCode())
                    || candidateReport.getAnswerList().isEmpty()){
                item.setAbsent(true);
            }
            res.add(item);
        });
        return RestResponse.success(res);
    }




}
