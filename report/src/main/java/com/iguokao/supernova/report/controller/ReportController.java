package com.iguokao.supernova.report.controller;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ExamRemote;
import com.iguokao.supernova.common.response.PeriodFinishedResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.report.document.CandidateReport;
import com.iguokao.supernova.report.enums.ExceptionEnum;
import com.iguokao.supernova.report.request.CandidateAnswerRequest;
import com.iguokao.supernova.report.request.FaceDiffRequest;
import com.iguokao.supernova.report.request.SyncRequest;
import com.iguokao.supernova.report.service.CacheService;
import com.iguokao.supernova.report.service.CandidateReportService;
import com.iguokao.supernova.report.service.TencentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/report")
@RequiredArgsConstructor
public class ReportController {

    private final CandidateReportService candidateReportService;
    private final ExamRemote examRemote;
    private final TencentService tencentService;
    private final CacheService cacheService;
    private final AuditService auditService;

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/room/answer/upload")
    @Operation(summary = "上传房间答案")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = CandidateAnswerRequest.class))))
    public RestResponse<String> answerUpload(@RequestBody List<CandidateAnswerRequest> request) {
        List<CandidateReport> list = new ArrayList<>();
        request.forEach(c -> {
            CandidateReport report = CandidateAnswerRequest.of(c);
            list.add(report);
        });
        this.candidateReportService.uploadRoomAnswer(list);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/candidate/answer/upload")
    @Operation(summary = "上传考生答案")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> candidateAnswerUpload(@RequestBody CandidateAnswerRequest request) {
        CandidateReport report = CandidateAnswerRequest.of(request);
        this.candidateReportService.uploadCandidateAnswer(report);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/face/diff")
    @Operation(summary = "人脸对比 Win7专用")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Boolean.class)))
    public RestResponse<Boolean> faceDiff(@RequestBody FaceDiffRequest request) {
        Boolean res = this.tencentService.compareFace(request.getDiffUrl(), request.getBaseUrl());
        return RestResponse.success(res);
    }

    @GetMapping("/candidate/count/{periodId}")
    @Operation(summary = "报告数量")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Integer.class)))
    public RestResponse<Integer> candidateCount(@PathVariable String periodId) {
        int count = this.candidateReportService.periodCandidateCount(periodId);
        return RestResponse.success(count);
    }

    @GetMapping("/candidate/reset/{candidateId}")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> reset(@PathVariable String candidateId) {
        this.candidateReportService.resetCandidateReport(candidateId);
        return RestResponse.success();
    }

    @PostMapping("/candidate/sync")
    @Operation(summary = "同步考生数据")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> syncCandidate(@RequestBody SyncRequest request) {
        RestResponse<PeriodFinishedResponse> response = this.examRemote.periodCheckFinished(request.getPeriodId());
        if(response.getCode() != 0){
            throw ServiceException.remote(response);
        }
        // 审计
        AuditItem item = new AuditItem(request.getCompanyId(), AuditTypeEnum.REPORT_SYNC.getCode(), request.getPeriodId());
        this.auditService.add(item);

        String lockKey = String.format("sync_%s", request.getPeriodId());
        if(this.cacheService.getLock(lockKey) != null){
            throw new ServiceException(ExceptionEnum.CANDIDATE_SYNCING);
        }
        if(response.getData().getFinishCount().equals(response.getData().getRoomCount())){
            this.candidateReportService.syncCandidate(request.getPeriodId());
            // 更新状态
            RestResponse<String> doneResponse = this.examRemote.periodDone(request.getPeriodId());
            if(doneResponse.getCode() != 0){
                throw ServiceException.remote(response);
            }
            return RestResponse.success();
        } else {
            throw new ServiceException(ExceptionEnum.PERIOD_NOT_FINISHED, String.format("，有%d个房间未完成提交答案，请检查房间状态",
                    response.getData().getRoomCount() - response.getData().getFinishCount()));
        }
    }

    @GetMapping("/not/sync/count/{periodId}")
    @Operation(summary = "未同步统计")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<Integer> notSyncCount(@PathVariable String periodId) {
        int count = this.candidateReportService.notSyncCount(periodId);
        return RestResponse.success(count);
    }

}
