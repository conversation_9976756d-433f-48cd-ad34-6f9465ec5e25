package com.iguokao.supernova.report.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Document("candidate_report")
@NoArgsConstructor
public class CandidateReport extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    @Indexed(name = "projectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    @Indexed(name = "subjectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId subjectId;

    @Indexed(name = "periodId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId periodId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId siteId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId roomId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId paperId;

    private Integer state;

    private Long num;  // 准考证号
    private String fullName;
    private String loginPassword;
    private Integer confirmState;
    private Integer idCardType;
    private Integer gender;
    private String avatar;
    private String city;
    private String idCardNum;
    private String mobile;
    private String email;
    private String refuseMessage;
    private Integer seatNum;
    private Integer paperIndex;
    private String note;
    private String custom1;
    private String custom2;
    private String custom3;

    private List<Answer> answerList = new ArrayList<>();
    private List<Action> actionList = new ArrayList<>();
    private Double score;
    private Date startedAt;
    private Date finishedAt;
    private String illegal;

    public CandidateReport(ObjectId candidateId) {
        this.set_id(candidateId);
    }
}
