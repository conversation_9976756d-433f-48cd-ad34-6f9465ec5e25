package com.iguokao.supernova.report.response;

import com.iguokao.supernova.report.document.CandidateReport;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ReviewAnswerResponse {
    private String questionId;
    private String answerId;
    private List<String> value = new ArrayList<>();
}
