package com.iguokao.supernova.report.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.response.ActionRemoteResponse;
import com.iguokao.supernova.common.response.CandidateRemoteResponse;
import com.iguokao.supernova.common.response.QuestionGroupOptionRemoteResponse;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.enums.QuestionScoreTypeEnum;
import com.iguokao.supernova.common.enums.QuestionTypeEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ExamRemote;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.report.document.Action;
import com.iguokao.supernova.report.document.Answer;
import com.iguokao.supernova.report.document.CandidateReport;
import com.iguokao.supernova.report.enums.AnswerJudgeResultEnum;
import com.iguokao.supernova.report.enums.ExceptionEnum;
import com.iguokao.supernova.report.repository.CandidateReportRepository;
import com.iguokao.supernova.report.request.ReviewResultInfoRequest;
import com.iguokao.supernova.report.util.ExcelThirdAnswerUtil;
import com.iguokao.supernova.report.util.ExcelThirdCandidateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

import static org.springframework.data.mongodb.core.query.Criteria.where;

@Service
@Slf4j
@RequiredArgsConstructor
public class CandidateReportServiceImpl implements CandidateReportService{
    private final CacheService cacheService;
    private final CandidateReportRepository candidateReportRepository;
    private final ExamRemote examRemote;
    private final MongoTemplate mongoTemplate;

    @Value(value = "${app.upload-path}")
    private String uploadPath;

    @Override
    public void uploadCandidateAnswer(CandidateReport report) {
        List<QuestionJudgeRemoteResponse> questionList = this.cacheService.getPeriodQuestion(report.getPeriodId().toString());
        List<ActionRemoteResponse> actionList = this.cacheService.getPeriodIllegalList(report.getPeriodId().toString());
        this.saveCandidateReportV2(report, questionList, actionList
                .stream()
                .filter(a -> a.getCandidateId() != null)
                .toList());
    }

    @Override
    public void uploadRoomAnswer(List<CandidateReport> reportList) {
        if(reportList.isEmpty()){
            return;
        }
        String periodId = reportList.get(0).getPeriodId().toString();
        String roomId = reportList.get(0).getRoomId().toString();
        List<QuestionJudgeRemoteResponse> questionList = this.cacheService.getPeriodQuestion(periodId);
        List<ActionRemoteResponse> actionList = this.cacheService.getPeriodIllegalList(periodId);

        for (CandidateReport report : reportList){
            this.saveCandidateReportV2(report, questionList, actionList
                    .stream()
                    .filter(a -> a.getCandidateId() != null)
                    .toList());
        }
        log.info("Period {} Room {} 上传答案成功 {} 人",
                reportList.get(0).getPeriodId().toString(),
                reportList.get(0).getRoomId().toString(),
                reportList.size());
        // 保存当前房间答案 完成
        RestResponse<String> response = this.examRemote.roomAnswer(periodId, roomId);
        if(response.getCode() != 0){
            throw ServiceException.remote(response);
        }
    }

    private void saveCandidateReportV2(CandidateReport report, List<QuestionJudgeRemoteResponse> questionList, List<ActionRemoteResponse> actionList){
        CandidateReport existReport = this.candidateReportRepository.findById(report.get_id())
                .orElse(null);
        if(existReport != null && report.getAnswerList().size() > existReport.getAnswerList().size()){
            // 存在 并且 新数据 答案数量 > 旧的
            existReport.setAnswerList(report.getAnswerList());
            existReport.setState(report.getState());
            existReport.setStartedAt(report.getStartedAt());
            existReport.setFinishedAt(report.getFinishedAt());
            double score = this.judgeAnswerList(questionList, report.getAnswerList());
            existReport.setScore(score);
        } else if(existReport == null) {
            existReport = report;
            double score = this.judgeAnswerList(questionList, report.getAnswerList());
            existReport.setScore(score);
        }
        List<ActionRemoteResponse> list = actionList
                .stream()
                .filter(a -> a.getCandidateId().equals(report.get_id().toString()))
                .toList();
        if(!list.isEmpty()){
            String illegal = String.join(",", list
                    .stream()
                    .map(ActionRemoteResponse::getText)
                    .toList());
            existReport.setIllegal(illegal);
            existReport.setActionList(Action.of(list));
        }
        this.candidateReportRepository.save(existReport);
    }

    private void saveCandidateReport(CandidateReport report, List<QuestionJudgeRemoteResponse> questionList, List<ActionRemoteResponse> actionList){
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(where("_id").is(report.get_id())),
                Aggregation.project()
                        .and("answerList").project("size").as("num"));

        AggregationResults<CandidateReport> res = mongoTemplate.aggregate(
                aggregation, CandidateReport.class, CandidateReport.class
        );
        if(!res.getMappedResults().isEmpty() && res.getMappedResults().get(0).getNum() >= report.getAnswerList().size()){
            return;
        }
        //如果考生状态是已完成或者其答案不为空
        if(report.getState().equals(CandidateStateEnum.FINISHED.getCode()) || !report.getAnswerList().isEmpty()){
            double score = this.judgeAnswerList(questionList, report.getAnswerList());
            report.setScore(score);
        }
        List<ActionRemoteResponse> list = actionList
                .stream()
                .filter(a -> a.getCandidateId().equals(report.get_id().toString()))
                .toList();
        if(!list.isEmpty()){
            String illegal = String.join(",", list
                    .stream()
                    .map(ActionRemoteResponse::getText)
                    .toList());
            report.setIllegal(illegal);
            report.setActionList(Action.of(list));
        }
        this.candidateReportRepository.save(report);
    }

    @Override
    public int periodCandidateCount(String periodId) {
        return this.candidateReportRepository.countByPeriodId(new ObjectId(periodId));
    }

    @Async
    @Override
    public void syncCandidate(String periodId) {
        String lockKey = String.format("sync_%s", periodId);
        this.cacheService.setLock(lockKey);

        List<CandidateReport> candidateReportList = this.candidateReportRepository.findByPeriodId(new ObjectId(periodId));
        if(candidateReportList.isEmpty()){
            return;
        }
        log.info("考生记录 - {}" , candidateReportList.size());
        int count = (int)candidateReportList
                .stream()
                .filter(c -> c.getSubjectId() == null)
                .count();
        if(count == 0){
            throw new ServiceException(ExceptionEnum.CANDIDATE_SYNCED);
        }
//        if(count < candidateReportList.size()){
//            throw new ServiceException(ExceptionEnum.CANDIDATE_SYNCING);
//        }
        RestResponse<List<CandidateRemoteResponse>> res  = this.examRemote.candidateList(periodId);
        log.info("远程获取 - {}" , res.getData().size());
        if(res.getCode() != 0){
            throw ServiceException.remote(res);
        }
        int i = 0;
        for(CandidateRemoteResponse candidate : res.getData()){
            CandidateReport report = this.candidateReportRepository.findById(new ObjectId(candidate.getCandidateId()))
                    .orElse(new CandidateReport(new ObjectId(candidate.getCandidateId())));
            BeanUtils.copyProperties(candidate, report, "state");
            if(report.getState() == null || report.getState() < (CandidateStateEnum.FINISHED.getCode())){
                report.setState(CandidateStateEnum.ABSENT.getCode());
            }
            report.setSiteId(new ObjectId(candidate.getSiteId()));
            report.setProjectId(new ObjectId(candidate.getProjectId()));
            report.setSubjectId(new ObjectId(candidate.getSubjectId()));
            report.setCompanyId(new ObjectId(candidate.getCompanyId()));
            this.candidateReportRepository.save(report);
            System.out.println(i++);
            this.cacheService.deleteLock(lockKey);
        }
//        for(CandidateReport candidateReport : candidateReportList){
//            CandidateRemoteResponse dto = this.examRemote.candidateInfo(candidateReport.get_id().toString()).getData();
//            if(dto == null){
//                System.out.println(candidateReport.get_id().toString());
//                continue;
//            }
//            BeanUtils.copyProperties(dto, candidateReport, "state");
//            if(dto.getSiteId() != null){
//                candidateReport.setSiteId(new ObjectId(dto.getSiteId()));
//            }
//            if(dto.getProjectId() != null){
//                candidateReport.setProjectId(new ObjectId(dto.getProjectId()));
//            }
//            if(dto.getSubjectId() != null){
//                candidateReport.setSubjectId(new ObjectId(dto.getSubjectId()));
//            }
//            if(dto.getPaperId() != null){
//                candidateReport.setPaperId(new ObjectId(dto.getPaperId()));
//            }
//            if(dto.getCompanyId() != null){
//                candidateReport.setCompanyId(new ObjectId(dto.getCompanyId()));
//            }
//            this.candidateReportRepository.save(candidateReport);
//            System.out.println(i);
//        }
        log.info(String.format("同步考生 %s %d", periodId, candidateReportList.size()));
    }


//    @Override
//    public void syncCandidate(String periodId) {
//        List<CandidateReport> candidateReportList = this.candidateReportRepository.findByPeriodId(new ObjectId(periodId));
//        if(candidateReportList.isEmpty()){
//            return;
//        }
//        log.info("考生记录 - {}" , candidateReportList.size());
//        int count = (int)candidateReportList
//                .stream()
//                .filter(c -> c.getSubjectId() == null)
//                .count();
//        if(count == 0){
//            throw new ServiceException(ExceptionEnum.CANDIDATE_SYNCED);
//        }
////        if(count < candidateReportList.size()){
////            throw new ServiceException(ExceptionEnum.CANDIDATE_SYNCING);
////        }
////        RestResponse<List<CandidateRemoteResponse>> res  = this.examRemote.candidateList(periodId);
////        log.info("远程获取 - {}" , res.getData().size());
////        if(res.getCode() != 0){
////            throw ServiceException.remote(res);
////        }
////        for(CandidateRemoteResponse candidate : res.getData()){
////            CandidateReport report = candidateReportList
////                    .stream()
////                    .filter(r -> candidate.getCandidateId().equals(r.get_id().toString()))
////                    .findFirst()
////                    .orElse(new CandidateReport(new ObjectId(candidate.getCandidateId())));
////            BeanUtils.copyProperties(candidate, report, "state");
////            if(report.getState() < (CandidateStateEnum.FINISHED.getCode())){
////                report.setState(CandidateStateEnum.ABSENT.getCode());
////            }
////            report.setSiteId(new ObjectId(candidate.getSiteId()));
////            report.setProjectId(new ObjectId(candidate.getProjectId()));
////            report.setSubjectId(new ObjectId(candidate.getSubjectId()));
////            report.setCompanyId(new ObjectId(candidate.getCompanyId()));
////            this.candidateReportRepository.save(report);
////            System.out.println(i++);
////
////        }
//        int i = 0;
//        for(CandidateReport candidateReport : candidateReportList){
//            CandidateRemoteResponse dto = this.examRemote.candidateInfo(candidateReport.get_id().toString()).getData();
//            if(dto == null){
//                System.out.println(candidateReport.get_id().toString());
//                continue;
//            }
//            BeanUtils.copyProperties(dto, candidateReport, "state");
//            if(dto.getSiteId() != null){
//                candidateReport.setSiteId(new ObjectId(dto.getSiteId()));
//            }
//            if(dto.getProjectId() != null){
//                candidateReport.setProjectId(new ObjectId(dto.getProjectId()));
//            }
//            if(dto.getSubjectId() != null){
//                candidateReport.setSubjectId(new ObjectId(dto.getSubjectId()));
//            }
//            if(dto.getPaperId() != null){
//                candidateReport.setPaperId(new ObjectId(dto.getPaperId()));
//            }
//            if(dto.getCompanyId() != null){
//                candidateReport.setCompanyId(new ObjectId(dto.getCompanyId()));
//            }
//            this.candidateReportRepository.save(candidateReport);
//            System.out.println(i);
//        }
//        log.info(String.format("同步考生 %s %d", periodId, candidateReportList.size()));
//    }

    @Override
    public List<CandidateReport> getBySubjectId(String subjectId) {
        return this.candidateReportRepository.findBySubjectId(new ObjectId(subjectId));
    }

    @Override
    public List<CandidateReport> getByPeriodId(String periodId) {
        return this.candidateReportRepository.findByPeriodId(new ObjectId(periodId));
    }

    @Override
    public CandidateReport getByCandidateId(String candidateId) {
        return this.candidateReportRepository.findById(new ObjectId(candidateId)).orElse(null);
    }

    @Override
    public List<CandidateReport> sortByFinalScoreAndLimit(String subjectId, int quantity) {

        Long count = this.candidateAvailableCount(subjectId);
        if(count < quantity){
            throw new ServiceException(ExceptionEnum.REVIEW_CANDIDATE_OUT_OF_RANGE);
        }

        Query query = new Query();
        query.addCriteria(
                Criteria.where("subjectId").is(new ObjectId(subjectId)).orOperator(
                        Criteria.where("state").is(CandidateStateEnum.FINISHED.getCode()),
                        Criteria.where("answerList").not().size(0)
                )
        ).with(Sort.by(Sort.Direction.DESC, "score","_id"));;

        query.fields()
                .include("_id")
                .include("score")
                .include("fullName")
                .include("mobile")
                .include("email");
        query.limit(quantity);
        return this.mongoTemplate.find(query, CandidateReport.class);
    }

    @Override
    public List<Answer> reviewAnswerList(String candidateId, List<String> questionIdList) {
        CandidateReport candidateReport = this.candidateReportRepository.findById(new ObjectId(candidateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
        List<Answer> answerList = new ArrayList<>();
        for(Answer answer : candidateReport.getAnswerList()){
            if(questionIdList.contains(answer.getQuestionId().toString())){
                answerList.add(answer);
            }
        }
        return answerList;
    }

    @Override
    public void asyncResult(String candidateId, List<ReviewResultInfoRequest> reviewResultList) {
        Query query = new Query().addCriteria(where("_id").is(new ObjectId(candidateId)));
        query.fields()
                .include("_id")
                .include("answerList")
                .include("score");
        CandidateReport candidateReport = mongoTemplate.findOne(query, CandidateReport.class);

        if(null != candidateReport){
            for(ReviewResultInfoRequest r : reviewResultList){
                for(Answer a : candidateReport.getAnswerList()){
                    if(a.getQuestionId().toString().equals(r.getQuestionId())){
                        a.setScore(r.getScore());
                        a.setJudgeResult(r.getJudgeResult());
                        break;
                    }
                }
            }
        }
        else {
            throw new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND);
        }

        double score = candidateReport.getAnswerList()
                .stream()
                .mapToDouble(Answer::getScore)
                .sum();
        // 更新考生
        Update candidateUpdate = new Update();
        candidateUpdate.set("answerList", candidateReport.getAnswerList());
        candidateUpdate.set("isReviewing", false);
        candidateUpdate.set("score", score);
        mongoTemplate.updateFirst(query, candidateUpdate, CandidateReport.class);
    }

    @Override
    public List<CandidateReport> getBySubjectIdAndScore(String subjectId) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "score","state","num"))
                .addCriteria(where("subjectId").is(new ObjectId(subjectId)));

        return mongoTemplate.find(query, CandidateReport.class);
    }

    @Override
    public void resetCandidateReport(String c) {
        CandidateReport report = this.candidateReportRepository.findById(new ObjectId(c)).orElse(null);
        List<QuestionJudgeRemoteResponse> questionList = this.cacheService.getPeriodQuestion(report.getPeriodId().toString());
        double score = this.judgeAnswerList(questionList, report.getAnswerList());
        report.setScore(score);
        this.candidateReportRepository.save(report);
    }

    @Override
    public Long reviewAvailableCount(String subjectId) {
        return this.candidateAvailableCount(subjectId);
    }

    @Override
    public int notSyncCount(String periodId) {
        return this.candidateReportRepository.countNotSynced(new ObjectId(periodId));
    }

    @Override
    public String sqliteExport(String subjectId) {
        String path = String.format("%s/sql_%s.db", uploadPath, subjectId);
        try {
            Files.deleteIfExists(Paths.get(path));
        } catch (IOException e) {
            throw new ServiceException(BaseExceptionEnum.IO_EXCEPTION);
        }
        try (Connection conn = DriverManager.getConnection(String.format("*******************************", path))) {
            conn.setAutoCommit(false);

            // 创建表
            String createCandidateSQL = "CREATE TABLE \"kaosheng\" (  " +
                    "\"ZKZH\"  VARCHAR(15) NOT NULL DEFAULT '',  " +
                    "\"KMDM\"  VARCHAR(1) NOT NULL DEFAULT '1',   " +
                    "\"BKJB\"  VARCHAR(2) NOT NULL DEFAULT '01',  " +
                    "\"BKZY\"  VARCHAR(2) NOT NULL DEFAULT '01',  " +
                    "\"QKBJ\"  VARCHAR(1) NOT NULL DEFAULT '',  " +
                    "\"WJBJ\"  VARCHAR(1) NOT NULL DEFAULT '',  " +
                    "\"KSXM\"  VARCHAR(64) DEFAULT '');";
            conn.createStatement().execute(createCandidateSQL);

            // 插入数据
            String insertCandidateSQL = "INSERT INTO kaosheng (ZKZH,KMDM,BKJB,BKZY,QKBJ,WJBJ,KSXM) VALUES (?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement statement = conn.prepareStatement(insertCandidateSQL)) {
                List<CandidateReport> list = this.getBySubjectId(subjectId);
                List<List<Object>>  rows = ExcelThirdCandidateUtil.getExcelData(list);
                for (List<Object> row : rows) {
                    for (int i = 0; i < row.size(); i++) {
                        statement.setObject(i + 1, row.get(i));
                    }
                    statement.addBatch();
                }
                statement.executeBatch();
                conn.commit();  // 确保事务提交
                log.info("数据成功插入Sqlite 考生 {}", subjectId);
            }

            String createAnswerSQL = "CREATE TABLE \"answer\" (" +
                    "\"ZKZH\"VARCHAR(15) NOT NULL DEFAULT ''," +
                    "\"STLX\"VARCHAR(1) NOT NULL DEFAULT ''," +
                    "\"STXH\"INT NOT NULL DEFAULT 1," +
                    "\"XTH\"INT NOT NULL DEFAULT 1," +
                    "\"KSXX\"VARCHAR(10) NOT NULL DEFAULT ''," +
                    "\"DTNR\"TEXT NOT NULL DEFAULT 0," +
                    "\"SFDT\"INT NOT NULL" +
                    ");";
            conn.createStatement().execute(createAnswerSQL);
            String insertAnswerSQL = "INSERT INTO answer (ZKZH,STLX,STXH,XTH,KSXX,DTNR,SFDT) VALUES (?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement statement = conn.prepareStatement(insertAnswerSQL)) {
                List<CandidateReport> list = this.getBySubjectIdAndScore(subjectId);
                list.removeIf(c -> c.getAnswerList().isEmpty());
                RestResponse<List<QuestionJudgeRemoteResponse>> restResponse;
                try {
                    restResponse = this.examRemote.subjectQuestion(subjectId);
                    if(restResponse.getCode() != 0){
                        throw ServiceException.remote(restResponse);
                    }
                } catch (Exception e){
                    throw new ServiceException(BaseExceptionEnum.REMOTE_COMMUNICATION_FAILED);
                }

                List<List<Object>>  rows = ExcelThirdAnswerUtil.getExcelData(list, restResponse.getData());
                for (List<Object> row : rows) {
                    for (int i = 0; i < row.size(); i++) {
                        statement.setObject(i + 1, row.get(i));
                    }
                    statement.addBatch();
                }
                statement.executeBatch();
                conn.commit();
                log.info("数据成功插入Sqlite 答案 {}", subjectId);
            }
        } catch (SQLException e) {
            log.error(e.getMessage());
            throw new ServiceException(ExceptionEnum.EXPORT_SQLITE_FAILED);
        }
        return path;
    }

    @Override
    public Tuple2<List<CandidateReport>, Integer> getAnswerPageBySubjectId(String subjectId, String questionId, Pageable pageable) {
        Query query = new Query();
        query.addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.fields()
                .include("fullName")
                .include("answerList")
                .include("_id");
        long count = this.mongoTemplate.count(query, CandidateReport.class);

        query.with(pageable);
        List<CandidateReport> list = this.mongoTemplate.find(query, CandidateReport.class);
        list.forEach(candidateReport -> candidateReport.getAnswerList()
                .removeIf(answer -> !questionId.equals(answer.getQuestionId().toString())));
        return new Tuple2<>(list, (int) count);
    }

    @Override
    public List<CandidateReport> getAnswerBySubjectId(String subjectId, String questionId, String candidateId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.fields()
                .include("fullName")
                .include("answerList")
                .include("_id");
        if(candidateId != null){
            query.addCriteria(Criteria.where("_id").is(new ObjectId(candidateId)));
        }
        List<CandidateReport> list = this.mongoTemplate.find(query, CandidateReport.class);
        list.forEach(candidateReport -> candidateReport.getAnswerList()
                .removeIf(answer -> !questionId.equals(answer.getQuestionId().toString())));
        return list;
    }

    @Override
    public List<CandidateReport> getAnswerByCandidateId(String subjectId, String questionId, List<String> candidateIdList) {
        Query query = new Query();
        query.addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)))
                .addCriteria(Criteria.where("_id").in(candidateIdList.stream().map(ObjectId::new).toList()));
        query.fields()
                .include("fullName")
                .include("answerList")
                .include("_id");

        List<CandidateReport> list = this.mongoTemplate.find(query, CandidateReport.class);
        list.forEach(candidateReport -> candidateReport.getAnswerList()
                .removeIf(answer -> !questionId.equals(answer.getQuestionId().toString())));
        return list;
}

    @Override
    public int getQuestionAnsweredCount(String subjectId, String questionId) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "_id"))
                .addCriteria(where("subjectId").is(new ObjectId(subjectId)));
        query.fields()
                .include("answerList")
                .include("_id");
        List<CandidateReport> list = mongoTemplate.find(query, CandidateReport.class);
        List<Answer> answerList = list
                .stream()
                .map(CandidateReport::getAnswerList)
                .flatMap(Collection::stream)
                .filter(a -> a.getQuestionId().toString().equals(questionId))
                .toList();
        return answerList.size();
    }

    private Long candidateAvailableCount(String subjectId){
        Query query = new Query();
        query.addCriteria(
                Criteria.where("subjectId").is(new ObjectId(subjectId)).orOperator(
                        Criteria.where("state").is(CandidateStateEnum.FINISHED.getCode()),
                        Criteria.where("answerList").not().size(0)
                )
        );
        return this.mongoTemplate.count(query, CandidateReport.class);
    }

    private double judgeAnswerList(List<QuestionJudgeRemoteResponse> questionList, List<Answer> answerList){
        double finalScore = 0;
        for(Answer answer: answerList){
            QuestionJudgeRemoteResponse question = questionList
                    .stream()
                    .filter(questionJudgeDTO -> questionJudgeDTO.getQuestionId().equals(answer.getQuestionId().toString()))
                    .findFirst()
                    .orElseThrow();
            finalScore += this.getScore(question, answer);
        }
        return finalScore;
    }

    private double getScore(QuestionJudgeRemoteResponse question, Answer answer){
        answer.setJudgeResult(AnswerJudgeResultEnum.NOT_JUDGE.getCode());
        Double checkScore = 0D;
        // 常规题判题
        if(question.getType().equals(QuestionTypeEnum.SINGLE_SELECTION.getCode())
                || question.getType().equals(QuestionTypeEnum.MULTI_SELECTION.getCode())
                || question.getType().equals(QuestionTypeEnum.YES_NO.getCode())
                || question.getType().equals(QuestionTypeEnum.FREE_SELECTION.getCode())){
            // 判题题目对错
            float isCorrect = trueOrFalse(answer, question);
            if (isCorrect == 1){
                // 正确
                checkScore = question.getQuestionScore();
                answer.setJudgeResult(AnswerJudgeResultEnum.CORRECT.getCode());
            }
            else if(isCorrect > 0){
                // 部分正确
                checkScore = question.getQuestionScore() * isCorrect;
                answer.setJudgeResult(AnswerJudgeResultEnum.PART_CORRECT.getCode());
            }
            else{
                // 错误
                answer.setJudgeResult(AnswerJudgeResultEnum.WRONG.getCode());
            }
        }
        // 复合题判题
        if(question.getType().equals(QuestionTypeEnum.GROUP_SELECTION.getCode())){
            checkScore = trueOrFalseCombo(answer, question);
        }

        answer.setScore(checkScore);

        return checkScore;
    }

    public float trueOrFalse(Answer answer, QuestionJudgeRemoteResponse question){
        float isCorrect = -1;
        //保证答案不为空
        if(answer.getValue() == null){
            return  isCorrect;
        }

        //单选 判断
        if (question.getType().equals(QuestionTypeEnum.SINGLE_SELECTION.getCode()) || question.getType().equals(QuestionTypeEnum.YES_NO.getCode())) {
            //完全正确得分
            if (question.getCorrectValue().equals(answer.getValue())) {
                isCorrect = 1;
            }
        }
        //多选 不定项
        else if (question.getType().equals(QuestionTypeEnum.MULTI_SELECTION.getCode()) || question.getType().equals(QuestionTypeEnum.FREE_SELECTION.getCode())) {
            //记分规则 0 默认严格积分，1是常规积分 2宽松计分
            float missCount = 0;
            float errorCount = 0;
            float successCount = 0;
            //严格计分
            if(question.getScoreType().equals(QuestionScoreTypeEnum.STRICT.getCode())){
                answer.getValue().sort(Comparator.naturalOrder());
                question.getCorrectValue().sort(Comparator.naturalOrder());
                if (question.getCorrectValue().equals(answer.getValue())){
                    isCorrect = 1;
                }
            }
            // 常规计分
            else if(question.getScoreType().equals(QuestionScoreTypeEnum.ROUTINE.getCode())){
                for(int ic =0; ic<answer.getValue().size();ic++){
                    String one = answer.getValue().get(ic);
                    if(question.getCorrectValue().contains(one)){
                        successCount++;
                    }
                    else {
                        errorCount++;
                    }
                }
                if(errorCount == 0){
                    isCorrect = successCount / question.getCorrectValue().size();
                }
            }
            // 宽松计分 （机考系统目前没有开放）
            else if(question.getScoreType().equals(QuestionScoreTypeEnum.LOOSE.getCode())){

                for(int ik =0;ik<answer.getValue().size();ik++){
                    String one = answer.getValue().get(ik);
                    if(question.getCorrectValue().contains(one)){
                        successCount++;
                    }
                    else {
                        errorCount++;
                    }
                }
                if(successCount == question.getCorrectValue().size() && successCount == answer.getValue().size()){
                    isCorrect = 1;
                    return isCorrect;
                }
                if(successCount == 0){
                    isCorrect = 0;
                    return isCorrect;
                }
                missCount = question.getCorrectValue().size() - successCount;
                float deduct = errorCount + missCount;
                float optionListSize = question.getOptionCount();
                isCorrect = 1- deduct/optionListSize;
            }

        }

        else {
            isCorrect = 0;
        }
        return isCorrect;
    }

    public double trueOrFalseCombo(Answer answer, QuestionJudgeRemoteResponse question) {
        //复合题
        double finishScore = 0D;
        //保存的各小题答案完整
        if(question.getGroupOptionList().size() == answer.getValue().size()){
            answer.setGroupScore(new ArrayList<>());
            for(int g=0; g<question.getGroupOptionList().size(); g++){
                //各小题对象
                QuestionGroupOptionRemoteResponse groupOption = question.getGroupOptionList().get(g);
                //对应各小题答案
                String selfValueStr = answer.getValue().get(g);
                List<String> selfValueList = new ArrayList<>();

                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    selfValueList = objectMapper.readValue(selfValueStr,new TypeReference<List<String>>() {});
                    selfValueList.sort(Comparator.naturalOrder());
                }
                catch (Exception e){
                    System.out.println(e.getMessage());
                }
                //正确答案排序
                groupOption.getCorrectValue().sort(Comparator.naturalOrder());
                //进行比较
                if (groupOption.getCorrectValue().equals(selfValueList)){
                    finishScore += groupOption.getQuestionScore();
                    answer.getGroupScore().add(groupOption.getQuestionScore());
                }
                else {
                    answer.getGroupScore().add(0D);
                }
            }

            //设置判题结果
            answer.setJudgeResult(AnswerJudgeResultEnum.WRONG.getCode());
            if(finishScore == question.getQuestionScore()){
                answer.setJudgeResult(AnswerJudgeResultEnum.CORRECT.getCode());
            } else if(finishScore > 0 && finishScore < question.getQuestionScore()){
                answer.setJudgeResult(AnswerJudgeResultEnum.PART_CORRECT.getCode());
            }
        }
        return finishScore;
    }


}
