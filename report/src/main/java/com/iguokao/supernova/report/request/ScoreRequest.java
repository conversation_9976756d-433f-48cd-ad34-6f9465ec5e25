package com.iguokao.supernova.report.request;

import com.iguokao.supernova.report.document.Answer;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class ScoreRequest {
    private String subjectName;
    private String subjectId;
    private String companyId;
    private Map<String, String> partInfo;
}
