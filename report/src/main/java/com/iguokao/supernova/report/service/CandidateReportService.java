package com.iguokao.supernova.report.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.report.document.Answer;
import com.iguokao.supernova.report.document.CandidateReport;
import com.iguokao.supernova.report.request.ReviewResultInfoRequest;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface CandidateReportService {
    void uploadCandidateAnswer(CandidateReport report);

    void uploadRoomAnswer(List<CandidateReport> reportList);
    int periodCandidateCount(String periodId);

    void syncCandidate(String periodId);

    List<CandidateReport> getBySubjectId(String subjectId);
    List<CandidateReport> getByPeriodId(String periodId);

    CandidateReport getByCandidateId(String candidateId);

    List<CandidateReport> sortByFinalScoreAndLimit(String subjectId, int parseInt);

    List<Answer> reviewAnswerList(String candidateId, List<String> questionIdList);

    void asyncResult(String candidateUuid, List<ReviewResultInfoRequest> reviewResultList);

    List<CandidateReport> getBySubjectIdAndScore(String subjectId);

    void resetCandidateReport(String c);

    Long reviewAvailableCount(String subjectId);

    int notSyncCount(String periodId);

    String sqliteExport(String subjectId);

    Tuple2<List<CandidateReport>, Integer> getAnswerPageBySubjectId(String subjectId, String questionId, Pageable pageable);
    List<CandidateReport> getAnswerBySubjectId(String subjectId, String questionId, String candidateId);
    List<CandidateReport> getAnswerByCandidateId(String subjectId, String questionId, List<String> candidateIdList);

    int getQuestionAnsweredCount(String subjectId, String questionId);

}
