package com.iguokao.supernova.report.response;

import com.iguokao.supernova.report.document.Answer;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class AnswerResponse {
    private String questionId;
    private String partId;
    private List<String> value;
    private Integer questionType;

    public static AnswerResponse of(Answer obj){
        if(obj == null){
            return null;
        }
        AnswerResponse res = new AnswerResponse();
//        res.setCandidateId();
        res.setQuestionId(obj.getQuestionId().toString());
        res.setPartId(obj.getPartId().toString());
        res.setValue(obj.getValue());
        res.setQuestionType(obj.getQuestionType());
        return res;
    }

    public static List<AnswerResponse> of(List<Answer> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<AnswerResponse> res = new ArrayList<>();
        for(Answer obj : list){
            res.add(of(obj));
        }
        return res;
    }


}
