package com.iguokao.supernova.report.config;

import com.iguokao.supernova.common.response.ActionRemoteResponse;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * <AUTHOR>
 * 缓存配置类
 */
@Configuration
@Slf4j
@ConfigurationProperties("app.redis")
@Getter
@Setter
class CacheConfig {

    private String host;
    private Integer port;
    private String password;
    private Integer database;
    private Integer maxActive;
    private Integer maxIdle;
    private Integer minIdle;

    @Bean
    LettuceConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration redisConfiguration = new RedisStandaloneConfiguration();
        redisConfiguration.setHostName(host);
        redisConfiguration.setPort(port);
        redisConfiguration.setPassword(password);
        redisConfiguration.setDatabase(database);

        GenericObjectPoolConfig<?> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(maxActive);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);

        LettuceClientConfiguration clientConfiguration = LettucePoolingClientConfiguration.
                builder()
                .poolConfig(poolConfig)
                .build();
        return new LettuceConnectionFactory(redisConfiguration, clientConfiguration);
    }

    @Bean
    public StringRedisTemplate stringRedisTemplate(LettuceConnectionFactory factory) {
        final StringRedisTemplate redisTemplate = new StringRedisTemplate();
        redisTemplate.setConnectionFactory(factory);
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, OperatorRemoteResponse> operatorRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, OperatorRemoteResponse> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(OperatorRemoteResponse.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, QuestionJudgeRemoteResponse> questionJudgeRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, QuestionJudgeRemoteResponse> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(QuestionJudgeRemoteResponse.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, ActionRemoteResponse> actionRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, ActionRemoteResponse> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(ActionRemoteResponse.class));
        return redisTemplate;
    }
}

