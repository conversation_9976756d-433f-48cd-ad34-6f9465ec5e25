package com.iguokao.supernova.report.service;

import com.iguokao.supernova.common.remote.ManagementRemote;
import com.iguokao.supernova.common.request.LoginKeyCheckRequest;
import com.iguokao.supernova.common.service.LoginKeyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LoginKeyServiceImpl implements LoginKeyService {
    private final ManagementRemote managementRemote;

    @Override
    public boolean check(String operatorId, String key) {
        LoginKeyCheckRequest request = new LoginKeyCheckRequest(operatorId, key);
        return this.managementRemote.loginKeyCheck(request).getData();
    }
}
