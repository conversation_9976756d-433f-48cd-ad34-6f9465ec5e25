package com.iguokao.supernova.report.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.report.document.CandidateReport;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class CandidateAnswerRequest {

    @NotNull(message = "periodId 不能为空")
    private String periodId;

    @NotNull(message = "roomId 不能为空")
    private String roomId;

    @NotNull(message = "candidateId 不能为空")
    private String candidateId;

    @NotNull(message = "考生状态不能为空")
    @Schema(description = "状态")
    private Integer state;

    @Schema(description = "开始时间 yyyy-MM-dd hh:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, locale="zh-CN", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date startedAt;

    @Schema(description = "完成时间 yyyy-MM-dd hh:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, locale="zh-CN", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date finishedAt;

    @Schema(description = "答案")
    private List<AnswerRequest> answerList;

    public static CandidateReport of(CandidateAnswerRequest request){
        if(request == null){
            return null;
        }
        CandidateReport res = new CandidateReport();
        res.set_id(new ObjectId(request.getCandidateId()));
        res.setPeriodId(new ObjectId(request.getPeriodId()));
        res.setRoomId(new ObjectId(request.getRoomId()));
        res.setStartedAt(request.getStartedAt());
        res.setFinishedAt(request.getFinishedAt());
        res.setAnswerList(AnswerRequest.of(request.getAnswerList()));
        res.setState(request.getState());
        return res;
    }
}
