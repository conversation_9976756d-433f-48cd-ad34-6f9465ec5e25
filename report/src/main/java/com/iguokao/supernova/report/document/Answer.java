package com.iguokao.supernova.report.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.report.enums.AnswerJudgeResultEnum;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class Answer {
    private ObjectId answerId;
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId questionId;
    private ObjectId partId;

    private Integer questionType;
    private List<String> value = new ArrayList<>();

    private List<String> file = new ArrayList<>();
    private List<String> history = new ArrayList<>();

    private Integer judgeResult = AnswerJudgeResultEnum.NOT_JUDGE.getCode();
    private Double score = .0;
    private List<Double> groupScore;
}
