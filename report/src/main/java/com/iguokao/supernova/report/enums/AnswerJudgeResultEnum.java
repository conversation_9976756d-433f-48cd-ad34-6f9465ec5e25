package com.iguokao.supernova.report.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum AnswerJudgeResultEnum implements BaseEnum {
    NOT_JUDGE(0, "未判"),
    WRONG(1, "错误"),
    PART_CORRECT(2, "部分正确"),
    CORRECT(3, "正确"),
    ;

    AnswerJudgeResultEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
