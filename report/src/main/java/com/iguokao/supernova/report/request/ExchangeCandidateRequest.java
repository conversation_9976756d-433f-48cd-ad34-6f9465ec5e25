package com.iguokao.supernova.report.request;

import com.iguokao.supernova.report.document.TransCandidate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.List;

@Setter
@Getter
public class ExchangeCandidateRequest {

    @NotNull(message = "candidateId 不能为空")
    private String candidateId;

    @Schema(description = "答案")
    private List<AnswerRequest> answerList;

    public static TransCandidate of(ExchangeCandidateRequest request){
        if(request == null){
            return null;
        }
        TransCandidate res = new TransCandidate();
        res.set_id(new ObjectId(request.getCandidateId()));
        res.setAnswerList(AnswerRequest.of(request.getAnswerList()));
        return res;
    }


}
