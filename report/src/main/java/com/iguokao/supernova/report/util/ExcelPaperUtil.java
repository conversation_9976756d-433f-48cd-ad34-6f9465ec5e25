package com.iguokao.supernova.report.util;

import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;
import com.iguokao.supernova.common.enums.QuestionTypeEnum;
import com.iguokao.supernova.common.util.ExcelUtil;
import com.iguokao.supernova.common.util.ScoreUtil;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.report.document.Answer;
import com.iguokao.supernova.report.document.CandidateReport;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class ExcelPaperUtil {

    public static List<List<String>> getExcelHeader(List<QuestionJudgeRemoteResponse> list) {
        List<List<String>> headerList = new ArrayList<>();
        ExcelUtil.addHeaderItem(headerList, "考生编号");
        ExcelUtil.addHeaderItem(headerList, "考生姓名");
        ExcelUtil.addHeaderItem(headerList, "参加状态");
        ExcelUtil.addHeaderItem(headerList, "手机");
        ExcelUtil.addHeaderItem(headerList, "考试用时（分钟）");
        ExcelUtil.addHeaderItem(headerList, "成绩");

        for(QuestionJudgeRemoteResponse q : list){
            String body = q.getBody();
            System.out.println(body);
            if(body.startsWith("&lt;p&gt;")){
                body = StringUtil.htmlRemoveMeta(body);
            }
            System.out.println(body);
            if(body.length() > 30){
                body = body.substring(0, 30) + "...";
            }
            ExcelUtil.addHeaderItem(headerList, body);
            ExcelUtil.addHeaderItem(headerList, "得分");
        }
        return headerList;
    }

    public static List<List<Object>> getExcelData(List<CandidateReport> list, List<QuestionJudgeRemoteResponse> questionList) {
        list = list
                .stream()
                .sorted(Comparator.comparing(CandidateReport::getState))
                .toList();
        List<List<Object>> dataList = new ArrayList<>();
        for(CandidateReport candidateReport : list){
            List<Object> row = new ArrayList<>();
            row.add(candidateReport.getNum());
            row.add(candidateReport.getFullName());
            if(candidateReport.getState().equals(CandidateStateEnum.FINISHED.getCode())
                    || !candidateReport.getAnswerList().isEmpty()){
                row.add("参考");
            } else {
                row.add("缺考");
            }
            row.add(candidateReport.getMobile());
            if(!candidateReport.getState().equals(CandidateStateEnum.ABSENT.getCode())
                    && candidateReport.getFinishedAt() !=null
                    && candidateReport.getStartedAt() != null){
                row.add((candidateReport.getFinishedAt().getTime() - candidateReport.getStartedAt().getTime())/1000 / 60);
            } else {
                row.add("");
            }
            if(candidateReport.getState().equals(CandidateStateEnum.FINISHED.getCode())
                    || !candidateReport.getAnswerList().isEmpty()){
                row.add(ScoreUtil.getFormatScore(candidateReport.getScore()));
            } else {
                row.add("");
            }
            for(QuestionJudgeRemoteResponse q : questionList){
                Answer answer = candidateReport.getAnswerList()
                        .stream()
                        .filter(a -> a.getQuestionId().toString().equals(q.getQuestionId()))
                        .findFirst()
                        .orElse(null);
                if(null != answer){
                    if(q.getType().equals(QuestionTypeEnum.YES_NO.getCode())){
                        row.add(answer.getValue().get(0).equals("0") ? "Y": "N");
                    } else if(q.getType().equals(QuestionTypeEnum.SINGLE_SELECTION.getCode())
                            || q.getType().equals(QuestionTypeEnum.MULTI_SELECTION.getCode())
                            || q.getType().equals(QuestionTypeEnum.FREE_SELECTION.getCode())){
                        row.add(formatAnswer(answer.getValue().toString()));
                    } else if(q.getType().equals(QuestionTypeEnum.GROUP_SELECTION.getCode())) {
                        row.add(String.join(", ", answer.getValue().stream().map(s -> String.format("[%s] ", formatAnswer(s))).toList()));
                    } else if(q.getType().equals(QuestionTypeEnum.QA.getCode())) {
                        row.add(StringUtil.htmlRemoveMeta(answer.getValue().toString()));
                    } else {
                        row.add(answer.getValue().toString());
                    }
                    row.add(ScoreUtil.getFormatScore(answer.getScore()));
                } else {
                    row.add("");
                    row.add("");
                }
            }
            dataList.add(row);
        }
        return dataList;
    }

    public static String formatAnswer(String answer){
        return answer.replace("[", "")
                .replace("]", "")
                .replace(",", "")
                .replace("0", "A")
                .replace("1", "B")
                .replace("2", "C")
                .replace("3", "D")
                .replace("4", "E")
                .replace("5", "F")
                .replace("6", "G")
                .replace("7", "H")
                .replace("8", "I")
                .replace("9", "J");
    }
}
