package com.iguokao.supernova.report.response;

import com.iguokao.supernova.report.document.CandidateReport;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ReviewCandidateResponse {
    @Schema(description = "考生 Id")
    private String candidateId;

    @Schema(description = "考生姓名")
    private String fullName;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "电子邮件")
    private String email;

    @Schema(description = "分数")
    private Double score;


    public static ReviewCandidateResponse of(CandidateReport obj){
        if(obj == null){
            return null;
        }
        ReviewCandidateResponse res = new ReviewCandidateResponse();
        BeanUtils.copyProperties(obj, res);
        res.setCandidateId(obj.get_id().toString());
        return res;
    }

    public static List<ReviewCandidateResponse> of(List<CandidateReport> list){
        if(list == null){
            return new ArrayList<>();
        }
        List<ReviewCandidateResponse> res = new ArrayList<>();
        for(CandidateReport obj : list){
            res.add(of(obj));
        }
        return res;
    }

}
