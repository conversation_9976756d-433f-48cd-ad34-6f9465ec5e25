package com.iguokao.supernova.report.service;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.iai.v20200303.IaiClient;
import com.tencentcloudapi.iai.v20200303.models.CompareFaceRequest;
import com.tencentcloudapi.iai.v20200303.models.CompareFaceResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
@RequiredArgsConstructor
public class TencentServiceImpl implements TencentService{
    private final Credential credential;
    private final IaiClient client;
    @Override
    public boolean compareFace(String diffUrl, String baseUrl) {
        CompareFaceRequest request = new CompareFaceRequest();
        request.setUrlA(baseUrl);
        request.setUrlB(diffUrl);
        try {
            CompareFaceResponse res = client.CompareFace(request);
            return res.getScore() > 60;
        } catch (TencentCloudSDKException e) {
            return false;
        }
    }
}
