package com.iguokao.supernova.report.util;

import com.iguokao.supernova.common.enums.QuestionTypeEnum;
import com.iguokao.supernova.common.response.QuestionGroupOptionRemoteResponse;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;
import com.iguokao.supernova.common.util.ExcelUtil;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.report.document.Answer;
import com.iguokao.supernova.report.document.CandidateReport;

import java.util.*;

public class ExcelThirdAnswerUtil {

    public static List<List<String>> getExcelHeader() {
        List<List<String>> headerList = new ArrayList<>();
        ExcelUtil.addHeaderItem(headerList, "ZKZH 准考证号");
        ExcelUtil.addHeaderItem(headerList, "STLX 试题类型");
        ExcelUtil.addHeaderItem(headerList, "STXH 试题序号");
        ExcelUtil.addHeaderItem(headerList, "XTH 小题号");
        ExcelUtil.addHeaderItem(headerList, "KSXX 考生选项");
        ExcelUtil.addHeaderItem(headerList, "DTNR 答题内容");
        ExcelUtil.addHeaderItem(headerList, "SPDF 是否答题");
        return headerList;
    }

    public static List<List<Object>> getExcelData(List<CandidateReport> list, List<QuestionJudgeRemoteResponse> questionList) {
        list = list
                .stream()
                .sorted(Comparator.comparing(CandidateReport::getState))
                .toList();
        List<List<Object>> res = new ArrayList<>();
        for(CandidateReport candidateReport : list){
            int i =1;
            for(QuestionJudgeRemoteResponse q : questionList){
                Answer answer = candidateReport.getAnswerList()
                        .stream()
                        .filter(a -> a.getQuestionId().toString().equals(q.getQuestionId()))
                        .findFirst()
                        .orElse(null);
                List<List<Object>> rows = getRows(i++, candidateReport, q, answer);
                res.addAll(rows);
            }
        }
        return res;
    }

    public static List<List<Object>> getRows(Integer qNum, CandidateReport candidateReport, QuestionJudgeRemoteResponse q, Answer a){
        List<List<Object>> list = new ArrayList<>();
        if(Objects.equals(q.getType(), QuestionTypeEnum.GROUP_SELECTION.getCode())){
            int i = 1;
            for(QuestionGroupOptionRemoteResponse op : q.getGroupOptionList()){
                List<Object> row = new ArrayList<>();
                row.add(candidateReport.getNum());
                row.add(getQuestionType(op.getType()));
                row.add(qNum);
                row.add(i++);
                if(a == null){
                    //
                    row.add("");
                    row.add("");
                    row.add(0);
                } else {
                    String aItem =  a.getValue().get(i-2);
                    if(aItem.equals("[]")){
                        row.add("");
                        row.add("");
                        row.add(0);
                    } else {
                        row.add(formatAnswer(aItem));
                        row.add("");
                        row.add(1);
                    }
                }
                list.add(row);
            }
        } else {
            List<Object> row = new ArrayList<>();
            row.add(candidateReport.getNum());
            row.add(getQuestionType(q.getType()));
            row.add(qNum);
            row.add("");
            if(a == null){
                //
                row.add("");
                row.add("");
                row.add(0);
            } else {
                if(q.getType().equals(QuestionTypeEnum.QA.getCode())){
                    row.add("");
                    String content = StringUtil.htmlRemoveMeta(a.getValue().get(0));
                    content = String.format("答题字数:%s \r\n%s", StringUtil.countWords(content),  content);
                    row.add(content);
                } else {
                    row.add(formatAnswer(String.join("", a.getValue())));
                    row.add("");
                }

                row.add(1);
            }
            list.add(row);
        }
        return list;
    }



    public static String getQuestionType( Integer type){
        if(type.equals(QuestionTypeEnum.SINGLE_SELECTION.getCode())){
            return "1";
        } else if(type.equals(QuestionTypeEnum.MULTI_SELECTION.getCode()) || type.equals(QuestionTypeEnum.FREE_SELECTION.getCode())){
            return "2";
        }  else {
            return "4";
        }
    }

    public static String formatAnswer(String answer){
        return answer.replace("[", "")
                .replace("\"", "")
                .replace("]", "")
                .replace(",", "")
                .replace("0", "A")
                .replace("1", "B")
                .replace("2", "C")
                .replace("3", "D")
                .replace("4", "E")
                .replace("5", "F")
                .replace("6", "G")
                .replace("7", "H")
                .replace("8", "I")
                .replace("9", "J");
    }
}
