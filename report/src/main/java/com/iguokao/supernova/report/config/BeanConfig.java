package com.iguokao.supernova.report.config;

import com.iguokao.supernova.common.exception.SecurityAccessDeniedHandler;
import com.iguokao.supernova.common.exception.SecurityAuthenticationEntryPoint;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.impl.JwtServiceImpl;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.iai.v20200303.IaiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.filter.ForwardedHeaderFilter;

@Configuration
public class BeanConfig {


    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityAccessDeniedHandler securityAccessDeniedHandler(){
        return new SecurityAccessDeniedHandler();
    }

    @Bean
    public SecurityAuthenticationEntryPoint securityAuthenticationEntryPoint(){
        return new SecurityAuthenticationEntryPoint();
    }

    @Bean
    ForwardedHeaderFilter forwardedHeaderFilter() {
        return new ForwardedHeaderFilter();
    }
    @Bean
    JwtService jwtService(){
        return new JwtServiceImpl();
    }

    @Bean
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(1000);
        executor.setThreadNamePrefix("Async-");
        executor.initialize();
        return executor;
    }

    @Bean
    Credential credential(@Value("${app.tencent.secret-id}") String secretId,
                          @Value("${app.tencent.secret-key}") String secretKey){
        return new Credential(secretId, secretKey);
    }

    @Bean
    IaiClient client(Credential cred){
        return new IaiClient(cred, "ap-beijing");
    }

}
