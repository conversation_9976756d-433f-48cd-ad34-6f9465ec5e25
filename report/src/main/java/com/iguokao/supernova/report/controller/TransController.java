package com.iguokao.supernova.report.controller;

import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.report.document.TransCandidate;
import com.iguokao.supernova.report.request.ExchangeCandidateRequest;
import com.iguokao.supernova.report.response.ExchangeCandidateResponse;
import com.iguokao.supernova.report.service.TransCandidateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/trans")
@RequiredArgsConstructor
public class TransController {
    private final TransCandidateService exchangeService;

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/answer/out")
    @Operation(summary = "转出数据暂存")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> out(@RequestBody List<ExchangeCandidateRequest> request) {
        List<TransCandidate> list = request
                .stream()
                .map(ExchangeCandidateRequest::of)
                .toList();
        this.exchangeService.out(list);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/answer/in")
    @Operation(summary = "接受 转场数据")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExchangeCandidateResponse.class))))
    public RestResponse<List<ExchangeCandidateResponse>> in(@RequestBody List<String> candidateIdList) {
        List<TransCandidate> list = this.exchangeService.in(candidateIdList);
        return RestResponse.success(ExchangeCandidateResponse.of(list));
    }
}
