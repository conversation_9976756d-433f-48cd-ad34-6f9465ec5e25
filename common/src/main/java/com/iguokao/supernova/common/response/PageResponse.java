package com.iguokao.supernova.common.response;


import com.iguokao.supernova.common.entity.Tuple2;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@Schema(name = "PageResponse REST标准分页数据")
public class PageResponse<T> {

    /** 总数量 **/
    @Schema(description = "总数量")
    private Integer total ;

    @Schema(description = "总页数")
    private Integer totalPages ;

    /** 页码 **/
    @Schema(description = "当前页码")
    private Integer page;

    /** 每页条数 **/
    @Schema(description = "每页条数")
    private Integer pageSize;

    @Schema(description = "分页数据")
    private List<T> pageData = new ArrayList<>();


	public PageResponse(Integer total, int page , int pageSize, int totalPages, List<T> pageData){
        this.total = total;
        this.page = page;
        this.pageSize = pageSize;
        this.totalPages = totalPages;
        this.pageData = Objects.requireNonNullElseGet(pageData, ArrayList::new);
    }

    public PageResponse(Page<?> page, List<T> pageData){
        this.total = page.getNumberOfElements();
        this.page = page.getNumber();
        this.pageSize = page.getSize();
        this.totalPages = page.getTotalPages();

        if (pageData != null){
            this.pageData = pageData;
        }
    }

    public PageResponse(List<T> list, int total, Pageable pageable){
        this.total = total;
        this.page = pageable.getPageNumber();
        this.pageSize = pageable.getPageSize();
        this.totalPages = (total + pageable.getPageSize() - 1)  / pageable.getPageSize();
        this.pageData =list;
    }
}
