package com.iguokao.supernova.common.converter;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import org.hibernate.validator.constraints.Length;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = {}) // 这里不需要自定义校验器，直接复用 @Length
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Length(min = 24, max = 24, message = "ID长度必须是24位") // 复用 @Length 逻辑
public @interface ValidObjectId {
    String message() default "ID长度必须是24位";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
