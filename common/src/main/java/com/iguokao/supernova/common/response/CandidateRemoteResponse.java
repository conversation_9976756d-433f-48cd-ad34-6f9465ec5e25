package com.iguokao.supernova.common.response;

import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class CandidateRemoteResponse {
    private String projectId;
    private String companyId;
    private String subjectId;
    private String periodId;
    private String siteId;
    private String roomId;
    private String paperId;
    private String candidateId;

    private Integer state;
    private Long num;  // 准考证号
    private String fullName;
    private String loginPassword;
    private Integer confirmState;
    private Integer idCardType;
    private Integer gender;
    private String avatar;
    private String city;
    private String idCardNum;
    private String mobile;
    private String email;
    private String refuseMessage;
    private Integer seatNum;
    private Integer paperIndex;
    private String note;
    private String custom1;
    private String custom2;
    private String custom3;
}
