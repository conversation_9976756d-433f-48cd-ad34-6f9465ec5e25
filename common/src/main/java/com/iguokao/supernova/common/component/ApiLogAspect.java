package com.iguokao.supernova.common.component;

import com.iguokao.supernova.common.entity.LoggerItem;
import com.iguokao.supernova.common.util.RequestUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

@Aspect
@Slf4j
public class ApiLogAspect {

    @Pointcut("execution(* com.iguokao.*.*.controller..*(..))")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        //保存日志
        saveSysLog(point, time, result);

        return result;
    }

    private void saveSysLog(ProceedingJoinPoint joinPoint, long time, Object result) {

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        LoggerItem loggerItem = new LoggerItem();
        loggerItem.setMethod(method.getName());
        //请求的方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = method.getName();

        loggerItem.setMethod(className + "." + methodName + "()");
        /**/
        //请求的参数
        Object[] args = joinPoint.getArgs();
        //获取request
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();

        //response
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        //设置IP地址
        loggerItem.setClientIp(RequestUtil.getClientIp(request));
        //	方法执行时间
        loggerItem.setTimeConsuming((int) time);
        // 设置请求的URI
        loggerItem.setUri(request.getRequestURI());
        // 请求方法
        loggerItem.setHttpMethod(request.getMethod());
        // 响应的状态码
        if(response!=null){
            loggerItem.setHttpStatusCode(String.valueOf(response.getStatus()));
        }
        // 创建时间
        loggerItem.setCreatedTime(new Date());

        //参数和返回参数
        loggerItem.setParamData(Arrays.toString(args));
        Object str = result;
        if (str != null) {
            str = str.toString().length() > 300 ? str.toString().substring(0, 300) : str.toString();
            loggerItem.setReturnData(str.toString());
        }
        log.info("API -  {}", loggerItem);
    }

}
