package com.iguokao.supernova.common.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class LoggerItem {
    private String clientIp;

    private String uri;

    private String requestBy;

    private String method;

    private String paramData;

    private Date createdTime;

    private String httpMethod;

    private String returnData;

    // 请求时httpStatusCode代码，如：200,400,404等
    private String httpStatusCode;
    //请求耗时秒单位
    private int timeConsuming;
}
