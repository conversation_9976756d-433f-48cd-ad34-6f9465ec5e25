package com.iguokao.supernova.common.enums;

import lombok.Getter;


@Getter
public enum CredentialCategoryEnum implements BaseEnum {

    ID_CARD(1, "居民身份证"),
    PASSPORT(2, "护照"),
    TAIWAN_PERMIT(3, "台湾居民来往大陆通行证（台胞证）"),
    HK_PERMIT(4, "港澳地区居民来往内地通行证"),
    MILITARY_ID(5, "军官证"),
    ;

    CredentialCategoryEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
