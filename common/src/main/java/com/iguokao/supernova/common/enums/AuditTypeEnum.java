package com.iguokao.supernova.common.enums;

import lombok.Getter;


@Getter
public enum AuditTypeEnum implements BaseEnum {
    LOGIN(1, "登录"),

    COMPANY_ADD(10, "添加"),
    COMPANY_UPDATE(11, "更新"),
    COMPANY_DELETE(12, "删除"),
    COMPANY_REGISTRATION_ENABLE(13, "开报名系统"),

    ROLE_ADD(20, "添加"),
    ROLE_UPDATE(21, "修改"),
    ROLE_DELETE(22, "删除"),
    ROLE_LINK_MENU(23, "关联菜单"),
    ROLE_LINK_PERMISSION(24, "关联权限"),

    PERMISSION_ADD(30, "添加"),
    PERMISSION_UPDATE(31, "修改"),
    PERMISSION_DELETE(32, "删除"),

    MENU_ADD(30, "添加"),
    MENU_UPDATE(31, "修改"),
    MENU_DELETE(32, "删除"),

    OPERATOR_ADD(30, "添加"),
    OPERATOR_UPDATE(31, "修改"),
    OPERATOR_DELETE(32, "删除"),
    OPERATOR_PASSWORD_CHANGE(33, "改密"),
    OPERATOR_PASSWORD_RESET(34, "重置"),
    OPERATOR_LINK_ROLE(35, "关联角色"),
    OPERATOR_LINK_COMPANY(36, "关联企业"),

    REPORT_SYNC(301, "重置"),
    REPORT_SCORE(302, "关联角色"),
    REPORT_ANSWER(303, "关联企业"),

    REGISTRATION_PROJECT_ADD(401, "报名-项目-添加"),
    REGISTRATION_PROJECT_UPDATE(402, "报名-项目-更新"),
    REGISTRATION_PROJECT_ONLINE(403, "报名-项目-上线"),
    REGISTRATION_REFUND(404, "报名 - 退款"),
    TEMPLATE_DATA_EDIT(501, "报名表 - 修改"),
    WITHDRAW(601, "提现"),
    ;

    AuditTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
