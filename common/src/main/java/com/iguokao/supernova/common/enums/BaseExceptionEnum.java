package com.iguokao.supernova.common.enums;

import lombok.Getter;

@Getter
public enum BaseExceptionEnum implements BaseEnum {
    UNKNOWN_EXCEPTION(1, "服务繁忙,请稍后再试"),

    OPERATOR_PASSWORD_WRONG(102, "账号不存在或者密码错误"),
    OPERATOR_LOCKED(103, "该操作员账号被锁定"),
    OPERATOR_NOT_FOUND(104, "该操作员未找到"),
    OPERATOR_LOGIN_AUTH_FAILED(105, "登录验证失败"),
    OPERATOR_TOKEN_AUTHORITY_NOT_FOUND(106, "该操作员未找到"),
    OPERATOR_ACCESS_DENIED(403, "访问限制"),
    OPERATOR_CAN_LOGIN_MULTI(409, "该操作员已在其他地方登录，请确定是您本人操作"),

    HTTP_REQUEST_METHOD_NOT_SUPPORTED(111, "该操作员未找到"),
    NULL_POINT_EXCEPTION(112, "对象为空，或者出现了空指针"),
    PARAMETER_VALIDATED_FAILED(113, "参数验证失败"),

    ENUM_MUST_IMPLEMENT_BASE(121, "枚举必须实现自BaseEnum接口"),
    ENUM_CONVERT_FAILED(122, "转换枚举类失败"),

    JWT_VALIDATED_FAILED(103, "JWT验证错误"),
    JWT_EXPIRED(132, "JWT过期"),
    JWT_NOT_MANAGEMENT(133, "JWT非后台"),
    JWT_ID_GET_FAILED(134, "ID获取失败"),

    JSON_OBJECT_CONVERT_EXCEPTION(141, "JSON对象转换错误"),
    JSON_ARRAY_CONVERT_EXCEPTION(142, "JSON数组转换错误"),
    JSON_DECODE_EXCEPTION(143, "JSON解析错误"),
    ARG_VALIDATED_FAILED(144, "参数验证失败"),

    NO_SUCH_ALGORITHM_EXCEPTION(150, "无此算法"),



    MAX_LEN_32(160, "最长32位"),
    REMOTE_COMMUNICATION_FAILED(170, "远程服务通讯失败"),
    IO_EXCEPTION(800, "IO错误"),
    ZIP_EXCEPTION(801, "文件打包错误"),

    IMAGE_CODE_NOT_VALID(900, "图片验证码验证失败"),
    IMAGE_CODE_NOT_FOUND(901, "图片验证码未找到或者已过期"),
    SMS_CODE_NOT_VALID(920, "短信验证码不正确"),
    SMS_CODE_NOT_FOUND(921, "短信验证码未找到"),

    ADMISSION_CARD_GENERATED(910, "准考证已经生成"),
    ADMISSION_CARD_GENERATING(911, "准考证生成中..."),
    IMPORT_PENDING(912, "导入中..."),
    OSS_DOWNLOAD_FAILED(920, "OSS下载失败"),
    ;

    public static final BaseEnum AUDIO_EXECUTE_FAILED = null;

    BaseExceptionEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
