package com.iguokao.supernova.common.remote;

import com.iguokao.supernova.common.response.*;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface StoneExamRemote {
    @RequestLine("POST /api/remote/gen/short")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> genShortUrl(@RequestBody ShortUrlRequest request);

    @RequestLine("POST /api/remote/gen/short/list")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> genShortUrlList(@RequestBody List<ShortUrlRequest> list);

}
