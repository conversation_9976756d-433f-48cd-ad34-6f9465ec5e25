package com.iguokao.supernova.common.exception;

import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.response.RestResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class SecurityAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json; charset=UTF-8");
        response.getOutputStream().write(new RestResponse<>(
                BaseExceptionEnum.OPERATOR_ACCESS_DENIED.getCode(),
                BaseExceptionEnum.OPERATOR_ACCESS_DENIED.getText(),
                ""
        ).toJsonString().getBytes(StandardCharsets.UTF_8));
        response.getOutputStream().close();
    }
}