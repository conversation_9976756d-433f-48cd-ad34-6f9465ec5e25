package com.iguokao.supernova.common.util;


import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import jakarta.xml.bind.DatatypeConverter;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class Md5Util {

    /**
     * MD5 计算散列值
     * @param s 需要计算的字符串
     * @return 计算的字符串摘要
     */
    public static String genSign(String s){
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new ServiceException(BaseExceptionEnum.NO_SUCH_ALGORITHM_EXCEPTION);
        }
        assert md != null;
        md.update(s.getBytes());
        byte[] digest = md.digest();
        return DatatypeConverter
                .printHexBinary(digest)
                .toLowerCase();
    }

//    /**
//     * 签名方法
//     * @param map key value方式的签名输入，key是需要签名的字段，value是字段对应的值
//     * @return 签名后的结果
//     */
//    public static String sign(Map<String, String> map) {
//        List<String> keList = map.keySet()
//                .stream()
//                .sorted(String.CASE_INSENSITIVE_ORDER.thenComparing(Comparator.naturalOrder()))
//                .toList();
//        StringBuilder sb = new StringBuilder();
//        for (int i = 0; i < keList.size(); i++) {
//            sb.append(String.format("%s=%s", keList.get(i), map.get(keList.get(i))));
//            if(i < keList.size() - 1){
//                sb.append("&");
//            }
//        }
//        String signStr = sb.toString();
//        return Md5Util.genSign(signStr);
//    }
//
//    // 这里是 验证的 Demo
//    public static void main(String[] s){
//        Map<String, String> map = new HashMap<>();
//        map.put("appKey", "9Z8rk4tAkR4xCNe-rgaHu");
//        map.put("appSecret", "02e74f10e0327ad868d138f2b4fdd6f0");
//        map.put("timestamp", "1648901684");
//        map.put("version", "v1");
//        map.put("nonce", "1235768770");
//
//        String sign = sign(map);
//    }
}
