package com.iguokao.supernova.common.util;

import org.springframework.core.env.Environment;

public class ProfileUtil {

    public static boolean isDev(Environment environment){
        if(null == environment){
            return true;
        }
        String profile = environment.getActiveProfiles()[0];
        return profile.equals("dev") || profile.equals("prod-t");
    }

    public static boolean isTestOrDev(Environment environment){
        if(null == environment){
            return true;
        }
        String profile = environment.getActiveProfiles()[0];
        return profile.equals("test") || profile.equals("dev");
    }


}
