package com.iguokao.supernova.common.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class OperatorRemoteResponse implements UserDetails{
    private String operatorId;
    private String relatedId;
    private String loginName;
    private String loginPassword;
    private String fullName;
    private String note;
    private String mobile;
    private Integer type;
    private String avatar;
    private Date expiredAt;
    private Boolean enabled = true;
    private Boolean accountNonLocked = true;
    private List<String> roleList = new ArrayList<>();

    @JsonIgnore
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if(roleList.isEmpty()){
            return new ArrayList<>();
        }
        return this.roleList
                .stream()
                .map(SimpleGrantedAuthority::new)
                .toList();
    }

    @JsonIgnore
    @Override
    public String getPassword() {
        return "";
    }

    @JsonIgnore
    @Override
    public String getUsername() {
        return operatorId;
    }

    @JsonIgnore
    @Override
    public boolean isAccountNonExpired() {
        if(this.expiredAt == null){
            return true;
        }
        return new Date().getTime() > this.expiredAt.getTime();
    }

    @JsonIgnore
    @Override
    public boolean isAccountNonLocked() {
        return this.accountNonLocked;
    }

    @JsonIgnore
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @JsonIgnore
    @Override
    public boolean isEnabled() {
        return this.enabled;
    }
}
