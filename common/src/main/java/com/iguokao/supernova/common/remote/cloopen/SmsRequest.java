package com.iguokao.supernova.common.remote.cloopen;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class SmsRequest {
    private String to;
    private String appId;
    private String reqId;
    private String subAppend;
    private String templateId;
    private List<String> datas = new ArrayList<>();
}

/*
{
  "to": "13911281234,15010151234,13811431234",
  "appId": "ff8080813fc70a7b013fc72312324213",
  "reqId": "abc123",
  "subAppend": "8888",
  "templateId": "1",
  "datas": [
    "替换内容",
    "替换内容"
  ]
}
 */