package com.iguokao.supernova.common.remote;

import com.iguokao.supernova.common.document.AiUsage;
import lombok.Getter;
import lombok.Setter;
import java.util.List;


/**
 {
 "output": {
 "choices": [
 {
 "finish_reason": "stop",
 "message": {
 "role": "assistant",
 "content": "{\n  score: 5,\n  scoreItemList: [\n    { key: \"观点明确\", value: 2 },\n    { key: \"分析深刻\", value: 1 },\n    { key: \"语言表达\", value: 2 }\n  ],\n  comment: \"答案基本表达了降准的表面影响，但缺乏对影响机制和传导路径的具体分析，内容较为笼统。\",\n  off-topic: false\n}",
 "reasoning_content": ""
 }
 }
 ]
 },
 "usage": {
 "total_tokens": 319.0,
 "output_tokens": 89.0,
 "input_tokens": 230.0
 },
 "request_id": "1bd6c365-e25a-9d60-8e6b-ea1fb5e6e965"
 }
 */

@Getter
@Setter
public class AiChatResponse {
    private String request_id;

    private AiUsage usage;
    private Output output;


    @Getter
    @Setter
    public static class Output {
        List<Choice> choices;
    }

    @Getter
    @Setter
    public static class Choice {
        private int index;
        private Message message;
        private String finish_reason;

        @Getter
        @Setter
        public static class Message {
            private String role;
            private String content;
            private String reasoning_content;
        }
    }

}
