package com.iguokao.supernova.common.service;

import com.aliyun.oss.OSS;
import com.iguokao.supernova.common.entity.OssSign;

import java.io.File;
import java.util.List;

public interface OssService {
    OSS getOSSClient();
    OssSign getOssSign(String bucketName, Integer expireSecond);

    void uploadFile(String bucket, String key, File file);

    String generateSignedUrl(String bucket, String key, int duration);

    Integer getFileSize(String bucket, String key);

    List<String> fileList(String bucketName, String path);
}
