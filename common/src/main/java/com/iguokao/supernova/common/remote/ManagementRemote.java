package com.iguokao.supernova.common.remote;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.request.LoginKeyCheckRequest;
import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import com.iguokao.supernova.common.response.RestResponse;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

public interface ManagementRemote {
    @RequestLine("GET /api/v1/remote/operator/info/{operatorId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<OperatorRemoteResponse> getOperatorById(@Param("operatorId") String operatorId);

    @RequestLine("POST /api/v1/remote/site/operator/add")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> addSiteOperator(@RequestBody OperatorRemoteResponse operator);

    @RequestLine("GET /api/v1/remote/company/info/{companyId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<CompanyRemoteResponse> getCompany(@Param("companyId") String companyId);

    @RequestLine("POST /api/v1/remote/login/key/check")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<Boolean> loginKeyCheck(@RequestBody LoginKeyCheckRequest request);

    @RequestLine("POST /api/v1/remote/audit")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> audit(@RequestBody AuditItem request);

    @RequestLine("GET /api/v1/remote/sms/code/{operatorId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> smsCode(@Param("operatorId") String operatorId);


}


