package com.iguokao.supernova.common.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.bson.types.ObjectId;

import java.io.IOException;
import java.util.List;

public class ObjectIdListSerializer extends JsonSerializer<List<ObjectId>> {
    @Override
    public void serialize(List<ObjectId> value, JsonGenerator jsonGen,SerializerProvider provider) throws IOException{
        List<String> list = value
                .stream()
                .map(ObjectId::toString)
                .toList();
        jsonGen.writeString(new ObjectMapper().writeValueAsString(list));
    }
}