package com.iguokao.supernova.common.document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class QuestionGroupOption {
    @JsonIgnore
    private Integer scoreType;
    // 复合题
    private Integer type;
    private Double questionScore;
    private String body;
    private Boolean isShowAnswerBox;
    private String analysis;
    private List<QuestionOption> optionList = new ArrayList<>();
    private List<String> correctValue = new ArrayList<>();
}
