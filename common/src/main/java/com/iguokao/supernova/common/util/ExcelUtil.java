package com.iguokao.supernova.common.util;

import java.util.ArrayList;
import java.util.List;

public class ExcelUtil {
    /**
     * Excel 头内容插入
     * @param headerList excel头当前数据
     * @param name 新列名
     */
    public static void addHeaderItem(List<List<String>> headerList, String name){
        List<String> subList = new ArrayList<>();
        subList.add(name);
        headerList.add(subList);
    }

    public static String formatQuestionBody(String body){
        return body
                .replaceAll("&lt;[^&]*&gt;", "")
                .replaceAll("&nbsp;", "");
//        return body
//                .replace("&lt;p&gt;&lt;font face=\"微软雅黑\" size=\"3\"&gt;", "")
//                .replace("&lt;/font&gt;&lt;/p&gt;", "")
//                .replace("&lt;p&gt;", "")
//                .replace("&lt;/p&gt;", "");
    }
}
