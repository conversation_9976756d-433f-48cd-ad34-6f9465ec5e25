package com.iguokao.supernova.common.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class QuestionJudgeRemoteResponse implements Serializable {
    private String questionId;
    private Double questionScore;
    private String body;
    private Integer scoreType;
    private Integer type;
    private Integer optionCount;
    private List<String> correctValue = new ArrayList<>();
    private List<QuestionGroupOptionRemoteResponse> groupOptionList = new ArrayList<>();
}
