package com.iguokao.supernova.common.remote;

import com.iguokao.supernova.common.request.SubjectCandidateAnswerRequest;
import com.iguokao.supernova.common.response.*;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface ReportRemote {
    @RequestLine("GET /api/v1/remote/score/{subjectId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<List<ScoreImportResponse>> scoreExport(@Param("subjectId") String subjectId);
}
