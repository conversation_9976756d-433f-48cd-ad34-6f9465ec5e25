package com.iguokao.supernova.common.response;

import com.aliyun.oss.OSS;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.PolicyConditions;
import com.iguokao.supernova.common.entity.OssSign;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.nio.charset.StandardCharsets;
import java.util.Date;

@Getter
@Setter
public class OssSignResponse {
    @Schema(description = "ID")
    private String accessId;

    @Schema(description = "策略")
    private String policy;

    @Schema(description = "签名")
    private String signature;

    @Schema(description = "目标目录")
    private String dir;

    @Schema(description = "上传地址")
    private String host;

    @Schema(description = "过期时间")
    private Long expire;

    public static OssSignResponse of(OssSign obj){
        if(obj == null){
            return null;
        }
        OssSignResponse res = new OssSignResponse();
        BeanUtils.copyProperties(obj, res);
        return res;
    }
}
