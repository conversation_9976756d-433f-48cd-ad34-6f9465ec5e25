package com.iguokao.supernova.common.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.*;
import com.iguokao.supernova.common.entity.OssSign;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.common.util.ProfileUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

@RequiredArgsConstructor
public class OssServiceImpl implements OssService {

    private final Environment environment;

    @Value("${app.ali.access-key-id}")
    private String accessKeyId;

    @Value("${app.ali.access-key-secret}")
    private String accessKeySecret;

    @Value("${app.ali.oss-endpoint}")
    private String ossEndPoint;

    @Value("${app.ali.oss-endpoint-internal}")
    private String ossEndPointInternal;

    @Override
    public OSS getOSSClient() {
        return new OSSClientBuilder().build(ossEndPoint, accessKeyId, accessKeySecret);
    }

    @Override
    public OssSign getOssSign(String bucketName, Integer expireSecond){
        OSS ossClient = this.getOSSClient();
        OssSign res = new OssSign();
        long expireEndTime = new Date().getTime() + expireSecond * 1000;
        Date expiration = new Date(expireEndTime);
        // PostObject请求最大可支持的文件大小为5 GB，即CONTENT_LENGTH_RANGE为5*1024*1024*1024。
        PolicyConditions policyConditions = new PolicyConditions();
        policyConditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
//            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);

        String postPolicy = ossClient.generatePostPolicy(expiration, policyConditions);
        byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
        String encodedPolicy = BinaryUtil.toBase64String(binaryData);
        String postSignature = ossClient.calculatePostSignature(postPolicy);

        res.setAccessId(accessKeyId);
        res.setPolicy(encodedPolicy);
        res.setSignature(postSignature);
        res.setExpire((expireEndTime / 1000));
        res.setHost(String.format("https://%s.%s", bucketName, ossEndPoint));
        ossClient.shutdown();
        return res;
    }

    @Override
    public void uploadFile(String bucket, String key, File file) {
        OSS ossClient = this.getOSSClient();
        ossClient.putObject(bucket, key, file);
    }

    @Override
    public String generateSignedUrl(String bucket, String key, int duration) {
        if(key == null || key.isEmpty()){
            return null;
        }
        OSS ossClient = this.getOSSClient();
        return ossClient.generatePresignedUrl(bucket, key, new Date(new Date().getTime() + duration * 1000L))
                .toString()
                .replace("http://", "https://");
    }

    @Override
    public Integer getFileSize(String bucket, String key) {
        OSS ossClient = this.getOSSClient();
        ObjectListing res = ossClient.listObjects (bucket, key);
        if(res.getObjectSummaries().isEmpty()){
            return 0;
        }
        return (int)res.getObjectSummaries().get(0).getSize();
    }

    public List<String> fileList(String bucketName, String path) {
        OSS ossClient = this.getOSSClient();
        List<String> allKeys = new ArrayList<>();
        try {
            String continuationToken = null;
            int i = 0;
            do {
                ListObjectsV2Request request = new ListObjectsV2Request()
                        .withBucketName(bucketName)
                        .withPrefix(path)
                        .withMaxKeys(1000)
                        .withContinuationToken(continuationToken);

                ListObjectsV2Result result = ossClient.listObjectsV2(request);

                result.getObjectSummaries().stream()
                        .sorted(Comparator.comparing(OSSObjectSummary::getLastModified))
                        .map(OSSObjectSummary::getKey)
                        .forEach(allKeys::add);

                // 更新 continuationToken，准备下一次分页请求
                continuationToken = result.getNextContinuationToken();
                System.out.println(i++);
            } while (continuationToken != null); // 如果 continuationToken 为空，表示已经获取所有数据

        } catch (Exception e) {
            throw new ServiceException(BaseExceptionEnum.OSS_DOWNLOAD_FAILED);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return allKeys;
    }
}
