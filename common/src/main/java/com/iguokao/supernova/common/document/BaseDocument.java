package com.iguokao.supernova.common.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;

import java.util.Date;

@Getter
@Setter
public class BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    @Id
    ObjectId _id;

    Date createdAt;
    Date updatedAt;

    public BaseDocument() {
        this.createdAt = new Date();
    }
}
