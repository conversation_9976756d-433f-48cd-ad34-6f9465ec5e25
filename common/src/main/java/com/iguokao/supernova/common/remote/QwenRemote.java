package com.iguokao.supernova.common.remote;


import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

public interface QwenRemote {

    @RequestLine("POST /services/aigc/text-generation/generation")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    AiChatResponse chatCompletion(@RequestBody AiChatRequest request);
}
