package com.iguokao.supernova.common.service.impl;

import com.iguokao.supernova.common.constant.RoleConstant;
import com.iguokao.supernova.common.service.JwtService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import javax.crypto.SecretKey;
import java.util.*;
import java.util.function.Function;

public class JwtServiceImpl implements JwtService {

    @Value("${app.security.jwt.secret-key}")
    private String secretKey;
    @Value("${app.security.jwt.expiration}")
    private long jwtExpiration;
    @Value("${app.security.jwt.refresh-token.expiration}")
    private long refreshExpiration;

    @Override
    public String currentOperatorId() {
        return SecurityContextHolder
                .getContext()
                .getAuthentication()
                .getPrincipal()
                .toString();
    }

    @Override
    public List<String> currentRole() {
        Authentication authentication = SecurityContextHolder
                .getContext()
                .getAuthentication();

        return authentication
                .getAuthorities()
                .stream()
                .map(GrantedAuthority::getAuthority)
                .filter(authority -> authority.startsWith(RoleConstant.PREFIX))
                .toList();
    }


    @Override
    public String getId(String prefix) {
        Authentication authentication = SecurityContextHolder
                .getContext()
                .getAuthentication();

        String id = authentication
                .getAuthorities()
                .stream()
                .map(GrantedAuthority::getAuthority)
                .filter(authority -> authority.startsWith(String.format("id_%s", prefix)))
                .findFirst()
                .orElse(null);

        if (id == null) {
            return null;
        }
        return id.replace(String.format("id_%s_", prefix), "");
    }

    public String extractOperatorId(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public List<GrantedAuthority> extractAuthorities(String token) {
        String authorities = extractClaim(token, claims -> {
            if (claims.get("authorities") instanceof ArrayList<?> obj) {
                return String.join(",", obj
                        .stream()
                        .map(String::valueOf)
                        .toList());
            }
            return null;
        });
        return AuthorityUtils.commaSeparatedStringToAuthorityList(authorities);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    @Override
    public String generateToken(Collection<? extends GrantedAuthority> authorityList, UserDetails userDetails, List<String> idList) {
        Map<String, Object> extraClaims = new HashMap<>();
        List<String> list = new ArrayList<>(authorityList
                .stream()
                .map(GrantedAuthority::getAuthority)
                .toList());
        if (!idList.isEmpty()) {
            idList.forEach(s -> list.add(String.format("id_%s", s)));
        }
        extraClaims.put("authorities", list);
        return generateToken(extraClaims, userDetails);
    }

    public String generateToken(Map<String, Object> extraClaims, UserDetails userDetails) {
        return buildToken(extraClaims, userDetails, jwtExpiration);
    }

    public String generateRefreshToken(UserDetails userDetails) {
        return buildToken(new HashMap<>(), userDetails, refreshExpiration);
    }

    public boolean isTokenValid(String token, UserDetails userDetails) {
        final String operatorId = extractOperatorId(token);
        return (operatorId.equals(userDetails.getUsername())) && !isTokenExpired(token);
    }

    public Claims extractAllClaims(String token) {
        return Jwts
                .parser()
                .verifyWith(getSignInKey())
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    private String buildToken(Map<String, Object> extraClaims, UserDetails userDetails, long expiration) {
//        return Jwts
//                .builder()
//                .setClaims(extraClaims)
//                .setSubject(userDetails.getUsername())
//                .setIssuedAt(new Date(System.currentTimeMillis()))
//                .setExpiration()
//                .signWith(getSignInKey(), SignatureAlgorithm.HS256)
//                .compact();

        return Jwts.builder()
                .claims(extraClaims)
                .subject(userDetails.getUsername())
                .issuedAt(new Date(System.currentTimeMillis()))
                .expiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(getSignInKey(), Jwts.SIG.HS256)
                .compact();
    }

    private boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    private Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    private SecretKey getSignInKey() {
        byte[] keyBytes = Decoders.BASE64.decode(secretKey);
        return Keys.hmacShaKeyFor(keyBytes);
    }
}