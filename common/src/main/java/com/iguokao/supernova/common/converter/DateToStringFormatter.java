package com.iguokao.supernova.common.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class DateToStringFormatter extends JsonSerializer<Date> {

    private final TimeZone tz = TimeZone.getTimeZone("Asia/Shanghai");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        if(value != null) {
            sdf.setTimeZone(tz);
            gen.writeString(sdf.format(value));
        } else {
            gen.writeNull();
        }
    }
}