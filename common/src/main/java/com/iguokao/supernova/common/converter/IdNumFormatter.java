package com.iguokao.supernova.common.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class IdNumFormatter extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        if(value != null) {
            if(value.length() == 18){
                String start = value.substring(0, 6);
                String end = value.substring(12, 17);
                gen.writeString(String.format("%sxxxxxx%s", start, end));
            }
        } else {
            gen.writeNull();
        }
    }
}