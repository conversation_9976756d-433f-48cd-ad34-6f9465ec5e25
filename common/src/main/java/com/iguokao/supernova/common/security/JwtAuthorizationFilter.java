package com.iguokao.supernova.common.security;

import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.constant.RoleConstant;
import com.iguokao.supernova.common.constant.SecurityConstant;
import com.iguokao.supernova.common.enums.BaseEnum;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.exception.auth.LoginKeyCheckFailedException;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.LoginKeyService;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
public class JwtAuthorizationFilter extends BasicAuthenticationFilter {
    private final JwtService jwtService;
    private final LoginKeyService loginKeyService;

    public JwtAuthorizationFilter(JwtService jwtService, LoginKeyService loginKeyService, AuthenticationManager authManager) {
        super(authManager);
        this.jwtService = jwtService;
        this.loginKeyService = loginKeyService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain chain) throws IOException, ServletException {

        final String authHeader = request.getHeader(SecurityConstant.AUTH_HEADER);
        final String jwt;

        if(authHeader == null || !authHeader.startsWith(SecurityConstant.JWT_PREFIX)){
            chain.doFilter(request, response);
            return;
        }
        jwt = authHeader.substring(7);
        try {
            String operatorId = jwtService.extractOperatorId(jwt);
            List<GrantedAuthority> authorityList = jwtService.extractAuthorities(jwt);
            Authentication authentication =  new UsernamePasswordAuthenticationToken(operatorId, null, authorityList);
            SecurityContextHolder.getContext().setAuthentication(authentication);
            // loginKey防止多点登录
            if(authorityList.stream().noneMatch(a -> a.getAuthority().equals(RoleConstant.ROOM))){
                String currentLoginKey = jwtService.getId(IdConstant.LOGIN_KEY_PREFIX);
                if(currentLoginKey != null && !this.loginKeyService.check(operatorId, currentLoginKey)){
                    throw new LoginKeyCheckFailedException(BaseExceptionEnum.OPERATOR_CAN_LOGIN_MULTI.getText());
                }
            }

            chain.doFilter(request, response);
        } catch (ExpiredJwtException e) {
            responseError(BaseExceptionEnum.JWT_EXPIRED, response);
            log.error("{}{}", BaseExceptionEnum.JWT_EXPIRED.getText(), e.getLocalizedMessage());
        } catch (JwtException e) {
            responseError(BaseExceptionEnum.OPERATOR_ACCESS_DENIED, response);
            log.error("{}{}", BaseExceptionEnum.OPERATOR_ACCESS_DENIED.getText(), e.getLocalizedMessage());
        } catch (LoginKeyCheckFailedException e){
            responseError(BaseExceptionEnum.OPERATOR_CAN_LOGIN_MULTI, response);
            log.error("{}{}", BaseExceptionEnum.OPERATOR_CAN_LOGIN_MULTI.getText(), e.getLocalizedMessage());
        }
    }

    private void responseError(BaseEnum e, HttpServletResponse res) throws IOException {
        res.setContentType("application/json; charset=utf-8");
        RestResponse<String> response = new RestResponse<String>(e.getCode(), e.getText(), "");
        res.getOutputStream().write(response.toJsonString().getBytes(StandardCharsets.UTF_8));
        res.getOutputStream().close();
    }
}
