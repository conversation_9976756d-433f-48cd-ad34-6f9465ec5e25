package com.iguokao.supernova.common.converter;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.RoundingMode;
import java.text.DecimalFormat;

public class DoubleFormatter extends JsonSerializer<Double> {

    private final DecimalFormat df = new DecimalFormat("0.00");

    @Override
    public void serialize(Double value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        if(value != null) {
            df.setMaximumFractionDigits(1);
            df.setGroupingSize(0);
            df.setRoundingMode(RoundingMode.HALF_UP);
            gen.writeNumber(df.format(value));
        } else {
            gen.writeNull();
        }
    }
}
