package com.iguokao.supernova.common.document;

import com.wf.captcha.SpecCaptcha;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
public class ImageCode {
    private String key;
    private String text;
    private String imageCode;

    public static ImageCode genCode(SpecCaptcha specCaptcha){
        ImageCode imageCode = new ImageCode();
        imageCode.setText(specCaptcha.text().toLowerCase());
        imageCode.setKey(UUID.randomUUID().toString().replace("-", "").toLowerCase());
        return imageCode;
    }
}
