package com.iguokao.supernova.common.remote;
import com.iguokao.supernova.common.request.*;
import com.iguokao.supernova.common.response.ImportCandidateResponse;
import com.iguokao.supernova.common.response.RestResponse;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface RegistrationRemote {

    @RequestLine("POST /api/v1/remote/company/register")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> register(@RequestBody CompanyConfigRegisterParam request);

    @RequestLine("POST /api/v1/remote/candidate/import")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<List<ImportCandidateResponse>> importCandidate(@RequestBody ImportCandidateRequest request);
}
