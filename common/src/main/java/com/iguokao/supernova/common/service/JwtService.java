package com.iguokao.supernova.common.service;

import io.jsonwebtoken.Claims;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public interface JwtService {
    String currentOperatorId();
    List<String> currentRole();

    String getId(String prefix);

    String extractOperatorId(String token);
    List<GrantedAuthority> extractAuthorities(String token);
    <T> T extractClaim(String token, Function<Claims, T> claimsResolver);
    String generateToken(Collection<? extends GrantedAuthority> authorityList, UserDetails userDetails, List<String> idList);
    String generateToken(Map<String, Object> extraClaims, UserDetails userDetails);
    boolean isTokenValid(String token, UserDetails userDetails);
    String generateRefreshToken(UserDetails userDetails);
    Claims extractAllClaims(String token);
}
