package com.iguokao.supernova.common.service;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.CloopenRemote;
import com.iguokao.supernova.common.remote.cloopen.SmsResponse;

import java.util.Random;

public interface ImageCodeService {
    ImageCode getImageCode(String key);
    void setImageCode(ImageCode imageCode);
    void deleteImageCode(String key);

    static void checkAndRun(ImageCode imageCode, ImageCodeService imageCodeService, Runnable runnable){
        ImageCode code =  imageCodeService.getImageCode(imageCode.getKey());
        if(null == code){
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_FOUND);
        } else if(code.getText().equals(imageCode.getImageCode().toLowerCase())) {
            runnable.run();
        } else {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_VALID);
        }
        imageCodeService.deleteImageCode(imageCode.getKey());
    }
}
