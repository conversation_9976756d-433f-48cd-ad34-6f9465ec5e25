package com.iguokao.supernova.common.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum ActionTypeEnum implements BaseEnum {
    LOGIN(1, "登录"),
    ENV_CHECK(2, "环境检测"),
    CAMERA_TEST(3, "摄像头测试"),
    EXAM_TEST(4, "试考"),

    DATA_CANDIDATE(6, "下载考生数据"),
    DATA_PAPER(7, "下载试卷"),
    PASSWORD_GET(8, "获取密钥"),

    CHECK_IN(10, "开始进场"),
    SIGNATURE(11, "考生签到"),
    STARTED(12, "考试开始"),
    STARTED_HAND(13, "手动开考"),

    SEAT_CHANGE(20, "换座位"),
    OFF_RULE(21, "违规"),
    ILLEGAL(22, "违纪"),
    ROOM_CHANGE(23, "转场"),
    DELAY(24, "延时"),
    LATE_ENTER(25, "迟到入场"),
    SUBMIT_FORCE(26, "强制交卷"),
    SUBMIT_CANCEL(27, "撤销交卷"),
    LATE(28, "迟到"),

    DELAY_ROOM(30, "考场延时"),

    FINISHED(50, "考试完成"),
    SUBMIT_ANSWER(51, "已提交答案数据"),
    SUBMIT_ACTION(52, "已提交日志"),
    DATA_CLEAR(60, "清除数据"),
    ;

    ActionTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
