package com.iguokao.supernova.common.document;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Getter
@Setter
public class AuditItem {
    private String operatorId;
    private String companyId;
    private Integer type;
    private Integer value;
    private String text;

    public AuditItem(String companyId, Integer type, Object json) {
        this.companyId = companyId;
        this.type = type;
        if(json != null){
            try {
                this.text = new ObjectMapper().writeValueAsString(json);
            } catch (JsonProcessingException e){
                throw new ServiceException(BaseExceptionEnum.JSON_OBJECT_CONVERT_EXCEPTION);
            }
        }
    }
}
