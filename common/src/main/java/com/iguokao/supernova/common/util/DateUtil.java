package com.iguokao.supernova.common.util;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

public class DateUtil
{

    public static String dateToStr(Date date, String pattern)
    {
        if ((date == null) || (date.toString().isEmpty()))
            return null;
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        return formatter.format(date);
    }

    public static String dateToStr(Date date) {
        return dateToStr(date, "yyyy/MM/dd HH:mm:ss");
    }

    public static String dateToStrStd(Date date) {
        if(date == null)
            return null;
        return dateToStr(date, "yyyy-MM-dd HH:mm:ss");
    }

    public static boolean isWorkTime(Date date) {
        // 转换 Date 为 LocalDateTime
        LocalDateTime localDateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 获取时间部分
        LocalTime time = localDateTime.toLocalTime();
        // 获取星期几
        DayOfWeek dayOfWeek = localDateTime.getDayOfWeek();

        // 定义工作时间段
        LocalTime start = LocalTime.of(9, 0);
        LocalTime end = LocalTime.of(17, 0);

        // 判断是否是周一到周五（工作日）
        boolean isWeekday = dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY;

        // 只有是工作日，并且时间在 9:00-17:00 之间，才返回 true
        return isWeekday && !time.isBefore(start) && !time.isAfter(end);
    }
}
