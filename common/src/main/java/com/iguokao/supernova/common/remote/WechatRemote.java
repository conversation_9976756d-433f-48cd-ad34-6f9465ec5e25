package com.iguokao.supernova.common.remote;

import com.iguokao.supernova.common.request.PaperTaskAddRequest;
import com.iguokao.supernova.common.response.AuthorizationCodeResponse;
import com.iguokao.supernova.common.response.RestResponse;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

public interface WechatRemote {
    @RequestLine("GET /sns/oauth2/access_token?appid={appid}&secret={secret}&code={code}&grant_type=authorization_code")
    AuthorizationCodeResponse authorization_code(@Param("appid") String appid,
                                                 @Param("secret") String secret,
                                                 @Param("code") String code);
}
