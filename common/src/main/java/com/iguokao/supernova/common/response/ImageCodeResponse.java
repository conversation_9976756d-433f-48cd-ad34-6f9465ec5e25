package com.iguokao.supernova.common.response;

import com.iguokao.supernova.common.document.ImageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "图片验证码 Response", description = "")
public class ImageCodeResponse {
    @Schema(name = "图片验证码信息 Base64")
    private String imageCode;

    @Schema(name = "key, 验证码key")
    private String key;

    public static ImageCodeResponse of(ImageCode obj){
        ImageCodeResponse vo = new ImageCodeResponse();
        vo.setImageCode(obj.getImageCode());
        vo.setKey(obj.getKey());
        return vo;
    }
}
