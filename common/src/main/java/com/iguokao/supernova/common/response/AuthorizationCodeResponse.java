package com.iguokao.supernova.common.response;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AuthorizationCodeResponse {
    private String access_token;
    private Integer expires_in;
    private String refresh_token;
    private String openid;
    private String scope;

}

/*
{
  "access_token": "89_44AjTO9Dp6UMnkUQWdHo6zNZx2uredQvpaySRPFjqxFQgnYb96N3PxQ865KZ5c5kB5t4oYXxRlJVCJQPWRNWtn9G9EYfHUq1LRYKISMiFxE",
  "expires_in": 7200,
  "refresh_token": "89__Q1aG7oJhmR6ZJgkPe3VhIREUgd-_UvcoSQLZqPCItmXTdin-E6G40S9nEitz_B62Fkq4Kmkk38T8fuMg3h7P0hu0pRKKoGVFY2m3-p0qaA",
  "openid": "oddXT5hH9GjdALRf_kd0nw0oM0qk",
  "scope": "snsapi_base"
}
* */