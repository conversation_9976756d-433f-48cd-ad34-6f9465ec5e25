package com.iguokao.supernova.common.exception;

import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.response.RestResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
public class SecurityAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authenticationException) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json; charset=UTF-8");
        response.getOutputStream().write(new RestResponse<>(
                BaseExceptionEnum.OPERATOR_LOGIN_AUTH_FAILED.getCode(),
                BaseExceptionEnum.OPERATOR_LOGIN_AUTH_FAILED.getText(),
                ""
        ).toJsonString().getBytes(StandardCharsets.UTF_8));
        response.getOutputStream().close();
    }
}
