package com.iguokao.supernova.common.exception;

import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.response.RestResponse;
import feign.codec.DecodeException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.annotation.HandlerMethodValidationException;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 全局一场处理
 */
@Slf4j
@ControllerAdvice
@RequiredArgsConstructor
public class BaseExceptionHandler {

    private final Environment environment;

    @ResponseBody
    @ExceptionHandler(value = ConstraintViolationException.class)
    public RestResponse<String> exception(ConstraintViolationException e) {
        printInfo(e);
        return RestResponse.failed(BaseExceptionEnum.PARAMETER_VALIDATED_FAILED.getCode(), BaseExceptionEnum.PARAMETER_VALIDATED_FAILED.getText());
    }

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public RestResponse<String> exception(Exception e) {
        printInfo(e);
        return RestResponse.failed(BaseExceptionEnum.UNKNOWN_EXCEPTION.getCode(), BaseExceptionEnum.UNKNOWN_EXCEPTION.getText());
    }

    @ResponseBody
    @ExceptionHandler(value = BaseException.class)
    public RestResponse<String> handleISSException(BaseException e) {
        printInfo(e);
        return RestResponse.failed(e.getCode(), e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = AccessDeniedException.class)
    public RestResponse<String> handleAccessDeniedException(AccessDeniedException e) {
        printInfo(e);
        return RestResponse.failed(BaseExceptionEnum.OPERATOR_ACCESS_DENIED.getCode(), BaseExceptionEnum.OPERATOR_ACCESS_DENIED.getText());
    }

    @ResponseBody
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public RestResponse<String> exception(HttpRequestMethodNotSupportedException e) {
        printInfo(e);
        return RestResponse.failed(BaseExceptionEnum.HTTP_REQUEST_METHOD_NOT_SUPPORTED.getCode(), BaseExceptionEnum.HTTP_REQUEST_METHOD_NOT_SUPPORTED.getText());
    }


    @ResponseBody
    @ExceptionHandler(value = IllegalArgumentException.class)
    public RestResponse<String> exception(IllegalArgumentException e) {
        printInfo(e);
        return RestResponse.failed(BaseExceptionEnum.PARAMETER_VALIDATED_FAILED.getCode(), BaseExceptionEnum.PARAMETER_VALIDATED_FAILED.getText());
    }

    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public RestResponse<String> exception(MethodArgumentNotValidException e) {
        printInfo(e);
        return RestResponse.failed(BaseExceptionEnum.PARAMETER_VALIDATED_FAILED.getCode(), BaseExceptionEnum.PARAMETER_VALIDATED_FAILED.getText());
    }

    @ResponseBody
    @ExceptionHandler(value = ServiceException.class)
    public RestResponse<String> serviceExceptionHandler(ServiceException e) {
        printInfo(e);
        return RestResponse.failed(e.getCode(), e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = DecodeException.class)
    public RestResponse<String> decodeExceptionHandler(DecodeException e) {
        printInfo(e);
        return RestResponse.failed(BaseExceptionEnum.JSON_DECODE_EXCEPTION.getCode(), BaseExceptionEnum.JSON_DECODE_EXCEPTION.getText());
    }

    @ResponseBody
    @ExceptionHandler(value = HandlerMethodValidationException.class)
    public RestResponse<String> handlerMethodValidationException(HandlerMethodValidationException e) {
        printInfo(e);
        return RestResponse.failed(BaseExceptionEnum.ARG_VALIDATED_FAILED.getCode(), BaseExceptionEnum.ARG_VALIDATED_FAILED.getText());
    }

    public void printInfo(Exception e) {
        log.error("服务错误 - Exception - {}", e.getMessage());
        try {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            String token = request.getHeader("Authorization");
            log.error(token);
            log.error("URI - {}", request.getRequestURI());
            if(request.getContentType() != null){
                boolean isMultipart = request.getContentType().toLowerCase().contains("multipart/form-data");
                if ("POST".equalsIgnoreCase(request.getMethod()) && !isMultipart) {
                    String post = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
                    log.error("POST数据 - {}", post);
                }
            }

        } catch (Exception ex) {
            log.error(ex.toString());
        }
        String profile = environment.getActiveProfiles()[0];
        if(!profile.equals("prod")) {
            e.printStackTrace();
        }
        e.printStackTrace();
    }
}
