package com.iguokao.supernova.common.converter;

import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;

import java.util.List;

public class JsonArrayConverter<T> implements AttributeConverter<List<T>, String> {
    ObjectMapper objectMapper = new ObjectMapper();

    public JsonArrayConverter(Class<T> type) {
    }

    @Override
    public String convertToDatabaseColumn(List<T> ts) {
        try {
            return objectMapper.writeValueAsString(ts);
        } catch (JsonProcessingException e) {
            throw new ServiceException(BaseExceptionEnum.JSON_ARRAY_CONVERT_EXCEPTION);
        }
    }

    @Override
    public List<T> convertToEntityAttribute(String s) {
        try {
            return objectMapper.readValue(s, new TypeReference<List<T>>() {});
        } catch (JsonProcessingException e) {
            throw new ServiceException(BaseExceptionEnum.JSON_ARRAY_CONVERT_EXCEPTION);
        }
    }
}
