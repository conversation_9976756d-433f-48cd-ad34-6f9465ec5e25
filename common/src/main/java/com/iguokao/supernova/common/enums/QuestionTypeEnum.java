package com.iguokao.supernova.common.enums;

import lombok.Getter;


@Getter
public enum QuestionTypeEnum implements BaseEnum {
    SINGLE_SELECTION(1, "单选题"),
    MULTI_SELECTION(2, "多选题"),
    YES_NO(3, "判断题"),
    QA(5, "问答题"),
    FREE_SELECTION(7, "不定项选择题"),
    GROUP_SELECTION(13, "复合选择题")
    ;

    QuestionTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
