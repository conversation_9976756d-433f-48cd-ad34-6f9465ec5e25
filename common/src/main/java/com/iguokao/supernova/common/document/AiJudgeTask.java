package com.iguokao.supernova.common.document;

import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AiJudgeTask extends BaseDocument{
    private ObjectId subjectId;
    private ObjectId periodId;
    private ObjectId questionId;
    private String questionBody;
    private String judgeRule;
    private String name;
    private Double score;
    private List<JudgeItem> scoreItemList = new ArrayList<>();

    private Integer resultCount = 0;
    private Double totalTokens = .0;

}
