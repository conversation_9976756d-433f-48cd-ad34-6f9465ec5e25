package com.iguokao.supernova.common.remote;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.response.*;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

import java.util.List;

public interface ExamRemote {
    @RequestLine("GET /api/v1/remote/question/{periodId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<List<QuestionJudgeRemoteResponse>> periodQuestion(@Param("periodId") String periodId);

    @RequestLine("GET /api/v1/remote/subject/question/{subjectId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<List<QuestionJudgeRemoteResponse>> subjectQuestion(@Param("subjectId") String subjectId);

    @RequestLine("GET /api/v1/remote/action/{periodId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<List<ActionRemoteResponse>> illegalAction(@Param("periodId")  String periodId);

    @RequestLine("GET /api/v1/remote/candidate/{periodId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<List<CandidateRemoteResponse>> candidateList(@Param("periodId")  String periodId);

    @RequestLine("GET /api/v1/remote/candidate/one/{candidateId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<CandidateRemoteResponse> candidateInfo(@Param("candidateId")  String candidateId);

    @RequestLine("GET /api/v1/remote/room/answer/{periodId}/{roomId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> roomAnswer(@Param("periodId")  String periodId, @Param("roomId")  String roomId);

    @RequestLine("GET /api/v1/remote/room/action/{periodId}/{roomId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> roomAction(@Param("periodId")  String periodId, @Param("roomId")  String roomId);

    @RequestLine("GET /api/v1/remote/period/check/finished/{periodId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<PeriodFinishedResponse> periodCheckFinished(@Param("periodId")  String periodId);

    @RequestLine("GET /api/v1/remote/period/done/{periodId}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> periodDone(@Param("periodId")  String periodId);

    @RequestLine("GET /api/v1/remote/admission/{projectId}/{idCardNum}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<AdmissionCardResponse> admissionExport(@Param("projectId")  String projectId, @Param("idCardNum")  String idCardNum);

    @RequestLine("GET /api/v1/remote/admission/download/{projectId}/{idCardNum}")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> admissionDownload(@Param("projectId")  String projectId, @Param("idCardNum")  String idCardNum);
}
