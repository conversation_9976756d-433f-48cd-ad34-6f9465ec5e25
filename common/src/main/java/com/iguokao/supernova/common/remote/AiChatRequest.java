package com.iguokao.supernova.common.remote;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AiChatRequest {
    private String model = "qwen3-235b-a22b";
    private AiInput input = new AiInput();
    private AiParameters parameters = new AiParameters();

    @Getter
    @Setter
    public static class AiMessage {
        private String role;    // system / user / assistant
        private String content;
    }

    @Getter
    @Setter
    public static class AiParameters {
        private Double temperature = 0.2;
        private Boolean enable_thinking = false;
    }

    @Getter
    @Setter
    public static class AiInput {
        private List<AiMessage> messages = new ArrayList<>();
    }
}
