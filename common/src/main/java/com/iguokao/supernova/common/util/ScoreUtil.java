package com.iguokao.supernova.common.util;

import java.math.RoundingMode;
import java.text.DecimalFormat;

public class ScoreUtil {

    public static Double validQuestionScore(String score) {
        // 整数的正则表达式
        String integerPattern = "^-?[0-9]+$";
        // 小数的正则表达式
        String decimalPattern = "^-?[0-9]+(\\.[0-9]+)?$";

        try {
            // 整数
            if(score.matches(integerPattern)){
                return Double.parseDouble(score);
            }//小数
            else if(score.matches(decimalPattern)){
                double num = Double.parseDouble(score);
                String result = String.format ("%.1f", num);
                return Double.valueOf(result);
            }//垃圾数据
            else {
                return 0D;
            }
        }
        catch (Exception e){
            System.out.println(e.getMessage());
            return 0D;
        }
    }

    static DecimalFormat df = new DecimalFormat("0.0");
    public static String getFormatScore(Double score){
        if(score == null){
            return "0";
        }
//        df.setMaximumFractionDigits(1);
//        df.setGroupingSize(0);
//        df.setRoundingMode(RoundingMode.HALF_UP);
//        return df.format(score);

        return String.format ("%.1f", score);
    }


}
