package com.iguokao.supernova.common.converter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.types.ObjectId;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class ObjectIdListDeserializer extends JsonDeserializer<List<ObjectId>> {
    @Override
    public List<ObjectId> deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String[] list = new ObjectMapper().readValue(jsonParser.getText(), String[].class);
        return Arrays.stream(list).map(ObjectId::new).toList();
    }
}