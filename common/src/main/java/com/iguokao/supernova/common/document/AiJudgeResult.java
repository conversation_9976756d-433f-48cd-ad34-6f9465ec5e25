package com.iguokao.supernova.common.document;

import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AiJudgeResult extends BaseDocument{
    private ObjectId taskId;

    private ObjectId subjectId;
    private ObjectId periodId;
    private ObjectId questionId;
    private ObjectId candidateId;
    private ObjectId answerId;

    private AiUsage usage;

    private Boolean offTopic;
    private Double score;
    private String comment;
    private List<JudgeItem> scoreItemList = new ArrayList<>();
}
