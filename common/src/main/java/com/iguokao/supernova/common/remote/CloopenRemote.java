package com.iguokao.supernova.common.remote;

import com.iguokao.supernova.common.remote.cloopen.SmsRequest;
import com.iguokao.supernova.common.remote.cloopen.SmsResponse;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.common.util.Md5Util;
import feign.HeaderMap;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;


public interface CloopenRemote {

    /**
     *
     POST /2013-12-26/Accounts/abcdefghijklmnopqrstuvwxyz012345/SMS/TemplateSMS?sig=
     C1F20E7A9733CE94F680C70A1DBABCDE HTTP/1.1

     Host:***********:8883
     content-length: 139
     Accept:application/json;
     Content-Type:application/json;charset=utf-8;
     Authorization:****************************************************************

     {"to":"***********,***********,***********","appId":"ff8080813fc70a7b013fc72312324213","reqId":"abc123","subAppend":"8888","templateId":"1","datas":["替换内容","替换内容"]}
     */
    @RequestLine("POST /2013-12-26/Accounts/{accountSid}/SMS/TemplateSMS?sig={SigParameter}")
    SmsResponse send(@Param("accountSid") String accountSid,
                     @Param("SigParameter") String sigParameter,
                     @HeaderMap Map<String, String> headerMap,
                     @RequestBody SmsRequest request);


    /**
     * SigParameter	String	必选	REST API 验证参数，生成规则如下
     * 1.使用MD5加密（账户Id + 账户授权令牌 + 时间戳）。
     * 其中账户Id和账户授权令牌根据url的验证级别对应主账户。
     * 时间戳是当前系统时间，格式"yyyyMMddHHmmss"。
     * 时间戳有效时间为24小时，如：***************.
     * SigParameter参数需要大写，如不能写成sig=abcdefg而应该写成sig=ABCDEFG
     * @param accountSid SID
     * @param authToken 短信Token
     * @return SigParameter
     */
    static String genSigParameter(String accountSid, String authToken){
       String raw = String.format("%s%s%s",
               accountSid,
               authToken,
               DateUtil.dateToStr(new Date(), "yyyyMMddHHmmss"));
       return Md5Util.genSign(raw).toUpperCase();
    }

    static String genAuthHeader(String accountSid){
        String raw = String.format("%s:%s",
                accountSid,
                DateUtil.dateToStr(new Date(), "yyyyMMddHHmmss"));
        return Base64.getEncoder().encodeToString(raw.getBytes());
    }


    static SmsResponse sendSns(CloopenRemote cloopenRemote,
                       String template,
                       String mobile,
                       List<String> argList,
                       String cloopenSmsAppId,
                       String cloopenAccountSid,
                       String cloopenAuthToken){
        SmsRequest req = new SmsRequest();
        req.setAppId(cloopenSmsAppId);
        req.setDatas(argList);
        req.setTemplateId(template);
        req.setTo(mobile);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", CloopenRemote.genAuthHeader(cloopenAccountSid));
        headerMap.put("Accept", "application/json");
        headerMap.put("Content-Type", "application/json;charset=utf-8");

        String sigParameter = CloopenRemote.genSigParameter(cloopenAccountSid, cloopenAuthToken);
        return cloopenRemote.send(cloopenAccountSid, sigParameter, headerMap, req);
    }



}
