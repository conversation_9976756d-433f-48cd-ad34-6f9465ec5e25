package com.iguokao.supernova.common.response;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Getter
@Setter
public class CompanyRemoteResponse {
    private String companyId;
    private String name;
    private String shortName;
    private String logo;
    private String theme;
}
