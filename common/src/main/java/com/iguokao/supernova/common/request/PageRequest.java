/*
 * Copyright 2015-2102 Fast(http://www.cloudate.net) Group.
 *  
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *  
 *      http://www.apache.org/licenses/LICENSE-2.0
 *  
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.iguokao.supernova.common.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class PageRequest {

    @NotNull(message = "当前页数 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "当前页数")
    private int page ;

    @NotNull(message = "每页条目 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "每页条目")
    private int pageSize;

}
