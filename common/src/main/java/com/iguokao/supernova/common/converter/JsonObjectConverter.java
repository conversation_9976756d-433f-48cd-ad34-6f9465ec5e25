package com.iguokao.supernova.common.converter;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import jakarta.persistence.AttributeConverter;


public class JsonObjectConverter<T>implements AttributeConverter<T, String> {
    private final Class<T> type;
    ObjectMapper objectMapper = new ObjectMapper();

    public JsonObjectConverter(Class<T> type) {
        this.type = type;
    }

    @Override
    public String convertToDatabaseColumn(T t) {
        try {
            return objectMapper.writeValueAsString(t);
        } catch (JsonProcessingException e) {
            throw new ServiceException(BaseExceptionEnum.JSON_OBJECT_CONVERT_EXCEPTION);
        }
    }

    @Override
    public T convertToEntityAttribute(String s) {
        try {
            return objectMapper.readValue(s, type);
        } catch (JsonProcessingException e) {
            throw new ServiceException(BaseExceptionEnum.JSON_OBJECT_CONVERT_EXCEPTION);
        }
    }

}
