package com.iguokao.supernova.common.exception;

import com.iguokao.supernova.common.enums.BaseEnum;
import com.iguokao.supernova.common.response.RestResponse;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ServiceException extends BaseException {

    public ServiceException(BaseEnum errEnum) {
        super(errEnum.getCode(), errEnum.getText());
    }

    public ServiceException(BaseEnum errEnum, String extraInfo) {
        super(errEnum.getCode(), errEnum.getText() + extraInfo);
    }

    public ServiceException(BaseEnum errEnum, String extraInfo, boolean isPrintStackTrace) {
        super(errEnum.getCode(), errEnum.getText() + extraInfo);
    }

    public ServiceException(Integer code, String message) {
        super(code, message);
    }

    public static ServiceException remote(RestResponse<?> res) {
        return new ServiceException(res.getCode(), "远程错误: " + res.getMessage());
    }
}
