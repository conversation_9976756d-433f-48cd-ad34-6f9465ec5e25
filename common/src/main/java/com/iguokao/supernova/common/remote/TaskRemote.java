package com.iguokao.supernova.common.remote;
import com.iguokao.supernova.common.request.EmailTaskAddRequest;
import com.iguokao.supernova.common.request.PaperTaskAddRequest;
import com.iguokao.supernova.common.request.SmsTaskAddRequest;
import com.iguokao.supernova.common.response.RestResponse;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

public interface TaskRemote {

    @RequestLine("POST /api/task/email/add")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> addTask(@RequestBody EmailTaskAddRequest taskAddRequest);

    @RequestLine("POST /api/task/sms/add")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> addSmsTask(@RequestBody SmsTaskAddRequest taskAddRequest);

    @RequestLine("POST /api/task/pdf/paper")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    RestResponse<String> addPdfTask(@RequestBody PaperTaskAddRequest taskAddRequest);

}
