package com.iguokao.supernova.common.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TencentRtcVod {
    @JsonProperty("UserId")
    private String UserId;

    @JsonProperty("TrackType")
    private String TrackType;

    @JsonProperty("MediaId")
    private String MediaId;

    @JsonProperty("FileId")
    private String FileId;

    @JsonProperty("VideoUrl")
    private String VideoUrl;

    @JsonProperty("CacheFile")
    private String CacheFile;

    @JsonProperty("StartTimeStamp")
    private Long StartTimeStamp;

    @JsonProperty("EndTimeStamp")
    private Long EndTimeStamp;
}

/*
{
        "UserId": "6679088c8f43782995dc9bc1_6603b5513600a17154d9c867_p",
        "TrackType": "audio_video",
        "MediaId": "main",
        "FileId": "1253642699111488573",
        "VideoUrl": "https://1500018593.vod2.myqcloud.com/6cae02c2vodcq1500018593/0717c0ca1253642699111488573/f0.mp4",
        "CacheFile": "1400795330_475402244__UserId_s_NjY3OTA4OGM4ZjQzNzgyOTk1ZGM5YmMxXzY2MDNiNTUxMzYwMGExNzE1NGQ5Yzg2N19w__UserId_e_main.mp4",
        "StartTimeStamp": 1719222763876,
        "EndTimeStamp": 1719222841448
      }
 */
