package com.iguokao.supernova.common.response;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Standard Rest Response")
public class RestResponse<T> implements Serializable {

    @Schema(description = "Rest custom code, 0 is success")
    private Integer code;

    @Schema(description = "Rest message, if code is 0, message is ok")
    private String message;

    @Schema(description = "response data")
    private T data;

    public static <T> RestResponse<T> success() {
        return new RestResponse<>(0, "ok", null);
    }

    public static <T> RestResponse<T> success(T t) {
        return new RestResponse<>(0, "ok", t);
    }

    public static <T> RestResponse<T> failed(int code, String message) {
        return new RestResponse<>(code, message, null);
    }

    public static <T> RestResponse<T> failed(BaseEnum err) {
        return new RestResponse<>(err.getCode(), err.getText(), null);
    }

    public static <T> RestResponse<T> failed(BaseEnum err, String customErrMessage) {
        return new RestResponse<>(err.getCode(),
                err.getText() + customErrMessage,
                null);
    }

    @SneakyThrows
    public String toJsonString() {
        return "{\"code\": " + code + ", " +
                "\"message\": \"" + message + "\", " +
                "\"data\": " + new ObjectMapper().writeValueAsString(data) +"}";
    }
}
