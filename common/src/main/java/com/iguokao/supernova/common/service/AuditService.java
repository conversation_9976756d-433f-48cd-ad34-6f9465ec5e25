package com.iguokao.supernova.common.service;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;

public interface AuditService {
    void add(AuditItem item);
}
