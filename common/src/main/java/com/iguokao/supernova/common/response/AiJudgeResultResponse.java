package com.iguokao.supernova.common.response;

import com.iguokao.supernova.common.document.AiJudgeResult;
import com.iguokao.supernova.common.document.JudgeItem;
import com.tencentcloudapi.dbbrain.v20191016.models.ScoreItem;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class AiJudgeResultResponse {
    private Boolean offTopic;
    private Double score;
    private String comment;
    private Double totalTokens;
    private Map<String, Double> scoreItemMap = new HashMap<>();

    public static AiJudgeResultResponse of(AiJudgeResult result) {
        AiJudgeResultResponse res = new AiJudgeResultResponse();
        res.setOffTopic(result.getOffTopic());
        res.setScore(result.getScore());
        res.setComment(result.getComment());
        res.setTotalTokens(result.getUsage().getTotal_tokens());
        for(JudgeItem item : result.getScoreItemList()){
            res.getScoreItemMap().put(item.getName(), item.getScore());
        }
        return res;
    }
}
