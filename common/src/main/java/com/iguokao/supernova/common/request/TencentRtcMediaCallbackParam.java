package com.iguokao.supernova.common.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TencentRtcMediaCallbackParam {
    @JsonProperty("EventGroupId")
    private Long EventGroupId;

    @JsonProperty("EventType")
    private Long EventType;

    @JsonProperty("CallbackTs")
    private Long CallbackTs;

    @JsonProperty("EventInfo")
    private TencentRtcEventInfo EventInfo;
}

//{"EventGroupId":2,"EventType":201,"CallbackTs":1646033626779,"EventInfo":{"RoomId":1905113001,"EventTs":1646033626,"EventMsTs":1646033626669,"UserId":"621c668839671a0012d18a51_m"}}