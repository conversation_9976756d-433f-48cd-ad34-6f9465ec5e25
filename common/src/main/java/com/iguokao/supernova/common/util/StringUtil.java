package com.iguokao.supernova.common.util;

import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringEscapeUtils;
import org.bson.types.ObjectId;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {
    public static String genPassword(int len){
        if(len > 32){
            throw new ServiceException(BaseExceptionEnum.MAX_LEN_32);
        }
        String password = gen32().substring(0, len);
        while (password.contains("0")
                || password.contains("o")
                || password.contains("O")
                || password.contains("1")
                || password.contains("l")){
            password = genPassword(len);
        }
        return password;
    }

    public static String genUpperCode(int len){
        if(len > 32){
            throw new ServiceException(BaseExceptionEnum.MAX_LEN_32);
        }
        String code = gen32().substring(0, len);
        return code.substring(0, len);
    }

    private static String gen32(){
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String getBase64EncodedImage(String imageURL) throws IOException {
        if(null == imageURL){
            return null;
        }
        URL url = new URL(imageURL);
        InputStream is = url.openStream();
        byte[] bytes = org.apache.commons.io.IOUtils.toByteArray(is);
        return Base64.encodeBase64String(bytes);
    }

    public static String getBase64Str(String input){
        return Base64.encodeBase64String(input.getBytes());
    }


    public static String[] getNames(Class<? extends Enum<?>> e) {
        return Arrays.stream(e.getEnumConstants()).map(Enum::name).toArray(String[]::new);
    }

    public static String getInfo(Class<? extends Enum<?>> e) {
        List<String> list = Arrays
                .stream(e.getEnumConstants())
                .map(ee -> String.format("%d-%s", ee.ordinal(), ee.name()))
                .toList();
        return String.join(",", list);
    }

    public static boolean checkCity(String city) {
        String[] cities = {"北京市","天津市","石家庄市","唐山市","秦皇岛市","邯郸市","邢台市","保定市","张家口市","承德市","沧州市","廊坊市","衡水市","太原市","大同市","阳泉市","长治市","晋城市","朔州市","晋中市","运城市","忻州市","临汾市","吕梁市","呼和浩特市","包头市","乌海市","赤峰市","通辽市","鄂尔多斯市","呼伦贝尔市","巴彦淖尔市","乌兰察布市","兴安盟","锡林郭勒盟","阿拉善盟","沈阳市","大连市","鞍山市","抚顺市","本溪市","丹东市","锦州市","营口市","阜新市","辽阳市","盘锦市","铁岭市","朝阳市","葫芦岛市","长春市","吉林市","四平市","辽源市","通化市","白山市","松原市","白城市","延边朝鲜族自治州","哈尔滨市","齐齐哈尔市","鸡西市","鹤岗市","双鸭山市","大庆市","伊春市","佳木斯市","七台河市","牡丹江市","黑河市","绥化市","大兴安岭地区","上海市","南京市","无锡市","徐州市","常州市","苏州市","南通市","连云港市","淮安市","盐城市","扬州市","镇江市","泰州市","宿迁市","杭州市","宁波市","温州市","嘉兴市","湖州市","绍兴市","金华市","衢州市","舟山市","台州市","丽水市","合肥市","芜湖市","蚌埠市","淮南市","马鞍山市","淮北市","铜陵市","安庆市","黄山市","滁州市","阜阳市","宿州市","六安市","亳州市","池州市","宣城市","福州市","厦门市","莆田市","三明市","泉州市","漳州市","南平市","龙岩市","宁德市","南昌市","景德镇市","萍乡市","九江市","新余市","鹰潭市","赣州市","吉安市","宜春市","抚州市","上饶市","济南市","青岛市","淄博市","枣庄市","东营市","烟台市","潍坊市","济宁市","泰安市","威海市","日照市","莱芜市","临沂市","德州市","聊城市","滨州市","菏泽市","郑州市","开封市","洛阳市","平顶山市","安阳市","鹤壁市","新乡市","焦作市","濮阳市","许昌市","漯河市","三门峡市","南阳市","商丘市","信阳市","周口市","驻马店市","武汉市","黄石市","十堰市","宜昌市","襄阳市","鄂州市","荆门市","孝感市","荆州市","黄冈市","咸宁市","随州市","恩施土家族苗族自治州","长沙市","株洲市","湘潭市","衡阳市","邵阳市","岳阳市","常德市","张家界市","益阳市","郴州市","永州市","怀化市","娄底市","湘西土家族苗族自治州","广州市","韶关市","深圳市","珠海市","汕头市","佛山市","江门市","湛江市","茂名市","肇庆市","惠州市","梅州市","汕尾市","河源市","阳江市","清远市","东莞市","中山市","东沙群岛","潮州市","揭阳市","云浮市","南宁市","柳州市","桂林市","梧州市","北海市","防城港市","钦州市","贵港市","玉林市","百色市","贺州市","河池市","来宾市","崇左市","海口市","三亚市","三沙市","重庆市","成都市","自贡市","攀枝花市","泸州市","德阳市","绵阳市","广元市","遂宁市","内江市","乐山市","南充市","眉山市","宜宾市","广安市","达州市","雅安市","巴中市","资阳市","阿坝藏族羌族自治州","甘孜藏族自治州","凉山彝族自治州","贵阳市","六盘水市","遵义市","安顺市","铜仁市","黔西南布依族苗族自治州","毕节市","黔东南苗族侗族自治州","黔南布依族苗族自治州","昆明市","曲靖市","玉溪市","保山市","昭通市","丽江市","普洱市","临沧市","楚雄彝族自治州","红河哈尼族彝族自治州","文山壮族苗族自治州","西双版纳傣族自治州","大理白族自治州","德宏傣族景颇族自治州","怒江傈僳族自治州","迪庆藏族自治州","拉萨市","昌都市","山南地区","日喀则市","那曲地区","阿里地区","林芝市","西安市","铜川市","宝鸡市","咸阳市","渭南市","延安市","汉中市","榆林市","安康市","商洛市","兰州市","嘉峪关市","金昌市","白银市","天水市","武威市","张掖市","平凉市","酒泉市","庆阳市","定西市","陇南市","临夏回族自治州","甘南藏族自治州","西宁市","海东市","海北藏族自治州","黄南藏族自治州","海南藏族自治州","果洛藏族自治州","玉树藏族自治州","海西蒙古族藏族自治州","银川市","石嘴山市","吴忠市","固原市","中卫市","乌鲁木齐市","克拉玛依市","吐鲁番市","哈密地区","昌吉回族自治州","博尔塔拉蒙古自治州","巴音郭楞蒙古自治州","阿克苏地区","克孜勒苏柯尔克孜自治州","喀什地区","和田地区","伊犁哈萨克自治州","塔城地区","阿勒泰地区","台北市","高雄市","台南市","台中市","金门县","南投县","基隆市","新竹市","嘉义市","新北市","宜兰县","新竹县","桃园县","苗栗县","彰化县","嘉义县","云林县","屏东县","台东县","花莲县","澎湖县","连江县","香港岛","九龙","新界","澳门半岛","离岛"};
        String res = Arrays
                .stream(cities)
                .filter(s -> s.equals(city))
                .findFirst()
                .orElse(null);
        return res != null;
    }

    public static Map<String, String> splitQueryString(String query) throws UnsupportedEncodingException {
        Map<String, String> query_pairs = new LinkedHashMap<String, String>();
        String[] pairs = query.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            query_pairs.put(URLDecoder.decode(pair.substring(0, idx), StandardCharsets.UTF_8), URLDecoder.decode(pair.substring(idx + 1), "UTF-8"));
        }
        return query_pairs;
    }

    public static boolean validIdCard(String idCard) {
        // 定义权重系数
        int[] weightCodes = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        // 定义校验码
        char[] checkCodes = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

        // 长度校验
        if (idCard == null || idCard.length() != 18) {
            return false;
        }

        // 前17位校验
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            char ch = idCard.charAt(i);
            if (ch < '0' || ch > '9') {
                return false;
            }
            int digit = ch - '0';
            sum += digit * weightCodes[i];
        }

        // 校验码校验
        int checkCodeIndex = sum % 11;
        char checkCode = checkCodes[checkCodeIndex];
        return idCard.charAt(17) == checkCode;

    }

    public static String idCardNumSecure(String idCardNum){
        int len = idCardNum.length();
        return idCardNum.substring(0, len-8 > 0 ? len-8 : 1) + "XXXX" + idCardNum.substring(len-4, len);
    }

    public static boolean validEmail(String email) {
        if(null == email){
            return false;
        }
        String regex = "^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$";
        return Pattern.matches(regex, email);
    }

    public static boolean validMobile(String mobile) {
        if(null == mobile){
            return false;
        }
        String reg = "^[1][3,4,5,6,7,8,9][0-9]{9}$";
        return Pattern.matches(reg, mobile);
    }

    public static String toPriceString(Double price){
        if (price == null){
            return null;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(price);
    }

    public static boolean validObjectId(String id) {
        try {
            new ObjectId(id);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public static String htmlRemoveMeta(String html) {
        // 首先，解码 HTML 实体字符
        html = StringEscapeUtils.unescapeHtml4(html);

        // 替换 &nbsp; 为普通空格
        html = html.replace("&nbsp;", " ");

        // 提取 <p> 标签内容的正则
        String regex = "<p[^>]*>(.*?)</p>";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(html);
        StringBuilder result = new StringBuilder();

        // 提取 <p> 标签的内容
        while (matcher.find()) {
            result.append(matcher.group(1)).append("\r\n");
        }

        regex = "<[^>]+>";

        // 返回提取的 <p> 内容
        return result.toString().replaceAll(regex, "");
    }

    public static int countWords(String input) {
        // 正则表达式：匹配以下类型
        // 1. 英文单词 (\b[a-zA-Z]+\b)
        // 2. 连续数字 (\b\d+\b)
        // 3. 单个中文字符 ([\u4e00-\u9fa5])
        // 4. 单个标点符号 ([^\s\w]) （非空格、非单词字符）
        String regex = "\\b[a-zA-Z]+\\b|\\b\\d+\\b|[\\u4e00-\\u9fa5]|[^\\s\\w]";

        // 使用正则表达式进行匹配
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }

    /**
     * 根据身份证号码获取性别
     *
     * @param idCard 身份证号码，支持15位或18位
     * @return 性别，"男"表示男性，"女"表示女性，"未知"表示无法识别
     */
    public static String getGenderByIdCard(String idCard) {
        if (idCard == null || (idCard.length() != 15 && idCard.length() != 18)) {
            return null;
        }
        int genderCode;
        if (idCard.length() == 15) {
            // 15位身份证号码，性别位于第15位
            genderCode = idCard.charAt(14) - '0';
        } else {
            // 18位身份证号码，性别位于第17位
            genderCode = idCard.charAt(16) - '0';
        }
        return (genderCode % 2 == 0) ? "女" : "男";
    }
}
