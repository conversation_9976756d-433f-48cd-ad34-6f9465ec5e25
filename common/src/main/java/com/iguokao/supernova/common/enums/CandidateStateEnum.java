package com.iguokao.supernova.common.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum CandidateStateEnum implements BaseEnum {

    INIT(0, "初始"),
    DOWNLOADED(1, "下载准考证"),
    SIGNED(2, "签到"),
    SIGNED_FAILED(3, "签到失败"),
    LOGIN(7, "已登录"),
    STARTED(10, "作答中"),
    FINISHED(20, "完成"),
    ABSENT(30, "缺考"),
    ILLEGAL(40, "违纪"),
    ;

    CandidateStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
