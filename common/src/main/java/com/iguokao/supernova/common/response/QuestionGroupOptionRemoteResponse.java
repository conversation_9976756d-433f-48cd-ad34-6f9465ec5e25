package com.iguokao.supernova.common.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class QuestionGroupOptionRemoteResponse implements Serializable {
    private Integer type;
    private Double questionScore;
    private String body;
    private Boolean isShowAnswerBox;
    private String analysis;
    private List<QuestionOptionRemoteResponse> optionList = new ArrayList<>();
    private List<String> correctValue = new ArrayList<>();
}
