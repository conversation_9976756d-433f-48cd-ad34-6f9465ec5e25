package com.iguokao.supernova.common.response;

import com.iguokao.supernova.common.document.AdmissionCard;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AdmissionCardResponse {

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "须知")
    private String admissionCardRequirement;

    List<AdmissionCard> admissionCardList = new ArrayList<>();


}
