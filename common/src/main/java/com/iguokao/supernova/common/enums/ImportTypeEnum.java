package com.iguokao.supernova.common.enums;

import lombok.Getter;


@Getter
public enum ImportTypeEnum implements BaseEnum {
    ALL(1, "所有"),
    SELECTED(2, "筛选过的"),
    CONFIRMED(3, "确认的"),
    CONFIRMED_AND_NOT_CONFIRMED(4, "未拒绝"),
    REFUSED(5, "拒绝"),
    NOT_CONFORM(6, "未确认"),
    ;

    ImportTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
