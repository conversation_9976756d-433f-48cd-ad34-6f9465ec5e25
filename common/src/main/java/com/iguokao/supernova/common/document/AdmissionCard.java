package com.iguokao.supernova.common.document;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AdmissionCard {
    private String candidateId;
    private String projectId;
    private String periodId;
    private String subjectId;
    private String siteId;
    private String roomId;

    private String subjectName;

    private String siteName;
    private String siteAddress;
    private String district;

    private String roomName;
    private String roomAddress;
    private String roomNum;

    private String fullName;
    private Long num;
    private String idCardNum;
    private String email;
    private Integer gender;
    private String avatar;
    private String city;
    private Integer seatNum;
    private Date startAt;
    private Integer duration;
}
