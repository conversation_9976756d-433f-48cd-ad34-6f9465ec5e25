package com.iguokao.supernova.common.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TencentRtcRecordCallbackRequest {
    @JsonProperty("EventGroupId")
    private Long EventGroupId;

    @JsonProperty("EventType")
    private Long EventType;

    @JsonProperty("CallbackTs")
    private Long CallbackTs;

    @JsonProperty("EventInfo")
    private TencentRtcEventInfo EventInfo;
}

/*
{
        "app": "117456.livepush.myqcloud.com",
        "appid": 1303908819,
        "appname": "trtc_1400633040",
        "channel_id": "1400633040_190483110_620e05520735690012db5ae0_m_main",
        "duration": 13,
        "end_time": 1645788083,
        "end_time_usec": 106017,
        "event_type": 100,
        "file_format": "hls",
        "file_id": "387702296611777164",
        "file_size": 686679,
        "media_start_time": 0,
        "product_name": "CSIG_TRTC",
        "record_bps": 0,
        "record_file_id": "387702296611777164",
        "sign": "b479af628baffd5eb1cf2459b3f53826",
        "start_time": 1645788072,
        "start_time_usec": 82853,
        "stream_id": "1400633040_190483110_620e05520735690012db5ae0_m_main",
        "stream_param": "txSecret=2fcdf3b84a497db598f2c66a4d74e12f&txTime=74a2d3a5&from=interactive&client_business_type=0&sdkappid=1400633040&sdkapptype=1&groupid=190483110&userid=NjIwZTA1NTIwNzM1NjkwMDEyZGI1YWUwX20=&ts=6218bba4&userdefinerecordid=620e05520735690012db5ae0_m&tinyid=144115242627995057&roomid=10028886&record_name=620e05520735690012db5ae0_m_main&pid=a7c7748f12675ad2c07f841221c98fdb&product_name=CSIG_TRTC&cliRecoId=0&trtcclientip=**************&useMixPlayer=1&txHost=117456.livepush.myqcloud.com",
        "t": 1645788684,
        "task_id": "1831014824845401862",
        "video_id": "1303908819_67be18e3197b471ab8eb7811fab27094",
        "video_url": "http://1500009069.vod2.myqcloud.com/6c9c6100vodcq1500009069/638c5c99387702296611777164/playlist.m3u8"
        }
 */
