package com.iguokao.supernova.registration.controller;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.iguokao.supernova.common.converter.ValidObjectId;
import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.remote.ManagementRemote;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.excel.TemplateDataExporter;
import com.iguokao.supernova.registration.excel.TemplateDataExporterV2;
import com.iguokao.supernova.registration.request.*;
import com.iguokao.supernova.registration.response.*;
import com.iguokao.supernova.registration.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/v1/template/v2")
@RequiredArgsConstructor
public class TemplateControllerV2 {
    private final TemplateService templateService;
    private final JwtService jwtService;
    private final ProjectService projectService;
    private final SubjectService subjectService;
    private final ManagementRemote managementRemote;
    private final CacheService cacheService;
    private final SmsService smsService;

    @GetMapping("/export/excel/{projectId}")
    @Operation(summary = "导出考生报名数据")
    public void export(@PathVariable String projectId, HttpServletResponse response) throws IOException {
        //考生ID 全名 手机号 证件类型 证件号  是否是导入考生 Email 所在地 考区 报名表填写完毕 审核状态 支付订单号 备注1 备注1 备注1 需要开发票 抬头 税号 发票类型 发票路径
        List<TemplateData> templateDataList = this.templateService.getByProjectId(projectId);
        Project project = this.projectService.getById(projectId);
        List<Subject> subjectList = this.subjectService.getListByProjectId(projectId);

        Template template = this.templateService.getById(project.getTemplateId().toString());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("报名表_%s", project.getName()), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<List<String>> res = TemplateDataExporterV2.data(templateDataList, template, subjectList);

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        EasyExcel.write(response.getOutputStream())
                .sheet("报名表")
                .head(TemplateDataExporterV2.head(template))
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(res);
    }
}
