package com.iguokao.supernova.registration.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter
@Setter
@Document("pay_info")
public class PayInfo extends BaseDocument {
    private WechatPay wechatPay;

    @Getter
    @Setter
    public static class WechatPay{
        private String appId;
        private String merchantId;
        private String merchantSerialNumber;
        private String apiV3Key;
        private String privateKeyPath;
    }
}
