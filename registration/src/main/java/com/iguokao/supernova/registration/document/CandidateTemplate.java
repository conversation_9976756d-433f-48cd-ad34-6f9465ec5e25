package com.iguokao.supernova.registration.document;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document("candidate_template")
public class CandidateTemplate extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId candidateId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId templateId;

    private String fullName;
    private Integer gender;
    private String avatar;
    private String city;
    private Integer idCardType;
    private String idCardNum;
    private String mobile;
    private String email;

    private List<TemplateItemData> templateItemDataList = new ArrayList<>();
    private List<Registration> registrationList = new ArrayList<>();
}

