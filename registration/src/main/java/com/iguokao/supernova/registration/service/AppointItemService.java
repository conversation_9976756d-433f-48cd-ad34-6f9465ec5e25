package com.iguokao.supernova.registration.service;


import com.iguokao.supernova.registration.document.AppointItem;

import java.util.List;

public interface AppointItemService {

    void addAllByExcel(List<AppointItem> list, String projectId);

    List<AppointItem> getAppointItems(String projectId);

    void deleteById(String appointItemId);

    AppointItem getById(String appointItemId);

    void update(AppointItem appointItem);
}
