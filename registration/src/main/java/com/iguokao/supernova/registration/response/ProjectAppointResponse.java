package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.registration.document.Registration;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ProjectAppointResponse {
    private String candidateId;
    private String projectId;
    private String siteId;
    private String city;
    private Integer confirmState;

    public static ProjectAppointResponse of(Registration obj){
        if(obj==null){
            return null;
        }
        ProjectAppointResponse res = new ProjectAppointResponse();
        BeanUtils.copyProperties(obj, res);
        res.setCandidateId(obj.getCandidateId().toString());
        res.setProjectId(obj.getProjectId().toString());
        if(obj.getSiteId() != null){
            res.setSiteId(obj.getSiteId().toString());
        }
        return res;
    }

    public static List<ProjectAppointResponse> of(List<Registration> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<ProjectAppointResponse> res = new ArrayList<>();
        for(Registration obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
