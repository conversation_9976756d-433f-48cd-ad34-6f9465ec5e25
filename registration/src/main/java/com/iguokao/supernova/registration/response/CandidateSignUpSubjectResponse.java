package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.Registration;
import com.iguokao.supernova.registration.document.Subject;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class CandidateSignUpSubjectResponse {

    private String subjectId;
    private String name;
    private String info;
    private String price;
    private Integer passScore;

    private Boolean templateDataPassed;
    private String templateDataCity;

    private Boolean pay;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date endAt;

    public static CandidateSignUpSubjectResponse of(Subject obj){
        if(obj==null){
            return null;
        }
        CandidateSignUpSubjectResponse res = new CandidateSignUpSubjectResponse();
        BeanUtils.copyProperties(obj, res);
        res.setSubjectId(obj.get_id().toString());
        res.setPrice(StringUtil.toPriceString(obj.getPrice()));
        return res;
    }
}
