package com.iguokao.supernova.registration.repository;

import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.CompanyConfig;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Optional;

public interface CompanyConfigRepository extends MongoRepository<CompanyConfig, ObjectId> {
    int countBy_id(ObjectId companyId);
    Optional<CompanyConfig> findBySn(String sn);
}
