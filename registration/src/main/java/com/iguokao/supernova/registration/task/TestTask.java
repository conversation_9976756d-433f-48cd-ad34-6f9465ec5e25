package com.iguokao.supernova.registration.task;


import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@RequiredArgsConstructor
public class TestTask {
    private final OrderService orderService;
    private final CacheService cacheService;

    @Scheduled(cron = "*/5 * * * * ?")
    public void cancel(){
        log.info("定时任务 - 测试 {}", new Date());
        this.cacheService.runLockTask("test", 10, () -> {
            System.out.println("sssss");
        });
    }
}
