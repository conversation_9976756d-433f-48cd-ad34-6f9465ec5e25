package com.iguokao.supernova.registration.repository;


import com.iguokao.supernova.registration.document.Score;
import com.iguokao.supernova.registration.document.Subject;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface ScoreRepository extends MongoRepository<Score, ObjectId> {
    Optional<Score> findByProjectIdAndIdCardNum(ObjectId projectId, String idCardNum);
    List<Score> findByCandidateId(ObjectId candidateId);
}
