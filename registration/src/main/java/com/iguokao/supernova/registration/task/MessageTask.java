package com.iguokao.supernova.registration.task;


import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.MessageService;
import com.iguokao.supernova.registration.service.MqttService;
import com.iguokao.supernova.registration.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class MessageTask {
    private final MqttService mqttService;
    private final CacheService cacheService;

    @Scheduled(cron = "0 */5 * * * ?")
    public void cancel(){
        log.info("定时任务 - 同步MQTT User {}", new Date());
        this.cacheService.runLockTask("mqtt_sync", 10, this.mqttService::sync);
    }
}
