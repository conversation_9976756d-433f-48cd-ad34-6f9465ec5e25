package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum ExceptionEnum implements BaseEnum {

    COMPANY_CONFIG_EXIST(4001, "企业配置已经存在"),
    COMPANY_CONFIG_NOT_FOUND(4002, "企业配置不存在"),
    COMPANY_PAY_NOT_SET(4003, "企业配置没有配置收款商户ID"),
    COMPANY_TOKEN_NO_NOT_SET(4004, "企业配置没有配置收款TokenNo"),
    COMPANY_HF_ID_NOT_SET(4005, "企业配置没有配置HfId"),

    CANDIDATE_EXIST(4010, "考生已经存在，请直接登录"),
    CANDIDATE_NOT_FOUND(4011, "考生未找到，请先进行注册"),
    CANDIDATE_SMS_CODE_NOT_FOUND(4012, "短信验证码未找到或者已过期"),
    CANDIDATE_SMS_CODE_NOT_VALID(4013, "短信验证码验证失败"),
    CANDIDATE_ID_CARD_ERR(4014, "身份证错误"),
    CANDIDATE_SIGN_VERIFY_FAILED(4015, "签名验证失败"),
    CANDIDATE_SIGN_EXPIRED(4016, "签名过期，登录链接有效期是7天"),
    CANDIDATE_MOBILE_EXISTS(4017, "考生手机号已经存在，无法修改"),
    CANDIDATE_BEYOND_10000(4018, "考生数量超过1万，导入单批次最多1万人"),
    CANDIDATE_LOGIN_FAILED(4019, "登录失败，请检查用户名和密码"),
    CANDIDATE_MOBILE_ERR(4020, "您提交的考生手机号错误"),

    SMS_TIMES_BEYOND_LIMIT(4020, "手机号或者IP超出当日发送限制，请明日再试"),
    SMS_TYPE_NOT_SUPPORT(4021, "发送类型无法支持"),
    EMAIL_NOT_EXIST(4022, "邮件模版不存在"),

    ANNOUNCEMENT_NOT_FOUND(4030, "发布没有找到"),

    PROJECT_NAME_EXIST(4101, "项目名称已经存在"),
    PROJECT_NOT_FOUND(4102, "项目不存在"),
    PROJECT_ONLINE(4103, "项目已经上线"),
    PROJECT_APPOINT_NOT_ENABLE(4104, "项目预约未开启"),
    PROJECT_APPOINT_NOT_ON_TIME(4104, "项目预约未开启，请稍后再试"),

    SUBJECT_NAME_ERR(4121, "科目名称错误"),
    SUBJECT_NOT_FOUND(4122, "科目不存在"),

    REGISTRATION_NOT_FOUND(4130, "预约关联数据不存在"),
    REGISTRATION_EXIST(4131, "预约已经存在，不要重复操作"),

    APPOINT_ITEM_NOT_FOUND(4310, "配额未找到"),
    APPOINT_LOCK_GET_FAILED(4311, "网络繁忙，预约失败，请稍后再试"),
    APPOINT_LIMIT_REACH(4312, "预约已经达到了限额，请选择其他选项"),

    ORDER_NOT_FOUND(4401, "订单未找到"),
    ORDER_PREPAY_FAILED(4402, "预支付失败"),
    ORDER_PROJECT_PAID(4401, "该项目已经存在支付的项目"),
    ORDER_PAID_AGAIN(4402, "订单重复支付，如未成功请重新扫码支付"),
    ORDER_PAID_TIME_ERR(4402, "订单支付时间错误，未开放支付，或者支付已经关闭"),
    ORDER_PAID_TIME_OUT(4403, "抱歉！报名缴费已截止！"),
    ORDER_PAID_TIME_EAR(4404, "您好，报名缴费未到开放时间，请稍后再试！"),
    ORDER_REFUND_ERR(4405, "退款失败"),
    ORDER_SETTLED(4406, "订单已经结算，无法退款"),
    ORDER_WITHDRAWING(4407, "当前项目有其他订单正在提现中，结束前无法再次提现"),
    ORDER_REFUNDED(4408, "订单退款中或者已经退款，请勿重复操作"),

    TEMPLATE_NOT_FOUND(4501, "模版未找到"),
    TEMPLATE_CAN_NOT_CHANGE(4502, "模版不能更换企业"),
    TEMPLATE_DATA_NOT_FOUND(4503, "此考生还未进行填报"),
    TEMPLATE_GROUP_NOT_EMPTY(4504, "模版组列表不是空"),
    TEMPLATE_ITEM_NOT_EMPTY(4505, "模版项列表不是空"),
    TEMPLATE_DATA_EDIT_ERR(4506, "您好，您的报名表审核已通过，不能再修改！"),
    TEMPLATE_DATA_TIME_ERR(4507, "您好，报名截止时间已过，不能再修改报名信息！"),
    TEMPLATE_DATA_NOT_PASS(4508, "报名表未审核通过，无法进行付款"),
    TEMPLATE_DATA_NOT_FINISH(4509, "报名表必填项未完成，无法进行提交"),

    MQTT_USER_FILE_NOT_FOUND(4601, "MQTT用户列表未找到"),
    TOKEN_EXPIRED(4602, "短令牌已经失效"),
    WITHDRAW_PASSWORD_ERROR(4603, "提现密码错误"),
    WITHDRAW_PASSWORD_EMPTY(4604, "提现密未设置，请联系管理员"),
    DG_EXCEPTION(4610, "斗拱平台出现错误，请联系管理员处理"),

    ADMISSION_EXPIRED(4701, "准考证下载未开放或者已经过期，请检查开放时间"),
    ;



    public static final BaseEnum AUDIO_EXECUTE_FAILED = null;

    ExceptionEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
