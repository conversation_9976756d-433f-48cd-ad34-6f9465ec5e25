package com.iguokao.supernova.registration.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;


@Getter
@Setter
public class CandidateDeleteRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "科目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String subjectId;


    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生id数组")
    @NotEmpty
    private List<String> candidateIdList;
}
