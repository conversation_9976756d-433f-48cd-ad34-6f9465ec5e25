package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.Notification;
import com.iguokao.supernova.registration.document.Registration;
import com.iguokao.supernova.registration.document.Subject;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.repository.CandidateRepository;
import com.iguokao.supernova.registration.repository.RegistrationRepository;
import com.iguokao.supernova.registration.repository.SubjectRepository;
import com.iguokao.supernova.registration.service.NotificationService;
import com.iguokao.supernova.registration.service.SubjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationServiceImpl implements NotificationService {
    private final SubjectRepository subjectRepository;
    private final CandidateRepository candidateRepository;
    private final MongoTemplate mongoTemplate;
    private final RegistrationRepository registrationRepository;

}
