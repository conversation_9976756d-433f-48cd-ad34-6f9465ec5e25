package com.iguokao.supernova.registration.excel;

import com.iguokao.supernova.common.enums.CredentialCategoryEnum;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.InvoiceEnum;

import java.util.ArrayList;
import java.util.List;

public class TemplateDataExporterV2 {

    public static List<List<String>> head(Template template) {
        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("考生ID");

        List<String> head1 = new ArrayList<>();
        head1.add("姓名");

        List<String> head2 = new ArrayList<>();
        head2.add("手机号");

        List<String> head3 = new ArrayList<>();
        head3.add("证件类型");

        List<String> head4 = new ArrayList<>();
        head4.add("证件号");

        List<String> head5 = new ArrayList<>();
        head5.add("报名方式");

        List<String> head102 = new ArrayList<>();
        head102.add("科目");
        List<String> head103 = new ArrayList<>();
        head103.add("生日");

        List<String> head6 = new ArrayList<>();
        head6.add("Email");

        List<String> head7 = new ArrayList<>();
        head7.add("所在地");

        List<String> head8 = new ArrayList<>();
        head8.add("考区");

        List<String> head104 = new ArrayList<>();
        head104.add("报名填表时间");

        List<String> head105 = new ArrayList<>();
        head105.add("报名完成时间");

        List<String> head9 = new ArrayList<>();
        head9.add("报名表填写完毕");

        List<String> head10 = new ArrayList<>();
        head10.add("审核状态");

        List<String> head106 = new ArrayList<>();
        head106.add("审核不通过原因");

        List<String> head101 = new ArrayList<>();
        head101.add("支付状态");

        List<String> head11 = new ArrayList<>();
        head11.add("支付订单号");

        List<String> head110 = new ArrayList<>();
        head110.add("支付时间");

        List<String> head12 = new ArrayList<>();
        head12.add("备注1");

        List<String> head13 = new ArrayList<>();
        head13.add("备注2");

        List<String> head14 = new ArrayList<>();
        head14.add("备注3");

        List<String> head15 = new ArrayList<>();
        head15.add("需要开发票");

        List<String> head16 = new ArrayList<>();
        head16.add("抬头");

        List<String> head17 = new ArrayList<>();
        head17.add("税号");

        List<String> head18 = new ArrayList<>();
        head18.add("发票类型");

        List<String> head19 = new ArrayList<>();
        head19.add("发票路径");

        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        list.add(head102);
        list.add(head103);

        list.add(head6);
        list.add(head7);
        list.add(head8);
        list.add(head104);
        list.add(head105);

        list.add(head9);
        list.add(head10);
        list.add(head106);
        list.add(head101);

        list.add(head11);
        list.add(head110);
        list.add(head12);
        list.add(head13);
        list.add(head14);
        list.add(head15);
        list.add(head16);
        list.add(head17);
        list.add(head18);
        list.add(head19);

        for(TemplateGroup group : template.getGroupList()){
            for(TemplateItem templateItem : group.getItemList()){
                List<String> headDef = new ArrayList<>();
                headDef.add(templateItem.getName());
                list.add(headDef);
            }
        }
        return list;
    }

    public static List<List<String>> data(List<TemplateData> templateDataList, Template template, List<Subject> subjectList) {
        List<List<String>> list = new ArrayList<>();

        for(TemplateData templateData : templateDataList){
            List<String> line = new ArrayList<>();
            line.add(templateData.getCandidateId().toString());
            line.add(templateData.getFullName());
            line.add(templateData.getMobile());
            line.add(handleIdCardType(templateData.getIdCardType()));
            line.add(templateData.getIdCardNum());
            line.add(templateData.getImported() ? "导入报名" : "个人报名");

            List<String> subjectNameList = new ArrayList<>();
            templateData.getSubjectIdList().forEach(subjectId -> {
                subjectList
                        .stream()
                        .filter(subject -> subject.get_id().equals(subjectId))
                        .findFirst()
                        .ifPresent(subject -> subjectNameList.add(subject.getName()));
            });
            line.add(String.join(",", subjectNameList));
            line.add(DateUtil.dateToStrStd(templateData.getBirthday()));

            line.add(templateData.getEmail());
            line.add(templateData.getLocation());
            line.add(templateData.getCity());
            line.add(DateUtil.dateToStrStd(templateData.getCreatedAt()));
            line.add(DateUtil.dateToStrStd(templateData.getUpdatedAt()));
            line.add(templateData.getFinished() ? "已完成" : "未完成");
            if(templateData.getPassed() == null){
                line.add("未审核");
            }
            else if(!templateData.getPassed()){
                line.add("未通过");
            }
            else {
                line.add("通过");
            }

            //未通过
            if(templateData.getPassed() != null && !templateData.getPassed()){
                ApproveItem item = templateData.getApproveList()
                        .stream()
                        .filter(i -> !i.getPassed())
                        .reduce((first, second) -> second)
                        .orElse(null);
                if(item != null){
                    line.add(item.getMessage());
                } else {
                    line.add("");
                }
            }
            //审核通过或者未审核
            else {
                line.add("");
            }
            line.add(templateData.getSuccessTradeNo() == null ? "未支付" : "已支付");
            line.add(templateData.getSuccessTradeNo());
            line.add(DateUtil.dateToStrStd(templateData.getPaidAt()));
            line.add(templateData.getRemark1());
            line.add(templateData.getRemark2());
            line.add(templateData.getRemark3());


            if(templateData.getInvoice() == null){
                line.add("未选择");
            }
            else if(!templateData.getInvoice()){
                line.add("不需要");
            }
            else {
                line.add("需要");
            }
            line.add(templateData.getInvoiceTitle());
            line.add(templateData.getInvoiceTin());
            line.add(handleInvoiceType(templateData.getInvoiceType()));
            line.add(templateData.getInvoiceLink());

            for(TemplateGroup group : template.getGroupList()){
                for(TemplateItem templateItem : group.getItemList()){
                    int hit = 0;
                    for(TemplateItemData templateItemData : templateData.getTemplateItemList()){
                        if(templateItem.getItemId().toString().equals(templateItemData.getItemId().toString())){
                            line.add(templateItemData.getValue());
                            hit = 1;
                        }
                    }
                    if(hit == 0){
                        line.add("");
                    }
                }
            }

            list.add(line);
        }

        return list;
    }

    private static String handleIdCardType(Integer type){
        for(CredentialCategoryEnum e : CredentialCategoryEnum.values()){
            if(e.getCode().equals(type)){
                return e.getText();
            }
        }
        return "未知类型";
    }

    private static String handleInvoiceType(Integer type){
        for(InvoiceEnum e : InvoiceEnum.values()){
            if(e.getCode().equals(type)){
                return e.getText();
            }
        }
        return "未知类型";
    }
}
