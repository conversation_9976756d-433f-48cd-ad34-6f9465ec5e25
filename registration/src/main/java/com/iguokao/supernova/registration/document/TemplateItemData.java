package com.iguokao.supernova.registration.document;

import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class TemplateItemData {
    private ObjectId itemId;
    private String value;

    public static List<TemplateItemData> of(Map<String, String> map, List<TemplateItemData> templateItemList){
        if(null == map){
            return null;
        }
        List<TemplateItemData> res = new ArrayList<>();
        map.keySet().forEach(k -> {
            TemplateItemData d = new TemplateItemData();
            d.setItemId(new ObjectId(k));
            d.setValue(map.get(k));
            res.add(d);
        });

        //更新提交
        if(!templateItemList.isEmpty()){
            List<TemplateItemData> res2 = new ArrayList<>();
            for(TemplateItemData data : templateItemList){
                int count = 0;
                for(String k : map.keySet()){
                    if(data.getItemId().toString().equals(k)){
                        count = 1;
                        break;
                    }
                }
                if(count == 0){
                    res2.add(data);
                }
            }
            List<TemplateItemData> res3 = new ArrayList<>();
            res3.addAll(res);
            res3.addAll(res2);
            return res3;
        }
        //第一次提交
        else {
            return res;
        }

    }
}

