package com.iguokao.supernova.registration.request;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CompanyConfigUpdateRequest {
    private String companyId;
    private String sn;

    private String name;
    private String logo;
    private String banner;
    private String agreement;
    private String miniBanner;

    private Boolean myProject;
    private Boolean appointEnabled;
    private Boolean admissionEnabled;
    private Boolean scoreEnabled;
    private Boolean invoiceEnable;
    private Boolean chatEnable;
    private Boolean myInfoEnable;
    private Boolean projectListEnable;
    private String smsApproveSuccessTemplate;
    private String smsApproveRejectedTemplate;
    private String smsRefundTemplate;
    private String support;

    private String primaryColor;
    private String menuColor;
    private String menuHoverColor;

    private List<Integer> invoiceTypeList;

    private String supplementaryName;
}
