package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;

@Getter
@Setter
public class TemplateAddRequest {
    private String companyId;
    private String name;
    private Boolean uploadAvatar;
    private Boolean location;
}
