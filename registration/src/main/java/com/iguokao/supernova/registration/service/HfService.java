package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.registration.document.HfResult;
import com.iguokao.supernova.registration.document.Order;

public interface HfService {
    String balance(String hfId);
    HfResult pay(Order order, String openid, String hfSplitId);
    HfResult refund(Order order);
    HfResult withdraw(Order order, String tradeNo);
    String fee(String hfId, String amount);
}
