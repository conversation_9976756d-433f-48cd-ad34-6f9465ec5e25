package com.iguokao.supernova.registration.request;

import com.iguokao.supernova.common.request.PageRequest;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.registration.response.AnnouncementResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class PageAnnouncementRequest extends PageRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "companyId")
    @Length(min = 24, max = 24, message = "companyId长度超限")
    private String companyId;

    private String categoryId;

}
