package com.iguokao.supernova.registration.security;

import com.iguokao.supernova.common.exception.SecurityAccessDeniedHandler;
import com.iguokao.supernova.common.exception.SecurityAuthenticationEntryPoint;
import com.iguokao.supernova.common.security.JwtAuthorizationFilter;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.LoginKeyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.expression.WebExpressionAuthorizationManager;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import static com.iguokao.supernova.common.constant.SecurityConstant.ENDPOINTS_WHITELIST;

@Configuration
@Slf4j
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = false, jsr250Enabled = false, prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtService jwtService;
    private final AuthenticationConfiguration config;
    private final SecurityAuthenticationEntryPoint authenticationEntryPoint;
    private final SecurityAccessDeniedHandler accessDeniedHandler;
    private final LoginKeyService loginKeyService;

    @Value("${app.security.remote-ip}")
    private String remoteIp;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http)throws Exception{
        AntPathRequestMatcher[] matchers = new AntPathRequestMatcher[ENDPOINTS_WHITELIST.length];
        for (int i = 0; i < ENDPOINTS_WHITELIST.length; i++) {
            matchers[i] = new AntPathRequestMatcher(ENDPOINTS_WHITELIST[i]);
        }

        // 远程
        String remoteServer = String.format("%s or hasIpAddress('127.0.0.1/32')", remoteIp);
        log.info("允许远程 - {}", remoteServer);
        WebExpressionAuthorizationManager remoteManager = new WebExpressionAuthorizationManager(remoteServer);

        http
//                .cors(Customizer.withDefaults())
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/validate_code")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/sms_code")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/authorization/code/*")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/company/*")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/project/*")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/login")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/sms/login")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/link/login/**")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/register")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/candidate/announcement/page")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/wechat/pay/callback")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/wechat/refund/callback")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/pay/huifu/withdraw/callback")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/pay/huifu/pay/callback")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/pay/huifu/refund/callback")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/order/cancel/**")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/pay/wx/prepay")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/actuator/**")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/remote/**")).access(remoteManager)
                        .requestMatchers(matchers).permitAll()
                        .anyRequest().authenticated()
                )
//                .authenticationProvider(authenticationProvider)
                .addFilterBefore(new JwtAuthorizationFilter(jwtService, loginKeyService, config.getAuthenticationManager()), UsernamePasswordAuthenticationFilter.class)
                .sessionManagement(sessionManagement -> sessionManagement.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .headers(httpSecurityHeadersConfigurer -> httpSecurityHeadersConfigurer.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable))
                .exceptionHandling(exceptionHandlingConfigurer -> exceptionHandlingConfigurer
                        .authenticationEntryPoint(authenticationEntryPoint)
                        .accessDeniedHandler(accessDeniedHandler)
                );
        return http.build();
    }
}