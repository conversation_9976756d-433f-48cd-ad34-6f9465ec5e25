package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.CloopenRemote;
import com.iguokao.supernova.common.remote.cloopen.SmsResponse;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.enums.OrderStateEnum;
import com.iguokao.supernova.registration.repository.*;
import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.SmsService;
import com.iguokao.supernova.registration.service.TemplateService;
import com.mongodb.client.result.UpdateResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateServiceImpl implements TemplateService {
    private final TemplateRepository templateRepository;
    private final CandidateRepository candidateRepository;
    private final TemplateDataRepository templateDataRepository;
    private final RegistrationRepository registrationRepository;
    private final ProjectRepository projectRepository;
    private final MongoTemplate mongoTemplate;
    private final CacheService cacheService;
    private final OrderRepository orderRepository;
    private final CompanyConfigRepository companyConfigRepository;
    private final SmsService smsService;

    @Override
    public Tuple2<List<Template>, Integer> page(String companyId, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt"));

        if(companyId != null){
            query = query.addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));
        }
        // 计算总数
        long count = this.mongoTemplate.count(query, Template.class);
        // 分页信息
        query = query.with(pageable);
        List<Template> list = this.mongoTemplate.find(query, Template.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public void add(Template template) {
        this.templateRepository.insert(template);
    }

    @Override
    public void update(Template template) {
        Template t = this.getById(template.get_id().toString());
        t.setName(template.getName());
        t.setLocation(template.getLocation());
        t.setUploadAvatar(template.getUploadAvatar());
        this.templateRepository.save(t);
    }

    @Override
    public void remove(String templateId) {
        Template template = this.getById(templateId);
        if (template.getGroupList().isEmpty()){
            this.templateRepository.deleteById(new ObjectId(templateId));
        } else {
            throw new ServiceException(ExceptionEnum.TEMPLATE_GROUP_NOT_EMPTY);
        }
    }

    @Override
    public void link(String templateId, String projectId) {
        Template template = this.getById(templateId);
        if(template.getProjectId() != null){
            throw new ServiceException(ExceptionEnum.TEMPLATE_CAN_NOT_CHANGE);
        }
        template.setProjectId(new ObjectId(projectId));
        this.templateRepository.save(template);
    }

    @Override
    public Template getById(String templateId) {
        return this.templateRepository.findById(new ObjectId(templateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.TEMPLATE_NOT_FOUND));
    }

    @Override
    public void addGroup(String templateId, TemplateGroup group) {
        Template template = this.getById(templateId);
        template.getGroupList().add(group);
        this.templateRepository.save(template);
    }

    @Override
    public void updateGroup(String templateId, TemplateGroup group) {
        Template template = this.getById(templateId);
        template.getGroupList().stream()
                .filter(g -> g.getGroupId().equals(group.getGroupId()))
                .findFirst()
                .ifPresent(g -> {
                    g.setName(group.getName());
                });
        this.templateRepository.save(template);
    }

    @Override
    public void removeGroup(String templateId, String groupId) {
        Template template = this.getById(templateId);
        template.getGroupList().stream().filter(group -> group.getGroupId().toString().equals(groupId)).findFirst().ifPresent(g -> {
            if(g.getItemList().isEmpty()){
                template.getGroupList().removeIf(group -> group.getGroupId().toString().equals(groupId));
            } else {
                throw new ServiceException(ExceptionEnum.TEMPLATE_ITEM_NOT_EMPTY);
            }
        });
        this.templateRepository.save(template);
    }

    @Override
    public void addItem(String templateId, String groupId, TemplateItem templateItem) {
        Template template = this.getById(templateId);
        template.getGroupList().stream()
                .filter(group -> group.getGroupId().toString().equals(groupId))
                .findFirst()
                .ifPresent(group -> group.getItemList().add(templateItem));
        this.templateRepository.save(template);
    }

    @Override
    public void updateItem(String templateId, String groupId, TemplateItem templateItem) {
        Template template = this.getById(templateId);
        template.getGroupList().stream()
                .filter(group -> group.getGroupId().toString().equals(groupId))
                .findFirst()
                .flatMap(group -> group.getItemList().stream()
                        .filter(item -> item.getItemId().equals(templateItem.getItemId()))
                        .findFirst()).ifPresent(item -> {
                    BeanUtils.copyProperties(templateItem, item);
                });
        this.templateRepository.save(template);
    }

    @Override
    public void removeItem(String templateId, String groupId, String itemId) {
        Template template = this.getById(templateId);
        template.getGroupList()
                .stream()
                .filter(group -> group.getGroupId().toString().equals(groupId))
                .findFirst()
                .ifPresent(g -> {
                    g.getItemList().removeIf(item -> item.getItemId().toString().equals(itemId));
                });
        this.templateRepository.save(template);
    }

    @Override
    public void submit(String templateId, String projectId, String candidateId, Map<String, String> data) {
        TemplateData templateData = new TemplateData();
        templateData.setProjectId(new ObjectId(templateId));
        templateData.set_id(new ObjectId(templateId));
        templateData.setCandidateId(new ObjectId(candidateId));
        //templateData.setTemplateItemList(TemplateItemData.of(data));
        this.templateDataRepository.save(templateData);
    }

    @Override
    public TemplateData getDataByProjectIdAndCandidateId(String projectId, String candidateId, String templateId) {
        TemplateData templateData = templateDataRepository.findByProjectIdAndCandidateId(new ObjectId(projectId), new ObjectId(candidateId)).orElse(null);

        Candidate candidate = this.candidateRepository.findById(new ObjectId(candidateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));

        //如果是第一个次获取
        if(null == templateData){
            TemplateData templateData1 = new TemplateData();
            templateData1.setTemplateId(new ObjectId(templateId));
            templateData1.setProjectId(new ObjectId(projectId));
            templateData1.setCandidateId(new ObjectId(candidateId));

            templateDateUpdateCandidateInfo(candidate,templateData1);
            return templateData1;
        }

        //templateDateUpdateCandidateInfo(candidate,templateData);
        return templateData;
    }

    private void templateDateUpdateCandidateInfo(Candidate candidate,TemplateData templateData) {
        templateData.setFullName(candidate.getFullName());
        templateData.setMobile(candidate.getMobile());
        templateData.setIdCardNum(candidate.getIdCardNum());
        templateData.setIdCardType(candidate.getIdCardType());
        templateData.setEmail(candidate.getEmail());
    }

    @Override
    public TemplateData findByProjectIdAndCandidateId(String projectId, String candidateId, String templateId) {
        return templateDataRepository.findByProjectIdAndCandidateId(new ObjectId(projectId), new ObjectId(candidateId))
                .orElse(null);
    }

    @Override
    public void updateTemplateData(TemplateData templateData, Boolean judgmentPass) {

        Candidate candidate = this.candidateRepository.findById(templateData.getCandidateId()).orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));

        if(judgmentPass){
            //已经通过了,不可再修改
            if(templateData.getPassed() != null && templateData.getPassed()){
                throw new ServiceException(ExceptionEnum.TEMPLATE_DATA_EDIT_ERR);
            }

            Project project = this.projectRepository.findById(templateData.getProjectId()).orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
            Date date = new Date();
            //报名时间已经结束了,不可再修改
            if(project.getSignUpEndAt().getTime() <= date.getTime()){
                throw new ServiceException(ExceptionEnum.TEMPLATE_DATA_TIME_ERR);
            }
        }

        //之前没通过的考生模板数据，重新更新后，变成未审核
        if(templateData.getPassed() != null && !templateData.getPassed()){
            templateData.setPassed(null);
            templateData.setUpdatedAt(new Date());
        }
        this.templateDataRepository.save(templateData);

        candidate.setFullName(templateData.getFullName() == null ? candidate.getFullName() : templateData.getFullName());
        candidate.setIdCardNum(templateData.getIdCardNum() == null ? candidate.getIdCardNum() : templateData.getIdCardNum());
        candidate.setIdCardType(templateData.getIdCardType() == null ? candidate.getIdCardType() : templateData.getIdCardType());
        candidate.setEmail(templateData.getEmail() == null ? candidate.getEmail() : templateData.getEmail());
        this.candidateRepository.save(candidate);
    }

    @Override
    public Map<String, String> data(String templateId, String projectId, String candidateId) {
        return Map.of();
    }

    @Override
    public Query getSearchQuery(String projectId, String templateId, String fullName, String mobile, String idCardNum, Integer passState, Integer gender, Boolean finished, Boolean payed, Map<String, String> searchList, String remark1, String remark2, String remark3, String city, String subjectId, Date signUpStart, Date signUpEnd, Date birthdayStart, Date birthdayEnd, Boolean imported) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt", "_id"))
                .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)))
                .addCriteria(Criteria.where("templateId").is(new ObjectId(templateId)));

        if (subjectId != null) {
            List<ObjectId> subjectIdList = new ArrayList<>();
            subjectIdList.add(new ObjectId(subjectId));
            query = query.addCriteria(Criteria.where("subjectIdList").in(subjectIdList));
        }
        if (city != null) {
            query = query.addCriteria(Criteria.where("city").is(city));
        }
        if (gender != null) {
            query = query.addCriteria(Criteria.where("gender").is(gender));
        }
        if (fullName != null) {
            query = query.addCriteria(Criteria.where("fullName").is(fullName));
        }
        if (mobile != null) {
            query = query.addCriteria(Criteria.where("mobile").is(mobile));
        }
        if (idCardNum != null) {
            query = query.addCriteria(Criteria.where("idCardNum").is(idCardNum));
        }
        if (imported != null) {
            query = query.addCriteria(Criteria.where("imported").is(imported));
        }
        if (passState > 0) {
            //未审核
            if (passState == 1) {
                query = query.addCriteria(Criteria.where("passed").is(null));
            }
            //已通过
            if (passState == 2) {
                query = query.addCriteria(Criteria.where("passed").is(true));
            }
            //拒绝通过
            if (passState == 3) {
                query = query.addCriteria(Criteria.where("passed").is(false));
            }

        }
        if (finished != null) {
            query = query.addCriteria(Criteria.where("finished").is(finished));
        }
        if (payed != null) {

            if (payed) {
                query = query.addCriteria(Criteria.where("successTradeNo").exists(true));
            } else {
                query = query.addCriteria(Criteria.where("successTradeNo").exists(false));
            }

        }

        if (searchList != null) {
            List<String> keyList = new ArrayList<>();
            for (String key : searchList.keySet()) {
                keyList.add(searchList.get(key));
            }
            query = query.addCriteria(Criteria.where("templateItemList.value").all(keyList));
        }

        if (remark1 != null) {
            query = query.addCriteria(Criteria.where("remark1").is(remark1));
        }
        if (remark2 != null) {
            query = query.addCriteria(Criteria.where("remark2").is(remark2));
        }
        if (remark3 != null) {
            query = query.addCriteria(Criteria.where("remark3").is(remark3));
        }
        if (signUpStart != null && signUpEnd != null) {
            query = query.addCriteria(Criteria.where("createdAt").gte(signUpStart).lte(signUpEnd)); // 时间范围查询
        }
        if (birthdayStart != null && birthdayEnd != null) {
            query = query.addCriteria(Criteria.where("birthday").gte(birthdayStart).lte(birthdayEnd)); // 时间范围查询
        }
        return query;
    }

    @Override
    public String sendSms(Query query, String smsTemplateId, Boolean skipSmsSent) {
        if(skipSmsSent){
            query.addCriteria(Criteria.where("smsHistoryList").not().elemMatch(Criteria.where("smsTemplateId").is(smsTemplateId)));
        }
//        long count = this.mongoTemplate.count(query, TemplateData.class);
        // 分页信息
        query.fields()
                .include("fullName")
                .include("mobile");
        List<TemplateData> list = this.mongoTemplate.find(query, TemplateData.class);
        String benchId = new ObjectId().toString();
        this.sendSms(list, smsTemplateId, benchId);
        return benchId;
    }

    @Override
    public String sendSmsByTemplateDataIdList(List<String> templateDataIdList, String smsTemplateId, Boolean skipSmsSent) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt", "_id"))
                .addCriteria(Criteria.where("_id").in(templateDataIdList
                        .stream()
                        .map(ObjectId::new)
                        .toList()));
        if(skipSmsSent){
            query.addCriteria(Criteria
                    .where("smsHistoryList")
                    .not()
                    .elemMatch(Criteria.where("smsTemplateId").is(smsTemplateId)));
        }
        query.fields()
                .include("fullName")
                .include("mobile");
        List<TemplateData> list = this.mongoTemplate.find(query, TemplateData.class);
        String benchId = new ObjectId().toString();
        this.sendSms(list, smsTemplateId, benchId);
        return benchId;
    }

    @Async
    private String sendSms(List<TemplateData> templateDataList, String smsTemplateId, String benchId) {
        SmsProgress progress = new SmsProgress();
        progress.setTotal(templateDataList.size());
        progress.setSmsTemplateId(smsTemplateId);
        progress.setBenchId(benchId);
        this.smsService.smsProgressAdd(progress);
        this.benchSmsSend(templateDataList, smsTemplateId, progress);
        return progress.getBenchId();
    }

    @Override
    public Tuple2<List<TemplateData>, Integer> dataPage(Query query,  Pageable pageable) {
        // 计算总数
        long count = this.mongoTemplate.count(query, TemplateData.class);
        // 分页信息
        query = query.with(pageable);
        List<TemplateData> list = this.mongoTemplate.find(query, TemplateData.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public String queue(String projectId, String candidateId, String templateId, Integer type) {
        TemplateData templateData = templateDataRepository.findByProjectIdAndCandidateId(new ObjectId(projectId), new ObjectId(candidateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.TEMPLATE_DATA_NOT_FOUND));

        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "_id","candidateId"))
                .addCriteria(Criteria.where("projectId").is(templateData.getProjectId()))
                .addCriteria(Criteria.where("templateId").is(templateData.getTemplateId()))
                .addCriteria(Criteria.where("candidateId").ne(templateData.getCandidateId()));

        //上一条
        if(type == 1){
            query = query.addCriteria(Criteria.where("_id").lt(templateData.get_id()));
        }
        //下一条
        else {
            query = query.addCriteria(Criteria.where("_id").gt(templateData.get_id()));
        }


        TemplateData findData = this.mongoTemplate.findOne(query, TemplateData.class);

        return findData == null ? null : findData.getCandidateId().toString();
    }

    @Override
    public void judge(String companyId, List<SmsSendItem> sendItemList, ApproveItem item) {
        CompanyConfig companyConfig = this.companyConfigRepository.findById(new ObjectId(companyId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.COMPANY_CONFIG_NOT_FOUND));

        for (SmsSendItem sendItem : sendItemList){
            Query query = new Query().addCriteria(Criteria.where("_id").is(new ObjectId(sendItem.getTemplateDataId())));
            Update update = new Update();
            if(item.getPassed()){
                update.set("passed", true);
            } else {
                query.addCriteria(Criteria.where("successTradeNo").exists(false));
                update.set("passed", false);
            }
            update.push("approveList", item);
            UpdateResult res = mongoTemplate.updateFirst(query, update, TemplateData.class);
            this.handlePassedResult(sendItem.getTemplateDataId(), item.getPassed());
            if(res.getModifiedCount() > 0){
                if(companyConfig.getSmsApproveSuccessTemplate() != null && item.getPassed()){
                    String errCode = this.smsService.sendSms(sendItem.getMobile(), companyConfig.getSmsApproveSuccessTemplate(), List.of(sendItem.getFullName()));
                    this.addSmsHistory(sendItem.getTemplateDataId(), companyConfig.getSmsApproveSuccessTemplate(), errCode);
                } else if (companyConfig.getSmsApproveRejectedTemplate() != null && !item.getPassed()){
                    String errCode = this.smsService.sendSms(sendItem.getMobile(), companyConfig.getSmsApproveRejectedTemplate(), List.of(sendItem.getFullName()));
                    this.addSmsHistory(sendItem.getTemplateDataId(), companyConfig.getSmsApproveSuccessTemplate(), errCode);
                }
            }
        }
    }

    @Async
    private void benchSmsSend(List<TemplateData> list, String smsTemplateId, SmsProgress progress ){
        for (TemplateData templateData : list){
            String res  = this.smsService.sendSms(templateData.getMobile(), smsTemplateId, List.of(templateData.getFullName()));
            if(res == null){
                progress.setSuccess(progress.getSuccess() + 1);
            } else {
                progress.setFail(progress.getFail() + 1);
            }
            this.addSmsHistory(templateData.get_id().toString(), smsTemplateId, res);
        }
    }


    private void addSmsHistory(String templateDataId, String smsTemplateId, String errCode){
        SmsHistory smsHistory = new SmsHistory();
        smsHistory.setSmsTemplateId(smsTemplateId);
        smsHistory.setCreatedAt(new Date());
        smsHistory.setErrCode(errCode);
        Query query = new Query().addCriteria(Criteria.where("_id").is(new ObjectId(templateDataId)));
        Update update = new Update();
        update.push("smsHistoryList", smsHistory);
        mongoTemplate.updateFirst(query, update, TemplateData.class);
    }

    @Override
    public List<TemplateData> getDataByCandidateId(String candidateId) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "createdAt"))
                .addCriteria(Criteria.where("candidateId").is(new ObjectId(candidateId)));


        return this.mongoTemplate.find(query, TemplateData.class);
    }

    @Override
    public Tuple2<List<Subject>, List<Project>> getCandidateSubject(List<TemplateData> templateDataList) {
        List<String> projectIdList = new ArrayList<>();
        List<String> subjectIdList = new ArrayList<>();

        for(TemplateData data : templateDataList){
            projectIdList.add(data.getProjectId().toString());
            for(ObjectId subjectId : data.getSubjectIdList()){
                subjectIdList.add(subjectId.toString());
            }
        }

        List<Project> projectList = this.cacheService.getProjectList(projectIdList);
        List<Subject> subjectList = this.cacheService.getSubjectList(subjectIdList);
        return new Tuple2<>(subjectList, projectList);
    }

    @Override
    public void updateProjectId(String templateId, String projectId) {
        Project project = this.projectRepository.findById(new ObjectId(projectId)).orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
        //现在的project已经引用了一个模板,先删除projectId字段
        if(project.getTemplateId() != null){
            Query query = new Query()
                    .addCriteria(Criteria.where("_id").is(project.getTemplateId()));
            Update update = new Update();
            update.unset("projectId");
            mongoTemplate.updateFirst(query, update, Template.class);
        }

        //新的模板被project引用
        Template template = this.templateRepository.findById(new ObjectId(templateId)).orElseThrow(() -> new ServiceException(ExceptionEnum.TEMPLATE_NOT_FOUND));
        template.setProjectId(new ObjectId(projectId));
        this.templateRepository.save(template);
    }

    @Override
    public void copy(String templateId) {
        Template template = this.templateRepository.findById(new ObjectId(templateId)).orElseThrow(() -> new ServiceException(ExceptionEnum.TEMPLATE_NOT_FOUND));
        Template newTem = new Template();
        newTem.setCompanyId(template.getCompanyId());
        newTem.setParentId(template.get_id());
        newTem.setName(template.getName() + "_" + new Date().getTime());

        newTem.setUploadAvatar(template.getUploadAvatar());
        newTem.setGroupList(template.getGroupList());
        this.templateRepository.save(newTem);
    }

    @Override
    public TemplateData getDataByProjectIdAndCandidateId(String projectId, String candidateId) {
        return templateDataRepository.findByProjectIdAndCandidateId(new ObjectId(projectId), new ObjectId(candidateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.TEMPLATE_DATA_NOT_FOUND));
    }

    @Override
    public List<TemplateData> getInvoiceListByCandidateId(String candidateId) {
        Query query = new Query()
                .addCriteria(Criteria.where("candidateId").is(new ObjectId(candidateId)))
                .addCriteria(Criteria.where("invoice").is(true))
                .addCriteria(Criteria.where("successTradeNo").ne(null));
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        query.with(sort);
        query.fields()
                .include("projectId")
                .include("candidateId")
                .include("successTradeNo")
                .include("invoice")
                .include("invoiceType")
                .include("invoiceTitle")
                .include("invoiceTin")
                .include("invoicePrice")
                .include("invoiceLink");
        List<TemplateData> list =  this.mongoTemplate.find(query, TemplateData.class);
        List<Integer> stateList = new ArrayList<>();
        stateList.add(OrderStateEnum.SUCCESS.getCode());
        stateList.add(OrderStateEnum.FINISHED.getCode());
        List<Order> orderList = this.orderRepository.findByCandidateIdAndStateIn(new ObjectId(candidateId), stateList);
        for(TemplateData data : list){
            Order order = orderList
                    .stream()
                    .filter(o -> o.getProjectId().equals(data.getProjectId()))
                    .findFirst()
                    .orElse(null);
            if(order != null){
                data.setRemark1(order.getDescription());
                data.setInvoicePrice(StringUtil.toPriceString(order.getAmount()));
            }
        }
        return list;
    }

    @Override
    public void setRemark(List<String> templateDataIdList, String remark1, String remark2, String remark3) {
        List<ObjectId> idList = templateDataIdList
                .stream()
                .map(ObjectId::new)
                .toList();

        Query query = new Query()
                .addCriteria(Criteria.where("_id").in(idList));
        Update update = new Update();
        if(remark1 != null){
            update.set("remark1",remark1);
        }
        if(remark2 != null){
            update.set("remark2",remark2);
        }
        if(remark3 != null){
            update.set("remark3",remark3);
        }

        mongoTemplate.updateMulti(query, update, TemplateData.class);

    }

    @Override
    public List<TemplateData> getByProjectId(String projectId) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "candidateId","finished","passed"))
                .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)));
        return this.mongoTemplate.find(query, TemplateData.class);
    }

    @Override
    public List<Integer> passInfo(String projectId) {
        List<Integer> list = new ArrayList<>();
        Integer total = this.templateDataRepository.countByProjectId(new ObjectId(projectId));
        Integer paid = this.templateDataRepository.countByProjectIdAndSuccessTradeNoExists(new ObjectId(projectId),true);
        Integer passSuc = this.templateDataRepository.countByProjectIdAndPassed(new ObjectId(projectId),true);
        Integer passFail = this.templateDataRepository.countByProjectIdAndPassed(new ObjectId(projectId),false);
        Integer finishedNotApproved = this.templateDataRepository.countByProjectIdAndFinishedAndPassed(new ObjectId(projectId),true, null);

        list.add(total);
        list.add(paid);
        list.add(passSuc);
        list.add(passFail);
        list.add(finishedNotApproved);
        return list;
    }

    @Override
    public void passAll(String projectId) {

    }

    @Override
    public TemplateData checkFinished(String projectId, String candidateId, String templateId) {
       Template template = this.templateRepository.findById(new ObjectId(templateId)).orElseThrow(() -> new ServiceException(ExceptionEnum.TEMPLATE_NOT_FOUND));
       TemplateData templateData = templateDataRepository.findByProjectIdAndCandidateId(new ObjectId(projectId), new ObjectId(candidateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.TEMPLATE_DATA_NOT_FOUND));

       List<String> templateDataItemIdList = new ArrayList<>();
       for(TemplateItemData itemData : templateData.getTemplateItemList()){
           templateDataItemIdList.add(itemData.getItemId().toString());
       }

       for(TemplateGroup group : template.getGroupList()){
           for(TemplateItem item : group.getItemList()){
               if(item.getRequired()){
                    if(!templateDataItemIdList.contains(item.getItemId().toString())){
                        throw new ServiceException(ExceptionEnum.TEMPLATE_DATA_NOT_FINISH);
                    }
               }
           }
       }

        return templateData;
    }

    @Override
    public void addByGroup(TemplateData templateData) {
        this.templateDataRepository.save(templateData);
    }

    @Override
    public void clearGroup(String candidateId) {
        this.templateDataRepository.deleteByCandidateId(new ObjectId(candidateId));
    }

    private void handlePassedResult(String templateDataId, Boolean passed) {
        TemplateData templateData = this.templateDataRepository.findById(new ObjectId(templateDataId)).orElseThrow(() -> new ServiceException(ExceptionEnum.TEMPLATE_DATA_NOT_FOUND));
        for (ObjectId subjectId : templateData.getSubjectIdList()) {
            //处理通过
            if (passed) {
                Registration registration = this.registrationRepository.findRegistrationByProjectIdAndCandidateIdAndSubjectId(templateData.getProjectId(),
                        templateData.getCandidateId(), subjectId);
                //第一次
                if (registration == null) {
                    Registration registrationN = new Registration();
                    registrationN.setCandidateId(templateData.getCandidateId());
                    registrationN.setProjectId(templateData.getProjectId());
                    registrationN.setSubjectId(subjectId);

                    registrationN.setCity(templateData.getCity());
                    registrationN.setFullName(templateData.getFullName());
                    registrationN.setMobile(templateData.getMobile());
                    registrationN.setEmail(templateData.getEmail());
                    registrationN.setShortCode(StringUtil.genPassword(8));
                    registrationN.setPassed(true);
                    this.registrationRepository.insert(registrationN);
                }
                //已经存在
                else {
                    System.out.println("数据异常：" + subjectId.toString() + "," + templateData.getCandidateId().toString());
                }
            }
            //处理不通过
            else {
                this.registrationRepository.removeRegistrationByProjectIdAndCandidateIdAndSubjectId(templateData.getProjectId(),
                        templateData.getCandidateId(), subjectId);
            }
        }
    }

}
