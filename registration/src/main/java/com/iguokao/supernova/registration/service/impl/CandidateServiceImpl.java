package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.enums.CredentialCategoryEnum;
import com.iguokao.supernova.common.enums.ImportTypeEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.CloopenRemote;
import com.iguokao.supernova.common.remote.ShortUrlRequest;
import com.iguokao.supernova.common.remote.StoneExamRemote;
import com.iguokao.supernova.common.remote.cloopen.SmsResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.util.RequestUtil;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.CandidateConfirmStateEnum;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.repository.*;
import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.CandidateService;
import com.mongodb.client.result.DeleteResult;
import com.wf.captcha.SpecCaptcha;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class CandidateServiceImpl implements CandidateService {
    private final CandidateRepository candidateRepository;
    private final RegistrationRepository registrationRepository;
    private final OrderRepository orderRepository;
    private final ProjectRepository projectRepository;
    private final TemplateDataRepository templateDataRepository;
    private final MongoTemplate mongoTemplate;
    private final CacheService cacheService;
    private final BCryptPasswordEncoder bCryptPasswordEncoder;
    private final CloopenRemote cloopenRemote;
    private final JwtService jwtService;
    private final StoneExamRemote stoneExamRemote;
    private final ScoreRepository scoreRepository;

    @Value(value = "${app.cloopen.account-sid}")
    private String cloopenAccountSid;
    @Value(value = "${app.cloopen.sms-app-id}")
    private String cloopenSmsAppId;
    @Value(value = "${app.cloopen.auth-token}")
    private String cloopenAuthToken;

    @Value(value = "${app.cloopen.template.candidate-login}")
    private String smsTemplateLogin;

    @Value(value = "${app.appointment-link}")
    private String appointmentLink;

    private static final String SUFFIX_REGISTER = "reg";
    private static final String SUFFIX_LOGIN = "log";

    private final AuthenticationManager authenticationManager;

    @Override
    public ImageCode getImageCode() {
        SpecCaptcha specCaptcha = new SpecCaptcha(160, 40, 4);
        ImageCode imageCode = new ImageCode();
        imageCode.setText(specCaptcha.text().toLowerCase());
        imageCode.setKey(UUID.randomUUID().toString().replace("-", "").toLowerCase());
        // 加入缓存
        log.info("ImageCode - {} - {}", imageCode.getKey(), imageCode.getText());
        this.cacheService.setImageCode(imageCode);
        // 输入
        imageCode.setImageCode(specCaptcha.toBase64());
        return imageCode;
    }

    @Override
    public void sendRegisterSms(String companyId, ImageCode imageCode, String mobile, HttpServletRequest request) {
        this.sendSms(imageCode, mobile, SUFFIX_REGISTER, request);
    }

    @Override
    public void sendLoginSms(String companyId, ImageCode imageCode, String mobile, HttpServletRequest request) {
        if (this.candidateRepository.countByCompanyIdAndLoginName(new ObjectId(companyId), mobile) == 0) {
            throw new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND);
        }
        this.sendSms(imageCode, mobile, SUFFIX_LOGIN, request);
    }

    @Override
    public String register(Candidate candidate, String smsCode) {
        if (this.candidateRepository.countByCompanyIdAndLoginName(candidate.getCompanyId(), candidate.getLoginName()) > 0) {
            throw new ServiceException(ExceptionEnum.CANDIDATE_EXIST);
        }

        String code = this.cacheService.getSmsCode(String.format("%s_%s", candidate.getMobile(), SUFFIX_REGISTER));
        if (null == code) {
            throw new ServiceException(ExceptionEnum.CANDIDATE_SMS_CODE_NOT_FOUND);
        } else if (code.equals(smsCode)) {
            // 注册考生信息
            candidate.setLoginName(candidate.getMobile());
            candidate.setLoginPassword(bCryptPasswordEncoder.encode(candidate.getLoginPassword().trim()));
            candidate.setIdCardNum(candidate.getIdCardNum().toUpperCase());
            this.candidateRepository.insert(candidate);
        } else {
            throw new ServiceException(ExceptionEnum.CANDIDATE_SMS_CODE_NOT_VALID);
        }
        return this.getToken(candidate);
    }

    @Override
    public String smsLogin(ImageCode imageCode, String companyId, String loginName, String loginPassword) {
        ImageCode code = this.cacheService.getImageCode(imageCode.getKey());
        if (null == code) {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_FOUND);
        } else if (code.getText().equals(imageCode.getImageCode().toLowerCase())) {
            String smsKey = String.format("%s_%s", loginName, SUFFIX_LOGIN);
            String smsCode = this.cacheService.getSmsCode(smsKey);
            if (smsCode == null || !smsCode.equals(loginPassword)) {
                throw new ServiceException(BaseExceptionEnum.SMS_CODE_NOT_VALID);
            } else {
                Candidate candidate = this.candidateRepository.findByCompanyIdAndLoginName(new ObjectId(companyId), loginName)
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
                List<String> idList = new ArrayList<>();
                idList.add(companyId);
                this.cacheService.deleteImageCode(imageCode.getKey());
                return jwtService.generateToken(candidate.getAuthorities(), candidate, idList);
            }
        } else {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_VALID);
        }
    }

    @Override
    public String login(ImageCode imageCode, String companyId, String loginName, String loginPassword) {
        ImageCode code = this.cacheService.getImageCode(imageCode.getKey());
        if (null == code) {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_FOUND);
        } else if (code.getText().equals(imageCode.getImageCode().toLowerCase())) {
            Candidate candidate = this.candidateRepository.findByCompanyIdAndLoginName(new ObjectId(companyId), loginName)
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
            if(!bCryptPasswordEncoder.matches(loginPassword, candidate.getLoginPassword())){
                throw new ServiceException(ExceptionEnum.CANDIDATE_LOGIN_FAILED);
            }
            this.cacheService.deleteImageCode(imageCode.getKey());
            return this.getToken(candidate);
        } else {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_VALID);
        }
    }

    @Override
    public String login(String companyId, String loginName, Long expiredAt, String signature) {
        Candidate candidate = this.candidateRepository.findByCompanyIdAndLoginName(new ObjectId(companyId), loginName)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
        if (candidate.genLoginSign(expiredAt).equals(signature)) {
            if (new Date().getTime() < expiredAt * 1000) {
                return this.getToken(candidate);
            } else {
                throw new ServiceException(ExceptionEnum.CANDIDATE_SIGN_EXPIRED);
            }
        }
        throw new ServiceException(ExceptionEnum.CANDIDATE_SIGN_VERIFY_FAILED);
    }

    private String getToken(Candidate candidate) {
        List<String> idList = new ArrayList<>();
        idList.add(String.format("candidateId_%s", candidate.get_id().toString()));
        idList.add(String.format("companyId_%s", candidate.getCompanyId().toString()));
        return jwtService.generateToken(candidate.getAuthorities(), candidate, idList);
    }


    @Override
    public List<String> getExistsLoginNameByCompanyId(String companyId) {
        Query query = new Query(Criteria.where("companyId").is(new ObjectId(companyId)));
        return mongoTemplate.findDistinct(query, "loginName", Candidate.class, String.class);
    }


    @Override
    public void addOne(Candidate candidate) {
        this.insertCandidate(candidate);
    }

    @Override
    public void edit(String sn, String projectId, String candidateId, String fullName, String mobile, String email, String idCardNum, Integer idCardType, Integer gender) {
        Project project = this.projectRepository.findById(new ObjectId(projectId)).orElseThrow();

        Candidate candidate = this.candidateRepository.findById(new ObjectId(candidateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
        candidate.setFullName(fullName);

        if(StringUtil.validMobile(mobile) && !mobile.equals(candidate.getMobile())){
            if(this.candidateRepository.countByCompanyIdAndLoginName(candidate.getCompanyId(), mobile) > 0){
                throw new ServiceException(ExceptionEnum.CANDIDATE_MOBILE_EXISTS);
            }
            candidate.setLoginName(mobile);
            candidate.setMobile(mobile);

        }
        if(StringUtil.validEmail(email)){
            candidate.setEmail(email);
        }

        candidate.setIdCardType(idCardType);
        candidate.setIdCardNum(idCardNum);
        candidate.setGender(gender);

        this.candidateRepository.save(candidate);

        List<Registration> registrationList = this.registrationRepository.findByProjectIdAndCandidateId(new ObjectId(projectId), new ObjectId(candidateId));
        for(Registration registration : registrationList){
            registration.setFullName(candidate.getFullName());
            registration.setMobile(candidate.getMobile());
            registration.setEmail(candidate.getEmail());
            this.registrationRepository.save(registration);
            //重新生成考生的短链接
            if(registration.getShortCodeReady()){
                List<ShortUrlRequest> requestList = new ArrayList<>();
                ShortUrlRequest shortUrlRequest = new ShortUrlRequest();
                String longUrl = appointmentLink + sn + "/" + registration.getMobile() + "/" + project.getLoginExpireTime() + "/" + candidate.genLoginSign(project.getLoginExpireTime());
                shortUrlRequest.setUrl(longUrl);
                shortUrlRequest.setShortCode(registration.getShortCode());
                shortUrlRequest.setExamId(projectId);
                requestList.add(shortUrlRequest);
                this.stoneExamRemote.genShortUrlList(requestList);
            }

        }
    }

    @Override
    public long delete(String projectId, String subjectId, List<String> candidateIdList) {

        List<ObjectId> idList = candidateIdList
                .stream()
                .map(ObjectId::new)
                .toList();

        Query query = new Query()
                .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)))
                .addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)))
                .addCriteria(Criteria.where("candidateId").in(idList));

        DeleteResult result = this.mongoTemplate.remove(query, Registration.class);

        return result.getDeletedCount();
    }

    @Override
    public void addAllByExcel(List<Candidate> list, String projectId, String subjectId) {
        for (Candidate candidate : list) {
            Candidate ca = this.insertCandidate(candidate);
            handleReg(ca, projectId, subjectId);
        }
    }

    @Override
    public void addAllOnlyRegByExcel(List<Candidate> listOnlyReg, String projectId, String subjectId, String companyId) {
        for (Candidate candidate : listOnlyReg) {
            Candidate candidate1 = this.candidateRepository.findByCompanyIdAndLoginName(new ObjectId(companyId), candidate.getLoginName()).orElseThrow();
            candidate1.setCity(candidate.getCity());
            handleReg(candidate1, projectId, subjectId);
        }
    }

    @Override
    public Tuple2<List<Registration>, Integer> getRegistrationPage(String projectId, String subjectId, String fullName, String mobile, Integer emailState, Integer smsState, Integer confirmState, Pageable pageable) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt", "_id"))
                .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)));

        if (null != fullName) {
            query = query.addCriteria(Criteria.where("fullName").is(fullName));
        }
        if (null != mobile) {
            query = query.addCriteria(Criteria.where("mobile").is(mobile));
        }
        if (null != subjectId) {
            query = query.addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        }
        if (null != emailState) {
            query = query.addCriteria(Criteria.where("emailState").is(emailState));
        }
        if (null != smsState) {
            query = query.addCriteria(Criteria.where("smsState").is(smsState));
        }
        if (null != confirmState) {
            query = query.addCriteria(Criteria.where("confirmState").is(confirmState));
        }

        // 计算总数
        long count = this.mongoTemplate.count(query, Registration.class);
        // 分页信息
        query = query.with(pageable);
        List<Registration> list = this.mongoTemplate.find(query, Registration.class);

        return new Tuple2<>(list, (int) count);
    }

    @Override
    public Candidate getById(String candidateId) {
        return this.candidateRepository.findById(new ObjectId(candidateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
    }

    @Override
    public List<Candidate> getImportCandidate(String subjectId, Integer type) {
        List<Registration> registrationList;
        if (type.equals(ImportTypeEnum.CONFIRMED.getCode())) {
            registrationList = this.registrationRepository.findBySubjectIdAndConfirmState(new ObjectId(subjectId), CandidateConfirmStateEnum.AGREE.getCode());
        } else if (type.equals(ImportTypeEnum.CONFIRMED_AND_NOT_CONFIRMED.getCode())) {
            registrationList = this.registrationRepository.findBySubjectIdAndConfirmStateNot(new ObjectId(subjectId), CandidateConfirmStateEnum.REFUSE.getCode());
        } else if (type.equals(ImportTypeEnum.SELECTED.getCode())) {
            registrationList = this.registrationRepository.findBySubjectIdAndPassed(new ObjectId(subjectId), true);
        }  else if (type.equals(ImportTypeEnum.REFUSED.getCode())) {
            registrationList = this.registrationRepository.findBySubjectIdAndConfirmState(new ObjectId(subjectId), CandidateConfirmStateEnum.REFUSE.getCode());
        }  else if (type.equals(ImportTypeEnum.NOT_CONFORM.getCode())) {
            registrationList = this.registrationRepository.findBySubjectIdAndConfirmState(new ObjectId(subjectId), CandidateConfirmStateEnum.NOT_CONFIRMED.getCode());
        }else {
            registrationList = this.registrationRepository.findBySubjectId(new ObjectId(subjectId));
        }
        List<ObjectId> idList = registrationList
                .stream()
                .map(Registration::getCandidateId)
                .toList();
        if (!idList.isEmpty()) {
            List<Candidate> list = this.candidateRepository.findBy_idIn(idList);
            list.forEach(candidate -> {
                Registration registration = registrationList
                        .stream()
                        .filter(r -> r.getSubjectId().toString().equals(subjectId)
                                && r.getCandidateId().equals(candidate.get_id()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.REGISTRATION_NOT_FOUND));
                candidate.setCity(registration.getCity());
            });
            return list;
        }
        return List.of();
    }

    @Override
    public List<Score> getScoreList(String candidateId) {
        return this.scoreRepository.findByCandidateId(new ObjectId(candidateId));
    }

    @Override
    public void updateInfo(Candidate candidate) {
        this.candidateRepository.save(candidate);

        List<Registration> registrationList = this.registrationRepository.findByCandidateId(candidate.get_id());
        for(Registration registration : registrationList){
            registration.setFullName(candidate.getFullName());
            registration.setEmail(candidate.getEmail());
            this.registrationRepository.save(registration);
        }

        List<TemplateData> templateDataList = this.templateDataRepository.findByCandidateId(candidate.get_id());
        for(TemplateData templateData : templateDataList){
            templateData.setFullName(candidate.getFullName());
            templateData.setEmail(candidate.getEmail());
            templateData.setIdCardType(candidate.getIdCardType());
            templateData.setIdCardNum(candidate.getIdCardNum());
            this.templateDataRepository.save(templateData);
        }

        List<Order> orderList = this.orderRepository.findByCandidateId(candidate.get_id());
        for(Order order : orderList){
            order.setFullName(candidate.getFullName());
            this.orderRepository.save(order);
        }

    }

    @Override
    public Candidate getCandidateByLoginName(String projectId, String mobile) {
       return this.candidateRepository.findByCompanyIdAndLoginName(new ObjectId(projectId), mobile)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
    }

    @Override
    public Candidate getCandidateByFullName(String projectId, String fullName) {
        return this.candidateRepository.findByCompanyIdAndLoginName(new ObjectId(projectId), fullName)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
    }

    @Override
    public String smsCodeLogin(String companyId, String loginName, String mobile, String smsCode, String imageCodeKey) {
        String redisCode = this.cacheService.getSmsCode(String.format("%s_%s", mobile, SUFFIX_LOGIN));
        if(redisCode == null || !redisCode.equals(smsCode)){
            throw new ServiceException(ExceptionEnum.CANDIDATE_ID_CARD_ERR);
        }
        Candidate candidate = this.candidateRepository.findByCompanyIdAndLoginName(new ObjectId(companyId), loginName)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));

        this.cacheService.deleteImageCode(imageCodeKey);
        return this.getToken(candidate);
    }

    @Override
    public void passwordChange(String candidateId, String mobile, String password) {
        Candidate candidate = this.candidateRepository.findById(new ObjectId(candidateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
        if(!candidate.getLoginName().equals(mobile)){
            throw new ServiceException(ExceptionEnum.CANDIDATE_MOBILE_ERR);
        }
        candidate.setLoginPassword(bCryptPasswordEncoder.encode(password));
        this.candidateRepository.save(candidate);
    }

    @Override
    public Tuple2<List<Candidate>, Integer> page(String companyId, String fullName, String mobile, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt", "_id"))
                .addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));

        if (null != fullName) {
            query = query.addCriteria(Criteria.where("fullName").is(fullName));
        }
        if (null != mobile) {
            query = query.addCriteria(Criteria.where("mobile").is(mobile));
        }

        // 计算总数
        long count = this.mongoTemplate.count(query, Candidate.class);
        // 分页信息
        query = query.with(pageable);
        List<Candidate> list = this.mongoTemplate.find(query, Candidate.class);

        return new Tuple2<>(list, (int) count);
    }

    @Override
    public void addByGroup(Candidate candidate,String projectId, String subjectId){
        Candidate checkC = this.candidateRepository.findByMobile(candidate.getMobile());

        Registration registrationN = new Registration();
        registrationN.setProjectId(new ObjectId(projectId));
        registrationN.setSubjectId(new ObjectId(subjectId));

        if(checkC == null){
            candidate.setLoginPassword(bCryptPasswordEncoder.encode(candidate.getLoginPassword().trim()));
            candidate.setIdCardNum(candidate.getIdCardNum().toUpperCase());
            candidate.getTagList().add("group");
            this.candidateRepository.insert(candidate);

            registrationN.setCandidateId(candidate.get_id());
            registrationN.setCity(candidate.getCity());
            registrationN.setFullName(candidate.getFullName());
            registrationN.setMobile(candidate.getMobile());
            registrationN.setEmail(candidate.getEmail());
        }
        else {
            registrationN.setCandidateId(checkC.get_id());
            registrationN.setCity(checkC.getCity());
            registrationN.setFullName(checkC.getFullName());
            registrationN.setMobile(checkC.getMobile());
            registrationN.setEmail(checkC.getEmail());

            checkC.getTagList().add("ex-group");
            this.candidateRepository.save(checkC);
        }

        registrationN.setShortCode(StringUtil.genPassword(8));
        registrationN.setPassed(true);

        this.registrationRepository.insert(registrationN);
    }

    @Override
    public String clearGroupByMobile(String mobile) {
        Candidate candidate = this.candidateRepository.findByMobile(mobile);
        if(candidate != null){
            this.candidateRepository.deleteById(candidate.get_id());
            //this.registrationRepository.deleteByCandidateId(candidate.get_id());
            return candidate.get_id().toString();
        }
        return null;

    }

    @Override
    public Candidate findByMobile(String mobile) {
        return this.candidateRepository.findByMobile(mobile);
    }


    private Candidate insertCandidate(Candidate candidate) {
         candidate.setIdCardNum(candidate.getIdCardNum().toUpperCase());

        //如果是身份证号
        if(candidate.getIdCardType().equals(CredentialCategoryEnum.ID_CARD.getCode())){

            if (!StringUtil.validIdCard(candidate.getIdCardNum())) {
                throw new ServiceException(ExceptionEnum.CANDIDATE_ID_CARD_ERR);
            }

            //根据身份证号处理性别
            if (Integer.parseInt(candidate.getIdCardNum().substring(16, 17)) % 2 == 0) {
                candidate.setGender(2);
            } else {
                //男
                candidate.setGender(1);
            }
        }

        return this.candidateRepository.insert(candidate);
    }

    private void handleReg(Candidate candidate, String projectId, String subjectId) {
        Registration registration = new Registration();
        registration.setCandidateId(candidate.get_id());
        registration.setProjectId(new ObjectId(projectId));
        registration.setSubjectId(new ObjectId(subjectId));
        registration.setCity(candidate.getCity());
        registration.setFullName(candidate.getFullName());
        registration.setMobile(candidate.getMobile());
        registration.setEmail(candidate.getEmail());
        registration.setShortCode(StringUtil.genPassword(8));
        this.registrationRepository.insert(registration);
    }

    public void sendSms(ImageCode imageCode, String mobile, String suffix, HttpServletRequest request) {
        String ip = RequestUtil.getClientIp(request).split(",")[0];
        Tuple2<Integer, Integer> count = this.cacheService.getSmsCount(ip, mobile);
        //一个ip一天30次上限  ，一个手机号一天10次上限
        if (count.first() >= 30 || count.second() >= 10) {
            throw new ServiceException(ExceptionEnum.SMS_TIMES_BEYOND_LIMIT);
        }
        ImageCode code = this.cacheService.getImageCode(imageCode.getKey());
        if (null == code) {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_FOUND);
        } else if (code.getText().equals(imageCode.getImageCode().toLowerCase())) {
            //int smsCode = (int)((Math.random()*9+1)*100000);
            int smsCode = new Random().nextInt(8888) + 1000;
            List<String> argList = new ArrayList<>();
            argList.add(String.valueOf(smsCode));
            argList.add("15");
            SmsResponse res = CloopenRemote.sendSns(cloopenRemote,
                    smsTemplateLogin,
                    mobile,
                    argList,
                    cloopenSmsAppId,
                    cloopenAccountSid,
                    cloopenAuthToken);
            log.info(res.toString());
            this.cacheService.increaseSmsCount(ip, mobile);
            this.cacheService.setSmsCode(String.format("%s_%s", mobile, suffix), String.valueOf(smsCode));
        } else {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_VALID);
        }
        this.cacheService.deleteImageCode(imageCode.getKey());
    }
}
