package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.service.ImageCodeService;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.registration.document.Message;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface MessageService {

    void add(Message message);
    Tuple2<List<Message>, Integer> page(String candidateId, Pageable pageable);

    Tuple2<List<Message>, Integer> adminPage(String companyId, Boolean isNotReplied, Pageable pageable);

    List<Message> candidateMessage(String candidateId);

    void reply(Message message);
}
