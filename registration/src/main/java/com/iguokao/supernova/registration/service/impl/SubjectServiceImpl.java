package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ReportRemote;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.response.ScoreImportResponse;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.CandidateConfirmStateEnum;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.repository.*;
import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.SubjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class SubjectServiceImpl implements SubjectService {

    private final SubjectRepository subjectRepository;
    private final ProjectRepository projectRepository;
    private final RegistrationRepository registrationRepository;
    private final ScoreRepository scoreRepository;
    private final CandidateRepository candidateRepository;

    private final MongoTemplate mongoTemplate;
    private final CacheService cacheService;
    private final ReportRemote reportRemote;

    @Override
    public String add(Subject subject) {

        if (this.subjectRepository.countByProjectIdAndName(subject.getProjectId(), subject.getName()) > 0) {
            throw new ServiceException(ExceptionEnum.SUBJECT_NAME_ERR);
        }
        Subject subject1 = this.subjectRepository.insert(subject);
        return subject1.get_id().toString();
    }

    @Override
    public Subject getById(String subjectId) {
        return this.subjectRepository.findById(new ObjectId(subjectId)).orElseThrow(() -> new ServiceException(ExceptionEnum.SUBJECT_NOT_FOUND));
    }

    @Override
    public void update(Subject subject) {
        this.subjectRepository.save(subject);
        this.cacheService.deleteSubject(subject.get_id().toString());
    }

    @Override
    public List<Subject> getListByProjectId(String projectId) {
        return this.subjectRepository.findByProjectId(new ObjectId(projectId));
    }

    @Override
    public Tuple2<Map<Subject, CandidateCount>, List<Project>> getImportList(String companyId) {
        Date start = new Date(new Date().getTime() - 86400000 * 90L);
        List<Project> projectList = this.projectRepository.importProject(new ObjectId(companyId), start);
        List<ObjectId> idList = projectList
                .stream()
                .map(Project::get_id)
                .toList();
        List<Subject> subjectList = this.subjectRepository.findByProjectIdIn(idList);
        subjectList = subjectList
                .stream()
                .sorted(Comparator.comparing(Subject::get_id).reversed())
                .toList();
        Map<Subject, CandidateCount> map = new HashMap<>();
        subjectList.forEach(s -> {
            CandidateCount count = new CandidateCount();
            count.setCandidateCount(this.registrationRepository.countBySubjectId(s.get_id()));
            count.setConfirmCount(this.registrationRepository.countBySubjectIdAndConfirmState(s.get_id(), CandidateConfirmStateEnum.AGREE.getCode()));
            count.setConfirmAndNotConfirmCount(this.registrationRepository.countBySubjectIdAndConfirmStateNot(s.get_id(),CandidateConfirmStateEnum.REFUSE.getCode()));
            count.setSelectedCount(this.registrationRepository.countBySubjectIdAndPassed(s.get_id(),true));
            map.put(s, count);
        });
        return new Tuple2<>(map, projectList);
    }

    @Override
    public void scoreImport(String subjectId, String examSubjectId) {
        RestResponse<List<ScoreImportResponse>> res = this.reportRemote.scoreExport(examSubjectId);
        Subject subject = this.cacheService.getSubject(subjectId);
        Project project = this.cacheService.getProject(subject.getProjectId().toString());
        int count = 0;
        if(res.getCode() == 0){
            for(ScoreImportResponse item : res.getData()){
                Candidate candidate = this.candidateRepository.findByCompanyIdAndIdCardNum(project.getCompanyId(), item.getIdCardNum())
                        .orElse(null);
                if(candidate == null){
                    continue;
                }
                Score score = this.scoreRepository.findByProjectIdAndIdCardNum(subject.getProjectId(), item.getIdCardNum())
                        .orElse(null);
                if(score == null){
                    score = new Score();
                    score.setProjectId(subject.getProjectId());
                    score.setIdCardNum(item.getIdCardNum());
                    score.setCompanyId(project.getCompanyId());
                    score.setCandidateId(candidate.get_id());

                    ScoreItem i = new ScoreItem();
                    i.setSubjectId(subject.get_id());
                    i.setScore(item.getScore());
                    i.setAbsent(item.getAbsent());
                    score.getScoreList().add(i);
                    count++;
                    this.scoreRepository.save(score);
                } else {
                    ScoreItem scoreItem = score.getScoreList()
                            .stream()
                            .filter(si -> si.getSubjectId().equals(subject.get_id()))
                            .findFirst()
                            .orElse(null);
                    if(scoreItem == null){
                        ScoreItem i = new ScoreItem();
                        i.setSubjectId(subject.get_id());
                        i.setScore(item.getScore());
                        score.getScoreList().add(i);
                        count++;
                        this.scoreRepository.save(score);
                    }
                }
            }
        }
        Query query = new Query()
                .addCriteria(Criteria.where("_id").is(new ObjectId(subjectId)));
        Update update = new Update();
        int beforeCount = subject.getScoreImportedCount() == null ? 0 : subject.getScoreImportedCount();
        update.set("scoreImportedCount", beforeCount + count);
        mongoTemplate.updateFirst(query, update, Subject.class);
    }

}
