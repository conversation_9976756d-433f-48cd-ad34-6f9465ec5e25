package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.registration.document.CandidateCount;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import com.iguokao.supernova.registration.document.TemplateData;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class InvoiceResponse {
    private String projectId;
    private String name;
    private Boolean invoice;
    private Integer invoiceType;
    private String invoiceTitle;
    private String invoiceTin;
    private String invoicePrice;
    private String invoiceLink;

    public static List<InvoiceResponse> of(List<TemplateData> list) {
        if (list == null) {
            return new ArrayList<>();
        }
        List<InvoiceResponse> res = new ArrayList<>();
        for (TemplateData obj : list) {
            InvoiceResponse invoiceResponse = new InvoiceResponse();
            invoiceResponse.setProjectId(obj.getProjectId().toString());
            invoiceResponse.setName(obj.getRemark1());
            BeanUtils.copyProperties(obj, invoiceResponse);
            res.add(invoiceResponse);
        }
        return res;
    }
}
