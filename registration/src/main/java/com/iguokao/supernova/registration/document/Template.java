package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class Template extends BaseDocument {
    @Indexed(name = "companyId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    @Indexed(name = "projectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId parentId;

    private String name;
    private Boolean uploadAvatar = true;
    private Boolean location = true;

    private List<TemplateGroup> groupList = new ArrayList<>();
}
