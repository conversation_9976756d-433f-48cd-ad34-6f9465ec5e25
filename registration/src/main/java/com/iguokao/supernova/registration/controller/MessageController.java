package com.iguokao.supernova.registration.controller;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.registration.document.Message;
import com.iguokao.supernova.registration.request.AdminMessagePageRequest;
import com.iguokao.supernova.registration.request.MessageSendRequest;
import com.iguokao.supernova.registration.response.MessageResponse;
import com.iguokao.supernova.registration.service.MessageService;
import com.iguokao.supernova.registration.service.MqttService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/message")
@RequiredArgsConstructor
public class MessageController {
    private final MessageService messageService;
    private final JwtService jwtService;
    private final MqttService mqttService;

    @PostMapping("/list")
    @Operation(summary = "消息列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<PageResponse<MessageResponse>> List(@Validated @RequestBody AdminMessagePageRequest request) {
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
        Tuple2<List<Message>, Integer> page = this.messageService.adminPage(request.getCompanyId(), request.getIsNotReplied(),pageable);

        List<MessageResponse> messageResponses = new ArrayList<>();
        for(Message message : page.first()){
            messageResponses.add(MessageResponse.of(message));
        }
        List<String> onlineList = mqttService.getOnlineCandidateId();
        messageResponses = MessageResponse.of(messageResponses, onlineList);
        return RestResponse.success(new PageResponse<>(messageResponses, page.second(), pageable));

    }

    @GetMapping("/context/{candidateId}")
    @Operation(summary = "消息列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<MessageResponse>> context(@PathVariable String candidateId) {
        List<Message> messageList = messageService.candidateMessage(candidateId);
        return RestResponse.success(MessageResponse.of(messageList));
    }

    @PostMapping("/reply")
    @Operation(summary = "回复消息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> reply(@RequestBody MessageSendRequest request) {
        String operatorId = this.jwtService.currentOperatorId();
        Message message = new Message();
        message.setType(request.getType());
        message.setContent(request.getContent());
        message.setCandidateId(new ObjectId(request.getCandidateId()));
        message.setOperatorId(new ObjectId(operatorId));
        message.setReplied(true);
        this.messageService.reply(message);
        return RestResponse.success();
    }
}
