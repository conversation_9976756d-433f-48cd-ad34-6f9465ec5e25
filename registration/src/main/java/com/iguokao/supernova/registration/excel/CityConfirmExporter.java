package com.iguokao.supernova.registration.excel;

import java.util.ArrayList;
import java.util.List;

public class CityConfirmExporter {

    public static List<List<String>> head() {
        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("城市");

        List<String> head1 = new ArrayList<>();
        head1.add("总人数");

        List<String> head2 = new ArrayList<>();
        head2.add("已确认人数");

        List<String> head3 = new ArrayList<>();
        head3.add("拒绝参加人数");

        List<String> head4 = new ArrayList<>();
        head4.add("未确认人数");

        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);

        return list;
    }

    public static List<List<Object>> data(List<CityConfirmItem> list) {
        List<List<Object>> res = new ArrayList<>();
        for(CityConfirmItem item : list){
            List<Object> line = new ArrayList<>();
            line.add(item.getCity());
            line.add("    " + item.getTotal() + "   ");
            line.add("   " + item.getConfirm() + "   ");
            line.add("  " + item.getRefuse() + "  ");
            line.add("   " + item.getUnKnow() + "   " );

            res.add(line);
        }
        return res;
    }
}
