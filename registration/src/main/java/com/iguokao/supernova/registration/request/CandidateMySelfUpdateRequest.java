package com.iguokao.supernova.registration.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;


@Getter
@Setter
public class CandidateMySelfUpdateRequest {

    @Schema(description = "姓名")
    @Length(min = 1, max = 100, message = "用户名长度超限")
    private String fullName;

    @Schema(description = "邮箱")
    @Length(min = 5, max = 100, message = "邮箱长度超限")
    private String email;

    @Schema(description = "身份证件类型")
    @Min(value = 1, message = "身份证件类型错误")
    @Max(value = 10, message = "身份证件类型错误")
    private Integer idCardType;

    @Schema(description = "身份证")
    @Length(min = 10, max = 30, message = "证件号码长度超限")
    private String idCardNum;

}
