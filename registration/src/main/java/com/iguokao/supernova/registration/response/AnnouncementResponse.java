package com.iguokao.supernova.registration.response;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.registration.document.Announcement;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class AnnouncementResponse {
    private String companyId;
    private String categoryId;
    private String announcementId;

    private String title;
    private String content;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date createdAt;

    private String link;

    public static AnnouncementResponse of(Announcement obj){
        if(obj==null){
            return null;
        }
        AnnouncementResponse res = new AnnouncementResponse();
        BeanUtils.copyProperties(obj, res);
        res.setCompanyId(obj.getCompanyId().toString());
        res.setCategoryId(obj.getCategoryId().toString());
        res.setAnnouncementId(obj.get_id().toString());
        return res;
    }

    public static List<AnnouncementResponse> of(List<Announcement> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<AnnouncementResponse> res = new ArrayList<>();
        for(Announcement obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
