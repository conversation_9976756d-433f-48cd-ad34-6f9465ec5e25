package com.iguokao.supernova.registration.controller;

import com.iguokao.supernova.common.converter.ValidObjectId;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.registration.document.CandidateCount;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import com.iguokao.supernova.registration.request.ScoreImportRequest;
import com.iguokao.supernova.registration.request.SubjectAddRequest;
import com.iguokao.supernova.registration.request.SubjectUpdateRequest;
import com.iguokao.supernova.registration.response.ImportSubjectResponse;
import com.iguokao.supernova.registration.response.ProjectResponse;
import com.iguokao.supernova.registration.response.SubjectResponse;
import com.iguokao.supernova.registration.service.RegistrationService;
import com.iguokao.supernova.registration.service.SubjectService;
import io.swagger.v3.oas.annotations.Operation;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/subject")
@RequiredArgsConstructor
public class SubjectController {

    private final SubjectService subjectService;
    private final RegistrationService registrationService;

    @PostMapping("/add")
    @Operation(summary = "创建科目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody SubjectAddRequest param) {
        Subject subject = new Subject();
        BeanUtils.copyProperties(param, subject);
        subject.setProjectId(new ObjectId(param.getProjectId()));
        String subjectIdStr = this.subjectService.add(subject);
        return RestResponse.success(subjectIdStr);
    }

    @PostMapping("/update")
    @Operation(summary = "修改项目")
    public RestResponse<String> update(@RequestBody SubjectUpdateRequest param){
        Subject subject = this.subjectService.getById(param.getSubjectId());
        BeanUtils.copyProperties(param, subject);
        this.subjectService.update(subject);
        return RestResponse.success();
    }

    @GetMapping("/info/{subjectId}")
    @Operation(summary = "科目详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ProjectResponse.class)))
    public RestResponse<SubjectResponse> info(@ValidObjectId @PathVariable String subjectId) {
        Subject subject = this.subjectService.getById(subjectId);
        return RestResponse.success(SubjectResponse.of(subject));
    }

    @GetMapping("/list/{projectId}")
    @Operation(summary = "项目下科目列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ProjectResponse.class)))
    public RestResponse<List<SubjectResponse>> list(@ValidObjectId @PathVariable String projectId) {
        List<Subject> subjectList = this.subjectService.getListByProjectId(projectId);
        List<SubjectResponse> subjectResponseList = new ArrayList<>();
        for(Subject subject : subjectList){
            SubjectResponse subjectResponse = SubjectResponse.of(subject);
            subjectResponse.setCandidateCount(this.registrationService.countAllByProjectIdAndSubjectId(projectId,subject.get_id().toString()));
            subjectResponseList.add(subjectResponse);
        }
        return RestResponse.success(subjectResponseList);
    }

    @GetMapping("/import/list/{companyId}")
    @Operation(summary = "可以导入的项目")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ImportSubjectResponse.class))))
    public RestResponse<List<ImportSubjectResponse>> importList(@ValidObjectId @PathVariable String companyId) {
        Tuple2<Map<Subject, CandidateCount>, List<Project>> data = this.subjectService.getImportList(companyId);
        List<ImportSubjectResponse> list = new ArrayList<>();
        data.first().keySet().forEach(subject -> {
            Project project = data.second()
                    .stream()
                    .filter(p -> p.get_id().equals(subject.getProjectId()))
                    .findFirst()
                    .orElse(null);
            list.add(ImportSubjectResponse.of(project, subject, data.first().get(subject)));
        });
        return RestResponse.success(list);
    }

    @PostMapping("/score/import")
    @Operation(summary = "成绩导入")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> scoreImport(@RequestBody ScoreImportRequest request) {
        this.subjectService.scoreImport(request.getSubjectId(), request.getExamSubjectId());
        return RestResponse.success();
    }
}
