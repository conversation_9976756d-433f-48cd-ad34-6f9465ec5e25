package com.iguokao.supernova.registration.excel;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.Registration;
import com.iguokao.supernova.registration.document.TemplateData;
import com.iguokao.supernova.registration.document.TemplateItemData;
import com.iguokao.supernova.registration.service.CandidateService;
import com.iguokao.supernova.registration.service.RegistrationService;
import com.iguokao.supernova.registration.service.TemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class CandidateTemplateClearItemListener extends AnalysisEventListener<CandidateTemplateClearItem> {


    private final CandidateService candidateService;
    private final TemplateService templateService;
    private final RegistrationService registrationService;

    List<Date> m = new ArrayList<>();
    List<String> k = new ArrayList<>();
    int count = 0;
    @Override
    public void invoke(CandidateTemplateClearItem item, AnalysisContext analysisContext) {

        Candidate candidate = candidateService.findByMobile(item.getMobile());
        if(candidate != null){
            m.add(candidate.getCreatedAt());
            k.add(candidate.getMobile());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        List<Date> a = m;
        List<String> b = k;
    }

}