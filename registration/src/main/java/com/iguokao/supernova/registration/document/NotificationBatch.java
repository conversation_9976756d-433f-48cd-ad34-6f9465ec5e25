package com.iguokao.supernova.registration.document;


import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;


@Getter
@Setter
@Document("notification_batch")
public class NotificationBatch extends BaseDocument{
    @Indexed(name = "projectId_index")
    private ObjectId projectId;
    private Integer type;  //1 为邮件批次  2为短信批次
    private Integer total;
    private Integer success = 0;
    private Integer completed = 0;
    private List<NotificationFailDetail> failList = new ArrayList<>();
}
