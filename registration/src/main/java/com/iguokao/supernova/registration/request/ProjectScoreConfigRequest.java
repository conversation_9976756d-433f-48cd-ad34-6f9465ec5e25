package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ProjectScoreConfigRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "开启/关闭 查分模块")
    private Boolean score;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "开始时间")
    private Date scoreStartAt;

    private String examProjectId;
}
