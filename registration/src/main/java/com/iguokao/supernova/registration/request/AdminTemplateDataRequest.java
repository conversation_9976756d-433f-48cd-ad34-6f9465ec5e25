package com.iguokao.supernova.registration.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;


@Getter
@Setter
public class AdminTemplateDataRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生id")
    @Length(min = 24, max = 24, message = "candidateId 错误")
    private String candidateId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "报名项目id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "模板id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String templateId;

}
