package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.registration.document.Announcement;
import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.Registration;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class CandidateResponse {
    private String companyId;
    private String candidateId;
    private String subjectId;
    private String registrationId;

    private String loginName;
    private String fullName;
    private Integer gender;
    private String avatar;
    private String city;
    private Integer idCardType;
    private String idCardNum;
    private String mobile;
    private String email;

    private Integer smsState;
    private Integer emailState;
    private Integer confirmState;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date createdAt;
    public static CandidateResponse of(Candidate obj, Registration registration){
        if(obj==null){
            return null;
        }
        CandidateResponse res = new CandidateResponse();
        BeanUtils.copyProperties(obj, res);
        res.setCompanyId(obj.getCompanyId().toString());
        res.setCandidateId(obj.get_id().toString());
        if(null != registration){
            res.setSubjectId(registration.getSubjectId().toString());
            res.setConfirmState(registration.getConfirmState());
            res.setRegistrationId(registration.get_id().toString());
            res.setEmailState(registration.getEmailState());
            res.setSmsState(registration.getSmsState());
            res.setCity(registration.getCity());
        }

        return res;
    }

    public static List<CandidateResponse> of(List<Candidate> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<CandidateResponse> res = new ArrayList<>();
        for(Candidate obj : list){
            res.add(of(obj,null));
        }
        return res;
    }
}
