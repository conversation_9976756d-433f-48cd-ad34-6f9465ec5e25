package com.iguokao.supernova.registration.response;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Map;

@Getter
@Setter
public class TemplateDataStatisticResponse {
    private String projectId;
    private Map<String, Long> cityCount;
    private Integer templateDataCount;
    private Integer templateDataSubjectCount;

    private Integer approveCount;
    private Integer paidCount;
    private Integer templateFinishedCount;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date createdAt;

    private Map<String, Long> templateDataDayCount;
    private Map<String, Long> payDayCount;

}
