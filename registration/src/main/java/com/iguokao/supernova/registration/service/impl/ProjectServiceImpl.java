package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ShortUrlRequest;
import com.iguokao.supernova.common.remote.StoneExamRemote;
import com.iguokao.supernova.common.remote.TaskRemote;
import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Registration;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.repository.ProjectRepository;

import com.iguokao.supernova.registration.repository.RegistrationRepository;
import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.ProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectServiceImpl implements ProjectService {

    private final ProjectRepository projectRepository;
    private final RegistrationRepository registrationRepository;
    private final MongoTemplate mongoTemplate;
    private final StoneExamRemote stoneExamRemote;
    private final CacheService cacheService;

    @Value(value = "${app.appointment-link}")
    private String appointmentLink;

    @Override
    public String add(Project project) {

        if(this.projectRepository.countByNameAndCompanyId(project.getName(), project.getCompanyId()) > 0){
            throw new ServiceException(ExceptionEnum.PROJECT_NAME_EXIST);
        }

        project = this.projectRepository.insert(project);
        return project.get_id().toString();
    }

    @Override
    public Project getById(String projectId) {
        return this.projectRepository.findById(new ObjectId(projectId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
    }

    @Override
    public void update(Project project) {
        if(this.projectRepository.countByNameAndCompanyId(project.getName(), project.getCompanyId()) > 1){
            throw new ServiceException(ExceptionEnum.PROJECT_NAME_EXIST);
        }
        this.projectRepository.save(project);
        this.cacheService.deleteProject(project.get_id().toString());
    }

    @Override
    public boolean online(String projectId) {
        Project project = this.projectRepository.findById(new ObjectId(projectId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));

        Query query = new Query()
                .addCriteria(Criteria.where("_id").is(new ObjectId(projectId)));
        Update candidateUpdate = new Update();
        candidateUpdate.set("online", !project.getOnline());
        mongoTemplate.updateFirst(query, candidateUpdate, Project.class);
        this.cacheService.deleteProject(projectId);

        this.cacheService.deleteProjectList(project.getCompanyId().toString());
        return !project.getOnline();
    }

    @Override
    public Tuple2<List<Project>, Integer> getPage(String companyId, String name, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "_id", "createdAt"));
        if(null != companyId){
            query = query.addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));
        }
        if(null != name){
            query = query.addCriteria(Criteria.where("name").is(name));
        }
        // 计算总数
        long count = this.mongoTemplate.count(query, Project.class);
        // 分页信息
        query = query.with(pageable);
        List<Project> list = this.mongoTemplate.find(query, Project.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public int countShortUrl(String projectId) {
        return this.registrationRepository.countByProjectIdAndShortCodeReady(new ObjectId(projectId), true);
    }

    @Override
    public void genShortUrl(String projectId, String sn) {
        long et = 0;
        Project project = this.projectRepository.findById(new ObjectId(projectId)).orElseThrow();
        if(project.getLoginExpireTime() == null || project.getLoginExpireTime() == 0){
            et = new Date().getTime() + 86400*7*1000;
            project.setLoginExpireTime(et);
            this.projectRepository.save(project);

        }
        else {
            et = project.getLoginExpireTime();
        }

        List<Registration> registrationList = this.registrationRepository.findByProjectIdAndShortCodeReady(new ObjectId(projectId), false);
        List<ShortUrlRequest> requestList = new ArrayList<>();
        List<ObjectId> idList = new ArrayList<>();

        for(int i =0; i<registrationList.size(); i++){
            Registration registration = registrationList.get(i);

            Candidate candidate = new Candidate();
            candidate.setCompanyId(project.getCompanyId());
            candidate.set_id(registration.getCandidateId());
            candidate.setLoginName(registration.getMobile());

            ShortUrlRequest shortUrlRequest = new ShortUrlRequest();
            String longUrl = appointmentLink + sn + "/" + registration.getMobile() + "/" + et + "/" + candidate.genLoginSign(et);
            shortUrlRequest.setUrl(longUrl);
            shortUrlRequest.setShortCode(registration.getShortCode());
            shortUrlRequest.setExamId(projectId);

            requestList.add(shortUrlRequest);
            idList.add(registration.get_id());

            if(registrationList.size() == 500 || i == (registrationList.size() - 1) ){
                this.stoneExamRemote.genShortUrlList(requestList);

                Query query = new Query()
                        .addCriteria(Criteria.where("_id").in(idList));
                Update update = new Update();
                update.set("shortCodeReady", true);
                mongoTemplate.updateMulti(query, update, Registration.class);

                requestList.clear();
                idList.clear();
            }
        }
    }


}
