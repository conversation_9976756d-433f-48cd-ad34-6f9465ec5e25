package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Getter
@Setter
@Document
public class Subject extends BaseDocument {
    @Indexed(name = "projectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    private String name;
    private String info;
    private Double price;

    private Date startAt;
    private Date endAt;

    private Integer passScore;
    private Integer scoreImportedCount = 0;
}
