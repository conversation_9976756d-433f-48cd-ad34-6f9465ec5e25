package com.iguokao.supernova.registration.document;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;


@Getter
@Setter
public class HfResult{
    private String state;
    private String payload;
    private String resId;
    private String result;
    private String hfSeqId;
}
