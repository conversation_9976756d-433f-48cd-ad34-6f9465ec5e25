package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum ProjectStateEnum implements BaseEnum {
    OFFLINE(0, "离线"),
    ONLINE(1, "在线"),
    ;

    ProjectStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
