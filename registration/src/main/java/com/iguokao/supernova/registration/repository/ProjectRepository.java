package com.iguokao.supernova.registration.repository;

import com.iguokao.supernova.registration.document.Project;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.Date;
import java.util.List;

public interface ProjectRepository extends MongoRepository<Project, ObjectId> {
    int countByName(String name);

    int countByNameAndCompanyId(String name, ObjectId companyId);

    List<Project> findBy_idIn(List<ObjectId> list);


    @Query("{'companyId':?0, 'online': true, 'signUp' : true, 'startAt' : {$gte: ?1} }")
    List<Project> onlineSignUpProject(ObjectId companyId, Date now, Sort sort);

    @Query("{'companyId':?0, 'online': true, 'appoint' : true, 'startAt' : {$gte: ?1} }")
    List<Project> onlineAppointProject(ObjectId companyId, Date now, Sort sort);

    @Query("{'companyId':?0, 'online': true, 'startAt' : {$gte: ?1} }")
    List<Project> importProject(ObjectId companyId, Date now);
}
