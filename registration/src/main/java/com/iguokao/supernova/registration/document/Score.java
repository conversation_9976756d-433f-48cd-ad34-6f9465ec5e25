package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
@CompoundIndexes({
        @CompoundIndex(def = "{'projectId': -1, 'idCardNum': -1}", name = "projectId_idCardNum_index")
})
public class Score extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    @Indexed(name = "candidateId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId candidateId;

    private String idCardNum;

    private List<ScoreItem> scoreList = new ArrayList<>();
}
