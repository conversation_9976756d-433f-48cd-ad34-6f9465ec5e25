package com.iguokao.supernova.registration.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class SubjectAppointRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "projectId")
    @Length(min = 24, max = 24, message = "projectId长度超限")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "确认状态")
    @Min(value = 10, message = "确认状态错误")
    @Max(value = 50, message = "确认状态错误")
    private Integer confirmState;

    private String siteId;

    private String city;
}
