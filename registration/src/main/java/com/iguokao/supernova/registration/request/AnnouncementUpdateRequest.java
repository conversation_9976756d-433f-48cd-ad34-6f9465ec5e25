package com.iguokao.supernova.registration.request;

import com.iguokao.supernova.registration.document.AnnouncementCategory;
import com.iguokao.supernova.registration.response.AnnouncementCategoryResponse;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AnnouncementUpdateRequest {
    private String announcementId;
    private String title;
    private String content;
    private String link;
}
