package com.iguokao.supernova.registration.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class PrePayRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "订单号")
    @Length(min = 5, max = 100, message = "订单号长度超限")
    private String tradeNo;

    private String wxCode;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司")
    @Length(min = 1, max = 50, message = "公司数据异常")
    private String companySn;

    private String token;
}
