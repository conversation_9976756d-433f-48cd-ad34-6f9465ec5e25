package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum RefundEnum implements BaseEnum {

    NO(0, "无需退费"),
    APPLIED(1, "已申请"),
    CONFIRMED(2, "已确认"),
    FINISHED(3, "已退费"),
    ;

    RefundEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
