package com.iguokao.supernova.registration.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;


@Getter
@Setter
public class CandidateEditRequest {

    @Schema(description = "公司简称")
    private String sn;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生id")
    @Length(min = 24, max = 24, message = "24位id")
    private String candidateId;

    @Schema(description = "姓名")
    private String fullName;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "身份证")
    private String idCardNum;

    @Schema(description = "身份证类型")
    private Integer idCardType;

    @Schema(description = "性别")
    private Integer gender;

}
