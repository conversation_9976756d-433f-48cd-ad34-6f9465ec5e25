package com.iguokao.supernova.registration.excel;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.Registration;
import com.iguokao.supernova.registration.document.TemplateData;
import com.iguokao.supernova.registration.document.TemplateItemData;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.service.CandidateService;
import com.iguokao.supernova.registration.service.TemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class CandidateTemplateItemListener extends AnalysisEventListener<CandidateTemplateItem> {

    private final String companyId;
    private final String projectId;

    private final List<ExcelErrResponse> errList;
    private final CandidateService candidateService;
    private final TemplateService templateService;

    private final List<Candidate> list = new ArrayList<>();

    @Override
    public void invoke(CandidateTemplateItem item, AnalysisContext analysisContext) {
        Integer currentRow = analysisContext.readRowHolder().getRowIndex();

        //可能是第一行 表头
        if(null != item.getFullName() && (item.getFullName().contains("姓名") || item.getFullName().contains("红色为必填项"))){
            return;
        }

        try {
            // 重要的处理部分
            //step1
            Candidate candidate  = new Candidate();
            candidate.setCompanyId(new ObjectId(companyId));
            candidate.set_id(new ObjectId());
            candidate.setLoginName(item.getMobile());
            candidate.setIdCardNum(item.getIdCardNum().toUpperCase());
            candidate.setIdCardType(1);
            candidate.setLoginPassword(candidate.getIdCardNum().substring(candidate.getIdCardNum().length() - 8));

            candidate.setFullName(item.getFullName());
            if(item.getGenderStr() != null && item.getGenderStr().equals("男")){
                candidate.setGender(1);
            } else if(item.getGenderStr() != null && item.getGenderStr().equals("女")){
                candidate.setGender(2);
            }
            candidate.setMobile(item.getMobile());
            candidate.setEmail(item.getEmail());
            candidate.setCity(item.getCity());

            String one = "67eb7a112ee28d38f6ef7be3";
            String two = "67eb7a482ee28d38f6ef7be4";
            String three = "67eb7aad2ee28d38f6ef7bec";

            String candidateSubjectId = null;
            if(item.getSubjectName().equals("高级企业合规师考试")){
                candidateSubjectId = three;
            }
            if(item.getSubjectName().equals("中级企业合规师考试")){
                candidateSubjectId = two;
            }
            if(item.getSubjectName().equals("初级企业合规师考试")){
                candidateSubjectId = one;
            }

            candidateService.addByGroup(candidate,projectId,candidateSubjectId);

            //step2
            TemplateData templateData = new TemplateData();
            templateData.setCandidateId(candidate.get_id());
            templateData.setProjectId(new ObjectId(projectId));
            templateData.setTemplateId(new ObjectId("67eb78a92ee28d38f6ef7bda"));
            templateData.setImported(true);
            templateData.setSuccessTradeNo("线下");

            templateData.setPassed(true);
            templateData.setFinished(true);
            templateData.setGender(candidate.getGender());
            templateData.setFullName(candidate.getFullName());
            templateData.setMobile(candidate.getMobile());

            templateData.setIdCardNum(candidate.getIdCardNum());
            templateData.setIdCardType(1);
            templateData.setEmail(candidate.getEmail());
            templateData.setAvatar("registration/avatar/67eb794d2ee28d38f6ef7be0/" + candidate.getIdCardNum() + ".jpg");

            templateData.setCity(candidate.getCity());

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            templateData.setBirthday(sdf.parse(item.getBirth()));


            if(item.getSubjectName().equals("高级企业合规师考试")){
                templateData.getSubjectIdList().add(new ObjectId(three));
            }
            if(item.getSubjectName().equals("中级企业合规师考试")){
                templateData.getSubjectIdList().add(new ObjectId(two));
            }
            if(item.getSubjectName().equals("初级企业合规师考试")){
                templateData.getSubjectIdList().add(new ObjectId(one));
            }

            TemplateItemData data1 = new TemplateItemData();
            data1.setItemId(new ObjectId("67e0c6a69b89a614876749d1"));
            data1.setValue(item.getEthnicity());

            TemplateItemData data2 = new TemplateItemData();
            data2.setItemId(new ObjectId("67e0c6b99b89a614876749d2"));
            data2.setValue(item.getUniversity());


            TemplateItemData data3 = new TemplateItemData();
            data3.setItemId(new ObjectId("67e0c6df9b89a614876749da"));
            data3.setValue(item.getCategory());

            TemplateItemData data4 = new TemplateItemData();
            data4.setItemId(new ObjectId("67e0c6ff9b89a614876749db"));
            data4.setValue(item.getMajor());

            TemplateItemData data5 = new TemplateItemData();
            data5.setItemId(new ObjectId("67e0c7269b89a614876749dc"));
            data5.setValue(item.getEducation());

            TemplateItemData data6 = new TemplateItemData();
            data6.setItemId(new ObjectId("67e0c73c9b89a614876749e0"));
            data6.setValue(item.getDegree());

            TemplateItemData data7 = new TemplateItemData();
            data7.setItemId(new ObjectId("67e0c74b9b89a614876749e1"));
            data7.setValue(item.getCompanyName());

            TemplateItemData data8 = new TemplateItemData();
            data8.setItemId(new ObjectId("67aee8c0da40906a71978830"));
            data8.setValue(item.getPostalArea());

            TemplateItemData data9 = new TemplateItemData();
            data9.setItemId(new ObjectId("67aee8ceda40906a71978831"));
            data9.setValue(item.getPostalAddress());

            templateData.getTemplateItemList().add(data1);
            templateData.getTemplateItemList().add(data2);
            templateData.getTemplateItemList().add(data3);
            templateData.getTemplateItemList().add(data4);
            templateData.getTemplateItemList().add(data5);
            templateData.getTemplateItemList().add(data6);
            templateData.getTemplateItemList().add(data7);
            templateData.getTemplateItemList().add(data8);
            templateData.getTemplateItemList().add(data9);

            templateService.addByGroup(templateData);

        }
        catch (Exception e){
            handleErr(currentRow,"导入数据非法");
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    private void handleErr(Integer i, String info){
        ExcelErrResponse errBody = new ExcelErrResponse();
        errBody.setRow(i + 1);
        errBody.setError("请检查：第" + (i + 1) + "行数据," + info);
        errList.add(errBody);
    }

    private String handleInput(String s){
        if(null == s){
            return null;
        }
        s = s.trim();//去掉前后空格
        s = s.replaceAll(" ","");//去除中间空格
        return s;
    }
}