package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.common.response.PageResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PageTemplateResponse extends PageResponse<TemplateResponse> {

    public PageTemplateResponse(Integer total, int page, int pageSize, int totalPages, List<TemplateResponse> pageData) {
        super(total, page, pageSize, totalPages, pageData);
    }
}
