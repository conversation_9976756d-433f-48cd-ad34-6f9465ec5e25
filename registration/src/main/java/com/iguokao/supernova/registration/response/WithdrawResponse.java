package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.registration.document.Balance;
import com.iguokao.supernova.registration.document.Withdraw;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

@Getter
@Setter
public class WithdrawResponse {
    private String tradeAmount; // 总订单金额
    private String orderCount; // 总交易笔数
    private String fee; // 总手续费
    private String amount; // 总交易金额
    private Integer wxOrderCount;
    private Integer aliOrderCount;
    private String tradeNo; // 总交易金额


    public static WithdrawResponse of(Withdraw withdraw) {
        if(withdraw == null)
            return null;
        WithdrawResponse res = new WithdrawResponse();
        BeanUtils.copyProperties(withdraw, res);
        return res;
    }
}
