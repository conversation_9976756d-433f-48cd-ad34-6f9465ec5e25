package com.iguokao.supernova.registration.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;


@Getter
@Setter
public class CandidateSmsSendRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "手机号")
    @Length(min = 11, max = 11, message = "考生手机号长度超限")
    private String mobile;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "图形验证码字符串")
    private String imageCode;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "验证码的token")
    @Length(min = 10, max = 1000, message = "验证码的token长度超限")
    private String key;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "企业ID")
    @Length(min = 24, max = 24, message = "企业长度超限")
    private String companyId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "类型 1 注册 2 登录")
    @Min(value = 1, message = "类型错误")
    @Max(value = 3, message = "类型错误")
    private Integer type;
}
