package com.iguokao.supernova.registration.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class MessageSendRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生id")
    @Length(min = 24, max = 24, message = "考生id异常")
    private String candidateId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生姓名")
    @Length(min = 1, max = 100, message = "考生姓名异常")
    private String fullName;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "内容")
    @Length(min = 1, max = 1000, message = "内容异常")
    private String content;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "类型")
    @Min(value = 0, message = "类型错误")
    @Max(value = 10, message = "类型错误")
    private Integer type;
}
