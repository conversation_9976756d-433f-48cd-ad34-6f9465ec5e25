package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Getter
@Setter
public class ProjectAddRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司id")
    @Length(min = 24, max = 24, message = "24位id")
    private String companyId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String name;

    @Schema(description = "项目备注")
    private String agreement;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "开始时间")
    private Date startAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "结束时间")
    private Date endAt;

}
