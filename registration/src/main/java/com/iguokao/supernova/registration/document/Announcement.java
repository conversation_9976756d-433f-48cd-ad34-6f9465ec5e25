package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter
@Setter
@Document
@CompoundIndexes({
        @CompoundIndex(def = "{'companyId': -1, 'categoryId': -1}", name = "companyId_categoryId_index")
})
public class Announcement extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId categoryId;

    private String title;
    private String content;
    private String link;
}
