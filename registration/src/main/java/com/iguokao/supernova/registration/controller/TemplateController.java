package com.iguokao.supernova.registration.controller;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.converter.ValidObjectId;
import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.remote.ManagementRemote;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.excel.TemplateDataExporter;
import com.iguokao.supernova.registration.request.*;
import com.iguokao.supernova.registration.response.*;
import com.iguokao.supernova.registration.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/template")
@RequiredArgsConstructor
public class TemplateController {
    private final TemplateService templateService;
    private final JwtService jwtService;
    private final ProjectService projectService;
    private final SubjectService subjectService;
    private final ManagementRemote managementRemote;
    private final CacheService cacheService;
    private final SmsService smsService;

    @GetMapping("/{templateId}")
    @Operation(summary = "模板详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = TemplateResponse.class)))
    public RestResponse<TemplateResponse> template(@PathVariable String templateId) {
        Template template = this.templateService.getById(templateId);
        return RestResponse.success(TemplateResponse.of(template));
    }
    @PostMapping("/page")
    @Operation(summary = "所有的模版")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<PageTemplateResponse> page(@RequestBody PageTemplateRequest request) {
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
        Tuple2<List<Template>, Integer> page = this.templateService.page(request.getCompanyId(), pageable);
        int totalPages = (page.second() + pageable.getPageSize() - 1)  / pageable.getPageSize();
        PageTemplateResponse res = new PageTemplateResponse(
                page.second(),
                request.getPage(),
                request.getPageSize(),
                totalPages,
                TemplateResponse.of(page.first()));
        return RestResponse.success(res);
    }

    @PostMapping("/candidate/data/page")
    @Operation(summary = "考生填写的报名数据列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<PageTemplateDataResponse> candidateTemplateDataPage(@RequestBody AdminPageTemplateDataRequest request) {
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
        Query query = this.templateService.getSearchQuery(
                request.getProjectId(),
                request.getTemplateId(),
                request.getFullName(),
                request.getMobile(),
                request.getIdCardNum(),
                request.getPassState(),
                request.getGender(),
                request.getFinished(),
                request.getPayed(),
                request.getSearchList(),
                request.getRemark1(),
                request.getRemark2(),
                request.getRemark3(),
                request.getCity(),
                request.getSubjectId(),
                request.getSignUpStart(),
                request.getSignUpEnd(),
                request.getBirthdayStart(),
                request.getBirthdayEnd(),
                request.getImported());
        Tuple2<List<TemplateData>, Integer> page = this.templateService.dataPage(query, pageable);
        int totalPages = (page.second() + pageable.getPageSize() - 1)  / pageable.getPageSize();
        PageTemplateDataResponse res = new PageTemplateDataResponse(
                page.second(),
                request.getPage(),
                request.getPageSize(),
                totalPages,
                TemplateDataResponse.of(page.first()));
        return RestResponse.success(res);
    }

    @PostMapping("/candidate/data/info")
    @Operation(summary = "考生填写的报名数据详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<TemplateDataResponse> candidateTemplateDataInfo(@RequestBody AdminTemplateDataRequest request) {
        TemplateData templateData = this.templateService.getDataByProjectIdAndCandidateId(request.getProjectId(),
                request.getCandidateId(),request.getTemplateId());
        return RestResponse.success(TemplateDataResponse.of(templateData));
    }


    @PostMapping("/candidate/data/queue")
    @Operation(summary = "考生填写的报名数据详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> candidateTemplateDataQueue(@RequestBody AdminTemplateDataQueueRequest request) {
        String candidateId = this.templateService.queue(request.getProjectId(),request.getCandidateId(),request.getTemplateId(),request.getType());
        return RestResponse.success(candidateId);
    }

    @PostMapping("/candidate/data/judge")
    @Operation(summary = "考生填写的报名数据审核")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> candidateTemplateDataJudge(@RequestBody AdminTemplateDataJudgeRequest request) {
        String operatorId = this.jwtService.currentOperatorId();
        ApproveItem item = new ApproveItem();
        item.setOperatorId(new ObjectId(operatorId));
        item.setCreatedAt(new Date());
        item.setPassed(request.getPassed());
        item.setMessage(request.getRejectReason());
        List<SmsSendItem> list = new ArrayList<>();
        request.getCandidateInfoList().forEach(candidateInfo -> {
            SmsSendItem smsSendItem = new SmsSendItem();
            smsSendItem.setFullName(candidateInfo.getFullName());
            smsSendItem.setMobile(candidateInfo.getMobile());
            smsSendItem.setTemplateDataId(candidateInfo.getTemplateDataId());
            list.add(smsSendItem);
        });
        this.templateService.judge(request.getCompanyId(), list, item);
        return RestResponse.success();
    }

    @GetMapping("/data/pass/{projectId}")
    @Operation(summary = "无须审查直接通过所有data表数据")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> passAll(@ValidObjectId @PathVariable String projectId) {
        this.templateService.passAll(projectId);
        return RestResponse.success();
    }

    @PostMapping("/add")
    @Operation(summary = "添加模版")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody TemplateAddRequest request) {
        Template template = new Template();
        template.setCompanyId(new ObjectId(request.getCompanyId()));
        template.setName(request.getName());
        template.setUploadAvatar(request.getUploadAvatar());
        template.setLocation(request.getLocation());
        this.templateService.add(template);
        return RestResponse.success();
    }

    @PostMapping("/update")
    @Operation(summary = "添加模版")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody TemplateUpdateRequest request) {
        Template template = new Template();
        template.set_id(new ObjectId(request.getTemplateId()));
        template.setCompanyId(new ObjectId(request.getCompanyId()));
        template.setName(request.getName());
        template.setUploadAvatar(request.getUploadAvatar());
        template.setLocation(request.getLocation());
        this.templateService.update(template);
        return RestResponse.success();
    }

    @GetMapping("/remove/{templateId}")
    @Operation(summary = "删除模版")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@ValidObjectId @PathVariable String templateId) {
        this.templateService.remove(templateId);
        return RestResponse.success();
    }
    @PostMapping("/group/add")
    @Operation(summary = "添加模版 组")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> addGroup(@RequestBody TemplateGroupAddRequest request) {
        TemplateGroup templateGroup = new TemplateGroup();
        templateGroup.setName(request.getName());
        templateGroup.setGroupId(new ObjectId());
        this.templateService.addGroup(request.getTemplateId(), templateGroup);
        return RestResponse.success();
    }

    @PostMapping("/group/update")
    @Operation(summary = "编辑模版 组")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> addGroup(@RequestBody TemplateGroupUpdateRequest request) {
        TemplateGroup templateGroup = new TemplateGroup();
        templateGroup.setName(request.getName());
        templateGroup.setGroupId(new ObjectId(request.getGroupId()));
        this.templateService.updateGroup(request.getTemplateId(), templateGroup);
        return RestResponse.success();
    }

    @GetMapping("/group/remove/{templateId}/{groupId}")
    @Operation(summary = "删除模版组")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> removeGroup(@ValidObjectId @PathVariable String templateId,
                                            @ValidObjectId @PathVariable String groupId) {
        this.templateService.removeGroup(templateId, groupId);
        return RestResponse.success();
    }

    @PostMapping("/item/add")
    @Operation(summary = "添加模版 项")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> addItem(@RequestBody TemplateItemAddRequest request) {
        TemplateItem templateItem = new TemplateItem();
        templateItem.setItemId(new ObjectId());
        BeanUtils.copyProperties(request, templateItem);
        this.templateService.addItem(request.getTemplateId(), request.getGroupId(), templateItem);
        return RestResponse.success();
    }

    @PostMapping("/item/update")
    @Operation(summary = "更新模版 项目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> updateItem(@RequestBody TemplateItemUpdateRequest request) {
        TemplateItem templateItem = new TemplateItem();
        templateItem.setItemId(new ObjectId(request.getItemId()));
        BeanUtils.copyProperties(request, templateItem);
        this.templateService.updateItem(request.getTemplateId(), request.getGroupId(), templateItem);
        return RestResponse.success();
    }

    @GetMapping("/item/remove/{templateId}/{groupId}/{itemId}")
    @Operation(summary = "删除模版项")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> removeItem(@ValidObjectId @PathVariable String templateId,
                                           @ValidObjectId @PathVariable String groupId,
                                           @ValidObjectId @PathVariable String itemId) {
        this.templateService.removeItem(templateId, groupId, itemId);
        return RestResponse.success();
    }

    @GetMapping("/copy/{templateId}")
    @Operation(summary = "复制模版")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> copy(@ValidObjectId @PathVariable String templateId) {
        this.templateService.copy(templateId);
        return RestResponse.success();
    }

    @PostMapping("/edit/data")
    @Operation(summary = "考生报名表数据编辑")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> editData(@Validated @RequestBody AdminCandidateTemplateDataRequest request){
        TemplateData templateData = this.templateService.findByProjectIdAndCandidateId(request.getProjectId(), request.getCandidateId(), request.getTemplateId());
        templateData.setFullName(request.getFullName());
        templateData.setMobile(request.getMobile());
        templateData.setEmail(request.getEmail());
        templateData.setGender(request.getGender());
        templateData.setIdCardType(request.getIdCardType());
        templateData.setIdCardNum(request.getIdCardNum());
        templateData.setBirthday(request.getBirthday());
        templateData.setCity(request.getCity());

        templateData.setTemplateItemList(TemplateItemData.of(request.getTemplateItemList(), templateData.getTemplateItemList()));
        this.templateService.updateTemplateData(templateData,false);

        String operatorId = this.jwtService.currentOperatorId();
        Project project = this.cacheService.getProject(request.getProjectId());
        AuditItem item = new AuditItem(project.getCompanyId().toString(), AuditTypeEnum.TEMPLATE_DATA_EDIT.getCode(), request);
        item.setOperatorId(operatorId);
        this.managementRemote.audit(item);
        return RestResponse.success();
    }

    @PostMapping("/set/remark")
    @Operation(summary = "批量设置自定义字段")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> setRemark(@RequestBody AdminSetRemarkRequest request) {
        this.templateService.setRemark(request.getTemplateDataIdList(),request.getRemark1(),request.getRemark2(),request.getRemark3());
        return RestResponse.success();
    }

    @PostMapping("/link")
    @Operation(summary = "连接 企业 和 模版")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> link(@RequestBody TemplateLinkRequest request) {
        this.templateService.link(request.getTemplateId(), request.getProjectId());
        return RestResponse.success();
    }

    @GetMapping("/pass/info/{projectId}")
    @Operation(summary = "通过统计情况")
    public RestResponse<CandidatePassInfoResponse> passInfo(@PathVariable String projectId){
        CandidatePassInfoResponse infoResponse = new CandidatePassInfoResponse();
        List<Integer> infoList = this.templateService.passInfo(projectId);
        infoResponse.setTotal(infoList.get(0));
        infoResponse.setPayCount(infoList.get(1));
        infoResponse.setPassSuccess(infoList.get(2));
        infoResponse.setPassFail(infoList.get(3));
        infoResponse.setFinishedNotApproved(infoList.get(4));
        return RestResponse.success(infoResponse);
    }

    @GetMapping("/export/excel/{projectId}")
    @Operation(summary = "导出考生报名数据")
    public void export(@PathVariable String projectId, HttpServletResponse response) throws IOException {
        //考生ID 全名 手机号 证件类型 证件号  是否是导入考生 Email 所在地 考区 报名表填写完毕 审核状态 支付订单号 备注1 备注1 备注1 需要开发票 抬头 税号 发票类型 发票路径
        List<TemplateData> templateDataList = this.templateService.getByProjectId(projectId);
        Project project = this.projectService.getById(projectId);
        List<Subject> subjectList = this.subjectService.getListByProjectId(projectId);

        Template template = this.templateService.getById(project.getTemplateId().toString());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("报名表_%s", project.getName()), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<List<String>> res = TemplateDataExporter.data(templateDataList, template, subjectList);

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        EasyExcel.write(response.getOutputStream())
                .sheet("报名表")
                .head(TemplateDataExporter.head(template))
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(res);
    }

    @PostMapping("/send/sms")
    @Operation(summary = "发送短信")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendSms(@RequestBody TemplateDataSmsRequest request) {
        Query query = this.templateService.getSearchQuery(
                request.getProjectId(),
                request.getTemplateId(),
                request.getFullName(),
                request.getMobile(),
                request.getIdCardNum(),
                request.getPassState(),
                request.getGender(),
                request.getFinished(),
                request.getPayed(),
                request.getSearchList(),
                request.getRemark1(),
                request.getRemark2(),
                request.getRemark3(),
                request.getCity(),
                request.getSubjectId(),
                request.getSignUpStart(),
                request.getSignUpEnd(),
                request.getBirthdayStart(),
                request.getBirthdayEnd(),
                request.getImported());

        String smsBenchId = this.templateService.sendSms(query, request.getSmsTemplateId(), request.getSkipSmsSent());
        return RestResponse.success(smsBenchId);
    }

    @PostMapping("/send/sms/list")
    @Operation(summary = "发送短信 勾选")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendSmsList(@RequestBody TemplateDataListSmsRequest request) {
        String smsBenchId = this.templateService.sendSmsByTemplateDataIdList(request.getTemplateDataIdList(), request.getSmsTemplateId(), request.getSkipSmsSent());
        return RestResponse.success(smsBenchId);
    }

    @GetMapping("/sms/progress/{benchId}")
    @Operation(summary = "统计")
    public RestResponse<SmsProgressResponse> smsProgress(@PathVariable String benchId){
        SmsProgress smsProgress = this.smsService.smsProgress(benchId);
        return RestResponse.success(SmsProgressResponse.of(smsProgress));
    }
}
