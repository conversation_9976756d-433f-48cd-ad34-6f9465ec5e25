package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.registration.document.ApproveItem;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import com.iguokao.supernova.registration.document.TemplateData;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.*;

@Getter
@Setter
public class CandidateSignUpProjectResponse {

    private String projectName;
    private String projectId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date projectSignUpStartAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date projectSignUpEndAt;
    private Boolean pay;
    private String successTradeNo;
    private Boolean passed;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date updatedAt;
    private Boolean approve;
    private Boolean appoint;
    private String rejectReason;
    private List<CandidateSignUpSubjectResponse> subjectList = new ArrayList<>();

    public static List<CandidateSignUpProjectResponse> of(List<TemplateData> templateData,List<Subject> subjectList, List<Project> projectList){
        List<CandidateSignUpProjectResponse> list = new ArrayList<>();
        for(TemplateData data : templateData){
            CandidateSignUpProjectResponse res = new CandidateSignUpProjectResponse();
            res.setSuccessTradeNo(data.getSuccessTradeNo());
            res.setPassed(data.getPassed());
            res.setUpdatedAt(data.getUpdatedAt());
            Optional<ApproveItem> item = data.getApproveList()
                    .stream()
                    .filter(a -> !a.getPassed())
                    .min(Comparator.comparing(ApproveItem::getCreatedAt, Comparator.nullsLast(Comparator.reverseOrder())));
            item.ifPresent(i -> {
                res.setRejectReason(i.getMessage());
            });
            for(Project project : projectList){
                if(data.getProjectId().toString().equals(project.get_id().toString())){
                    res.setProjectName(project.getName());
                    res.setPay(project.getPay());
                    res.setProjectId(project.get_id().toString());
                    res.setProjectSignUpStartAt(project.getSignUpStartAt());
                    res.setProjectSignUpEndAt(project.getSignUpEndAt());
                    res.setAppoint(project.getAppoint());
                    res.setApprove(project.getApprove());
                }
            }

            for(ObjectId sid : data.getSubjectIdList()){
               for(Subject subject : subjectList){
                   if(sid.toString().equals(subject.get_id().toString())){
                       CandidateSignUpSubjectResponse candidateSignUpSubjectResponse = CandidateSignUpSubjectResponse.of(subject);
                       candidateSignUpSubjectResponse.setTemplateDataCity(data.getCity());
                       candidateSignUpSubjectResponse.setTemplateDataPassed(data.getPassed());
                       res.getSubjectList().add(candidateSignUpSubjectResponse);
                   }
               }
            }
            list.add(res);
        }
        list = list.stream()
                .sorted(Comparator.comparing(CandidateSignUpProjectResponse::getProjectSignUpStartAt, Comparator.nullsLast(Comparator.reverseOrder())))
                .toList();
        return list;
    }
}
