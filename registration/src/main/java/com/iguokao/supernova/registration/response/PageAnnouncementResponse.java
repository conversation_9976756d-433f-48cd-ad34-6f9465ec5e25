package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.common.response.PageResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PageAnnouncementResponse extends PageResponse<AnnouncementResponse> {

    public PageAnnouncementResponse(Integer total, int page, int pageSize, int totalPages, List<AnnouncementResponse> pageData) {
        super(total, page, pageSize, totalPages, pageData);
    }
}
