package com.iguokao.supernova.registration.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;


@Getter
@Setter
public class AdminTemplateDataQueueRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生id")
    @Length(min = 24, max = 24, message = "candidateId 错误")
    private String candidateId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "报名项目id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "模板id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String templateId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "上、下")
    @Min(value = 1, message = "规则错误")
    @Max(value = 2, message = "规则错误")
    private Integer type;

}
