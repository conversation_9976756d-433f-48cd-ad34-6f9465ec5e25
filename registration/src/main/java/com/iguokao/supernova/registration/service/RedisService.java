package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.service.ImageCodeService;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import com.iguokao.supernova.registration.response.CandidateStatisticResponse;
import com.iguokao.supernova.registration.response.TemplateDataStatisticResponse;

import java.util.List;

public interface RedisService extends ImageCodeService {

    // 获取短信验证码
    String getSmsCode(String key);
    // 设置短信验证码
    void setSmsCode(String key, String code);
    // 删除短信验证码
    void deleteSmsCode(String key);
    Tuple2<Integer, Integer> getSmsCount(String ip, String mobile);
    void increaseSmsCount(String ip, String mobile);

    CompanyConfig getCompanyConfig(String sn);
    void setCompanyConfig(CompanyConfig companyConfig);
    void deleteCompany(String sn);

    Project getProject(String projectId);
    List<Project> getProjectList(List<String> projectIdList);
    void setProject(Project project);
    void deleteProject(String projectId);

    Subject getSubject(String subjectId);
    List<Subject> getSubjectList(List<String> subjectIdList);
    void setSubject(Subject subject);
    void deleteSubject(String subjectId);

    List<Project> getProjectList(String companyId);
    void setProjectList(List<Project> projectList);
    void deleteProjectList(String companyId);

    void setAppointLimit(String projectId, String key, int limit);
    void changeAppointLimit(String projectId, String key, int value);
    List<Integer> getAppointLimitList(String projectId, List<String> keyList);

    String getOrderNumber();

    String getLock(String key);
    void setLock(String key);
    void deleteLock(String key);

    List<AdmissionCard> getAdmissionCardByIdCardNum(String projectId, String idCardNum);
    void setAdmissionCard(List<AdmissionCard> list);
    void deleteAdmissionCardByIdCardNum(String projectId, String idCardNum);

    String genShortToken(String tradeNo, String candidateId);
    String getToken(String token);

    void runLockTask(String key, Integer expireSeconds, Runnable func);

    TemplateDataStatisticResponse getTemplateDataStatistic(String projectId);
    void setTemplateDataStatistic(String projectId, TemplateDataStatisticResponse statisticResponse);

    CandidateStatisticResponse getCandidateStatistic(String companyId);
    void setCandidateStatistic(String companyId, CandidateStatisticResponse statisticResponse);
}
