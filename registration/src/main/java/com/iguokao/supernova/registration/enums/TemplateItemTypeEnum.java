package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum TemplateItemTypeEnum implements BaseEnum {
    INPUT(1, "文本框"),
    SELECT(2, "下拉"),
    RADIO(3, "单选"),
    PHOTO(4, "照片"),
    ATTACHMENT(5, "附件"),
    TEXT(6, "文本"),
    DATE(7, "日期"),
    LEVEL_SELECT(8, "级联"),
    GROUP_SELECT(9, "组选择"),

    ;

    TemplateItemTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
