package com.iguokao.supernova.registration.document;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;


@Getter
@Setter
@Document
public class Email extends BaseDocument {
    @Indexed(name = "companyId_index")
    private ObjectId companyId;
    private String name;  // 名称
    private String title;
    private String content;  // 内容
}
