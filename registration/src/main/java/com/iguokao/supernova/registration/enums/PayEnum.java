package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum PayEnum implements BaseEnum {

    NO(0, "无需支付"),
    DIRECT(1, "直接支付"),
    APPROVED(2, "审核后支付"),
    ;

    PayEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
