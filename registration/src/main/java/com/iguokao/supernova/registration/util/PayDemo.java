package com.iguokao.supernova.registration.util;

import java.util.*;
import com.huifu.bspay.sdk.opps.core.config.MerConfig;
import com.huifu.bspay.sdk.opps.core.net.BasePayRequest;
import com.huifu.bspay.sdk.opps.core.BasePay;
import com.huifu.bspay.sdk.opps.core.utils.DateTools;
import com.huifu.bspay.sdk.opps.core.utils.SequenceTools;

/**
 * 应用场景 - 示例
 */
public class PayDemo {

    // 汇付分配的产品号
    public static final String DEMO_PRODUCT_ID = "PAYUN";

    // 汇付分配的系统号
    public static final String DEMO_SYS_ID = "6666000162417390";

    // 服务商私钥，用于调用接口时进行签名
    public static final String DEMO_RSA_PRIVATE_KEY = "MIIEuwIBADANBgkqhkiG9w0BAQEFAASCBKUwggShAgEAAoIBAQDU2dy4nQjp7AgAsPNE/vJxxgd9KilYxnnSPcQAKQpqE1sNBjZcUVoggt6hf0FyVzul3q9Tv6XdW6G78F4bZ/x4yeX2f7jc+RPXUV1BmgW8MSroEbP4vt/nrPq38PXdfVJrJ3erSPuEu4dIZIfdYDfnhXSzZZnS/xaBIaoLTwDtjTl7eW/oxxRG/ffCfPCurU4lTTxoYl5rUYGS5VmTpp2VPeIY47QxoFBhLEQK2TSYE5i7i5kTcNbxaRUPNJ5HsEZSjYJwJhkkIbFnar+CXjW9SzSliORz1ej2/imiXvipUC66/7ZCNZrXLgsajdTPq7zo89rZ4mXWGGtwEKKRR7/5AgMBAAECggEAAoQ9GXsUoCYzHDj7XO+e7Uy879RBojC3v4Fziz69xm4wp4yETetrUDkwr/WgEAQRt57hcUrLVGNRNZAWHychVpdCgaHWDT/62OyeGH8dV4tR38w+L1Z1MYxh9+GtZbJyrBAoWGbX2T4M9fe0UIZ8jewnkOuf82nvLO5fz/VQ6qR5Ia5Wrh5yV5zLezfK/tAKfb8wUUXdMFqLril21bmNrIi3T533UQdkXI6kZVN0dROeIFdjqecDEVojdD5yM/27X4OBdmrDfYRqjhe86ktzm6DYJ60WDREru9dhIATETSMC/u57x9hXlECP2VSh86GE6v1zJkBpJPqYFHv8YgxYwQKBgQDcv+gi9NgWp/b3iROiJRYYKlISCQPmQnxusnCpfZCjkaOY9ElUap/FIDaJ6fMuc3EENQNnabM/qdiPQFLYG4I47jw4kP+lRtcTcc9Ha+fp81+zhA1Tv/eSI2h5duT2OzW6v1cSvjIBI3mE1RhgQ9JiWTRVb4zoJ0OrIV4tPEiYWQKBgQD21xDLBp/kecwLoohGsF20sAogltjXNLLEtG8BmKRXL+1lZYgTfiYpYIKztofFLb7YAQSES6NVxpiZPZxk2Ey5V82Jy0CVcCEt33DQRgx47Rr4T5uJpdYcvZOeKhd3EmZ219XMbLKhlWUqT4qsA4IVILMJOPzEVutDnDNWHaFwoQKBgQCv52BUkqSqkReUMq/pcgIEKZ0ScAqibnJ3R0vNU5hxLvmdRXQ1h21fCIdNKJa2ehkh0BixTCIg5fgCVrBtMqZ3NSfZr3l3O1bhULxqXksEOwIUq3JvPTZLRlnXCG305EOxVw1MizCPhbQsrB3rf7/izNCOEADJcZhTgphoFXSHqQKBgBjOQBfHEKZrPwAkU0GXU7NmxX3E6wGlgBmYcFdeuZUxcc/wxh3/XWJnGnto6nf8QuN+JrAM0ds62/b6ZA8WVY+CrVsdm93oqMztWW70uygj28P8bAsHKV82BfGtFTKYHMfwpMGoYsMuOj5oQ+1JZPculA9nNK+edIIU3mvXCnRBAn89FXKfpVBBI8cReXHv9twxqA4AvV8ATbuycPd2Ek1aoUNcAl0AOdIKRJAbZgHZUoVsTlRE5u8slTB7o6WJNAMYI7YPVKrQs7p3z6PPCZ3ZgjmXc91KnU81nay2F7WDpiRebQkofFN2hZenkKl9/3dsT/JAv3MmAPYXKxHGQMW2";
    // 汇付公钥，用于对汇付返回报文进行签名验证
    public static final String DEMO_RSA_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1NncuJ0I6ewIALDzRP7yccYHfSopWMZ50j3EACkKahNbDQY2XFFaIILeoX9Bclc7pd6vU7+l3Vuhu/BeG2f8eMnl9n+43PkT11FdQZoFvDEq6BGz+L7f56z6t/D13X1Sayd3q0j7hLuHSGSH3WA354V0s2WZ0v8WgSGqC08A7Y05e3lv6McURv33wnzwrq1OJU08aGJea1GBkuVZk6adlT3iGOO0MaBQYSxECtk0mBOYu4uZE3DW8WkVDzSeR7BGUo2CcCYZJCGxZ2q/gl41vUs0pYjkc9Xo9v4pol74qVAuuv+2QjWa1y4LGo3Uz6u86PPa2eJl1hhrcBCikUe/+QIDAQAB";
    public static void main(String[] args) throws Exception {
        // 1. 数据初始化，填入对应的商户配置
        MerConfig merConfig = new MerConfig();
        merConfig.setProcutId(DEMO_PRODUCT_ID);
        merConfig.setSysId(DEMO_SYS_ID);
        merConfig.setRsaPrivateKey(DEMO_RSA_PRIVATE_KEY);
        merConfig.setRsaPublicKey(DEMO_RSA_PUBLIC_KEY);
        BasePay.initWithMerConfig(merConfig);
        // 2.组装请求参数
        Map<String, Object> paramsInfo = new HashMap<>();
        // 请求日期
        paramsInfo.put("req_date", DateTools.getCurrentDateYYYYMMDD());
        // 请求流水号
        paramsInfo.put("req_seq_id", SequenceTools.getReqSeqId32());
        // 商户号
        paramsInfo.put("huifu_id", "6666000164450471");
        // 商品描述
        paramsInfo.put("goods_desc", "hibs自动化-通用版验证");
        // 交易类型
        paramsInfo.put("trade_type", "T_JSAPI");
        // 交易金额
        paramsInfo.put("trans_amt", "0.10");
        // 交易有效期
        paramsInfo.put("time_expire", "20250518235959");
        // 微信参数集合
        paramsInfo.put("wx_data", "{\"sub_appid\":\"wxe0a15e1518f51e43\",\"sub_openid\":\"oddXT5hH9GjdALRf_kd0nw0oM0qk\",\"detail\":{\"cost_price\":\"43.00\",\"receipt_id\":\"20220628132043853798\",\"goods_detail\":[{\"goods_id\":\"6934572310301\",\"goods_name\":\"太龙双黄连口服液\",\"price\":\"43.00\",\"quantity\":\"1\",\"wxpay_goods_id\":\"12235413214070356458058\"}]}}");
//        paramsInfo.put("alipay_data", "{\"buyer_id\":\"7adc077da40906a71978825\",\"buyer_logon_id\":\"string\",\"extend_params\":{\"food_order_type\":\"qr_order\",\"industry_reflux_info\":\"string\",\"parking_id\":\"123wsx\",\"sys_service_provider_id\":\"1111111\"},\"goods_detail\":[{\"goods_id\":\"123112321\",\"goods_name\":\"汇付\",\"price\":\"43.00\",\"quantity\":\"20\",\"categories_tree\":\"string\"}],\"merchant_order_no\":\"string\",\"operator_id\":\"123213213\",\"product_code\":\"string\",\"seller_id\":\"string\"}");

        // 是否延迟交易
        paramsInfo.put("delay_acct_flag", "N");
        // 分账对象
//        paramsInfo.put("acct_split_bunch", "{\"acct_infos\":[{\"div_amt\":\"0.10\",\"huifu_id\":\"6666000161968692\"}]}");
        // 传入分账遇到优惠的处理规则
        paramsInfo.put("term_div_coupon_type", "0");
        // 禁用信用卡标记
        paramsInfo.put("limit_pay_type", "NO_CREDIT");
        // 场景类型
        paramsInfo.put("pay_scene", "02");
        // 备注
        paramsInfo.put("remark", "string");
        paramsInfo.put("notify_url", "https://supernova-api.iguokao.com/registration/api/v1/pay/huifu/callback");
        // 3. 发起API调用
        Map<String, Object> response = BasePayRequest.requestBasePay("v2/trade/payment/jspay", paramsInfo, null, false);;
        System.out.println(response);
    }
}