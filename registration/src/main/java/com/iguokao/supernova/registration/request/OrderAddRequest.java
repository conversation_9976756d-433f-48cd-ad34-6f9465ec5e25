package com.iguokao.supernova.registration.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class OrderAddRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "projectId")
    @Length(min = 24, max = 24, message = "projectId长度超限")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "科目列表")
    @NotEmpty(message = "科目数据异常")
    private List<String> subjectIdList;

    private String source;

    @Schema(description = "姓名")
    @Length(min = 1, max = 100, message = "用户名长度超限")
    private String fullName;

    @Schema(description = "手机号")
    @Length(min = 11, max = 11, message = "手机号长度超限")
    private String mobile;

    private String companySn;
}
