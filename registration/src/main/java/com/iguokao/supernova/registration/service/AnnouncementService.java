package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.registration.document.Announcement;
import com.iguokao.supernova.registration.document.AnnouncementCategory;
import com.iguokao.supernova.registration.document.CompanyConfig;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AnnouncementService {
    void add(Announcement announcement);
    void update(Announcement announcement);
    Tuple2<List<Announcement>, Integer> page(String companyId, String categoryId, Pageable pageable);

    void remove(String announcementId);
}
