package com.iguokao.supernova.registration.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document("company_config")
public class CompanyConfig extends BaseDocument {
    private String name;
    private String logo;
    private String banner;
    private String miniBanner;

    @Indexed(name = "sn_index")
    private String sn;
    private String agreement;

    private Boolean myProject;
    private Boolean appointEnabled;
    private Boolean admissionEnabled;
    private Boolean scoreEnabled;
    private Boolean invoiceEnable;
    private Boolean chatEnable;
    private Boolean myInfoEnable;
    private Boolean projectListEnable;
    private String support;

    private String supplementaryName;

    private String hfId;
    private String hfTokenNo;
    private String hfSplitId;

    private String withdrawPassword;
    private String smsApproveSuccessTemplate;
    private String smsApproveRejectedTemplate;
    private String smsRefundTemplate;

    private String primaryColor;
    private String menuColor;
    private String menuHoverColor;

    private List<String> payChannelList = new ArrayList<>();
    private List<Integer> invoiceTypeList = new ArrayList<>();
    private List<AnnouncementCategory> announcementCategoryList = new ArrayList<>();
    private List<SmsTemplate> smsTemplateList = new ArrayList<>();

    public CompanyConfig() {
        AnnouncementCategory ac = new AnnouncementCategory();
        ac.setName("企业公告");
        ac.setCategoryId(new ObjectId());
        this.announcementCategoryList.add(ac);
    }
}
