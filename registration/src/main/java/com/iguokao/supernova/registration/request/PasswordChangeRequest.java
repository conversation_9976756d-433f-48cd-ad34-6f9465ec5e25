package com.iguokao.supernova.registration.request;

import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class PasswordChangeRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "密码")
    @Length(min = 3, max = 100, message = "密码长度超限")
    private String password;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "手机号")
    @Length(min = 11, max = 11, message = "手机号长度超限")
    private String mobile;
}
