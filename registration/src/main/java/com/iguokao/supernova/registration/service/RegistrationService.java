package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Registration;
import com.iguokao.supernova.registration.document.Subject;

import java.util.List;

public interface RegistrationService{
    Tuple2<List<Subject>, List<Project>> getCandidateSubject(String candidateId);
    List<Registration> getListByCandidateIdAndSubjectIdList(String candidateId, List<String> projectIdList);
    void changeRegistration(String subjectId, String candidateId, Integer confirmState, String city, String siteId);

    List<String> getExistsLoginNameBySubjectId(String subjectId);

    Integer countAllByProjectId(String projectId);

    Integer countAllByProjectIdAndSubjectId(String projectId, String subjectId);

    Integer countConfirmationByProjectId(String projectId, Integer state);

    Integer countByProjectIdAndEmailState(String projectId);

    Integer countByProjectIdAndSmsState(String projectId);

    List<String> getCityListByProjectId(String projectId);

    Integer countStateByProjectIdAndCity(String projectId, String city, Integer o);

    List<Registration> getByCandidateId(String candidateId);

    List<Registration> getListByCandidateId(String candidateId);
}
