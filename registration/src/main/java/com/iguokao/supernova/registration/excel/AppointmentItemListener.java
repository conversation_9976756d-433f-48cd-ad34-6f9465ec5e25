package com.iguokao.supernova.registration.excel;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.AppointItem;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.enums.AppointTypeEnum;
import com.iguokao.supernova.registration.service.AppointItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class AppointmentItemListener extends AnalysisEventListener<AppointmentItem> {

    private final Project project;
    private final List<ExcelErrResponse> errList;
    private final AppointItemService appointItemService;

    private final List<AppointItem> list = new ArrayList<>();

    @Override
    public void invoke(AppointmentItem item, AnalysisContext analysisContext) {
        Integer currentRow = analysisContext.readRowHolder().getRowIndex();

        //可能是第一行 表头
        if(null != item.getCity() && (item.getCity().contains("城市"))){
            return;
        }

        try {

            // 重要的处理部分
            AppointItem appointItem  = new AppointItem();
            appointItem.setProjectId(project.get_id());

            //处理城市
            if(null == item.getCity() || item.getCity().length() > 50 || !StringUtil.checkCity(item.getCity())){
                handleErr(currentRow, "城市名称错误" + item.getCity());
                return;
            }
            appointItem.setCity(handleInput(item.getCity()));

            //处理 考站
            if(project.getAppointType().equals(AppointTypeEnum.SITE.getCode())){
                //精确到考站
                if(StringUtil.validObjectId(item.getSiteId())){
                    appointItem.setSiteId(new ObjectId(item.getSiteId()));
                    if(null == item.getSiteName()){
                        handleErr(currentRow, "siteName错误");
                        return;
                    }
                    appointItem.setSiteName(item.getSiteName());

                    if(null != item.getSiteAddress() ){
                        appointItem.setSiteAddress(item.getSiteAddress());
                    }
                }
                else {
                    handleErr(currentRow, "siteId错误");
                    return;
                }
            }

            //处理配额
            if(null == item.getQuota() || item.getQuota() <=0 ){
                handleErr(currentRow, "配额异常");
                return;
            }
            appointItem.setQuota(item.getQuota());

            //添加到list中
            list.add(appointItem);
        }
        catch (Exception e){
            handleErr(currentRow,"导入数据非法");
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if(!list.isEmpty()){
            AppointItem appointItem = list.get(0);
            int check = 1;
            for(AppointItem at : list){
                if(null == appointItem.getSiteId() ){
                    if(null != at.getSiteId()){
                        check = 0;
                    }
                }
                else {
                    if(null == at.getSiteId()){
                        check = 0;
                    }
                }
            }
            if(check == 1){
                this.appointItemService.addAllByExcel(list,project.get_id().toString());
                log.info("导入完成");
            }
            else {
                handleErr(1,"导入数据非法,发现siteId不同步，造成所有数据无法导入");
                log.info("导入全部失败");
            }

        }
        else {
            log.info("导入全部失败");
        }
    }

    private void handleErr(Integer i, String info){
        ExcelErrResponse errBody = new ExcelErrResponse();
        errBody.setRow(i + 1);
        errBody.setError("请检查：第" + (i + 1) + "行数据," + info);
        errList.add(errBody);
    }

    private String handleInput(String s){
        if(null == s){
            return null;
        }
        s = s.trim();//去掉前后空格
        s = s.replaceAll(" ","");//去除中间空格
        return s;
    }
}