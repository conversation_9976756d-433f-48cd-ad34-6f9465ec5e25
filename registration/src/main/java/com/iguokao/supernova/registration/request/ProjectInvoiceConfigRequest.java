package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Getter
@Setter
public class ProjectInvoiceConfigRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    private Boolean invoice;
    private Integer invoiceType;
    private String invoiceTitle;
    private String invoiceTin;
    private Boolean invoicePersonal;
}
