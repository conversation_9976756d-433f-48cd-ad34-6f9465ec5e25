package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.registration.document.Announcement;
import com.iguokao.supernova.registration.document.AnnouncementCategory;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.repository.AnnouncementRepository;
import com.iguokao.supernova.registration.repository.CompanyConfigRepository;
import com.iguokao.supernova.registration.response.AnnouncementResponse;
import com.iguokao.supernova.registration.service.AnnouncementService;
import com.iguokao.supernova.registration.service.CompanyConfigService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.schema.JsonSchemaObject;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class AnnouncementServiceImpl implements AnnouncementService {
    private final AnnouncementRepository announcementRepository;
    private final MongoTemplate mongoTemplate;


    @Override
    public void add(Announcement announcement) {
        this.announcementRepository.save(announcement);
    }

    @Override
    public void update(Announcement announcement) {
        Announcement exist = this.announcementRepository.findById(announcement.get_id())
                .orElseThrow(()-> new ServiceException(ExceptionEnum.ANNOUNCEMENT_NOT_FOUND));
        exist.setUpdatedAt(new Date());
        exist.setTitle(announcement.getTitle());
        exist.setContent(announcement.getContent());
        exist.setLink(announcement.getLink());
        this.announcementRepository.save(exist);
    }

    @Override
    public Tuple2<List<Announcement>, Integer> page(String companyId, String categoryId, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt"))
                .addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));

        if(null != categoryId){
            query = query.addCriteria(Criteria.where("categoryId").is(new ObjectId(categoryId)));
        }
        // 计算总数
        long count = this.mongoTemplate.count(query, Announcement.class);
        // 分页信息
        query = query.with(pageable);
        List<Announcement> list = this.mongoTemplate.find(query, Announcement.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public void remove(String announcementId) {
        this.announcementRepository.deleteById(new ObjectId(announcementId));
    }
}
