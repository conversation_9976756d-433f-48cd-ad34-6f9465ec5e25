package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum SubjectConfirmEnum implements BaseEnum {
    CONFIRM_DEFAULT(1, "仅确认"),
    CONFIRM_CITY(2, "确认到城市"),
    CONFIRM_SITE(3, "确认到考点"),
    ;

    SubjectConfirmEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
