package com.iguokao.supernova.registration.repository;


import com.iguokao.supernova.registration.document.Subject;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface SubjectRepository extends MongoRepository<Subject, ObjectId> {
    int countByProjectIdAndName(ObjectId projectId, String name);

    List<Subject> findByProjectId(ObjectId projectId);
    List<Subject> findBy_idIn(List<ObjectId> list);
    List<Subject> findByProjectIdIn(List<ObjectId> list);

}
