package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.response.CandidateStatisticResponse;
import com.iguokao.supernova.registration.response.TemplateDataStatisticResponse;
import com.iguokao.supernova.registration.service.RedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public abstract class RedisServiceImpl implements RedisService {
    public static Integer DURATION_ONE_DAY = 3600 * 24;
    public static Integer DURATION_QUARTER = 3600 / 4;
    public static Integer DURATION_MINUTE = 60;
    public static Integer DURATION_FIVE_MINUTE = 300;
    public static Integer DURATION_TEN_MINUTE = 600;

    public static Integer DURATION_MONTH = 3600 * 24 * 30;

    public static String PREFIX_IMAGE_CODE = "ic";
    public static String PREFIX_SMS_CODE = "sms";
    public static String PREFIX_COMPANY = "cp";
    public static String PREFIX_PROJECT = "pj";
    public static String PREFIX_SUBJECT = "sj";
    public static String PREFIX_APPOINT = "ap";
    public static String PREFIX_LOCK = "lk";
    public static String KEY_ORDER_NUM = "po";
    public static String PREFIX_ADMISSION_CARD = "ac";
    public static String PREFIX_TOKEN = "tk";
    public static String PREFIX_STATISTIC = "stc";

    private final RedisTemplate<String, ImageCode> imageCodeRedisTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    private final RedisTemplate<String, CompanyConfig> companyConfigRedisTemplate;
    private final RedisTemplate<String, Project> projectRedisTemplate;
    private final RedisTemplate<String, Subject> subjectRedisTemplate;
    private final RedisTemplate<String, AdmissionCard> admissionCardRedisTemplate;
    private final RedisTemplate<String, TemplateDataStatisticResponse> statisticRedisTemplate;
    private final RedisTemplate<String, CandidateStatisticResponse> candidateStatisticRedisTemplate;

    @Override
    public ImageCode getImageCode(String key) {
        String k = String.format("%s:%s", PREFIX_IMAGE_CODE, key);
        return imageCodeRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setImageCode(ImageCode imageCode) {
        String k = String.format("%s:%s", PREFIX_IMAGE_CODE, imageCode.getKey());
        imageCodeRedisTemplate.opsForValue().set(k, imageCode, DURATION_MINUTE, TimeUnit.SECONDS);
    }

    @Override
    public void deleteImageCode(String key) {
        String k = String.format("%s:%s", PREFIX_IMAGE_CODE, key);
        imageCodeRedisTemplate.delete(k);
    }

    @Override
    public String getSmsCode(String key) {
        String k = String.format("%s:%s", PREFIX_SMS_CODE, key);
        return this.stringRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setSmsCode(String key, String code) {
        String k = String.format("%s:%s", PREFIX_SMS_CODE, key);
        this.stringRedisTemplate.opsForValue().set(k, code, DURATION_QUARTER, TimeUnit.SECONDS);
    }

    @Override
    public void deleteSmsCode(String key) {
        String k = String.format("%s:%s", PREFIX_SMS_CODE, key);
        this.stringRedisTemplate.delete(k);
    }

    @Override
    public Tuple2<Integer, Integer> getSmsCount(String ip, String mobile) {
        String keyIp = String.format("%s:ip:%s", PREFIX_SMS_CODE, ip);
        String ipCount = this.stringRedisTemplate.opsForValue().get(keyIp);
        String keyMobile = String.format("%s:mb:%s", PREFIX_SMS_CODE, mobile);
        String mobileCount = this.stringRedisTemplate.opsForValue().get(keyMobile);

        return new Tuple2<>(ipCount == null ? 0: Integer.parseInt(ipCount),
                mobileCount == null ? 0: Integer.parseInt(mobileCount));
    }

    @Override
    public void increaseSmsCount(String ip, String mobile) {
        int time =  86400 - (int)(new Date().getTime()/1000 + 8*3600) % 86400;

        String keyIp = String.format("%s:ip:%s", PREFIX_SMS_CODE, ip);
        this.stringRedisTemplate.opsForValue().increment(keyIp);
        this.stringRedisTemplate.expire(keyIp, time, TimeUnit.SECONDS);

        String keyMobile = String.format("%s:mb:%s", PREFIX_SMS_CODE, mobile);
        this.stringRedisTemplate.opsForValue().increment(keyMobile);
        this.stringRedisTemplate.expire(keyMobile, time, TimeUnit.SECONDS);
    }

    @Override
    public CompanyConfig getCompanyConfig(String sn) {
        String k = String.format("%s:%s", PREFIX_COMPANY, sn);
        return this.companyConfigRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setCompanyConfig(CompanyConfig companyConfig) {
        String k = String.format("%s:%s", PREFIX_COMPANY, companyConfig.getSn());
        this.companyConfigRedisTemplate.opsForValue().set(k, companyConfig, DURATION_ONE_DAY,  TimeUnit.SECONDS);
    }

    @Override
    public void deleteCompany(String sn) {
        String k = String.format("%s:%s", PREFIX_COMPANY, sn);
        this.companyConfigRedisTemplate.delete(k);
    }

    @Override
    public Project getProject(String projectId) {
        String k = String.format("%s:%s", PREFIX_PROJECT, projectId);
        return this.projectRedisTemplate.opsForValue().get(k);
    }

    @Override
    public List<Project> getProjectList(List<String> projectIdList) {
        List<String> keyList = projectIdList
                .stream()
                .map(projectId -> String.format("%s:%s", PREFIX_PROJECT, projectId))
                .toList();
        return this.projectRedisTemplate.opsForValue().multiGet(keyList);
    }

    @Override
    public void setProject(Project project) {
        String k = String.format("%s:%s", PREFIX_PROJECT, project.get_id().toString());
        this.projectRedisTemplate.opsForValue().set(k, project, DURATION_ONE_DAY,  TimeUnit.SECONDS);
    }

    @Override
    public void deleteProject(String projectId) {
        String k = String.format("%s:%s", PREFIX_PROJECT, projectId);
        this.projectRedisTemplate.delete(k);
    }

    @Override
    public List<Project> getProjectList(String companyId) {
        String k = String.format("%s:%s", PREFIX_PROJECT, companyId);
        return this.projectRedisTemplate.opsForList().range(k, 0, -1);
    }

    @Override
    public void setProjectList(List<Project> projectList) {
        if(!projectList.isEmpty()){
            String k = String.format("%s:%s", PREFIX_PROJECT, projectList.get(0).getCompanyId().toString());
            this.projectRedisTemplate.delete(k);
            this.projectRedisTemplate.opsForList().leftPushAll(k, projectList);
            this.projectRedisTemplate.expire(k, DURATION_FIVE_MINUTE, TimeUnit.SECONDS);
        }
    }

    @Override
    public void deleteProjectList(String companyId) {
        String k = String.format("%s:%s", PREFIX_PROJECT, companyId);
        this.projectRedisTemplate.delete(k);
    }

    @Override
    public Subject getSubject(String subjectId) {
        String k = String.format("%s:%s", PREFIX_SUBJECT, subjectId);
        return this.subjectRedisTemplate.opsForValue().get(k);
    }

    @Override
    public List<Subject> getSubjectList(List<String> subjectIdList) {
        List<String> keyList = subjectIdList
                .stream()
                .map(subjectId -> String.format("%s:%s", PREFIX_SUBJECT, subjectId))
                .toList();
        return this.subjectRedisTemplate.opsForValue()
                .multiGet(keyList);
    }

    @Override
    public void setSubject(Subject subject) {
        String k = String.format("%s:%s", PREFIX_SUBJECT, subject.get_id().toString());
        this.subjectRedisTemplate.opsForValue().set(k, subject, DURATION_ONE_DAY,  TimeUnit.SECONDS);
    }

    @Override
    public void deleteSubject(String subjectId) {
        String k = String.format("%s:%s", PREFIX_SUBJECT, subjectId);
        this.subjectRedisTemplate.delete(k);
    }

    @Override
    public void setAppointLimit(String projectId, String key, int limit) {
        String k = String.format("%s:%s:%s", PREFIX_APPOINT, projectId, key);
        this.stringRedisTemplate.opsForValue().set(k, String.valueOf(limit));
    }

    @Override
    public void changeAppointLimit(String projectId, String key, int value) {
        if(value == 0){
            return;
        }
        String lockKey = String.format("%s:%s:%s", PREFIX_LOCK, projectId, key);
        Boolean locked = false;
        try {
            locked = this.stringRedisTemplate.opsForValue()
                    .setIfAbsent(lockKey, PREFIX_LOCK, 10, TimeUnit.MILLISECONDS);
            if (Boolean.TRUE.equals(locked)) {
                String k = String.format("%s:%s:%s", PREFIX_APPOINT, projectId, key);
                Long res;
                if(value > 0){
                    res = this.stringRedisTemplate.opsForValue().increment(k);
                } else {
                    res = this.stringRedisTemplate.opsForValue().decrement(k);
                }
                if(res == null){
                    throw new ServiceException(ExceptionEnum.APPOINT_ITEM_NOT_FOUND);
                }else if(res < 0){
                    this.setAppointLimit(projectId, key, 0);
                    throw new ServiceException(ExceptionEnum.APPOINT_LIMIT_REACH);
                }
            } else {
                throw new ServiceException(ExceptionEnum.APPOINT_LOCK_GET_FAILED);
            }
        }finally {
            if (Boolean.TRUE.equals(locked)) {
                this.stringRedisTemplate.delete(lockKey);
            }
        }
    }

    @Override
    public List<Integer> getAppointLimitList(String projectId, List<String> keyList) {
        keyList = keyList
                .stream()
                .map(k -> String.format("%s:%s:%s", PREFIX_APPOINT, projectId, k))
                .toList();
        List<String> limitList = this.stringRedisTemplate.opsForValue().multiGet(keyList);
        List<Integer> res = new ArrayList<>();
        if(limitList != null && !limitList.isEmpty()){
            limitList.forEach(s -> res.add(Integer.parseInt(s)));
        }
        return res;
    }

    @Override
    public String getOrderNumber() {
        Long num = this.stringRedisTemplate.opsForValue().increment(KEY_ORDER_NUM);
        if(null == num){
            num = 1L;
            this.stringRedisTemplate.opsForValue().set(KEY_ORDER_NUM, num.toString());
        }
        return String.format("%s%06d",  DateUtil.dateToStr(new Date(), "yyMMddHHmmss"), num);
    }

    @Override
    public String getLock(String key) {
        String k = String.format("%s:%s", PREFIX_LOCK, key);
        return this.stringRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setLock(String key) {
        String k = String.format("%s:%s", PREFIX_LOCK, key);
        this.stringRedisTemplate.opsForValue().set(k, "lock");
    }

    @Override
    public void deleteLock(String key) {
        String k = String.format("%s:%s", PREFIX_LOCK, key);
        this.stringRedisTemplate.delete(k);
    }

    @Override
    public List<AdmissionCard> getAdmissionCardByIdCardNum(String projectId, String idCardNum) {
        String k = String.format("%s:%s:%s", PREFIX_ADMISSION_CARD, projectId, idCardNum);
        return this.admissionCardRedisTemplate.opsForList().range(k, 0, -1);
    }

    @Override
    public void deleteAdmissionCardByIdCardNum(String projectId, String idCardNum) {
        String k = String.format("%s:%s:%s", PREFIX_ADMISSION_CARD, projectId, idCardNum);
        this.admissionCardRedisTemplate.delete(k);
    }

    @Override
    public void setAdmissionCard(List<AdmissionCard> list) {
        String k = String.format("%s:%s:%s", PREFIX_ADMISSION_CARD, list.get(0).getProjectId(), list.get(0).getIdCardNum());
        this.admissionCardRedisTemplate.delete(k);
        this.admissionCardRedisTemplate.opsForList().leftPushAll(k, list);
        this.admissionCardRedisTemplate.expire(k, DURATION_MONTH, TimeUnit.SECONDS);
    }

    @Override
    public String getToken(String token) {
        String k = String.format("%s:%s", PREFIX_TOKEN, token);
        return this.stringRedisTemplate.opsForValue().get(k);
    }

    @Override
    public String genShortToken(String tradeNo, String candidateId) {
        String token = UUID.randomUUID().toString().replace("-", "").toLowerCase();
        String k = String.format("%s:%s", PREFIX_TOKEN, token);
        this.stringRedisTemplate.opsForValue().set(k, String.format("%s_%s", tradeNo, candidateId), DURATION_TEN_MINUTE, TimeUnit.SECONDS);
        return token;
    }

    @Override
    public void runLockTask(String key, Integer expireSeconds, Runnable func) {
        String lockKey = String.format("%s:%s", PREFIX_LOCK, key);
        String lockValue = UUID.randomUUID().toString();
        Boolean locked = this.stringRedisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, expireSeconds, TimeUnit.SECONDS);
        if (Boolean.TRUE.equals(locked)) {
            try {
                log.info("任务 - {} 执行", key);
                func.run();
            } finally {
                // 释放锁，确保只释放自己的锁
                if (lockValue.equals(this.stringRedisTemplate.opsForValue().get(lockKey))) {
                    this.stringRedisTemplate.delete(lockKey);
                    log.info("任务 - {} 完毕", key);
                }
            }
        } else {
            log.info("任务 - {} 未获得锁 跳过", key);
        }
    }

    @Override
    public TemplateDataStatisticResponse getTemplateDataStatistic(String projectId) {
        String k = String.format("%s:%s", PREFIX_STATISTIC, projectId);
        return this.statisticRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setTemplateDataStatistic(String projectId, TemplateDataStatisticResponse statisticResponse) {
        String k = String.format("%s:%s", PREFIX_STATISTIC, projectId);
        this.statisticRedisTemplate.opsForValue().set(k, statisticResponse, DURATION_FIVE_MINUTE,  TimeUnit.SECONDS);
    }

    @Override
    public CandidateStatisticResponse getCandidateStatistic(String companyId) {
        String k = String.format("%s:%s", PREFIX_STATISTIC, companyId);
        return this.candidateStatisticRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setCandidateStatistic(String companyId, CandidateStatisticResponse statisticResponse) {
        String k = String.format("%s:%s", PREFIX_STATISTIC, companyId);
        this.candidateStatisticRedisTemplate.opsForValue().set(k, statisticResponse, DURATION_FIVE_MINUTE,  TimeUnit.SECONDS);
    }
}
