package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Getter
@Setter
public class ProjectSupplementaryConfigRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Length(min = 24, max = 24, message = "24位id")
    @Schema(description = "模版Id")
    private String supplementaryTemplateId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "开启补充")
    private Boolean supplementary;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "开始时间")
    private Date supplementaryStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "结束时间")
    private Date supplementaryEndAt;


}
