package com.iguokao.supernova.registration.excel;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.iguokao.supernova.common.enums.CredentialCategoryEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.service.CandidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class CandidateItemListener extends AnalysisEventListener<CandidateItem> {

    private final String companyId;
    private final String projectId;
    private final String subjectId;
    private final List<String> existsLoginName;
    private final List<String> existsReg;
    private final List<ExcelErrResponse> errList;
    private final CandidateService candidateService;

    private final List<Candidate> list = new ArrayList<>();
    private final List<Candidate> listOnlyReg = new ArrayList<>();

    @Override
    public void invoke(CandidateItem item, AnalysisContext analysisContext) {
        Integer currentRow = analysisContext.readRowHolder().getRowIndex();

        //可能是第一行 表头
        if(null != item.getFullName() && (item.getFullName().contains("姓名") || item.getFullName().contains("红色为必填项"))){
            return;
        }

        try {
            // 重要的处理部分
            Candidate candidate  = new Candidate();
            candidate.setCompanyId(new ObjectId(companyId));

            //处理姓名
            if(null == item.getFullName() || item.getFullName().length() > 30){
                handleErr(currentRow, "考生姓名，不能为空也不能超过 30个字符");
                return;
            }
            candidate.setFullName(handleInput(item.getFullName()));

            //处理证件类型
            //为空
            if(null == item.getIdCardType()){
                handleErr(currentRow, "证件类型不能为空");
                return;
            }
            else {
                //身份证
                if(item.getIdCardType().equals(CredentialCategoryEnum.ID_CARD.getText())){
                    //处理身份证号
                    if(!StringUtil.validIdCard(item.getIdCardNum())){
                        handleErr(currentRow, "身份证格式有误");
                        return;
                    }
                    candidate.setIdCardType(CredentialCategoryEnum.ID_CARD.getCode());
                }
                //护照
                else if(item.getIdCardType().equals(CredentialCategoryEnum.PASSPORT.getText())){
                    candidate.setIdCardType(CredentialCategoryEnum.PASSPORT.getCode());
                }
                else if(item.getIdCardType().equals(CredentialCategoryEnum.TAIWAN_PERMIT.getText())){
                    candidate.setIdCardType(CredentialCategoryEnum.TAIWAN_PERMIT.getCode());
                }
                else if(item.getIdCardType().equals(CredentialCategoryEnum.HK_PERMIT.getText())){
                    candidate.setIdCardType(CredentialCategoryEnum.HK_PERMIT.getCode());
                }
                else if(item.getIdCardType().equals(CredentialCategoryEnum.MILITARY_ID.getText())){
                    candidate.setIdCardType(CredentialCategoryEnum.MILITARY_ID.getCode());
                }
                //枚举类型没有命中
                else {
                    handleErr(currentRow, "证件类型错误");
                    return;
                }

            }

            candidate.setIdCardNum(item.getIdCardNum());

            //处理手机号
            if(null != item.getMobile()){
                candidate.setMobile(handleInput(item.getMobile()));
                if(!StringUtil.validMobile(candidate.getMobile())){
                    handleErr(currentRow, "手机号格式错误");
                    return;
                }
            }

            //处理邮件地址
            if(null != item.getEmail()){
                candidate.setEmail(handleInput(item.getEmail()));
                if(!StringUtil.validEmail(candidate.getEmail())){
                    handleErr(currentRow, "邮件地址格式错误");
                    return;
                }
            }

            //处理城市
            if(null != item.getCity()){
                if( item.getCity().length() > 60 || !StringUtil.checkCity(item.getCity()) ){
                    handleErr(currentRow, "城市名称错误" + item.getCity());
                    return;
                }
                else{
                    candidate.setCity(handleInput(item.getCity()));
                }
            }
            else {
                candidate.setCity(null);
            }


            if(candidate.getIdCardType().equals(CredentialCategoryEnum.ID_CARD.getCode())){
                //根据身份证号处理性别
                if(Integer.parseInt(candidate.getIdCardNum().substring(16,17)) %2 == 0){
                    candidate.setGender(2);
                }
                else {
                    //男
                    candidate.setGender(1);
                }
            }


            candidate.setLoginName(candidate.getMobile());
            candidate.setLoginPassword(StringUtil.genPassword(8));

            //处理登录账号
            if(existsLoginName.contains(candidate.getLoginName())){
                if(!existsReg.contains(candidate.getMobile())){
                    listOnlyReg.add(candidate);
                }
                else {
                    handleErr(currentRow, "考生已经注册过该科目");
                    return;
                }

            }
            else {
                list.add(candidate);
            }

            existsLoginName.add(candidate.getLoginName());
            existsReg.add(candidate.getMobile());
        }
        catch (Exception e){
            handleErr(currentRow,"导入数据非法");
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if(list.size() > 10000){
            throw new ServiceException(ExceptionEnum.CANDIDATE_BEYOND_10000);
        }
        if(!list.isEmpty()){
            this.candidateService.addAllByExcel(list,projectId,subjectId);
            log.info("考生导入完成");
        }
        if(!listOnlyReg.isEmpty()){
            this.candidateService.addAllOnlyRegByExcel(listOnlyReg,projectId,subjectId,companyId);
            log.info("考生导入完成");
        }
    }

    private void handleErr(Integer i, String info){
        ExcelErrResponse errBody = new ExcelErrResponse();
        errBody.setRow(i + 1);
        errBody.setError("请检查：第" + (i + 1) + "行数据," + info);
        errList.add(errBody);
    }

    private String handleInput(String s){
        if(null == s){
            return null;
        }
        s = s.trim();//去掉前后空格
        s = s.replaceAll(" ","");//去除中间空格
        return s;
    }
}