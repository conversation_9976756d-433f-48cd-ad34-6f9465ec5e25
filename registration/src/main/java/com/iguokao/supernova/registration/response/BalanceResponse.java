package com.iguokao.supernova.registration.response;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BalanceResponse {
    private String hfBalance;

    private String tradeAmount; // 总订单金额
    private String orderCount; // 总交易笔数
    private String fee; // 总手续费
    private String amount; // 总交易金额
    private Integer wxOrderCount;
    private Integer aliOrderCount;

    private String notSettledFee; // 未结算手续费
    private String notSettledOrderCount; // 未结算交易笔数
    private String notSettledTradeAmount; // 未结算订单金额
    private String notSettledAmount; // 未结算交易金额

    private String settledFee;
    private String settledOrderCount;
    private String settledAmount;
    private String settledTradeAmount;
}
/*



已结算金额
已结算笔数

未结算金额
未结算笔数

已结算手续费
未结算手续费

汇富账户金额
* */
