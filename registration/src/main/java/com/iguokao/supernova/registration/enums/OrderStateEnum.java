package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum OrderStateEnum implements BaseEnum {
    NOT_PAY(0, "未支付"),
    PAYING(1, "支付中"),
    SUCCESS(2, "支付成功"),
    REFUNDING(3, "退款中"),
    CANCEL(4, "支付取消"),
    FAILED(6, "支付失败"),
    REFUNDED(7, "已经退款"),
    REFUNDED_PART(8, "部分退款"),
    FINISHED(9, "订单完成（不可退款）"),

    WITHDRAW_REQUEST(92, "提现请求"),
    WITHDRAW_CONFIRM(93, "提现待确认"),
    WITHDRAWING(94, "提现中"),
    WITHDRAW_CANCEL(97, "提现取消"),
    WITHDRAWN(98, "提现完成"),

    ;

    OrderStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
