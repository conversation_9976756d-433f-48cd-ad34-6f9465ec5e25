package com.iguokao.supernova.registration;

import com.iguokao.supernova.registration.document.Order;
import com.iguokao.supernova.registration.document.TemplateData;
import com.iguokao.supernova.registration.enums.OrderStateEnum;
import com.iguokao.supernova.registration.repository.CandidateRepository;
import com.iguokao.supernova.registration.repository.OrderRepository;
import com.iguokao.supernova.registration.repository.TemplateDataRepository;
import com.iguokao.supernova.registration.service.HfService;
import com.iguokao.supernova.registration.service.OrderService;
import com.iguokao.supernova.registration.service.TemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@EnableScheduling
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
@RequiredArgsConstructor
public class RegistrationApplication implements CommandLineRunner {

	public static void main(String[] args) {
		SpringApplication.run(RegistrationApplication.class, args);
	}

	private final CandidateRepository candidateRepository;
	private final OrderService orderService;
	private final TemplateDataRepository templateDataRepository;
	private final HfService hfService;


	@Value("${app.service.wechat.app-id}")
	private String appId;

	@Value("${app.service.wechat.app-secret}")
	private String appSecret;


	@Override
	public void run(String... args) throws Exception {
//		String d = this.hfService.fee("****************", "680.00");
//		System.out.println(d);
//		this.orderService.changeState("250415172821001201", OrderStateEnum.REFUNDED, null, "{\"acct_id\":\"*********\",\"acct_split_bunch\":{\"acct_infos\":[{\"acct_date\":\"********\",\"acct_id\":\"*********\",\"div_amt\":\"0.10\",\"huifu_id\":\"****************\"}],\"fee_acct_date\":\"\",\"fee_acct_id\":\"*********\",\"fee_amt\":\"0.00\",\"fee_huifu_id\":\"****************\"},\"actual_ref_amt\":\"0.10\",\"ali_response\":{\"refund_detail_item_list\":[{\"amount\":\"0.10\",\"fund_channel\":\"ALIPAYACCOUNT\"}]},\"atu_sub_mer_id\":\"****************\",\"auth_no\":\"\",\"bagent_id\":\"****************\",\"bank_code\":\"10000\",\"bank_desc\":\"Success\",\"bank_message\":\"Success\",\"bank_seq_id\":\"1_3143\",\"base_acct_id\":\"*********\",\"batch_id\":\"250415\",\"combinedpay_data\":[],\"combinedpay_fee_amt\":\"0.00\",\"devs_id\":\"\",\"end_time\":\"********172854\",\"hf_seq_id\":\"003100TOP4A250415172853P533ac13672700000\",\"huifu_id\":\"****************\",\"mer_name\":\"北京国聘考试技术有限公司\",\"mer_oper_id\":\"\",\"mer_ord_id\":\"r250415172821001201_3143\",\"mer_priv\":\"\",\"mypaytsf_discount\":\"0.00\",\"need_big_object\":false,\"ord_amt\":\"0.10\",\"order_id\":\"********1728530TOP4_AL2358898065\",\"org_allowance_type\":0,\"org_fee_amt\":\"0.00\",\"org_fee_flag\":2,\"org_fee_rec_type\":1,\"org_ord_amt\":\"0.10\",\"org_req_date\":\"********\",\"org_req_seq_id\":\"250415172821001201\",\"org_term_ord_id\":\"250415172821001201\",\"out_ord_id\":\"\",\"party_order_id\":\"03222504156290158914358\",\"pay_channel\":\"A\",\"product_id\":\"PAYUN\",\"ref_cut\":\"1\",\"ref_no\":\"1728531_3143\",\"remark\":\"\",\"req_date\":\"********\",\"req_seq_id\":\"r250415172821001201_3143\",\"sub_resp_code\":\"00000000\",\"sub_resp_desc\":\"交易成功\",\"sys_id\":\"****************\",\"total_ref_amt\":\"0.10\",\"total_ref_fee_amt\":\"0.00\",\"trans_date\":\"********\",\"trans_finish_time\":\"********172854\",\"trans_stat\":\"S\",\"trans_time\":\"172853\",\"trans_type\":\"TRANS_REFUND\"} ");
//				String[] s = new String[]{"250410152943000849"};
//		this.orderService.changeOrderListState(s, OrderStateEnum.FINISHED);
//		this.orderService.withdrawConfirm("250409154731000760");

//		StatisticResponse res = this.cacheService.getStatistic("67b3002a587021692bc19fff");
//		System.out.println(res);
//		AuthorizationCodeResponse res = wechatRemote.authorization_code(appId, appSecret, "0812e20000syMT16xD200IXn2H22e20e");
//		System.out.println(res);
//		PublishRequest request = new PublishRequest();
//		request.setPayload("tttt");
//		request.setQos(1);
//		request.setTopic("hello");
//		PublishResponse res =  this.emqRemote.publish(request);
//		System.out.println(res.getId());
//		String res = wechatPayService.decrypt( "transaction","AwN5nNn4TQRt", "VsYcya7Z0NAIkCOY1nRe68WwMMapq0XsAElUxhkf9sYylEJxcI9YRA6NpQT6VC8YaI7O7SMyu05Kwx2K9piQg0hSe3dZRV0cbjh4GJ3NCsOBZ+QZw4JYmCeddeMKG1ePZAHlma46eyOcVDn6tTOHSFin73mpRVmBJ/RMzRhK10zfmFs1LQwcLpwSZOJ1n0SNGr4scnpwivSgEX2brb5KgMJThiY8xON7Ukeg85s0z08GWSX4Glpc8m96sUMriyzRNZl6OOfNqIqoi5nxC0ba1Reez9mVsEkzS92VITYtq7i5ICg2SZkRsU9opZF4iXvxQZgH5OZJjVvk72jT1oVqNP4qptb6WXm73Ve9Q9xmgkreVBrI3f9JyGmqy6tUuXiee4qgxU93wm5hCpacaT0H3PMDlzzeVi7L5StXLg1PR0Tr07PizrvjBVZltijUmUmGdu3GCGcTYZU/+B9lXqI1St0ZfIjWCdTtR7a4rvlLDa05vtMaabdQCZVf/JZv8ymJH/uRvicGBZCDXmKkFLEukYBU5OfYu9KqNi9zfrFCP6rlcoFDodM77Hu1wsL7+LQSI7WBYIub");
//		System.out.println(res);

//		this.wechatPayService.refund("250116155357000008","250116155357000008R1", "4200002559202501084166633999", 10, 100);

//		String orderNo = cacheService.getOrderNumber();
//		String url = this.wechatPayService.prePay(orderNo, 100, "test");
//		System.out.println(url);
//		System.out.println(orderNo);;
////		this.wechatPayService.query("out_trade_no_001");
//		System.out.println(url);;

//		Candidate candidate = this.candidateRepository.findById(new ObjectId("6704fd8095352e0fef9cb975"))
//				.orElse(null);
//		System.out.println(candidate.get_id().toString());
//		System.out.println(candidate.getLoginName());
//		System.out.println(candidate.genLoginSign(1728814965L));
//		System.out.println(new ObjectId());

//		CompanyConfig config = this.cacheService.getCompanyConfig("abcd");
//		Jackson2JsonRedisSerializer<CompanyConfig> serializer = new Jackson2JsonRedisSerializer<>(CompanyConfig.class);
//		serializer.serialize(config);

//		CompanyConfig config = this.companyConfigRepository.findBySn("gtrl").orElse(null);
//		this.cacheService.setCompanyConfig(config);

	}
}
