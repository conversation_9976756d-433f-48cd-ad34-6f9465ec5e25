package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.registration.document.Project;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ProjectService {
    String add(Project project);

    Project getById(String projectId);

    void update(Project project);
    boolean online(String projectId);

    Tuple2<List<Project>, Integer> getPage(String companyId, String name, Pageable pageable);

    int countShortUrl(String projectId);

    void genShortUrl(String projectId, String sn);
}
