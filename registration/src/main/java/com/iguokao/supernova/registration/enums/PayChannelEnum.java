package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum PayChannelEnum implements BaseEnum {

    WECHAT(1, "微信支付"),
    ALIPAY(2, "支付宝"),
            ;

    PayChannelEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
