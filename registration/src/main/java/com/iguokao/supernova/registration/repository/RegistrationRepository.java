package com.iguokao.supernova.registration.repository;

import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.Registration;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface RegistrationRepository extends MongoRepository<Registration, ObjectId> {
    int countBySubjectId(ObjectId subjectId);
    int countBySubjectIdAndPassed(ObjectId subjectId, Boolean passed);
    int countBySubjectIdAndConfirmState(ObjectId subjectId,int confirmState);
    int countBySubjectIdAndConfirmStateNot(ObjectId subjectId,int confirmState);
    int countByProjectId(ObjectId projectId);
    int countByProjectIdAndShortCodeReady(ObjectId projectId, Boolean ready);
    int countByProjectIdAndCity(ObjectId projectId, String city);
    int countByProjectIdAndCityAndConfirmState(ObjectId projectId, String city, int confirmState);
    int countByProjectIdAndSubjectId(ObjectId projectId, ObjectId subjectId);
    int countByProjectIdAndConfirmState(ObjectId projectId, int confirmState);
    int countByProjectIdAndEmailStateGreaterThan(ObjectId projectId, int state);
    int countByProjectIdAndSmsStateGreaterThan(ObjectId projectId, int state);

    @Query(value = "{projectId:?0}", fields = "{candidateId: 1}") // 只查询 candidateId
    List<Registration> findDistinctCandidateId(ObjectId projectId);


    Registration findRegistrationByProjectIdAndCandidateIdAndSubjectId(ObjectId projectId, ObjectId candidateId, ObjectId subjectId);

    List<Registration> findByCandidateIdAndProjectId(ObjectId candidateId, ObjectId projectId);
    List<Registration> findByCandidateIdAndProjectIdIn(ObjectId candidateId, List<ObjectId> list);
    List<Registration> findBy_idIn(List<ObjectId> list);
    List<Registration> findByCandidateId(ObjectId candidateId);
    List<Registration> findByProjectIdAndCandidateId(ObjectId projectId, ObjectId candidateId);
    List<Registration> findBySubjectId(ObjectId subjectId);
    List<Registration> findBySubjectIdAndPassed(ObjectId subjectId, Boolean passed);
    List<Registration> findBySubjectIdAndConfirmState(ObjectId subjectId, Integer confirmState);
    List<Registration> findBySubjectIdAndConfirmStateNot(ObjectId subjectId, int confirmState);
    List<Registration> findByProjectIdAndShortCodeReady(ObjectId projectId, Boolean ready);

    void removeRegistrationByProjectIdAndCandidateIdAndSubjectId(ObjectId projectId, ObjectId candidateId, ObjectId subjectId);

    void deleteByCandidateId(ObjectId candidateId);
}
