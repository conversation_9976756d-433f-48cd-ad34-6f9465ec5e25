package com.iguokao.supernova.registration.config;

import com.iguokao.supernova.common.remote.*;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.remote.EmqRemote;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.okhttp.OkHttpClient;
import feign.slf4j.Slf4jLogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RemoteConfig {
    @Bean
    public ExamRemote examRemote(@Value("${app.service.exam}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(ExamRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(ExamRemote.class, service);
    }

    @Bean
    public StoneExamRemote stoneExamRemote(@Value("${app.service.stone-exam}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(StoneExamRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(StoneExamRemote.class, service);
    }

    @Bean
    public ManagementRemote managementRemote(@Value("${app.service.management}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(ManagementRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(ManagementRemote.class, service);
    }

    @Bean
    public ReportRemote reportRemote(@Value("${app.service.report}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(ReportRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(ReportRemote.class, service);
    }

    @Bean
    public CloopenRemote cloopenRemote(@Value("${app.cloopen.api}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(CloopenRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(CloopenRemote.class, service);
    }

    @Bean
    public TaskRemote taskRemote(@Value("${app.service.task}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(TaskRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(TaskRemote.class, service);
    }

    @Bean
    public EmqRemote emqRemote(@Value("${app.emq.api}") String service,
                               @Value("${app.emq.app-key}") String appKey,
                               @Value("${app.emq.app-secret}") String appSecret) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(EmqRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .requestInterceptor(template -> {
                    String authHeader = "Basic " + StringUtil.getBase64Str(String.format("%s:%s", appKey, appSecret));;
                    template.header("Authorization", authHeader);
                })
                .target(EmqRemote.class, service);
    }


    @Bean
    public WechatRemote wechatRemote(@Value("${app.service.wechat.api}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(WechatRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(WechatRemote.class, service);
    }
}
