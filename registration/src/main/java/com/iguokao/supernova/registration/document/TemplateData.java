package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Document("template_data")
public class TemplateData extends BaseDocument {
    @Indexed(name = "candidateId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId candidateId;

    @Indexed(name = "projectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId templateId;

    private Boolean imported = false;

    private String successTradeNo;
    private Date paidAt;
    private Boolean passed;
    private Boolean finished = false;
    private List<ApproveItem> approveList = new ArrayList<>();
    private Integer gender;

    private Boolean refund = false;

    private String fullName;
    private String mobile;
    private String idCardNum;
    private Integer idCardType;
    private String email;
    private String avatar;
    private String location;
    private String city;

    private Date birthday;
    // 开票前
    private Boolean invoice;
    private Integer invoiceType;
    private Boolean invoicePersonal;
    private String invoiceTitle;
    private String invoiceTin;
    // 开票后
    private String invoicePrice;
    private String invoiceLink;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private List<ObjectId> subjectIdList = new ArrayList<>();

    private String remark1;
    private String remark2;
    private String remark3;

    private List<TemplateItemData> templateItemList = new ArrayList<>();
    private List<TemplateItemData> supplementaryItemList = new ArrayList<>();

    private List<SmsHistory> smsHistoryList = new ArrayList<>();
}
