package com.iguokao.supernova.registration.repository;

import com.iguokao.supernova.registration.document.Action;
import com.iguokao.supernova.registration.document.Candidate;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface CandidateRepository extends MongoRepository<Candidate, ObjectId> {
    int countByCompanyIdAndLoginName(ObjectId companyId, String loginName);

    Optional<Candidate> findByCompanyIdAndLoginName(ObjectId companyId, String loginName);
    Optional<Candidate> findByCompanyIdAndIdCardNum(ObjectId companyId, String idCardNum);

    List<Candidate> findBy_idIn(List<ObjectId> list);
    List<Candidate> findByCompanyId(ObjectId companyId);

    Candidate findByMobile(String mobile);
}
