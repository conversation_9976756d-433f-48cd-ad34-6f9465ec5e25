package com.iguokao.supernova.registration.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;


@Getter
@Setter
public class CandidateRegisterRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登录用户名")
    @Length(min = 24, max = 24, message = "companyId 错误")
    private String companyId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登陆密码")
    @Length(min = 3, max = 60, message = "登陆密码长度超限")
    private String loginPassword;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生姓名")
    @Length(min = 1, max = 100, message = "考生姓名长度超限")
    private String fullName;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生手机号")
    @Length(min = 11, max = 11, message = "考生手机号长度超限")
    private String mobile;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "电子邮件")
    @Length(min = 6, max = 100, message = "电子邮件长度超限")
    private String email;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "短信验证码")
    @Length(min = 4, max = 10, message = "短信验证码长度超限")
    private String smsCode;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "证件类型")
    @Min(value = 1, message = "证件类型错误")
    @Max(value = 10, message = "证件类型错误")
    private Integer idCardType;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "证件号码")
    @Length(min = 10, max = 30, message = "证件号码长度超限")
    private String idCardNum;
}
