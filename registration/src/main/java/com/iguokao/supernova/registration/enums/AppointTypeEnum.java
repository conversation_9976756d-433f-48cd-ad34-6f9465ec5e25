package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum AppointTypeEnum implements BaseEnum {
    APPOINT_NONE(0, "无需"),
    CONFIRM_ONLY(1, "仅确认"),
    CITY(2, "城市确认"),
    SITE(3, "考点确认"),
    ;

    AppointTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
