package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.registration.document.Balance;
import com.iguokao.supernova.registration.document.Order;
import com.iguokao.supernova.registration.document.Withdraw;
import com.iguokao.supernova.registration.enums.OrderStateEnum;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface OrderService {
    String add(String candidateId,  String companyId, String companySn, String projectId, List<String> subjectIdList, String source, String fullName, String mobile);

    Order getByTradeNo(String tradeNo);

    Order changeState(String tradeNo, OrderStateEnum state, Double feeAmount, String payload);
    void changeOrderListState(String[] tradeNoList, OrderStateEnum state);
    void addPayload(String tradeNo, String payload);
    String wxPrepay(String tradeNo, String openid, String companySn);

    String aliPrepay(String tradeNo, String companySn);

    Tuple2<List<Order>, Integer> page(String companyId, String projectId, String candidateId, Integer state, String fullName, String mobile, String source, Date startAt, Date endAt, Boolean withdraw, Pageable pageable);

    String successTradeNo(String projectId, String candidateId);

    void cancel();

    List<Order> successList(String projectId);

    void refund(String tradeNo, String candidateId);

    String hfBalance(String companySn);


    Balance balance(String companySn, String projectId);

    Withdraw withdrawOrder(String projectId, String operatorId, Date startAt, Date endAt);

    void withdrawConfirm(String tradeNo);
    void withdrawCancel(String tradeNo);

    void withdrawPass(String tradeNo, String withdrawPassword);


}
