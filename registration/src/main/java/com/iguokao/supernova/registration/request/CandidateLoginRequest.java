package com.iguokao.supernova.registration.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;


@Getter
@Setter
public class  CandidateLoginRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登录用户名")
    @Length(min = 3, max = 60, message = "用户名长度超限")
    private String loginName;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登陆密码")
    @Length(min = 3, max = 60, message = "密码长度超限")
    private String loginPassword;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "图形验证码字符串")
    private String imageCode;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "验证码的token")
    private String key;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "企业ID")
    @Length(min = 24, max = 24, message = "数据异常")
    private String companyId;
}
