package com.iguokao.supernova.registration.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ManagementRemote;
import com.iguokao.supernova.common.remote.WechatRemote;
import com.iguokao.supernova.common.response.AuthorizationCodeResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.enums.MessageTypeEnum;
import com.iguokao.supernova.registration.enums.OrderStateEnum;
import com.iguokao.supernova.registration.request.*;
import com.iguokao.supernova.registration.remote.HfCallbackResponse;
import com.iguokao.supernova.registration.response.BalanceResponse;
import com.iguokao.supernova.registration.response.WithdrawResponse;
import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.MqttService;
import com.iguokao.supernova.registration.service.OrderService;
import com.iguokao.supernova.registration.service.TemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/v1/pay")
@RequiredArgsConstructor
public class PayController {
    private final OrderService orderService;
    private final WechatRemote wechatRemote;
    private final CacheService cacheService;
    private final TemplateService templateService;
    private final MqttService mqttService;
    private final ManagementRemote managementRemote;
    private final JwtService jwtService;

    @Value("${app.wechat.app-id}")
    private String wechatAppId;

    @Value("${app.wechat.app-secret}")
    private String wechatAppSecret;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostMapping("/wx/prepay")
    @Operation(summary = "预支付")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> wxPrepay(@RequestBody PrePayRequest request) {
        String content = this.cacheService.getToken(request.getToken());
        if(content == null){
            throw new ServiceException(ExceptionEnum.TOKEN_EXPIRED);
        }
        AuthorizationCodeResponse res = this.wechatRemote.authorization_code(wechatAppId, wechatAppSecret, request.getWxCode());
        String payInfo = this.orderService.wxPrepay(request.getTradeNo(), res.getOpenid(), request.getCompanySn());
        return RestResponse.success(payInfo);
    }

    @PostMapping(value = "/huifu/pay/callback", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    @Operation(summary = "支付回调")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> payCallback(@RequestParam Map<String, String> request) throws JsonProcessingException {
        HfCallbackResponse res = objectMapper.readValue(objectMapper.writeValueAsString(request), HfCallbackResponse.class);
//        System.out.println(objectMapper.writeValueAsString(res));
//        if (request.get("resp_data") != null && request.get("resp_data").length() > 100) {
        if (res.getResp_data() != null && res.getResp_data().length() > 100) {
            String json = res.getResp_data();
            JsonNode jsonNode = objectMapper.readTree(json);
            if(jsonNode.get("trans_stat").textValue().equals("S")){
                Double fee = Double.parseDouble(jsonNode.get("fee_amount").textValue());
                Order order = this.orderService.changeState(jsonNode.get("mer_ord_id").textValue(), OrderStateEnum.SUCCESS, fee, json);
                if(order != null){
                    Message message = new Message();
                    message.setCandidateId(order.getCandidateId());
                    message.setType(MessageTypeEnum.PAY_SUCCESS.getCode());
                    this.mqttService.sendMessage(message);
                }
            } else {
                this.orderService.changeState(jsonNode.get("mer_ord_id").textValue(), OrderStateEnum.FAILED, null, json);
            }
        } else {
            log.error(request.toString());
        }
        return RestResponse.success();
    }

    @PostMapping(value = "/huifu/refund/callback", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    @Operation(summary = "退款回调")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> refundCallback(@RequestParam Map<String, String> request, HttpServletRequest httpServletRequest) throws JsonProcessingException {

        System.out.println(objectMapper.writeValueAsString(request));
        HfCallbackResponse res = objectMapper.readValue(objectMapper.writeValueAsString(request), HfCallbackResponse.class);
//        System.out.println(objectMapper.writeValueAsString(res));
        if (res.getResp_data() != null && res.getResp_data().length() > 100) {
            String json = res.getResp_data();
            JsonNode jsonNode = objectMapper.readTree(json);
            if(jsonNode.get("trans_stat").textValue().equals("S")){
                this.orderService.changeState(jsonNode.get("org_req_seq_id").textValue(), OrderStateEnum.REFUNDED, null, json);
            } else {
                this.orderService.addPayload(jsonNode.get("org_req_seq_id").textValue(), json);
            }
        } else {
            log.error(request.toString());
        }
        return RestResponse.success();
    }

    @PostMapping(value = "/huifu/withdraw/callback", consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    @Operation(summary = "提现回调")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> withdrawCallback(@RequestParam Map<String, String> request) throws JsonProcessingException {
        System.out.println(objectMapper.writeValueAsString(request));
        HfCallbackResponse res = objectMapper.readValue(objectMapper.writeValueAsString(request), HfCallbackResponse.class);
//        System.out.println(objectMapper.writeValueAsString(res));
        if (res.getResp_data() != null && res.getResp_data().length() > 100) {
            String json = res.getResp_data();
            JsonNode jsonNode = objectMapper.readTree(json);
            if(jsonNode.get("trans_status").textValue().equals("P")){
                this.orderService.changeState(jsonNode.get("req_seq_id").textValue(), OrderStateEnum.WITHDRAWING, null, json);
            } else if(jsonNode.get("trans_status").textValue().equals("S")){
                this.orderService.changeState(jsonNode.get("req_seq_id").textValue(), OrderStateEnum.WITHDRAWN, null, json);
            }  else {
                this.orderService.addPayload(jsonNode.get("req_seq_id").textValue(), json);
            }
        } else {
            log.error(request.toString());
        }
        return RestResponse.success();
    }/*
    {
  "resp_desc": "成功调用",
  "resp_code": "10000",
  "sign": "VtwSUj9gmPXjGCZaOH9Wxl0di8Cn7S4H62GaLzT7tHveNsXnGQFFrTx7tYx0KaFrov3LI2bMHZTcY1aVwGb7xidGUnxyeal+lXeflGUpR9YgZBJHjvauTnj2NadUxynZB893QM2d+WcunJwtOftS/n2kXhpiSFeJQStNK1/BxRZQsp0TNDnKPOjSCfy2oJFuOO7HNWe4Z7o9co/UBKlTE3fvD2FI+AKfDTzyjOHx6zw3/F3piWEcvfe3ZQW3G2VNoKETzNOd33U++GTW3ISQPfJevueo0tAG3U4h5H/7fz11sqroSh/TlCP4yVZxzGOyMzxtwHTf327RSo0rkV/9vQ==",
  "resp_data": "{\"bank_code\":\"********\",\"sub_resp_code\":\"********\",\"mer_ord_id\":\"250410143949000839\",\"cash_amt\":\"1.00\",\"hf_seq_id\":\"003000TOP3A250410144937P746ac139cf900000\",\"channel_status\":\"P\",\"sys_id\":\"****************\",\"bank_account_mask\":\"020009*********1955\",\"acct_id\":\"A41347737\",\"fee_acct_id\":\"A41347737\",\"acct_status\":\"S\",\"req_seq_id\":\"250410143949000839\",\"req_date\":\"********\",\"bank_name\":\"中国工商银行\",\"msg_type\":\"02\",\"payment_hf_seq_id\":\"\",\"huifu_id\":\"****************\",\"acct_date\":\"********\",\"fee_huifu_id\":\"****************\",\"sub_resp_desc\":\"部分成功\",\"trans_status\":\"P\",\"fee_amt\":\"0.00\",\"trans_type\":\"ENCHASHMENT\"}"
}

{"resp_desc":"成功调用","resp_code":"10000","sign":"Tpz5/YTSUMNRjmF2IlDpxKjNKeuFE9lGdpgNt4O6NbEisJhoVIGtDWDUVvZPWho6RRwtgj8SfwD0tA+YJ1Sp/mQofQtPOK7VnquID8g8MDomGoBWpqIwAiu4RwlnOS4fYiCY4+GfiC55ZG63hlD2FpmLAnooCX1nUWnaaITtcHPUelhyobPOttgOxGf0REUSxI1mqn1tJQqbxUJ1O/7XEt11NW22dT80h20WOfgdWd+uXQKR5lMmPk1x3TwcsLE1P/g4I3tIX7ezX5O1i1RVMxstXKqNBOhQsrlkc4DVK6cyTsUBLXJ6E3pZ8g3fVMj1SdzZRUb8ncd8/c1hUTVwbA==","resp_data":"{\"bank_code\":\"********\",\"sub_resp_code\":\"********\",\"mer_ord_id\":\"250410153407000854\",\"cash_amt\":\"0.10\",\"hf_seq_id\":\"003000TOP3A250410153654P420ac139cf900000\",\"channel_status\":\"S\",\"sys_id\":\"****************\",\"bank_account_mask\":\"020009*********1955\",\"acct_id\":\"A41347737\",\"fee_acct_id\":\"A41347737\",\"acct_status\":\"S\",\"req_seq_id\":\"250410153407000854\",\"req_date\":\"********\",\"bank_name\":\"中国工商银行\",\"msg_type\":\"01\",\"payment_hf_seq_id\":\"\",\"huifu_id\":\"****************\",\"acct_date\":\"********\",\"fee_huifu_id\":\"****************\",\"sub_resp_desc\":\"成功\",\"trans_status\":\"S\",\"fee_amt\":\"0.00\",\"trans_type\":\"ENCHASHMENT\"}"}

    */

    /*
    支付宝
    {
  "resp_desc": "交易成功[000]",
  "resp_code": "********",
  "sign": "2ulC/gaHK2tiz0YFTkPWu5woFLMkM1H0vorpOuq5ifILHO7qMuX+zF9hnXdNzp0L4hfC7DBiZkgDv0HrLtQUcriBBEMuoAKW8FT/JpdzTGPEQRQoNCiEZfS0Ry+p9iMr/XI/Xlic2UJhi4pmVWpbCRwgjEK9A0+M+yUPT6D2hd3lJMSn7loJAeO1C9wM2l2Px3ElLTnZK4mOycHXXcPKs2XybiutNmFl4oWNsrsGSo27/R61qeQqlXzaUHO+6nHqP0kPQVD10UD4GzWAhlBDUIcG0xBX4Lbm+FirFVxA+gPqfsj7Jzp8k6mgYQ4GU62drduPOo8o1UjYoN2r1RJiHg==",
  "resp_data": "{\"acct_id\":\"A41347737\",\"acct_split_bunch\":{\"acct_infos\":[{\"acct_date\":\"********\",\"acct_id\":\"A41347737\",\"div_amt\":\"0.30\",\"huifu_id\":\"****************\"}],\"fee_acct_date\":\"\",\"fee_acct_id\":\"A41347737\",\"fee_amt\":\"0.00\",\"fee_huifu_id\":\"****************\"},\"actual_ref_amt\":\"0.30\",\"ali_response\":{\"refund_detail_item_list\":[{\"amount\":\"0.30\",\"fund_channel\":\"ALIPAYACCOUNT\"}]},\"atu_sub_mer_id\":\"****************\",\"auth_no\":\"\",\"bagent_id\":\"****************\",\"bank_code\":\"10000\",\"bank_desc\":\"Success\",\"bank_message\":\"Success\",\"bank_seq_id\":\"000292\",\"base_acct_id\":\"A41347737\",\"batch_id\":\"250331\",\"combinedpay_data\":[],\"combinedpay_fee_amt\":\"0.00\",\"devs_id\":\"\",\"end_time\":\"********172525\",\"hf_seq_id\":\"003100TOP3B250331172523P999ac13689500000\",\"huifu_id\":\"****************\",\"mer_name\":\"北京国聘考试技术有限公司\",\"mer_oper_id\":\"\",\"mer_ord_id\":\"r250328102226000292\",\"mer_priv\":\"\",\"mypaytsf_discount\":\"0.00\",\"need_big_object\":false,\"ord_amt\":\"0.30\",\"order_id\":\"********1725230TOP3_BL2724596604\",\"org_allowance_type\":0,\"org_fee_amt\":\"0.00\",\"org_fee_flag\":2,\"org_fee_rec_type\":1,\"org_ord_amt\":\"0.30\",\"org_req_date\":\"********\",\"org_req_seq_id\":\"250328102226000292\",\"org_term_ord_id\":\"250328102226000292\",\"out_ord_id\":\"\",\"party_order_id\":\"03212503283734663811299\",\"pay_channel\":\"A\",\"product_id\":\"PAYUN\",\"ref_cut\":\"1\",\"ref_no\":\"************\",\"remark\":\"\",\"req_date\":\"********\",\"req_seq_id\":\"r250328102226000292\",\"sub_resp_code\":\"********\",\"sub_resp_desc\":\"交易成功\",\"sys_id\":\"****************\",\"total_ref_amt\":\"0.30\",\"total_ref_fee_amt\":\"0.00\",\"trans_date\":\"********\",\"trans_finish_time\":\"********172525\",\"trans_stat\":\"S\",\"trans_time\":\"172523\",\"trans_type\":\"TRANS_REFUND\"}"
}
     */

    @PreAuthorize("hasAuthority('ROLE_FINANCE')")
    @PostMapping("/refund")
    @Operation(summary = "退款")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> refund(@RequestBody RefundRequest request) {
        String operatorId = jwtService.currentOperatorId();
        RestResponse<String> res = managementRemote.smsCode(operatorId);
        if(res.getCode() == 0){
            if(res.getData().startsWith("R") && res.getData().contains(request.getSmsCode())){
                this.orderService.refund(request.getTradeNo(), request.getCandidateId());
            } else {
                throw new ServiceException(BaseExceptionEnum.SMS_CODE_NOT_VALID);
            }
        } else {
            throw new ServiceException(BaseExceptionEnum.REMOTE_COMMUNICATION_FAILED);
        }
        log.info("退款成功 添加审计信息");
        Project project = this.cacheService.getProject(request.getProjectId());
        AuditItem item = new AuditItem(project.getCompanyId().toString(), AuditTypeEnum.REGISTRATION_REFUND.getCode(), request);
        item.setOperatorId(operatorId);
        this.managementRemote.audit(item);
        return RestResponse.success();
    }

    @PostMapping("/balance")
    @Operation(summary = "企业余额")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<BalanceResponse> balance(@RequestBody BalanceRequest request) {
        String hfBalance = this.orderService.hfBalance(request.getCompanySn());
        Balance balance = this.orderService.balance(request.getCompanySn(), request.getProjectId());
        BalanceResponse res = new BalanceResponse();
        BeanUtils.copyProperties(balance, res);
        res.setHfBalance(hfBalance);
        return RestResponse.success(res);
    }

//    @PreAuthorize("hasAuthority('ROLE_FINANCE')")
    @PostMapping("/withdraw/order/add")
    @Operation(summary = "提现 信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = WithdrawResponse.class)))
    public RestResponse<WithdrawResponse> withdrawOrderAdd(@RequestBody WithdrawRequest request) {
        String operatorId = jwtService.currentOperatorId();
        Withdraw withdraw = this.orderService.withdrawOrder(request.getProjectId(), operatorId, request.getStartAt(), request.getEndAt());
        return RestResponse.success(WithdrawResponse.of(withdraw));
    }

    @GetMapping("/withdraw/order/cancel/{tradeNo}")
    @Operation(summary = "提现 取消")
    @ApiResponse(content = @Content(schema = @Schema(implementation = WithdrawResponse.class)))
    public RestResponse<String> withdrawCancel(@PathVariable String tradeNo) {
        jwtService.currentOperatorId();
        this.orderService.withdrawCancel(tradeNo);
        return RestResponse.success();
    }

    @PostMapping("/withdraw/confirm")
    @Operation(summary = "提现 确认")
    @ApiResponse(content = @Content(schema = @Schema(implementation = WithdrawResponse.class)))
    public RestResponse<String> withdrawConfirm(@RequestBody WithdrawConfirmRequest request) {
        String operatorId = jwtService.currentOperatorId();
        RestResponse<String> res = managementRemote.smsCode(operatorId);
        if(res.getCode() == 0){
            if(res.getData().startsWith("W") && res.getData().contains(request.getSmsCode())){
                this.orderService.withdrawConfirm(request.getTradeNo());
                return RestResponse.success();
            } else {
                throw new ServiceException(BaseExceptionEnum.SMS_CODE_NOT_VALID);
            }
        } else {
            throw new ServiceException(BaseExceptionEnum.REMOTE_COMMUNICATION_FAILED);
        }
    }

    @PostMapping("/withdraw/pass")
    @Operation(summary = "提现")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> withdraw(@RequestBody WithdrawPassRequest request) {
        this.orderService.withdrawPass(request.getTradeNo(), request.getWithdrawPassword());
        return RestResponse.success();
    }
/*
 {
  "resp_code": "10000",
  "resp_desc": "成功调用",
  "sign": "kP0YeT3BxIRpc0SsrCLRh6ZCKDk/nvJhCCIHb4MSuJacEvkeK8H7QPG+uGegrC92HWtHxFYeoh2csH4sy7IQG9EC1Dh0aKC+r5j/GXb014ZqQBksVMtsSXJpe5JcbMIHfLgdgfWn3SvMsI8Co2BxseC6mC4VQHZDcAc0upoJwqhpsibzm1d+dFVKkLUL5veO1uNhXhPdYx6VSQJbj9MFCh5Vrkf1yc/lvLqGLm5E1ZJjHovMJLNrIBodGkKXO8vB7JtWoTqDN+0eVZFjp9nVO1b7F3CtMABarcpw+v13FjOMAmpkF1ZiYOaHFvHGIaWCVbOvLn2dVXQZsWTCwQvWmA==",
  "resp_data": "{\"acct_split_bunch\":{\"acct_infos\":[{\"div_amt\":\"753.00\",\"huifu_id\":\"****************\"}],\"fee_amt\":\"2.86\",\"fee_huifu_id\":\"****************\"},\"acct_stat\":\"I\",\"alipay_response\":{\"app_id\":\"\",\"buyer_id\":\"****************\",\"buyer_logon_id\":\"189****0308\"},\"bagent_id\":\"****************\",\"bank_code\":\"TRADE_SUCCESS\",\"bank_message\":\"TRADE_SUCCESS\",\"bank_order_no\":\"********22001427671459048436\",\"channel_type\":\"N\",\"debit_flag\":\"1\",\"end_time\":\"**************\",\"fee_amount\":\"2.86\",\"fee_amt\":\"2.86\",\"fee_flag\":2,\"fee_rec_type\":\"1\",\"fq_mer_discount_flag\":0,\"gate_id\":\"Dw\",\"hf_seq_id\":\"00290TOP1GR210317094952P693ac13262200000\",\"huifu_id\":\"****************\",\"is_delay_acct\":\"1\",\"is_div\":\"0\",\"mer_name\":\"重庆数链通科技有限公司\",\"mer_ord_id\":\"22577563652260773965\",\"mypaytsf_discount\":\"0.00\",\"notify_type\":1,\"org_auth_no\":\"\",\"org_huifu_seq_id\":\"\",\"org_trans_date\":\"\",\"out_ord_id\":\"********22001427671459048436\",\"out_trans_id\":\"********22001427671459048436\",\"party_order_id\":\"03242103173539288303515\",\"pay_scene\":\"02\",\"posp_seq_id\":\"03242103173539288303515\",\"product_id\":\"YMFZS\",\"req_date\":\"********\",\"req_seq_id\":\"07387152320091631003250860684265\",\"resp_code\":\"********\",\"resp_desc\":\"交易成功\",\"risk_check_info\":{\"client_ip\":\"\",\"latitude\":\"\",\"lc\":\"\"},\"settlement_amt\":\"753.00\",\"sub_resp_code\":\"********\",\"sub_resp_desc\":\"交易成功\",\"subsidy_stat\":\"I\",\"sys_id\":\"****************\",\"trade_type\":\"A_NATIVE\",\"trans_amt\":\"753.00\",\"trans_date\":\"********\",\"trans_stat\":\"S\",\"trans_time\":\"094952\",\"trans_type\":\"A_NATIVE\"}"
}
 */



}
