package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.Registration;
import com.iguokao.supernova.registration.document.Score;
import jakarta.servlet.http.HttpServletRequest;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface CandidateService {
    ImageCode getImageCode();

    void sendRegisterSms(String companyId, ImageCode imageCode, String mobile, HttpServletRequest request);
    void sendLoginSms(String companyId, ImageCode imageCode, String mobile, HttpServletRequest request);

    String register(Candidate candidate, String smsCode);

    String smsLogin(ImageCode imageCode, String companyId, String loginName, String loginPassword);

    String login(ImageCode imageCode, String companyId, String loginName, String loginPassword);
    String login(String companyId, String loginName, Long expiredAt, String signature);

    List<String> getExistsLoginNameByCompanyId(String companyId);

    void addOne(Candidate candidate);

    void edit(String sn ,String projectId ,String candidateId, String fullName, String mobile, String email, String idCardNum, Integer idCardType, Integer gender);
    long delete(String projectId, String subjectId, List<String> candidateIdList);

    void addAllByExcel(List<Candidate> list, String projectId, String subjectId);
    void addAllOnlyRegByExcel(List<Candidate> listOnlyReg, String projectId, String subjectId, String companyId);

    Tuple2<List<Registration>, Integer> getRegistrationPage(String projectId, String subjectId, String fullName, String mobile, Integer emailState, Integer smsState, Integer confirm, Pageable pageable);

    Candidate getById(String candidateId);

    List<Candidate> getImportCandidate(String subjectId, Integer type);

    List<Score> getScoreList(String candidateId);

    void updateInfo(Candidate candidate);

    Candidate getCandidateByLoginName(String projectId, String mobile);
    Candidate getCandidateByFullName(String projectId, String fullName);

    String smsCodeLogin(String companyId, String loginName, String mobile, String smsCode, String imageCodeKey);

    void passwordChange( String candidateId, String mobile, String password);

    Tuple2<List<Candidate>, Integer> page( String companyId, String fullName, String mobile, Pageable pageable);

    void addByGroup(Candidate candidate, String projectId, String subjectId);

    String clearGroupByMobile(String mobile);

    Candidate findByMobile(String mobile);
}
