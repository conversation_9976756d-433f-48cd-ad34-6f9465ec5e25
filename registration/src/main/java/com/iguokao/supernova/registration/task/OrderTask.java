package com.iguokao.supernova.registration.task;


import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderTask {
    private final OrderService orderService;
    private final CacheService cacheService;

    @Scheduled(cron = "0 0 * * * ?")
    public void cancel(){
        log.info("定时任务 - 取消订单 {}", new Date());
        this.cacheService.runLockTask("order_cancel", 10, this.orderService::cancel);
    }
}
