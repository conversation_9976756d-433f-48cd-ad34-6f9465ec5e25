package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Getter
@Setter
@Document
public class Action extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId subjectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId candidateId;

    private Integer type;
    private String text;
    private Integer value;
}
