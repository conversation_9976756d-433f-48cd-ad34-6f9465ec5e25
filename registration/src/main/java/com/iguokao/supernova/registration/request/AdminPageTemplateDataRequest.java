package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class AdminPageTemplateDataRequest extends PageRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Length(min = 24, max = 24, message = "24位id")
    private String subjectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "模板id")
    @Length(min = 24, max = 24, message = "24位id")
    private String templateId;

    private Integer gender;

    @Schema(description = "名字")
    private String fullName;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "证件号")
    private String idCardNum;

    @Schema(description = "通过状态")
    private Integer passState;

    @Schema(description = "是否完成资料的填写")
    private Boolean finished;

    @Schema(description = "是否已经进行付款")
    private Boolean payed;

    @Schema(description = "自定义1")
    private String remark1;

    @Schema(description = "自定义2")
    private String remark2;

    @Schema(description = "自定义3")
    private String remark3;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signUpStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signUpEnd;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date birthdayStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date birthdayEnd;

    private String city;

    private Boolean imported;

    private Map<String, String> searchList = new HashMap<>();
}
