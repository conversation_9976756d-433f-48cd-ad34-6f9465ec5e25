package com.iguokao.supernova.registration.controller;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.request.ImportCandidateRequest;
import com.iguokao.supernova.common.response.ImportCandidateResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.registration.document.Candidate;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.common.request.CompanyConfigRegisterParam;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import com.iguokao.supernova.registration.request.NotificationResultRequest;
import com.iguokao.supernova.registration.response.ImportSubjectResponse;
import com.iguokao.supernova.registration.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/remote")
@RequiredArgsConstructor
public class RemoteController {
    private final CompanyConfigService companyConfigService;
    private final EmailService emailService;
    private final CandidateService candidateService;
    private final CacheService cacheService;
    private final ProjectService projectService;

    @PostMapping("/company/register")
    @Operation(summary = "开通企业")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> reviewCandidateList(@RequestBody CompanyConfigRegisterParam param) {
        CompanyConfig config = new CompanyConfig();
        BeanUtils.copyProperties(param, config);
        config.set_id(new ObjectId(param.getCompanyId()));
        companyConfigService.register(config);
        return RestResponse.success();
    }

    @PostMapping("/notification/result")
    @Operation(summary = "发送通知结果")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> noticeResult(@RequestBody NotificationResultRequest request) {
        this.emailService.sendResult(request.getCandidateId(),request.getBatchName(),request.getType(),request.getSendResult(),request.getErr());
        return RestResponse.success();
    }

    @PostMapping("/candidate/import")
    @Operation(summary = "可以导入的项目")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ImportSubjectResponse.class))))
    public RestResponse<List<ImportCandidateResponse>> importList(@RequestBody ImportCandidateRequest request) {
        List<Candidate> list = this.candidateService.getImportCandidate(request.getSubjectId(), request.getType());
        List<ImportCandidateResponse> res = new ArrayList<>();
        list.forEach(candidate -> {
            ImportCandidateResponse item = new ImportCandidateResponse();
            BeanUtils.copyProperties(candidate, item);
            res.add(item);
        });
        return RestResponse.success(res);
    }

    @PostMapping("/admission/generate")
    @Operation(summary = "生成准考证")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ImportSubjectResponse.class))))
    public RestResponse<List<ImportCandidateResponse>> genAdmissionCard(@RequestBody List<AdmissionCard> list) {
        this.cacheService.setAdmissionCard(list);
        return RestResponse.success();
    }

    @GetMapping("/project/list/{companyId}")
    @Operation(summary = "生成准考证")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ImportSubjectResponse.class))))
    public RestResponse<Map<String, String>> projectList(@PathVariable String companyId) {
        Pageable pageable =  PageRequest.of(0, 100);
        Tuple2<List<Project>, Integer> page = this.projectService.getPage(companyId, null, pageable);
        Map<String, String> map = new HashMap<>();
        page.first().forEach(project -> {
            map.put(project.get_id().toString(), project.getName());
        });
        return RestResponse.success(map);
    }
}
