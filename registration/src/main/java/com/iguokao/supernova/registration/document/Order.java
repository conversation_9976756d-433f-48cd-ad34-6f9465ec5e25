package com.iguokao.supernova.registration.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Document
@CompoundIndexes({
        @CompoundIndex(def = "{'projectId': -1, 'candidateId': -1}", name = "projectId_candidateId_index")
})
public class Order extends BaseDocument {
    @Indexed(name = "companyId_index")
    private ObjectId companyId;

    @Indexed(name = "candidateId_index")
    private ObjectId candidateId;

    private ObjectId projectId;

    @Indexed(name = "tradeNo_index")
    private String tradeNo; // 订单号

    private Integer state; // 订单状态

    private List<PayItem> payItemList = new ArrayList<>();

    private String fullName;
    private String mobile;

    private String hfId;
    private String hfSeqId;
    private String payOrderId;
    private String description;
    private Double feeAmount = .0;
    private Double tradeAmount = .0; // 订单金额 单位分
    private Double amount = .0; // 支付金额 单位元
    private String source; // 支付源
    private List<String> resultList = new ArrayList<>();

    private Date paidAt;
    private Date refundedAt;
}
