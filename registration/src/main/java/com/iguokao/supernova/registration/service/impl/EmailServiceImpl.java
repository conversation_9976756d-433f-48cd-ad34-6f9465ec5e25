package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.TaskRemote;
import com.iguokao.supernova.common.request.EmailTaskAddRequest;
import com.iguokao.supernova.common.request.SmsTaskAddRequest;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.enums.NotificationSendStateEnum;
import com.iguokao.supernova.registration.repository.*;
import com.iguokao.supernova.registration.service.EmailService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;

import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {
    private final EmailRepository emailRepository;
    private final ProjectRepository projectRepository;
    private final NotificationBatchRepository notificationBatchRepository;
    private final CandidateRepository candidateRepository;
    private final RegistrationRepository registrationRepository;
    private final MongoTemplate mongoTemplate;
    private final TaskRemote taskRemote;

    @Value(value = "${app.service.task-callback}")
    private String notificationPrefix;

    @Value(value = "${app.appointment-link}")
    private String appointmentLink;

    @Value(value = "${app.short-url}")
    private String shortUrlPreFix;

    @Override
    public void add(String companyId, String name, String title, String content) {
        Email email = new Email();
        email.setCompanyId(new ObjectId(companyId));
        email.setName(name);
        email.setTitle(title);
        email.setContent(content);
        emailRepository.insert(email);
    }

    @Override
    public void remove(String emailId) {
        emailRepository.deleteById(new ObjectId(emailId));
    }

    @Override
    public void edit(String emailId, String name, String title, String content) {
        Email email = this.emailRepository.findById(new ObjectId(emailId)).orElseThrow(
                () -> new ServiceException(ExceptionEnum.EMAIL_NOT_EXIST));

        email.setName(name);
        email.setTitle(title);
        email.setContent(content);
        emailRepository.save(email);
    }

    @Override
    public List<Email> list(String companyId) {
        return emailRepository.findByCompanyId(new ObjectId(companyId));
    }

    @Override
    public void sendResult(String candidateId, String batchId, Integer type, Integer sendResult, String err) {

        Query query = new Query(Criteria.where("_id").is(new ObjectId(batchId)));
        Update update = new Update()
                .inc("completed", 1);
        //发送成功
        if(sendResult.equals(NotificationSendStateEnum.SEND_STATE_SUCCESS.getCode())){
            update.inc("success", 1);
        }
        mongoTemplate.updateFirst(query, update, NotificationBatch.class);

        //邮件
        String property = "emailState";
        // 短信
        if(type == 2){
            property = "smsState";
        }


        //更新考生字段
        Query query2 = new Query(Criteria.where("_id").is(new ObjectId(candidateId)));
        Update update2 = new Update().set(property, sendResult);
        mongoTemplate.updateFirst(query2, update2, Registration.class);

        // 发送失败 单独记录
        if(sendResult.equals(NotificationSendStateEnum.SEND_STATE_FAILED.getCode())){
            NotificationFailDetail detail = new NotificationFailDetail();
            detail.setCandidateId(candidateId);
            detail.setErrInfo(err);
            detail.setState(sendResult);
            Query query3 = new Query(Criteria.where("_id").is(new ObjectId(batchId)));
            Update update3 = new Update()
                    .addToSet("failList", detail);
            mongoTemplate.updateFirst(query3, update3, NotificationBatch.class);
        }
    }

    @Override
    public void sendGroup(String sn, String projectId, String tempId, List<String> candidateIdList, String testEmailAddress) {
        List<ObjectId> idList = candidateIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        List<Registration> registrationList = this.registrationRepository.findBy_idIn(idList);
        //发送测试邮件逻辑
        if(null != testEmailAddress && registrationList.size() == 1){
            registrationList.get(0).setEmail(testEmailAddress);
        }
        Long expireTime = this.handleLoginExpireTime(projectId);
        handleMail(projectId,tempId,registrationList,sn,expireTime);
    }

    @Override
    public void sendAll(String sn, String projectId, String subjectId, String tempId, Integer confirmState, Integer emailState) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt", "_id"))
                .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)));

        if (null != subjectId) {
            query = query.addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        }

        if (null != confirmState) {
            query = query.addCriteria(Criteria.where("confirmState").is(confirmState));
        }
        if (null != emailState) {
            query = query.addCriteria(Criteria.where("emailState").is(emailState));
        }

        List<Registration> registrationList = this.mongoTemplate.find(query, Registration.class);
        if(!registrationList.isEmpty()){
            Long expireTime = this.handleLoginExpireTime(projectId);
            handleMail(projectId,tempId,registrationList,sn,expireTime);
        }
    }

    @Override
    public void sendSmsGroup(String sn, String projectId, String tempId, List<String> candidateIdList) {
        List<ObjectId> idList = candidateIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        List<Registration> registrationList = this.registrationRepository.findBy_idIn(idList);
        Long expireTime = this.handleLoginExpireTime(projectId);
        handleSms(projectId,tempId,registrationList,sn,expireTime);
    }

    @Override
    public void sendSmsAll(String sn, String projectId, String subjectId, String tempId, Integer confirmState, Integer smsState) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "_id"))
                .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)));

        if (null != subjectId) {
            query = query.addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        }
        if (null != confirmState) {
            query = query.addCriteria(Criteria.where("confirmState").is(confirmState));
        }
        if (null != smsState) {
            query = query.addCriteria(Criteria.where("smsState").is(smsState));
        }

        List<Registration> registrationList = this.mongoTemplate.find(query, Registration.class);

        Long expireTime = this.handleLoginExpireTime(projectId);
        handleSms(projectId,tempId,registrationList,sn,expireTime);
    }

    void handleMail(String projectId, String emailTempId, List<Registration> registrationList, String sn, Long expireTime){
        Email email = this.emailRepository.findById(new ObjectId(emailTempId)).orElseThrow(
                () -> new ServiceException(ExceptionEnum.EMAIL_NOT_EXIST));

        Project project = this.projectRepository.findById(new ObjectId(projectId)).orElseThrow(
                () -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));

        email.setContent(email.getContent().replaceAll("&lt;","<"));
        email.setContent(email.getContent().replaceAll("&gt;",">"));

        List<Registration> emailSuccessCandidateList = new ArrayList<>();
        NotificationBatch batch = handleBatch(projectId,registrationList,emailSuccessCandidateList, 1);

        //筛选完毕，开始发送
        for(Registration registration : emailSuccessCandidateList){
            sendMail(registration,email,project,batch,sn,expireTime);
        }
    }

    void sendMail(Registration registration, Email email, Project project, NotificationBatch batch, String sn, Long expireTime){

        Candidate candidate = new Candidate();
        candidate.setCompanyId(project.getCompanyId());
        candidate.set_id(registration.getCandidateId());
        candidate.setLoginName(registration.getMobile());

        //String aUrl = appointmentLink + sn + "/" + registration.getMobile() + "/" + expireTime + "/" + candidate.genLoginSign(expireTime);
        String bUrl = shortUrlPreFix + registration.getShortCode();
        String newContent = email.getContent().replaceAll("<--姓名-->",registration.getFullName());
        newContent = newContent.replaceAll("<--笔试预约地址-->",bUrl);

        String totalMailContent = "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">"
                + "<html xmlns=\"http://www.w3.org/1999/xhtml\">"
                + "\r\n<head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=gb2312\" />\r\n<title></title>\r\n"
                + "<style>\r\n"
                + "body,pre,div {font-family: \"Microsoft YaHei\", Arial; padding:0px 0px; margin:0px 0px;}\r\n"
                + ".NoticeTool {background-color:orange;color:white;font-weight:bold;font-family:\"Microsoft YaHei\", \"Arial Black\", Verdana, 黑体, Arial;font-size:12.5pt;text-decoration:none;}\r\n"
                + ".NoticeTool a, .NoticeTool a:link, .NoticeTool a:hover, .NoticeTool a:visited, .NoticeTool a:active {display:block;width:100%;height:100%;text-decoration:none;}\r\n"
                + "</style>\r\n"
                + "</head>\r\n<body>\r\n" + newContent
                + "</body>"
                + "</html>";

        EmailTaskAddRequest addRequest = new EmailTaskAddRequest();
        addRequest.setFrom("国考云" + "<<EMAIL>>");
        addRequest.setEmail(registration.getEmail());
        addRequest.setContent(totalMailContent);
        addRequest.setSubject(email.getTitle());
        addRequest.setCandidateId(registration.get_id().toString());
        addRequest.setBatchName(batch.get_id().toString());
        addRequest.setUrl(notificationPrefix + "/api/v1/remote/notification/result");

        taskRemote.addTask(addRequest);
    }

    void handleSms(String projectId, String tempId, List<Registration> candidateList, String sn, Long expireTime){
        //        2790615
        //        机考正式通知
        //        【国考云】{1}预约系统现已开放，请于{2}前完成预约，否则将失去考试机会。预约网址请戳：{3}
        //        【国考云】国家能源集团2025校园招聘笔试预约系统现已开放，请于11月3日12:00前完成预约，否则将失去笔试机会。预约网址：https://igky.cc/xxxxx
        //【国考云】您还未预约{1}，如不能到场，请点击放弃。预约网址：{2}

        //【国考云】您还未预约国家能源集团2025校园招聘笔试，如不能到场，请点击放弃。预约网址：https://igky.cc/xxxxx     2792038

        Project project = this.projectRepository.findById(new ObjectId(projectId)).orElseThrow(
                () -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));

        List<Registration> smsSuccessCandidateList = new ArrayList<>();
        NotificationBatch batch = handleBatch(projectId,candidateList,smsSuccessCandidateList,2);

        String ets = DateUtil.dateToStr(project.getAppointEndAt(),"MM月dd日HH:mm");
        //筛选完毕，开始发送
        for(Registration registration : smsSuccessCandidateList){

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("to",registration.getMobile());
            jsonObject.put("appId","8a216da8790d6de5017941af72ee0da3");
            jsonObject.put("templateId",tempId);

            Candidate candidate = new Candidate();
            candidate.setCompanyId(project.getCompanyId());
            candidate.set_id(registration.getCandidateId());
            candidate.setLoginName(registration.getMobile());
            //String aUrl = appointmentLink + sn + "/" + registration.getMobile() + "/" + expireTime + "/" + candidate.genLoginSign(expireTime);
            String bUrl = shortUrlPreFix + registration.getShortCode();
            if(tempId.equals("2792038")){
                jsonObject.put("datas", Arrays.asList(project.getName(),bUrl));
            }
            else {
                jsonObject.put("datas", Arrays.asList(project.getName(), ets,bUrl));
            }


            SmsTaskAddRequest addRequest = new SmsTaskAddRequest();
            addRequest.setContent(jsonObject.toString());
            addRequest.setCandidateId(registration.get_id().toString());
            addRequest.setBatchName(batch.get_id().toString());
            addRequest.setUrl(notificationPrefix + "/api/v1/remote/notification/result");
            taskRemote.addSmsTask(addRequest);
        }
    }

    private NotificationBatch handleBatch(String projectId,  List<Registration> registrationList, List<Registration> successList, Integer type){
        NotificationBatch batch = new NotificationBatch();
        batch.setProjectId(new ObjectId(projectId));
        batch.setType(type);
        batch.setTotal(registrationList.size());

        for (Registration registration : registrationList) {

            //发送邮件
            if(type.equals(1)){
                //邮箱地址非法
                if (!StringUtil.validEmail(registration.getEmail())) {
                    NotificationFailDetail detail = new NotificationFailDetail();
                    detail.setCandidateId(registration.getCandidateId().toString());
                    detail.setErrInfo("邮箱地址错误！");
                    batch.getFailList().add(detail);
                } else {
                    successList.add(registration);
                }
            }
            else {
                // 发送短信
                if (!StringUtil.validMobile(registration.getMobile())) {
                    NotificationFailDetail detail = new NotificationFailDetail();
                    detail.setCandidateId(registration.getCandidateId().toString());
                    detail.setErrInfo("手机号格式错误！");
                    batch.getFailList().add(detail);
                } else {
                    successList.add(registration);
                }
            }
        }

        return this.notificationBatchRepository.save(batch);
    }

    private Long handleLoginExpireTime (String projectId){
        Project project = this.projectRepository.findById(new ObjectId(projectId)).orElseThrow();
        if(project.getLoginExpireTime() == null || project.getLoginExpireTime() == 0){
            long et = new Date().getTime() + 86400*7*1000;
            project.setLoginExpireTime(et);
            this.projectRepository.save(project);
            return et;
        }
        else {
            return project.getLoginExpireTime();
        }
    }
}
