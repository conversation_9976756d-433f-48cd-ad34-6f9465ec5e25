package com.iguokao.supernova.registration.util;

import com.huifu.bspay.sdk.opps.core.BasePay;
import com.huifu.bspay.sdk.opps.core.config.MerConfig;
import com.huifu.bspay.sdk.opps.core.net.BasePayRequest;
import com.huifu.bspay.sdk.opps.core.utils.DateTools;
import com.huifu.bspay.sdk.opps.core.utils.SequenceTools;

import java.util.HashMap;
import java.util.Map;

/**
 * 应用场景 - 示例
 */
public class RefundDemo {

    // 汇付分配的产品号
    public static final String DEMO_PRODUCT_ID = "PAYUN";

    // 汇付分配的系统号
    public static final String DEMO_SYS_ID = "6666000164450471";

    // 服务商私钥，用于调用接口时进行签名
    public static final String DEMO_RSA_PRIVATE_KEY = "MIIEuwIBADANBgkqhkiG9w0BAQEFAASCBKUwggShAgEAAoIBAQDU2dy4nQjp7AgAsPNE/vJxxgd9KilYxnnSPcQAKQpqE1sNBjZcUVoggt6hf0FyVzul3q9Tv6XdW6G78F4bZ/x4yeX2f7jc+RPXUV1BmgW8MSroEbP4vt/nrPq38PXdfVJrJ3erSPuEu4dIZIfdYDfnhXSzZZnS/xaBIaoLTwDtjTl7eW/oxxRG/ffCfPCurU4lTTxoYl5rUYGS5VmTpp2VPeIY47QxoFBhLEQK2TSYE5i7i5kTcNbxaRUPNJ5HsEZSjYJwJhkkIbFnar+CXjW9SzSliORz1ej2/imiXvipUC66/7ZCNZrXLgsajdTPq7zo89rZ4mXWGGtwEKKRR7/5AgMBAAECggEAAoQ9GXsUoCYzHDj7XO+e7Uy879RBojC3v4Fziz69xm4wp4yETetrUDkwr/WgEAQRt57hcUrLVGNRNZAWHychVpdCgaHWDT/62OyeGH8dV4tR38w+L1Z1MYxh9+GtZbJyrBAoWGbX2T4M9fe0UIZ8jewnkOuf82nvLO5fz/VQ6qR5Ia5Wrh5yV5zLezfK/tAKfb8wUUXdMFqLril21bmNrIi3T533UQdkXI6kZVN0dROeIFdjqecDEVojdD5yM/27X4OBdmrDfYRqjhe86ktzm6DYJ60WDREru9dhIATETSMC/u57x9hXlECP2VSh86GE6v1zJkBpJPqYFHv8YgxYwQKBgQDcv+gi9NgWp/b3iROiJRYYKlISCQPmQnxusnCpfZCjkaOY9ElUap/FIDaJ6fMuc3EENQNnabM/qdiPQFLYG4I47jw4kP+lRtcTcc9Ha+fp81+zhA1Tv/eSI2h5duT2OzW6v1cSvjIBI3mE1RhgQ9JiWTRVb4zoJ0OrIV4tPEiYWQKBgQD21xDLBp/kecwLoohGsF20sAogltjXNLLEtG8BmKRXL+1lZYgTfiYpYIKztofFLb7YAQSES6NVxpiZPZxk2Ey5V82Jy0CVcCEt33DQRgx47Rr4T5uJpdYcvZOeKhd3EmZ219XMbLKhlWUqT4qsA4IVILMJOPzEVutDnDNWHaFwoQKBgQCv52BUkqSqkReUMq/pcgIEKZ0ScAqibnJ3R0vNU5hxLvmdRXQ1h21fCIdNKJa2ehkh0BixTCIg5fgCVrBtMqZ3NSfZr3l3O1bhULxqXksEOwIUq3JvPTZLRlnXCG305EOxVw1MizCPhbQsrB3rf7/izNCOEADJcZhTgphoFXSHqQKBgBjOQBfHEKZrPwAkU0GXU7NmxX3E6wGlgBmYcFdeuZUxcc/wxh3/XWJnGnto6nf8QuN+JrAM0ds62/b6ZA8WVY+CrVsdm93oqMztWW70uygj28P8bAsHKV82BfGtFTKYHMfwpMGoYsMuOj5oQ+1JZPculA9nNK+edIIU3mvXCnRBAn89FXKfpVBBI8cReXHv9twxqA4AvV8ATbuycPd2Ek1aoUNcAl0AOdIKRJAbZgHZUoVsTlRE5u8slTB7o6WJNAMYI7YPVKrQs7p3z6PPCZ3ZgjmXc91KnU81nay2F7WDpiRebQkofFN2hZenkKl9/3dsT/JAv3MmAPYXKxHGQMW2";
    // 汇付公钥，用于对汇付返回报文进行签名验证
    public static final String DEMO_RSA_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA9BQyqlNnyPz8z6aUMTM1sQbQs+nbPwtX6ERsmAQTonUhmFEM585oURWL4BDJ/XSTGmmUxD0UICak4H6LQdkSSVCWtGV5Oh23YZ8ITqPwWDHUWyfueCRKiAFu8QjizLsB6tFYeIAKmZURaszwC0Vz3Wol6mUaJqD+pwll7glPWul7c2Ih0FX+KnEQEvdjumETIm4Hv40lIB4i7gSgW+8Fj6AOorEXhaQsZ1l9SuhKF/X7XGCS/4AQno5XKdBlFOb+CRBIMdnRR0Sgx9GnYiCmZBDpWYrnQLEW8wSCnsscJV17rifGGN6ZZOWviDiOJkRmZmYp0OiMM3/5sn5bqTYw4QIDAQAB";
    public static void main(String[] args) throws Exception {
        // 1. 数据初始化，填入对应的商户配置
        MerConfig merConfig = new MerConfig();
        merConfig.setProcutId(DEMO_PRODUCT_ID);
        merConfig.setSysId(DEMO_SYS_ID);
        merConfig.setRsaPrivateKey(DEMO_RSA_PRIVATE_KEY);
        merConfig.setRsaPublicKey(DEMO_RSA_PUBLIC_KEY);
        BasePay.initWithMerConfig(merConfig);
        // 2.组装请求参数
        Map<String, Object> paramsInfo = new HashMap<>();
        paramsInfo.put("huifu_id", "6666000164450471");

        // 请求日期
        paramsInfo.put("req_date", DateTools.getCurrentDateYYYYMMDD());
        // 请求流水号
        paramsInfo.put("req_seq_id", SequenceTools.getReqSeqId32());
        Map<String, Object> response = BasePayRequest.requestBasePay("/v2/trade/acctpayment/balance/query", paramsInfo, null, false);;
        System.out.println(response);
    }
}