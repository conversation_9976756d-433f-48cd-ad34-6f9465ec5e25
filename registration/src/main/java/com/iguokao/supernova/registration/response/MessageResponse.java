package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.registration.document.Message;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class MessageResponse {
    private String messageId;
    private String candidateId;

    private String operatorId;

    private String content;
    private String fullName;
    private String mobile;
    private String idCardNum;
    private Integer type;

    private Boolean replied;
    private Boolean online = false;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date createdAt;

    public static MessageResponse of(Message obj){
        if(obj==null){
            return null;
        }
        MessageResponse res = new MessageResponse();
        BeanUtils.copyProperties(obj, res);
        res.setMessageId(obj.get_id().toString());
        res.setCandidateId(obj.getCandidateId().toString());
        if(obj.getOperatorId() != null){
            res.setOperatorId(obj.getOperatorId().toString());
        }
        return res;
    }

    public static List<MessageResponse> of(List<Message> list){
        if(list == null){
            return new ArrayList<>();
        }
        List<MessageResponse> res = new ArrayList<>();
        for(Message obj : list){
            res.add(of(obj));
        }
        return res;
    }

    public static List<MessageResponse> of(List<MessageResponse> list, List<String> onlineList){
        if(list == null){
            return new ArrayList<>();
        }
        for(MessageResponse obj : list){
            if (onlineList.contains(obj.getCandidateId())){
                obj.setOnline(true);
            }
        }
        return list;
    }
}
