package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.registration.document.NotificationBatch;
import com.iguokao.supernova.registration.repository.NotificationBatchRepository;
import com.iguokao.supernova.registration.service.NotificationBatchService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@RequiredArgsConstructor
public class NotificationBatchServiceImpl implements NotificationBatchService {
    private final NotificationBatchRepository notificationBatchRepository;
    private final MongoTemplate mongoTemplate;

    @Override
    public List<NotificationBatch> latestList(String projectId, Integer count) {
        Query query = new Query(Criteria.where("projectId").is(new ObjectId(projectId)))
                .with(Sort.by(Sort.Direction.DESC, "createdAt"))
                .limit(count);

        return mongoTemplate.find(query, NotificationBatch.class);
    }
}
