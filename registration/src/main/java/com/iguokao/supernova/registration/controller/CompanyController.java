package com.iguokao.supernova.registration.controller;

import com.iguokao.supernova.common.converter.ValidObjectId;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.registration.document.AnnouncementCategory;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.registration.document.SmsTemplate;
import com.iguokao.supernova.registration.request.AnnouncementCategoryRequest;
import com.iguokao.supernova.registration.request.CompanyConfigUpdateRequest;
import com.iguokao.supernova.registration.request.SmsTemplateAddRequest;
import com.iguokao.supernova.registration.response.CompanyConfigResponse;
import com.iguokao.supernova.registration.service.CompanyConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/company")
@RequiredArgsConstructor
public class CompanyController {
    private final CompanyConfigService companyConfigService;

    @GetMapping("/info/{companyId}")
    @Operation(summary = "企业信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = CompanyConfigResponse.class)))
    public RestResponse<CompanyConfigResponse> update(@PathVariable String companyId) {
        CompanyConfig config = this.companyConfigService.getById(companyId);
        return RestResponse.success(CompanyConfigResponse.of(config));
    }

    @PostMapping("/update")
    @Operation(summary = "企业设置更新")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<Long> update(@RequestBody CompanyConfigUpdateRequest param) {
        CompanyConfig config = new CompanyConfig();
        BeanUtils.copyProperties(param, config);
        config.set_id(new ObjectId(param.getCompanyId()));
        this.companyConfigService.update(config);
        return RestResponse.success();
    }

    @PostMapping("/announcement/category/update/{companyId}")
    @Operation(summary = "发布类型更新")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> categoryUpdate(@PathVariable String companyId,
                                               @RequestBody List<AnnouncementCategoryRequest> requestList) {
        List<AnnouncementCategory> list = new ArrayList<>();
        requestList.forEach(a -> {
            AnnouncementCategory ac = new AnnouncementCategory();
            ac.setCategoryId(a.getCategoryId() == null ? new ObjectId()
                    : new ObjectId(a.getCategoryId()));
            ac.setName(a.getName());
            list.add(ac);
        });
        this.companyConfigService.update(companyId, list);
        return RestResponse.success();
    }

    @PostMapping("/sms/template/add")
    @Operation(summary = "添加短信模版")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> smsAdd(@RequestBody SmsTemplateAddRequest request) {
        SmsTemplate smsTemplate = new SmsTemplate();
        smsTemplate.setSmsTemplateId(request.getSmsTemplateId());
        smsTemplate.setContent(request.getContent());
        this.companyConfigService.addSmsTemplate(request.getCompanyId(), smsTemplate);
        return RestResponse.success();
    }

    @GetMapping("/sms/template/remove/{companyId}/{smsTemplateId}")
    @Operation(summary = "删除短信模版")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> smsRemove(@ValidObjectId @PathVariable String companyId,
                                          @PathVariable String smsTemplateId) {
        this.companyConfigService.removeSmsTemplate(companyId, smsTemplateId);
        return RestResponse.success();
    }
}
