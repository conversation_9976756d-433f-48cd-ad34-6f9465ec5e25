package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

@Getter
@Setter
public class ScoreItem {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId subjectId;
    private Double score;
    private Boolean absent;
}
