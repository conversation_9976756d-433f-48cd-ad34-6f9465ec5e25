package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.DoubleFormatter;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ScoreResponse {
    private String projectId;
    private String projectName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startedAt;
    private List<ScoreItemResponse> scoreList = new ArrayList<>();

    @Getter
    @Setter
    public static class ScoreItemResponse{
        private String subjectId;
        private String subjectName;

        @JsonSerialize(using = DoubleFormatter.class)
        private Double score;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
        private Date startedAt;

        private Boolean passed;
        private Boolean absent;

    }

    public static ScoreResponse of(Score obj, Project project, List<Subject> subjectList){
        if(obj==null || project==null || subjectList.isEmpty()){
            return null;
        }
        ScoreResponse res = new ScoreResponse();
        res.setProjectId(project.get_id().toString());
        res.setProjectName(project.getName());
        res.setStartedAt(project.getStartAt());
        for(ScoreItem i : obj.getScoreList()){
            Subject subject = subjectList
                    .stream()
                    .filter(s -> s.get_id().equals(i.getSubjectId()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SUBJECT_NOT_FOUND));
            ScoreItemResponse item = new ScoreItemResponse();
            item.setScore(i.getScore());
            item.setSubjectName(subject.getName());
            item.setPassed(i.getScore() > subject.getPassScore());
            item.setStartedAt(subject.getStartAt());
            item.setSubjectId(subject.get_id().toString());
            item.setAbsent(i.getAbsent());
            res.getScoreList().add(item);
        }
        return res;
    }
}
