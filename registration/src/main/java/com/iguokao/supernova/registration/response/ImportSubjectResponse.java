package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.registration.document.CandidateCount;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ImportSubjectResponse {

    private String projectId;
    private String projectName;
    private String subjectId;
    private String subjectName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date projectStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date projectEndAt;

    private Integer appointType;

    private Integer candidateCount;
    private Integer confirmCount;
    private Integer confirmAndNotConfirmCount;
    private Integer selectedCount;

    public static ImportSubjectResponse of(Project project, Subject subject, CandidateCount count){
        if(project==null || subject==null){
            return null;
        }
        ImportSubjectResponse res = new ImportSubjectResponse();
        res.setProjectId(project.get_id().toString());
        res.setSubjectId(subject.get_id().toString());
        res.setProjectStartAt(subject.getStartAt());
        res.setProjectEndAt(subject.getEndAt());
        res.setSubjectName(subject.getName());
        res.setProjectName(project.getName());
        res.setAppointType(project.getAppointType());

        res.setCandidateCount(count.getCandidateCount());
        res.setConfirmCount(count.getConfirmCount());
        res.setConfirmAndNotConfirmCount(count.getConfirmAndNotConfirmCount());
        res.setSelectedCount(count.getSelectedCount());

        return res;
    }
}
