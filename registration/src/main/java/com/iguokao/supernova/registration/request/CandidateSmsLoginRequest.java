package com.iguokao.supernova.registration.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;


@Getter
@Setter
public class CandidateSmsLoginRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登录用户名")
    @Length(min = 3, max = 60, message = "用户名长度超限")
    private String loginName;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "手机号")
    @Length(min = 11, max = 11, message = "手机号长度超限")
    private String mobile;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "验证码")
    @Length(min = 4, max = 10, message = "验证码长度超限")
    private String smsCode;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "图片验证码key")
    private String imageCodeKey;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "企业ID")
    @Length(min = 24, max = 24, message = "企业id长度超限")
    private String companyId;
}
