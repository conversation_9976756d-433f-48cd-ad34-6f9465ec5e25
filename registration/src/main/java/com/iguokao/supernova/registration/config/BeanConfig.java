package com.iguokao.supernova.registration.config;

import com.huifu.bspay.sdk.opps.core.BasePay;
import com.huifu.bspay.sdk.opps.core.config.MerConfig;
import com.iguokao.supernova.common.exception.SecurityAccessDeniedHandler;
import com.iguokao.supernova.common.exception.SecurityAuthenticationEntryPoint;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.common.service.impl.JwtServiceImpl;
import com.iguokao.supernova.common.service.impl.OssServiceImpl;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.iai.v20200303.IaiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.filter.ForwardedHeaderFilter;

@Configuration
public class BeanConfig {

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityAccessDeniedHandler securityAccessDeniedHandler(){
        return new SecurityAccessDeniedHandler();
    }

    @Bean
    public SecurityAuthenticationEntryPoint securityAuthenticationEntryPoint(){
        return new SecurityAuthenticationEntryPoint();
    }

    @Bean
    ForwardedHeaderFilter forwardedHeaderFilter() {
        return new ForwardedHeaderFilter();
    }
    @Bean
    JwtService jwtService(){
        return new JwtServiceImpl();
    }

    @Bean
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(1000);
        executor.setThreadNamePrefix("Async-");
        executor.initialize();
        return executor;
    }

    @Bean
    Credential credential(@Value("${app.tencent.secret-id}") String secretId,
                          @Value("${app.tencent.secret-key}") String secretKey){
        return new Credential(secretId, secretKey);
    }

    @Bean
    IaiClient client(Credential cred){
        return new IaiClient(cred, "ap-beijing");
    }


    @Bean
    OssService ossService(Environment environment){
        return new OssServiceImpl(environment);
    }


    @Bean
    MerConfig merConfig(@Value("${app.dg.sys-id}") String sysId,
                        @Value("${app.dg.product-id}") String productId,
                        @Value("${app.dg.public-key}") String publicKey,
                        @Value("${app.dg.private-key}") String privateKey) throws Exception {
        BasePay.debug = false;
        BasePay.prodMode = BasePay.MODE_PROD;

        MerConfig merConfig = new MerConfig();
        merConfig.setProcutId(productId);
        merConfig.setSysId(sysId);
        merConfig.setRsaPrivateKey(privateKey);
        merConfig.setRsaPublicKey(publicKey);
//        //自定义超时时间
//        merConfig.setCustomSocketTimeout(DemoTestConstants.CUSTOM_SOCKET_TIMEOUT);
//        merConfig.setCustomConnectTimeout(DemoTestConstants.CUSTOM_CONNECT_TIMEOUT);
//        merConfig.setCustomConnectionRequestTimeout(DemoTestConstants.CUSTOM_CONNECTION_REQUEST_TIMEOUT);
        BasePay.initWithMerConfig(merConfig);
        return merConfig;
    }

}
