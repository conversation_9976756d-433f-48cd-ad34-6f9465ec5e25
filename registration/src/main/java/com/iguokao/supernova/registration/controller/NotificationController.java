package com.iguokao.supernova.registration.controller;

import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.registration.document.Email;
import com.iguokao.supernova.registration.document.NotificationBatch;
import com.iguokao.supernova.registration.request.*;
import com.iguokao.supernova.registration.response.EmailResponse;
import com.iguokao.supernova.registration.service.EmailService;
import com.iguokao.supernova.registration.service.NotificationBatchService;
import com.iguokao.supernova.registration.service.NotificationService;
import com.iguokao.supernova.registration.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/notification")
@RequiredArgsConstructor
public class NotificationController {

    private final NotificationService notificationService;
    private final ProjectService projectService;
    private final EmailService emailService;
    private final NotificationBatchService notificationBatchService;

    @PostMapping("/email/add")
    @Operation(summary = "添加邮件模板")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> emailAdd(@RequestBody EmailAddRequest request) {
        this.emailService.add(request.getCompanyId(),request.getName(),request.getTitle(),request.getContent());
        return RestResponse.success();
    }


    @PostMapping("/email/edit")
    @Operation(summary = "编辑邮件删除")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> emailEdit(@RequestBody EmailEditRequest request) {
        this.emailService.edit(request.getEmailId(),request.getName(),request.getTitle(),request.getContent());
        return RestResponse.success();
    }

    @GetMapping("/email/remove/{emailId}")
    @Operation(summary = "删除邮件模板")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> emailRemove(@PathVariable  String emailId) {
        this.emailService.remove(emailId);
        return RestResponse.success();
    }

    @GetMapping("/email/list/{companyId}")
    @Operation(summary = "编辑邮件列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<EmailResponse>> emailList(@PathVariable String companyId) {
        List<Email> emailList = this.emailService.list(companyId);
        return RestResponse.success(EmailResponse.of(emailList));
    }

    @Async
    @PostMapping("/email/send/projectId")
    @Operation(summary = "按项目发送邮件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendAll(@RequestBody SendAllRequest request) {
        this.emailService.sendAll(request.getSn(),request.getProjectId(),request.getSubjectId(),request.getTempId(),request.getConfirmState(),request.getEmailState());
        return RestResponse.success();
    }

    @PostMapping("/email/send/group")
    @Operation(summary = "批量发送邮件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendGroup(@RequestBody SendGroupRequest request) {
        this.emailService.sendGroup(request.getSn(),
                request.getProjectId(),
                request.getTempId(),
                request.getCandidateIdList(),
                request.getTestEmailAddress());
        return RestResponse.success();
    }

    @Async
    @PostMapping("/sms/send/projectId")
    @Operation(summary = "按项目发送短信")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendSmsAll(@RequestBody SendAllRequest request) {
        this.emailService.sendSmsAll(request.getSn(),request.getProjectId(),request.getSubjectId(),request.getTempId(),request.getConfirmState(),request.getSmsState());
        return RestResponse.success();
    }

    @PostMapping("/sms/send/group")
    @Operation(summary = "批量发送短信")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendSmsGroup(@RequestBody SendGroupRequest request) {
        this.emailService.sendSmsGroup(request.getSn(), request.getProjectId(),request.getTempId(),request.getCandidateIdList());
        return RestResponse.success();
    }

    @GetMapping("/batch/list/{projectId}")
    @Operation(summary = "时段-科目最新发送批次列表（9条）")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<NotificationBatch>> batchList(@PathVariable String projectId) {
        List<NotificationBatch> batchList = this.notificationBatchService.latestList(projectId, 9);
        return RestResponse.success(batchList);
    }
}
