package com.iguokao.supernova.registration.excel;

import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.registration.document.Order;
import com.iguokao.supernova.registration.enums.OrderStateEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class OrderExporter {

    public static List<List<String>> head() {
        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("考生姓名");

        List<String> head1 = new ArrayList<>();
        head1.add("考生电话");

        List<String> head2 = new ArrayList<>();
        head2.add("订单号");

        List<String> head3 = new ArrayList<>();
        head3.add("订单状态");

        List<String> head4 = new ArrayList<>();
        head4.add("订单内容");

        List<String> head5 = new ArrayList<>();
        head5.add("支付渠道订单号");

        List<String> head6 = new ArrayList<>();
        head6.add("支付渠道");
        List<String> head7 = new ArrayList<>();
        head7.add("订单金额");
        List<String> head8 = new ArrayList<>();
        head8.add("创建时间");


        list.add(head0);
        list.add(head1);
        list.add(head2);

        list.add(head3);
        list.add(head4);
        list.add(head5);

        list.add(head6);
        list.add(head7);
        list.add(head8);

        return list;
    }

    public static List<List<Object>> data(List<Order> list) {
        List<List<Object>> res = new ArrayList<>();
        for(Order order : list){
            List<Object> line = new ArrayList<>();
            line.add(order.getFullName());
            line.add(order.getMobile());
            line.add(order.getTradeNo());

            line.add(Objects.equals(order.getState(), OrderStateEnum.SUCCESS.getCode()) ? "支付成功" : "订单完成");
            line.add(order.getDescription());
            line.add(order.getPayOrderId());

            line.add(order.getSource());
            line.add(order.getAmount().toString());
            line.add(DateUtil.dateToStrStd(order.getCreatedAt()));
            res.add(line);
        }
        return res;
    }
}
