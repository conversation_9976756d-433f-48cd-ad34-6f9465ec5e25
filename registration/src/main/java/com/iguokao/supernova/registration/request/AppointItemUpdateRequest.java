package com.iguokao.supernova.registration.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class AppointItemUpdateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "配额id")
    @Length(min = 24, max = 24, message = "24位id")
    private String appointItemId;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "考站名称")
    private String siteName;

    @Schema(description = "配额")
    private Integer quota;

}
