package com.iguokao.supernova.registration.document;

import com.iguokao.supernova.common.util.DateUtil;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.Date;


@Getter
@Setter
public class Notification {
    private ObjectId notificationId;
    private ObjectId projectId;
    private Integer type;
    private String content;
    private Date createdAt;
    private Boolean read = false;

    public Notification() {
        this.notificationId = new ObjectId();
        this.setCreatedAt(new Date());
    }

    public static String appointProject(Project project){
        String start = DateUtil.dateToStr(project.getAppointStartAt(), "yyyy年MM月dd日hh时mm分");
        String end = DateUtil.dateToStr(project.getAppointStartAt(), "yyyy年MM月dd日hh时mm分");

        return String.format("您报名参加的 %s 项目，将于 %s - %s 开始预约，请及时预约以免错过考试。",
                project.getName(), start, end);
    }

    public static String appointCandidate(Project project){
        return String.format("您报名参加的 %s 项目，已经预约成功，请您准时参加考试。",
                project.getName());
    }
}
