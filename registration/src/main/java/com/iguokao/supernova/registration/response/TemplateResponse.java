package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.registration.document.Email;
import com.iguokao.supernova.registration.document.Template;
import com.iguokao.supernova.registration.document.TemplateGroup;
import com.iguokao.supernova.registration.document.TemplateItem;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class TemplateResponse {
    private String templateId;
    private String companyId;
    private String projectId;
    private String name;
    private Boolean uploadAvatar;
    private Boolean location;
    private List<TemplateGroupResponse> groupList;

    @Setter
    @Getter
    public static class TemplateGroupResponse {
        private String groupId;
        private String name;
        private Integer style;
        private Integer columns;
        private Integer sort;
        private List<TemplateItemResponse> itemList = new ArrayList<>();

        public static TemplateGroupResponse of(TemplateGroup obj){
            TemplateGroupResponse res = new TemplateGroupResponse();
            if(obj.getGroupId() != null){
                res.setGroupId(obj.getGroupId().toString());
            }
            BeanUtils.copyProperties(obj, res);
            res.setItemList(TemplateItemResponse.of(obj.getItemList()));
            return res;
        }

        public static List<TemplateGroupResponse> of(List<TemplateGroup> list){
            if(list == null){
                return new ArrayList<>();
            }
            List<TemplateGroupResponse> res = new ArrayList<>();
            for(TemplateGroup obj : list){
                res.add(of(obj));
            }
            return res;
        }
    }

    @Setter
    @Getter
    public static class TemplateItemResponse {
        private String itemId;
        private String name;
        private Integer type;
        private Boolean required;
        private Boolean search;
        private String info;
        private List<String> optionList;
        private List<String> optionName;

        public static TemplateItemResponse of(TemplateItem obj){
            TemplateItemResponse res = new TemplateItemResponse();
            BeanUtils.copyProperties(obj, res);
            res.setItemId(obj.getItemId().toString());
            return res;
        }

        public static List<TemplateItemResponse> of(List<TemplateItem> list){
            if(list == null){
                return new ArrayList<>();
            }
            List<TemplateItemResponse> res = new ArrayList<>();
            for(TemplateItem obj : list){
                res.add(of(obj));
            }
            return res;
        }
    }

    public static TemplateResponse of(Template obj){
        TemplateResponse res = new TemplateResponse();
        res.setTemplateId(obj.get_id().toString());
        res.setCompanyId(obj.getCompanyId().toString());
        if(obj.getProjectId() != null){
            res.setProjectId(obj.getProjectId().toString());
        }
        res.setGroupList(TemplateGroupResponse.of(obj.getGroupList()));
        BeanUtils.copyProperties(obj, res);
        return res;
    }

    public static List<TemplateResponse> of(List<Template> list){
        if(list == null){
            return new ArrayList<>();
        }
        List<TemplateResponse> res = new ArrayList<>();
        for(Template obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
