package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.DoubleFormatter;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Score;
import com.iguokao.supernova.registration.document.ScoreItem;
import com.iguokao.supernova.registration.document.Subject;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ScoreRequest {
    private String idCardNum;
}
