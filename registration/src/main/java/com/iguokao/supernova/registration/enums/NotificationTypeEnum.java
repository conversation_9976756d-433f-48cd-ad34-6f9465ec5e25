package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum NotificationTypeEnum implements BaseEnum {
    REG_SUCCESS(1, "报名成功"),
    PAY(2, "支付"),
    PAY_SUCCESS(3, "支付成功"),
    APPOINT(4, "预约"),
    APPOINT_SUCCESS(5, "预约成功"),
    ;

    NotificationTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
