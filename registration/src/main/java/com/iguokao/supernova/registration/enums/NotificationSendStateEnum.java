package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum NotificationSendStateEnum implements BaseEnum {

    SEND_STATE_PENDING(10, "等待中"),
    SEND_STATE_SUCCESS(20, "成功"),
    SEND_STATE_FAILED(30, "失败"),
            ;

    NotificationSendStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
