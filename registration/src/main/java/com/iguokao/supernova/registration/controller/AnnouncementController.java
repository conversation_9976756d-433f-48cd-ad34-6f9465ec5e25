package com.iguokao.supernova.registration.controller;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.registration.document.Announcement;
import com.iguokao.supernova.registration.request.AnnouncementAddRequest;
import com.iguokao.supernova.registration.request.AnnouncementUpdateRequest;
import com.iguokao.supernova.registration.request.PageAnnouncementRequest;
import com.iguokao.supernova.registration.response.AnnouncementResponse;
import com.iguokao.supernova.registration.response.CandidateResponse;
import com.iguokao.supernova.registration.response.PageAnnouncementResponse;
import com.iguokao.supernova.registration.service.AnnouncementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/announcement")
@RequiredArgsConstructor
public class AnnouncementController {
    private final AnnouncementService announcementService;

    @PostMapping("/page")
    @Operation(summary = "转出数据暂存")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageAnnouncementResponse.class)))
    public RestResponse<PageResponse<AnnouncementResponse>> page(@RequestBody PageAnnouncementRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Announcement>, Integer> page = this.announcementService.page(request.getCompanyId(), request.getCategoryId(), pageable);
        List<AnnouncementResponse> res = AnnouncementResponse.of(page.first());
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }

    @PostMapping("/add")
    @Operation(summary = "添加发布")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody AnnouncementAddRequest request) {
        Announcement announcement = new Announcement();
        BeanUtils.copyProperties(request, announcement);
        announcement.setCategoryId(new ObjectId(request.getCategoryId()));
        announcement.setCompanyId(new ObjectId(request.getCompanyId()));
        this.announcementService.add(announcement);
        return RestResponse.success();
    }

    @PostMapping("/update")
    @Operation(summary = "更新发布")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody AnnouncementUpdateRequest request) {
        Announcement announcement = new Announcement();
        BeanUtils.copyProperties(request, announcement);
        announcement.set_id(new ObjectId(request.getAnnouncementId()));
        this.announcementService.update(announcement);
        return RestResponse.success();
    }

    @GetMapping("/remove/{announcementId}")
    @Operation(summary = "删除发布")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<CandidateResponse> info(@PathVariable String announcementId) {
        this.announcementService.remove(announcementId);
        return RestResponse.success();
    }

}
