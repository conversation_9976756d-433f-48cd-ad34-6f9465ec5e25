package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.registration.document.Email;

import java.util.List;

public interface EmailService {

    void add(String companyId, String name, String title, String content);

    void remove(String emailId);

    void edit(String emailId, String name, String title, String content);

    List<Email> list(String companyId);

    void sendGroup(String sn, String projectId, String tempId, List<String> candidateIdList, String testEmailAddress);

    void sendResult(String candidateId, String batchName, Integer type, Integer sendResult, String err);

    void sendAll(String sn, String projectId, String subjectId, String tempId, Integer confirmState, Integer emailState);

    void sendSmsGroup(String sn, String projectId, String tempId, List<String> candidateIdList);

    void sendSmsAll(String sn, String projectId, String subjectId, String tempId, Integer confirmState, Integer smsState);
}
