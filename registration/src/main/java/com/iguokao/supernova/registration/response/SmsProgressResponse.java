package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.registration.document.SmsProgress;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.List;

@Getter
@Setter
public class SmsProgressResponse {
    String smsTemplateId;
    String benchId;
    Integer total;
    Integer success;
    Integer fail;

    public static SmsProgressResponse of(SmsProgress smsProgress) {
        if(smsProgress == null)
            return null;
        SmsProgressResponse smsProgressResponse = new SmsProgressResponse();
        BeanUtils.copyProperties(smsProgress, smsProgressResponse);
        return smsProgressResponse;
    }

    public static List<SmsProgressResponse> of(List<SmsProgress> list) {
        return list.stream().map(SmsProgressResponse::of).toList();
    }
}
