package com.iguokao.supernova.registration.request;

import com.iguokao.supernova.registration.document.AnnouncementCategory;
import com.iguokao.supernova.registration.response.AnnouncementCategoryResponse;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AnnouncementCategoryUpdateRequest {
    private String companyId;
    private String categoryId;
    private String name;
    private String content;

    public static AnnouncementCategoryResponse of(AnnouncementCategory obj){
        if(obj==null){
            return null;
        }
        AnnouncementCategoryResponse res = new AnnouncementCategoryResponse();
        BeanUtils.copyProperties(obj, res);
        res.setCategoryId(obj.getCategoryId().toString());
        return res;
    }

    public static List<AnnouncementCategoryResponse> of(List<AnnouncementCategory> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<AnnouncementCategoryResponse> res = new ArrayList<>();
        for(AnnouncementCategory obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
