package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;


@Getter
@Setter
public class SubjectUpdateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "科目 Id")
    @Length(min = 24, max = 24, message = "subjectId 错误")
    private String subjectId;

    private String templateId;


    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "科目名称")
    @NotBlank(message = "科目名称不能为空")
    private String name;

    @Schema(description = "简析")
    private String info;

    @Schema(description = "报名费")
    private Double price;

    @Schema(description = "过线分数")
    private Integer passScore = 0;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "开始时间")
    private Date startAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "结束时间")
    private Date endAt;
}
