package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.enums.OrderStateEnum;
import com.iguokao.supernova.registration.repository.*;
import com.iguokao.supernova.registration.response.CandidateStatisticResponse;
import com.iguokao.supernova.registration.response.TemplateDataStatisticResponse;
import com.iguokao.supernova.registration.service.CacheService;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CacheServiceImpl extends RedisServiceImpl implements CacheService {

    private final CompanyConfigRepository companyConfigRepository;
    private final ProjectRepository projectRepository;
    private final SubjectRepository subjectRepository;
    private final MongoTemplate mongoTemplate;

    public CacheServiceImpl(RedisTemplate<String, ImageCode> imageCodeRedisTemplate, StringRedisTemplate stringRedisTemplate, RedisTemplate<String, CompanyConfig> companyConfigRedisTemplate, CompanyConfigRepository companyConfigRepository, RedisTemplate<String, Project> projectRedisTemplate, RedisTemplate<String, Subject> subjectRedisTemplate, RedisTemplate<String, AdmissionCard> admissionCardRedisTemplate, ProjectRepository projectRepository, SubjectRepository subjectRepository, RedisTemplate<String, TemplateDataStatisticResponse> statisticRedisTemplate, RedisTemplate<String, CandidateStatisticResponse> candidateStatisticRedisTemplate, MongoTemplate mongoTemplate) {
        super(imageCodeRedisTemplate, stringRedisTemplate,companyConfigRedisTemplate, projectRedisTemplate, subjectRedisTemplate, admissionCardRedisTemplate, statisticRedisTemplate, candidateStatisticRedisTemplate);
        this.companyConfigRepository = companyConfigRepository;
        this.projectRepository = projectRepository;
        this.subjectRepository = subjectRepository;
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public CompanyConfig getCompanyConfig(String sn) {
        CompanyConfig company = super.getCompanyConfig(sn);
        if(company == null){
            company = this.companyConfigRepository.findBySn(sn)
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.COMPANY_CONFIG_NOT_FOUND));
            super.setCompanyConfig(company);
        }
        return company;
    }

    @Override
    public Project getProject(String projectId) {
        Project project = super.getProject(projectId);
        if(project == null){
            project = this.projectRepository.findById(new ObjectId(projectId))
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
            super.setProject(project);
        }
        return project;
    }

    @Override
    public List<Project> getProjectList(String companyId) {
        List<Project> list = super.getProjectList(companyId);
        list = list.stream()
                .sorted(Comparator.comparing(Project::getSignUpStartAt, Comparator.nullsLast(Comparator.reverseOrder())))
                .toList();
        if(list.isEmpty()){
            Date start = new Date(new Date().getTime() - 86400000 * 365L);
            list = this.projectRepository.onlineSignUpProject(new ObjectId(companyId), start, Sort.by(Sort.Direction.DESC, "signUpStartAt"));
            List<Project> appointList =  this.projectRepository.onlineAppointProject(new ObjectId(companyId), start, Sort.by(Sort.Direction.DESC, "appointStartAt"));
            if(!appointList.isEmpty()){
                list.addAll(appointList);
            }
            if(!list.isEmpty()){
                super.setProjectList(list);
            }
        }
        return list;
    }

    @Override
    public List<Project> getProjectList(List<String> projectIdList) {
        List<Project> list = super.getProjectList(projectIdList);
        for(int i=0; i<projectIdList.size(); i++){
            if(list.get(i) == null){
                Project project = this.projectRepository.findById(new ObjectId(projectIdList.get(i)))
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
                list.set(i, project);
                super.setProject(project);
            }
        }
        return list;
    }

    @Override
    public List<Subject> getSubjectList(List<String> subjectIdList) {
        List<Subject> list = super.getSubjectList(subjectIdList);
        for(int i=0; i<subjectIdList.size(); i++){
            if(list.get(i) == null){
                Subject subject = this.subjectRepository.findById(new ObjectId(subjectIdList.get(i)))
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.SUBJECT_NOT_FOUND));
                list.set(i, subject);
                super.setSubject(subject);
            }
        }
        return list;
    }

    @Override
    public Subject getSubject(String subjectId) {
        Subject subject = super.getSubject(subjectId);
        if(subject == null){
            subject = this.subjectRepository.findById(new ObjectId(subjectId))
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SUBJECT_NOT_FOUND));
            super.setSubject(subject);
        }
        return subject;
    }

    @Override
    public TemplateDataStatisticResponse getTemplateDataStatistic(String projectId) {
        Project project = this.getProject(projectId);

        TemplateDataStatisticResponse res =  super.getTemplateDataStatistic(projectId);
        if(res == null){
            Query query = new Query();
            query.addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)));
            query.fields()
                    .include("city")
                    .include("subjectIdList")
                    .include("finished")
                    .include("passed")
                    .include("createdAt")
                    .include("successTradeNo");
            List<TemplateData> list = this.mongoTemplate.find(query, TemplateData.class);

            Map<String, Long> cityCountMap = list
                    .stream()
                    .filter(item -> item.getCity() != null)
                    .collect(Collectors.groupingBy(TemplateData::getCity, Collectors.counting()));
            res = new TemplateDataStatisticResponse();
            res.setProjectId(projectId);
            res.setCityCount(cityCountMap);
            res.setTemplateDataCount(list.size());

            res.setTemplateDataSubjectCount(list
                    .stream()
                    .mapToInt(td -> td.getSubjectIdList().size())
                    .sum());

            int approveCount = (int)list
                    .stream()
                    .filter(item -> item.getPassed() != null && item.getPassed())
                    .count();
            int paidCount = (int)list
                    .stream()
                    .filter(item -> item.getSuccessTradeNo() != null)
                    .count();
            int finishedCount = (int)list
                    .stream()
                    .filter(item -> item.getFinished() != null && item.getFinished())
                    .count();
            res.setApproveCount(approveCount);
            res.setTemplateFinishedCount(finishedCount);
            res.setPaidCount(paidCount);
            res.setCreatedAt(new Date());

            Map<String, Long> dayCountMap = list
                    .stream()
                    .collect(Collectors.groupingBy(templateData -> DateUtil.dateToStr(templateData.getCreatedAt(), "yyyy-MM-dd"), Collectors.counting()));
            res.setTemplateDataDayCount(dayCountMap);

            List<Integer> stateList = new ArrayList<>();
            stateList.add(OrderStateEnum.FINISHED.getCode());
            stateList.add(OrderStateEnum.SUCCESS.getCode());
            Query orderQuery = new Query();
            orderQuery
                    .addCriteria(Criteria.where("state").in(stateList))
                    .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)));

            query.fields()
                    .include("state")
                    .include("createdAt");
            List<Order> orderList = this.mongoTemplate.find(query, Order.class);
            Map<String, Long> payCountMap = orderList
                    .stream()
                    .collect(Collectors.groupingBy(order -> DateUtil.dateToStr(order.getCreatedAt(), "yyyy-MM-dd"), Collectors.counting()));
            res.setPayDayCount(payCountMap);
            super.setTemplateDataStatistic(projectId, res);
        }
        return res;
    }

    @Override
    public CandidateStatisticResponse getCandidateStatistic(String companyId) {
        CandidateStatisticResponse res =  super.getCandidateStatistic(companyId);
        if(res == null){
            Query query = new Query();
            query.addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));
            query.fields()
                    .include("createdAt");
            List<Candidate> list = this.mongoTemplate.find(query, Candidate.class);

            res = new CandidateStatisticResponse();
            res.setCandidateCount(list.size());
            res.setCompanyId(companyId);
            res.setCreatedAt(new Date());

            Map<String, Long> dayCountMap = list
                    .stream()
                    .collect(Collectors.groupingBy(candidate -> DateUtil.dateToStr(candidate.getCreatedAt(), "yyyy-MM-dd"), Collectors.counting()));
            res.setCandidateDayCount(dayCountMap);

            super.setCandidateStatistic(companyId, res);
        }
        return res;
    }
}
