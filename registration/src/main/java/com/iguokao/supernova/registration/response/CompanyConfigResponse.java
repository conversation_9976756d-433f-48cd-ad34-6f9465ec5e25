package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.registration.document.CompanyConfig;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.List;

@Getter
@Setter
public class CompanyConfigResponse {
    private String companyId;
    private String sn;

    private String name;
    private String logo;
    private String banner;
    private String agreement;
    private String miniBanner;

    private Boolean myProject;
    private Boolean appointEnabled;
    private Boolean admissionEnabled;
    private Boolean scoreEnabled;
    private Boolean invoiceEnable;
    private Boolean chatEnable;
    private Boolean myInfoEnable;
    private Boolean projectListEnable;
    private String support;

    private String hfId;
    private String hfTokenNo;
    private String hfSplitId;

    private String supplementaryName;
    private String smsApproveSuccessTemplate;
    private String smsApproveRejectedTemplate;
    private String smsRefundTemplate;

    private String primaryColor;
    private String menuColor;
    private String menuHoverColor;

    private List<Integer> invoiceTypeList;
    private List<AnnouncementCategoryResponse> announcementCategoryList;
    private List<SmsTemplateResponse> smsTemplateList;
    private List<String> payChannelList;

    public static CompanyConfigResponse of(CompanyConfig config) {
        if(config == null){
            return null;
        }
        CompanyConfigResponse res = new CompanyConfigResponse();
        BeanUtils.copyProperties(config, res);
        res.setCompanyId(config.get_id().toString());
        res.setAnnouncementCategoryList(AnnouncementCategoryResponse.of(config.getAnnouncementCategoryList()));
        res.setSmsTemplateList(config.getSmsTemplateList()
                .stream()
                .map(item -> {
                    SmsTemplateResponse i = new SmsTemplateResponse();
                    BeanUtils.copyProperties(item, i);
                    return i;
        }).toList());
        return res;
    }

    @Getter
    @Setter
    public static class SmsTemplateResponse {
        String smsTemplateId;
        String content;
    }
}
