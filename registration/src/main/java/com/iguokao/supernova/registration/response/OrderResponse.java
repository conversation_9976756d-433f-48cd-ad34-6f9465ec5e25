package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.registration.document.Order;
import com.iguokao.supernova.registration.document.PayItem;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class OrderResponse {
    private String orderId;
    private String companyId;
    private String candidateId;
    private String projectId;

    private List<PayItemResponse> payItemList = new ArrayList<>();

    @Indexed(name = "tradeNo_index")
    private String tradeNo; // 订单号
    private String fullName;
    private String mobile;
    private String payOrderId;
    private String description;
    private Double tradeAmount; // 订单金额 单位分
    private Double amount; // 支付金额 单位元
    private Double feeAmount; // 订单金额 单位分
    private String source; // 支付源
    private Integer state; // 订单状态
    private List<String> resultList;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date createdAt;

    @Getter
    @Setter
    public static class  PayItemResponse {
        private String subjectId;
        private String info;
        private Double amount; // 金额 单位分

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
        private Date requestAt; // 申请退款时间

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
        private Date refundAt;

        public static PayItemResponse of(PayItem item) {
            PayItemResponse response = new PayItemResponse();
            BeanUtils.copyProperties(item, response);
            return response;
        }

        public static List<PayItemResponse> of(List<PayItem> list) {
            if (list == null) {
                return new ArrayList<>();
            }
            List<PayItemResponse> responseList = new ArrayList<>();
            for (PayItem item : list) {
                responseList.add(PayItemResponse.of(item));
            }
            return responseList;
        }
    }

    public static OrderResponse of(Order order) {
        OrderResponse response = new OrderResponse();
        response.setOrderId(order.get_id().toHexString());
        response.setCompanyId(order.getCompanyId().toHexString());
        response.setCandidateId(order.getCandidateId().toHexString());
        response.setProjectId(order.getProjectId().toHexString());
        response.setPayItemList(PayItemResponse.of(order.getPayItemList()));
        BeanUtils.copyProperties(order, response);
        return response;
    }

    public static List<OrderResponse> of(List<Order> list) {
        if (list == null) {
            return new ArrayList<>();
        }
        List<OrderResponse> responseList = new ArrayList<>();
        for (Order order : list) {
            responseList.add(OrderResponse.of(order));
        }
        return responseList;
    }
}
