package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.registration.document.CandidateCount;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import org.openxmlformats.schemas.drawingml.x2006.chart.STGrouping;

import java.util.List;
import java.util.Map;

public interface SubjectService {
    String add(Subject subject);

    Subject getById(String subjectId);

    void update(Subject subject);

    List<Subject> getListByProjectId(String projectId);

    Tuple2<Map<Subject, CandidateCount>, List<Project>> getImportList(String companyId);

    void scoreImport(String subjectId, String examSubjectId);
}
