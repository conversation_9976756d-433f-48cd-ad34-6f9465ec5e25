package com.iguokao.supernova.registration.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class SendAllRequest {

    @Schema(description = "公司id")
    private String sn;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目 Id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "科目 Id")
    @Length(min = 24, max = 24, message = "subjectId 错误")
    private String subjectId;

    @Schema(description = "模板id")
    @Length(min = 6, max = 24, message = "tempId 错误")
    private String tempId;

    @Schema(description = "确认状态")
    private Integer confirmState;

    @Schema(description = "短信状态")
    private Integer smsState;

    @Schema(description = "邮件状态")
    private Integer emailState;

}
