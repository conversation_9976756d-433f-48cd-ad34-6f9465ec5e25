package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.common.response.PageResponse;
import org.springframework.data.domain.Pageable;

import java.util.List;


public class PageCandidateResponse extends PageResponse<CandidateResponse> {
    public PageCandidateResponse(List<CandidateResponse> list, int total, Pageable pageable) {
        super(list, total, pageable);
    }
}
