package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.registration.document.AnnouncementCategory;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.registration.document.SmsTemplate;

import java.util.List;

public interface CompanyConfigService {
    void register(CompanyConfig config);
    void update(CompanyConfig config);
    void update(String companyId, List<AnnouncementCategory> list);

    CompanyConfig getById(String companyId);
    CompanyConfig getBySn(String sn);

    void addSmsTemplate(String companyId, SmsTemplate smsTemplate);

    void removeSmsTemplate(String companyId, String smsTemplateId);
}
