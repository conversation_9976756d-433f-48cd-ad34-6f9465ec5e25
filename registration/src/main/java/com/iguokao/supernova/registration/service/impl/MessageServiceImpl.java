package com.iguokao.supernova.registration.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.remote.EmqRemote;
import com.iguokao.supernova.registration.remote.PublishRequest;
import com.iguokao.supernova.registration.repository.CandidateRepository;
import com.iguokao.supernova.registration.repository.MessageRepository;
import com.iguokao.supernova.registration.service.MessageService;
import com.iguokao.supernova.registration.service.MqttService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class MessageServiceImpl implements MessageService {
    private final MessageRepository messageRepository;
    private final CandidateRepository candidateRepository;
    private final MongoTemplate mongoTemplate;
    private final MqttService mqttService;

    @Override
    public void add(Message message) {
        Candidate candidate = this.candidateRepository.findById(message.getCandidateId()).orElseThrow();
        message.setCompanyId(candidate.getCompanyId());
        this.messageRepository.save(message);

        if(!DateUtil.isWorkTime(new Date())){
            message.setOperatorId(new ObjectId());
            message.setContent("已经收到您的消息，工作时间是工作日9:00 - 17:00，工作人员上线后会及时回复您的消息。");
            this.mqttService.sendMessage(message);
        }
    }

    @Override
    public Tuple2<List<Message>, Integer> page(String candidateId, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "createdAt"))
                .addCriteria(Criteria.where("candidateId").is(new ObjectId(candidateId)));

        // 计算总数
        long count = this.mongoTemplate.count(query, Message.class);
        // 分页信息
        query = query.with(pageable);
        List<Message> list = this.mongoTemplate.find(query, Message.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public Tuple2<List<Message>, Integer> adminPage(String companyId, Boolean isNotReplied, Pageable pageable) {

        Criteria criteria = Criteria.where("companyId").is(new ObjectId(companyId))
                .and("type").is(0);

        if(isNotReplied){
            criteria = criteria.and("replied").is(false);
        }

        TypedAggregation<Message> aggregation = Aggregation.newAggregation(Message.class,
                Aggregation.match(criteria),
                Aggregation.group("candidateId")
                        .last("candidateId").as("candidateId")
                        .last("operatorId").as("operatorId")
                        .last("content").as("content")
                        .last("fullName").as("fullName")
                        .last("mobile").as("mobile")
                        .last("idCardNum").as("idCardNum")
                        .last("type").as("type")
                        .last("createdAt").as("createdAt")
                        .last("replied").as("replied"),
                Aggregation.sort(Sort.by(Sort.Direction.DESC, "createdAt")),
                Aggregation.skip(pageable.getOffset()),
                Aggregation.limit(pageable.getPageSize())
        );

        AggregationResults<Message> list = mongoTemplate.aggregate(aggregation.withOptions(AggregationOptions.builder().allowDiskUse(true).build()),
                Message.class);

        // 分页
        Query query = new Query(criteria);
        List<ObjectId> count =  mongoTemplate.findDistinct(query, "candidateId", Message.class, ObjectId.class);

        return new Tuple2<>(list.getMappedResults(), count.size());
    }

    @Override
    public List<Message> candidateMessage(String candidateId) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "createdAt", "_id"));

        query = query.addCriteria(Criteria.where("candidateId").is(new ObjectId(candidateId)));

        return this.mongoTemplate.find(query, Message.class);
    }

    @Override
    public void reply(Message message) {
        this.mqttService.sendMessage(message);
        this.messageRepository.save(message);

        Query query = new Query()
                .addCriteria(Criteria.where("candidateId").is(message.getCandidateId()))
                .addCriteria(Criteria.where("createdAt").lt(message.getCreatedAt()));

        Update m = new Update();
        m.set("replied", true);
        mongoTemplate.updateMulti(query, m, Message.class);

    }
}
