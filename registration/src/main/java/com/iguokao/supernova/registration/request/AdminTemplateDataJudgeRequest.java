package com.iguokao.supernova.registration.request;

import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
public class AdminTemplateDataJudgeRequest {
    private String companyId;

    private List<CandidateInfo> candidateInfoList;

    private Boolean passed;

    private String rejectReason;

    @Getter
    @Setter
    public static class CandidateInfo {
        private String fullName;
        private String mobile;
        private String templateDataId;
    }
}
