package com.iguokao.supernova.registration.remote;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BalanceResponse {
    private String acct_id;
    private String acct_stat;
    private String acct_type;
    private String avl_bal;
    private String balance_amt;
    private String frz_bal;
    private String huifu_id;
    private String last_avl_bal;

//    {
//        "acct_id": "A41347737",
//            "acct_stat": "N",
//            "acct_type": "01",
//            "avl_bal": "0.40",
//            "balance_amt": "0.40",
//            "frz_bal": "0.00",
//            "huifu_id": "6666000162684688",
//            "last_avl_bal": "0.40"
//    }
}
