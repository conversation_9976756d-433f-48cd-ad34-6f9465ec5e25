package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.entity.Tuple2;

import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.request.AdminTemplateDataJudgeRequest;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface TemplateService {
    Tuple2<List<Template>, Integer> page(String companyId, Pageable pageable);
    void add(Template template);
    void update(Template template);
    void remove(String templateId);

    void link(String templateId, String projectId);
    Template getById(String templateId);

    void addGroup(String templateId, TemplateGroup group);
    void updateGroup(String templateId, TemplateGroup group);
    void removeGroup(String templateId, String groupId);

    void addItem(String templateId, String groupId, TemplateItem templateItem);
    void updateItem(String templateId, String groupId, TemplateItem templateItem);
    void removeItem(String templateId, String groupId, String itemId);

    void submit(String templateId, String projectId, String candidateId, Map<String, String> data);

    TemplateData getDataByProjectIdAndCandidateId(String projectId, String candidateId, String templateId);

    TemplateData findByProjectIdAndCandidateId(String projectId, String candidateId, String templateId);

    void updateTemplateData(TemplateData templateData, Boolean judgment);

    Map<String, String> data(String templateId, String projectId, String candidateId);

    Query getSearchQuery(String projectId, String templateId, String fullName, String mobile, String idCardNum, Integer passState, Integer gender, Boolean finished, Boolean payed,
                                                 Map<String ,String> searchList, String remark1, String remark2, String remark3, String city, @Length String subjectId, Date signUpStart, Date signUpEnd, Date birthdayStart, Date birthdayEnd, Boolean imported);

    Tuple2<List<TemplateData>, Integer> dataPage(Query query, Pageable pageable);

    String sendSms(Query query, String smsTemplateId, Boolean skipSmsSent);

    String sendSmsByTemplateDataIdList(List<String> templateDataIdList, String smsTemplateId, Boolean skipSmsSent);

    String queue(String projectId, String candidateId, String templateId, Integer type);

    void judge(String companyId, List<SmsSendItem> sendItemList, ApproveItem item);

    List<TemplateData> getDataByCandidateId(String candidateId);

    Tuple2<List<Subject>, List<Project>> getCandidateSubject(List<TemplateData> templateDataList);

    void updateProjectId(String templateId, String projectId);

    void copy(String templateId);

    TemplateData getDataByProjectIdAndCandidateId(String projectId, String candidateId);
    List<TemplateData> getInvoiceListByCandidateId(String candidateId);

    void setRemark(List<String> templateDataIdList, String remark1, String remark2, String remark3);

    List<TemplateData> getByProjectId(String projectId);

    List<Integer> passInfo(String projectId);


    void passAll(String projectId);

    TemplateData checkFinished(String projectId, String candidateId, String templateId);

    void addByGroup(TemplateData templateData);

    void clearGroup(String candidateId);
}
