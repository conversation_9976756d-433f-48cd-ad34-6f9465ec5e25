package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Getter
@Setter
public class AdminCandidateTemplateDataRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生id")
    @Length(min = 24, max = 24, message = "candidateId 错误")
    private String candidateId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "报名项目id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "模板id")
    @Length(min = 24, max = 24, message = "templateId 错误")
    private String templateId;

    private String fullName;

    private String mobile;

    private String email;

    private Integer gender;

    private String idCardNum;

    private Integer idCardType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", locale = "zh_CN", timezone = "GMT+8")
    private Date birthday;

    private String city;

    private Map<String, String> templateItemList = new HashMap<>();
}
