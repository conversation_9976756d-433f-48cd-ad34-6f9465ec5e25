package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.util.Md5Util;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.AppointTypeEnum;
import com.iguokao.supernova.registration.enums.CandidateConfirmStateEnum;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.repository.*;
import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.RegistrationService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.ini4j.Reg;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Locale.filter;

@RequiredArgsConstructor
@Service
public class RegistrationServiceImpl implements RegistrationService {

    private final RegistrationRepository registrationRepository;
    private final ProjectRepository projectRepository;
    private final SubjectRepository subjectRepository;
    private final MongoTemplate mongoTemplate;
    private final CandidateRepository candidateRepository;
    private final AppointItemRepository appointItemRepository;
    private final CacheService cacheService;

    @Override
    public Tuple2<List<Subject>, List<Project>> getCandidateSubject(String candidateId) {
        List<Registration> list = this.registrationRepository.findByCandidateId(new ObjectId(candidateId));
        List<String> subjectIdList = list
                .stream()
                .map(Registration::getSubjectId)
                .map(ObjectId::toString)
                .toList();
        List<String> projectIdList = list
                .stream()
                .map(Registration::getProjectId)
                .map(ObjectId::toString)
                .distinct()
                .toList();
        List<Project> projectList = this.cacheService.getProjectList(projectIdList);
        List<Subject> subjectList = this.cacheService.getSubjectList(subjectIdList);
        return new Tuple2<>(subjectList, projectList);
    }

    @Override
    public List<Registration> getListByCandidateIdAndSubjectIdList(String candidateId, List<String> projectIdList) {
        List<ObjectId> list = projectIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        List<Registration> res = this.registrationRepository.findByCandidateIdAndProjectIdIn(new ObjectId(candidateId), list);
        return res
                .stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Registration::getProjectId))),
                        ArrayList::new
                ));

    }

    @Override
    public void changeRegistration(String projectId, String candidateId, Integer confirmState, String city, String siteId) {
        List<Registration> list = this.registrationRepository.findByCandidateIdAndProjectId(new ObjectId(candidateId), new ObjectId(projectId));
        if(list.isEmpty()){
            return;
        }
        Registration registration = list.get(0);
        if(registration.getConfirmState() != 0 && registration.getConfirmState().equals(confirmState)){
            throw new ServiceException(ExceptionEnum.REGISTRATION_EXIST);
        }
        Project project = this.cacheService.getProject(registration.getProjectId().toString());
        Date now = new Date();
        if(!project.getAppoint()){
            throw new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND);
        } else if(now.getTime() < project.getAppointStartAt().getTime() || now.getTime() > project.getAppointEndAt().getTime()){
            throw new ServiceException(ExceptionEnum.PROJECT_APPOINT_NOT_ON_TIME);
        }
        int v = 0;
        if((registration.getConfirmState() == 0 || registration.getConfirmState().equals(CandidateConfirmStateEnum.REFUSE.getCode()))
                && confirmState.equals(CandidateConfirmStateEnum.AGREE.getCode())){
            // 没预约过 预约 或者 预约取消 重新预约
            v = -1;
        } else if(registration.getConfirmState() != 0 && confirmState.equals(CandidateConfirmStateEnum.REFUSE.getCode()) ){
            // 预约过 放弃
            v = 1;
        }

        if(project.getAppointType().equals(AppointTypeEnum.CONFIRM_ONLY.getCode())){
            list.forEach(r-> r.setConfirmState(confirmState));
        } else if(project.getAppointType().equals(AppointTypeEnum.CITY.getCode())) {
            list.forEach(r-> {
                r.setConfirmState(confirmState);
                r.setCity(city);
            });
            String k = city == null ? "" : Md5Util.genSign(city);
            this.cacheService.changeAppointLimit(project.get_id().toString(), k, v);
        } else if(project.getAppointType().equals(AppointTypeEnum.SITE.getCode())) {
            list.forEach(r-> {
                r.setConfirmState(confirmState);
                r.setCity(city);
                r.setSiteId(new ObjectId(siteId));
            });
            this.cacheService.changeAppointLimit(project.get_id().toString(), siteId, v);
        }
        this.registrationRepository.saveAll(list);
    }

    @Override
    public List<String> getExistsLoginNameBySubjectId(String subjectId) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        return mongoTemplate.findDistinct(query, "mobile",Registration.class, String.class);
    }

    @Override
    public Integer countAllByProjectId(String projectId) {
        return this.registrationRepository.countByProjectId(new ObjectId(projectId));
    }

    @Override
    public Integer countAllByProjectIdAndSubjectId(String projectId, String subjectId) {
        return this.registrationRepository.countByProjectIdAndSubjectId(new ObjectId(projectId),new ObjectId(subjectId));
    }

    @Override
    public Integer countConfirmationByProjectId(String projectId, Integer state) {
        return this.registrationRepository.countByProjectIdAndConfirmState(new ObjectId(projectId),state);
    }

    @Override
    public Integer countByProjectIdAndEmailState(String projectId) {
        return this.registrationRepository.countByProjectIdAndEmailStateGreaterThan(new ObjectId(projectId), 10);
    }

    @Override
    public Integer countByProjectIdAndSmsState(String projectId) {
        return this.registrationRepository.countByProjectIdAndSmsStateGreaterThan(new ObjectId(projectId), 10);
    }

    @Override
    public List<String> getCityListByProjectId(String projectId) {
        Query query = new Query(Criteria.where("projectId").is(new ObjectId(projectId))).with(Sort.by(Sort.Direction.ASC, "city"));
        return mongoTemplate.findDistinct(query, "city", Registration.class, String.class);
    }

    @Override
    public Integer countStateByProjectIdAndCity(String projectId, String city, Integer num) {
        if(null == num){
            return this.registrationRepository.countByProjectIdAndCity(new ObjectId(projectId), city);
        }
        else {
            return this.registrationRepository.countByProjectIdAndCityAndConfirmState(new ObjectId(projectId), city, num);
        }
    }

    @Override
    public List<Registration> getByCandidateId(String candidateId) {
        return this.registrationRepository.findByCandidateId(new ObjectId(candidateId));
    }

    @Override
    public List<Registration> getListByCandidateId(String candidateId) {
        List<Registration> res = this.registrationRepository.findByCandidateId(new ObjectId(candidateId));
        return res
                .stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Registration::getProjectId))),
                        ArrayList::new
                ));
    }

}
