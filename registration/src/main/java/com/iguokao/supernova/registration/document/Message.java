package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter
@Setter
@Document
public class Message extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    @Indexed(name = "candidateId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId candidateId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId operatorId;

    private String fullName;
    private String mobile;
    private String idCardNum;
    private String content;
    private Integer type;

    private Boolean replied;

    public String toJsonString(){
        try {
            return new ObjectMapper().writeValueAsString(this);
        }catch (JsonProcessingException e){
            throw new ServiceException(BaseExceptionEnum.JSON_OBJECT_CONVERT_EXCEPTION);
        }
    }
}
