package com.iguokao.supernova.registration.request;

import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class CandidatePageRequest extends PageRequest {
    @Schema(description = "项目Id")
    @Length(min = 24, max = 24, message = "24位id")
    private String companyId;

    @Schema(description = "项目Id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Schema(description = "科目Id")
    @Length(min = 24, max = 24, message = "24位id")
    private String subjectId;

    @Schema(description = "名称")
    private String fullName;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "邮件状态")
    private Integer emailState;

    @Schema(description = "短信状态")
    private Integer smsState;

    @Schema(description = "确认状态")
    private Integer confirmState = 0;

}
