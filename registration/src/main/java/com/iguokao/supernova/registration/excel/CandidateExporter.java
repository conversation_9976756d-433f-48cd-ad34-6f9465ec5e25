package com.iguokao.supernova.registration.excel;

import com.iguokao.supernova.common.response.ImportCandidateResponse;
import com.iguokao.supernova.registration.document.Candidate;

import java.util.ArrayList;
import java.util.List;

public class CandidateExporter {

    public static List<List<String>> head() {
        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("姓名");

        List<String> head1 = new ArrayList<>();
        head1.add("城市");

        List<String> head2 = new ArrayList<>();
        head2.add("证件号码");

        List<String> head3 = new ArrayList<>();
        head3.add("手机");

        List<String> head4 = new ArrayList<>();
        head4.add("邮件");

        List<String> head5 = new ArrayList<>();
        head5.add("确认状态");

        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);

        return list;
    }

    public static List<List<String>> data(List<Candidate> confirmedList,
                                          List<Candidate> notConfirmedList,
                                          List<Candidate> refusedList) {
        List<List<String>> list = new ArrayList<>();
        addToList(list, confirmedList, "已确认");
        addToList(list, notConfirmedList, "未确认");
        addToList(list, refusedList, "已拒绝");
        return list;
    }

    private static void addToList(List<List<String>> list, List<Candidate> subList, String confirmState){
        for(Candidate item : subList){
            List<String> line = new ArrayList<>();
            line.add(item.getFullName() == null ? "" : item.getFullName());
            line.add(item.getCity() == null ? "" : item.getCity());
            line.add(item.getIdCardNum() == null ? "" : item.getIdCardNum());
            line.add(item.getMobile() == null ? "" : item.getMobile());
            line.add(item.getEmail() == null ? "" : item.getEmail());

            line.add(confirmState);
            list.add(line);
        }
    }
}
