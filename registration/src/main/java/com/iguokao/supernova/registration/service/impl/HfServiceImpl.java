package com.iguokao.supernova.registration.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huifu.bspay.sdk.opps.core.exception.BasePayException;
import com.huifu.bspay.sdk.opps.core.net.BasePayRequest;
import com.huifu.bspay.sdk.opps.core.utils.DateTools;
import com.huifu.bspay.sdk.opps.core.utils.SequenceTools;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.HfResult;
import com.iguokao.supernova.registration.document.Order;
import com.iguokao.supernova.registration.document.PayItem;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.remote.BalanceResponse;
import com.iguokao.supernova.registration.service.HfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

@RequiredArgsConstructor
@Slf4j
@Service
public class HfServiceImpl implements HfService {
    public final ObjectMapper objectMapper;
    @Value("${app.dg.pay-callback}")
    String payCallbackUrl;

    @Value("${app.dg.refund-callback}")
    String refundCallbackUrl;

    @Value("${app.dg.withdraw-callback}")
    String withdrawCallbackUrl;

    @Value("${app.service.wechat.app-id}")
    String wxAppId;
    @Override
    public String balance(String hfId) {
        Map<String, Object> paramsInfo = new HashMap<>();
        paramsInfo.put("req_seq_id", SequenceTools.getReqSeqId32());
        paramsInfo.put("huifu_id", hfId);
        paramsInfo.put("req_date", DateTools.getCurrentDateYYYYMMDD());

        Map<String, Object> res = null;
        try {
            res = BasePayRequest.requestBasePay("/v2/trade/acctpayment/balance/query", paramsInfo, null, false);
            String json = objectMapper.writeValueAsString(res);
            System.out.println(json);
            if (res.get("resp_code").equals("********")) {
                List<BalanceResponse> list = objectMapper.readValue(res.get("acctInfo_list").toString(), new TypeReference<>() {});
                return list.get(0).getBalance_amt();
            }
        } catch (BasePayException | JsonProcessingException e) {
            log.error(e.toString());
        }
        return null;
    }

    @Override
    public HfResult pay(Order order, String openid, String hfSplitId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureTime = now.plusHours(24);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String expireTime = futureTime.format(formatter);

        Map<String, Object> paramsInfo = new HashMap<>();
        paramsInfo.put("req_date", DateTools.getCurrentDateYYYYMMDD());
        paramsInfo.put("req_seq_id", order.getTradeNo());
        paramsInfo.put("huifu_id", order.getHfId());
        paramsInfo.put("goods_desc", order.getDescription());
        switch (order.getSource()) {
            case "wx_pc" -> {
                paramsInfo.put("trade_type", "T_JSAPI");
                String goodsIds = order.getPayItemList().stream().map(PayItem::getSubjectId).map(ObjectId::toString).toList().toString();
                String wxData = String.format("{\"sub_appid\":\"%s\",\"sub_openid\":\"%s\",\"detail\":{\"cost_price\":\"%s\",\"receipt_id\":\"20220628132043853798\",\"goods_detail\":[{\"goods_id\":\"%s\",\"goods_name\":\"%s\",\"price\":\"%s\",\"quantity\":\"1\",\"wxpay_goods_id\":\"%s\"}]}}",
                        wxAppId, openid, order.getAmount().toString(), order.getAmount(), order.getDescription(), order.getAmount(), goodsIds);
                paramsInfo.put("wx_data", wxData);
            }
            case "ali_pc" -> paramsInfo.put("trade_type", "A_NATIVE");
        }
        paramsInfo.put("trans_amt", StringUtil.toPriceString(order.getAmount()));
        paramsInfo.put("time_expire", expireTime);
        paramsInfo.put("delay_acct_flag", "N");
        paramsInfo.put("term_div_coupon_type", "0");
        paramsInfo.put("limit_pay_type", "NO_CREDIT");
        paramsInfo.put("pay_scene", "02");
        paramsInfo.put("remark", order.getPayOrderId());
        if(hfSplitId != null){
            log.info("当前项目开启分账 - {}", hfSplitId);
            String fee = this.fee(order.getHfId(), StringUtil.toPriceString(order.getAmount()));
            paramsInfo.put("acct_split_bunch", String.format("{\"acct_infos\":[{\"div_amt\":\"%s\",\"huifu_id\":\"%s\"},{\"div_amt\":\"%s\",\"huifu_id\":\"%s\"}]}",
                    StringUtil.toPriceString(order.getAmount() - Double.parseDouble(fee)), hfSplitId, fee, order.getHfId()));
        }
        paramsInfo.put("notify_url", payCallbackUrl);
        Map<String, Object> res = null;
        try {
            System.out.println(objectMapper.writeValueAsString(paramsInfo));
            res = BasePayRequest.requestBasePay("v2/trade/payment/jspay", paramsInfo, null, false);
            String json = objectMapper.writeValueAsString(res);
            System.out.println(json);
            if(res.get("resp_desc").equals("重复交易")){
                throw new ServiceException(ExceptionEnum.ORDER_PAID_AGAIN);
            }
            HfResult hfResult = new HfResult();
            hfResult.setPayload(json);
            hfResult.setState(res.get("trans_stat").toString());
            if(res.get("party_order_id") != null){
                hfResult.setResId(res.get("party_order_id").toString());
            }
            if(res.get("hf_seq_id") != null){
                hfResult.setHfSeqId(res.get("hf_seq_id").toString());
            }
            switch (order.getSource()) {
                case "wx_pc" ->  {
                    if(res.get("pay_info") != null) hfResult.setResult(res.get("pay_info").toString());
                }
                case "ali_pc" -> {
                    if(res.get("qr_code") != null) hfResult.setResult(res.get("qr_code").toString());
                }
            }
            return hfResult;
        } catch (BasePayException | JsonProcessingException e) {
            log.error(e.toString());
            throw new ServiceException(ExceptionEnum.ORDER_PREPAY_FAILED);
        }
    }
    /*
    微信
    {
  "bank_code": "********",
  "pay_info": "{\"timeStamp\":\"**********\",\"package\":\"prepay_id=wx11175903557768ca09f108c466f78f0001\",\"paySign\":\"LuS/fTRFetUMXqwI8ogRu5CPsnF6zayqI3E0IbP1V++8ttGzn2jlTnw5Ci7Asi1IdG7bkVzYRJb8k2uVuai+IB9V/+4u0irTeUG3M/I+0r7CqLFS/7RN2GUYDyc5oJGm9/JjZZRqTUklof9bky8WhnPzDMuXeGamQum+np+aOuAV6dlLuvwCVXX6ra9QwHBs92DiFRSjmCxM5FwCduWYnU2+TtLuybsvF53E3Up9i3o0AoE9wM6aJ/IginSGLKqQyUaC340UijQ2oWfLXW+m+d8/MTAMdlXPsmq7pKcnq+tsU0be88QXDseDjN+rny29fBnN5TBDQBcjPO2C0SyENA==\",\"appId\":\"wxe0a15e1518f51e43\",\"signType\":\"RSA\",\"nonceStr\":\"6TxXBidT3wU84fqLA7GhbnIG6gozn085\"}",
  "resp_desc": "下单成功",
  "trans_stat": "P",
  "bank_message": "用户支付中",
  "hf_seq_id": "002900TOP3A250411175903P087ac13662b00000",
  "trans_amt": "0.10",
  "party_order_id": "032325041164743311201731",
  "unconfirm_amt": "0.10",
  "req_seq_id": "250411175612000927",
  "req_date": "********",
  "resp_code": "********",
  "trade_type": "T_JSAPI",
  "huifu_id": "****************",
  "is_clean_split": "N",
  "atu_sub_mer_id": "*********"
}
{
  "bank_code": "10000",
  "resp_desc": "下单成功",
  "trans_stat": "P",
  "bank_message": "Success",
  "hf_seq_id": "002900TOP1B250411180507P813ac139d3d00000",
  "trans_amt": "0.10",
  "party_order_id": "03222504116510804216747",
  "unconfirm_amt": "0.10",
  "req_seq_id": "250411180411000930",
  "req_date": "********",
  "resp_code": "********",
  "qr_code": "https://qr.alipay.com/bax03709uzqtvvhxlblp26db",
  "trade_type": "A_NATIVE",
  "huifu_id": "****************",
  "is_clean_split": "N",
  "atu_sub_mer_id": "****************"
}

    * */

    @Override
    public HfResult refund(Order order) {
        Map<String, Object> paramsInfo = new HashMap<>();
        paramsInfo.put("req_date", DateTools.getCurrentDateYYYYMMDD());
        paramsInfo.put("req_seq_id", String.format("r%s_%d", order.getTradeNo(), new Random().nextInt() % 10000));
        paramsInfo.put("huifu_id", order.getHfId());
        paramsInfo.put("ord_amt", StringUtil.toPriceString(order.getAmount()));
        String orderDate = DateUtil.dateToStr(order.getCreatedAt(), "yyyyMMdd");
        paramsInfo.put("org_req_date", orderDate);
        paramsInfo.put("org_hf_seq_id", order.getHfSeqId());
        paramsInfo.put("notify_url", refundCallbackUrl);

        // 3. 发起API调用
        Map<String, Object> res = null;
        try {
            res = BasePayRequest.requestBasePay("v2/trade/payment/scanpay/refund", paramsInfo, null, false);
            String json = objectMapper.writeValueAsString(res);
            System.out.println(json);
            HfResult hfResult = new HfResult();
            hfResult.setState(res.get("trans_stat").toString());
            hfResult.setPayload(json);
            hfResult.setResult(res.get("resp_desc").toString());
            return hfResult;
        } catch (BasePayException | JsonProcessingException e) {
            log.error(e.toString());
            throw new ServiceException(ExceptionEnum.DG_EXCEPTION);
        }
    }

    /*
    {"bank_code":"10000","org_req_date":"********","trans_date":"********","ord_amt":"0.10","resp_desc":"交易成功","trans_stat":"S","org_hf_seq_id":"002900TOP1B250411180507P813ac139d3d00000","bank_message":"Success","hf_seq_id":"003100TOP1B250411180933P937ac139d2700000","remark":"","trans_time":"180933","actual_ref_amt":"0.10","alipay_response":"{\"refund_detail_item_list\":[{\"amount\":\"0.10\",\"fund_channel\":\"ALIPAYACCOUNT\"}]}","org_req_seq_id":"250411180411000930","req_seq_id":"r250411180411000930_9766","product_id":"PAYUN","req_date":"********","resp_code":"********","pay_channel":"A","huifu_id":"****************","acct_split_bunch":"{\"acct_infos\":[{\"div_amt\":\"0.10\",\"huifu_id\":\"****************\"}],\"fee_amt\":\"0.00\"}","is_clean_split":"N","trans_finish_time":"********180935"}
    */

    @Override
    public HfResult withdraw(Order order, String tradeNo) {
        Map<String, Object> paramsInfo = new HashMap<>();
        paramsInfo.put("req_date", DateTools.getCurrentDateYYYYMMDD());
        paramsInfo.put("req_seq_id", order.getTradeNo());
        paramsInfo.put("huifu_id", order.getHfId());
        paramsInfo.put("into_acct_date_type", "D1");
        paramsInfo.put("token_no", tradeNo);
        paramsInfo.put("cash_amt", order.getTradeAmount().toString());
        paramsInfo.put("notify_url", withdrawCallbackUrl);

        Map<String, Object> res = null;
        try {
            res = BasePayRequest.requestBasePay("v2/trade/settlement/encashment", paramsInfo, null, false);
            String json = objectMapper.writeValueAsString(res);
            System.out.println(json);
            HfResult hfResult = new HfResult();
            hfResult.setState(res.get("trans_stat").toString());
            hfResult.setPayload(json);
            return hfResult;
        } catch (BasePayException | JsonProcessingException e) {
            log.error(e.toString());
            throw new ServiceException(ExceptionEnum.DG_EXCEPTION);
        }
    }

    @Override
    public String fee(String hfId, String amount) {
        Map<String, Object> paramsInfo = new HashMap<>();
        paramsInfo.put("req_seq_id", SequenceTools.getReqSeqId32());
        paramsInfo.put("huifu_id", hfId);
        paramsInfo.put("req_date", DateTools.getCurrentDateYYYYMMDD());
        paramsInfo.put("trade_type", "T_JSAPI");
        paramsInfo.put("trans_amt", amount);

        Map<String, Object> res = null;
        try {
            res = BasePayRequest.requestBasePay("v2/trade/feecalc", paramsInfo, null, false);
            String json = objectMapper.writeValueAsString(res);
            System.out.println(json);
            if (res.get("resp_code").equals("********")) {
                return res.get("fee_amt").toString();
            }
        } catch (BasePayException | JsonProcessingException e) {
            log.error(e.toString());
        }
        return null;
    }
}
