package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ProjectSubjectResponse {
    private String projectId;

    private String name;
    private String agreement;
    private Integer state;

    @Schema(description = "开始时间")
    private Date startAt;

    @Schema(description = "结束时间")
    private Date endAt;

    @Schema(description = "模版Id")
    private String templateId;

    @Schema(description = "有限报名")
    private Boolean limited;

    @Schema(description = "状态")
    private Boolean online;

    @Schema(description = "模版Id")
    private String supplementaryTemplateId;

    private Boolean pay;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date payStart;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date payEnd;

    // 准考证
    private Boolean admission;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date admissionStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date admissionEndAt;

    // 预约
    private Boolean appoint;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date appointStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date appointEndAt;
    private Integer appointType;
    private String appointNote;

    // 补充
    private Boolean supplementary;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date supplementaryStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date supplementaryEndAt;

    private List<SubjectResponse> subjectList;


    public static ProjectSubjectResponse of(Project obj, List<Subject> subjectList){
        if(obj==null || subjectList.isEmpty()){
            return null;
        }
        ProjectSubjectResponse res = new ProjectSubjectResponse();
        BeanUtils.copyProperties(obj, res);
        res.setProjectId(obj.get_id().toString());
        if(obj.getTemplateId() != null){
            res.setTemplateId(obj.getTemplateId().toString());
        }
        if(obj.getSupplementaryTemplateId() != null){
            res.setSupplementaryTemplateId(obj.getSupplementaryTemplateId().toString());
        }
        res.subjectList = SubjectResponse.of(subjectList);
        return res;
    }
}
