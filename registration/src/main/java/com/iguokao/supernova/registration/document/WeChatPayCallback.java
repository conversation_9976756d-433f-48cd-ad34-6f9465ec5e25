package com.iguokao.supernova.registration.document;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WeChatPayCallback {
    private String mchid;
    private String appid;
    private String out_trade_no;
    private String transaction_id;
    private String trade_type;
    private String trade_state;
    private String trade_state_desc;
    private String bank_type;
    private String attach;
    private String success_time;
    private WechatPayer payer;
    private WechatAmount amount;

    @Getter
    @Setter
    public static class WechatPayer {
        private String openid;
    }
}
/*
{
  "mchid": "**********",
  "appid": "wxe284455f222221e3",
  "out_trade_no": "250108091823000007",
  "transaction_id": "4200002559202501084166633999",
  "trade_type": "NATIVE",
  "trade_state": "SUCCESS",
  "trade_state_desc": "支付成功",
  "bank_type": "OTHERS",
  "attach": "",
  "success_time": "2025-01-08T14:35:01+08:00",
  "payer": {
    "openid": "oVYXK5OazO0f0cGSMJESJv--znbo"
  },
  "amount": {
    "total": 100,
    "payer_total": 100,
    "currency": "CNY",
    "payer_currency": "CNY"
  }
}
 */