package com.iguokao.supernova.registration.remote;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

public interface EmqRemote {
    @RequestLine("POST /api/v5/publish")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    PublishResponse publish(@RequestBody PublishRequest request);


    @RequestLine("GET /api/v5/clients?_page={page}&_limit=100")
    @Headers({"Content-Type: application/json; charset=utf-8"})
    ClientResponse client(@Param("page") Integer page);
}
