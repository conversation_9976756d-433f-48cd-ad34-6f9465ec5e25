package com.iguokao.supernova.registration.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.registration.document.Message;
import com.iguokao.supernova.registration.document.MqttUser;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Pageable;

import java.nio.charset.StandardCharsets;
import java.util.List;

public interface MqttService {
    List<String> getOnlineCandidateId();
    void sync();
    MqttUser getUser(String candidateId);

    void sendMessage(Message message);
}
