package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Getter
@Setter
@Document
public class Registration extends BaseDocument {
    @Indexed(name = "candidateId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId candidateId;

    @Indexed(name = "projectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    @Indexed(name = "subjectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId subjectId;

    // 预约信息
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId siteId;
    private String city;
    private Boolean passed;

    private String fullName;
    private String mobile;
    private String email;

    private Integer refundState = 0;
    private Integer confirmState = 0;
    private Integer smsState = 0;
    private Integer emailState = 0;

    private String shortCode;
    private Boolean shortCodeReady = false;

    private List<TemplateItemData> templateItemList;
    private List<TemplateItemData> supplementaryItemList;

}
