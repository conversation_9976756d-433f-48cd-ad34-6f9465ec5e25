package com.iguokao.supernova.registration.remote;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ClientResponse {

    private List<ClientResponseData> data;
    private ClientResponseMeta meta;

    @Getter
    @Setter
    public static class ClientResponseData{
        private String username;
        private String clientid;
    }

    @Getter
    @Setter
    public static class ClientResponseMeta{
        private Integer count;
        private Integer limit;
        private Integer page;
        private Boolean hasnext;
    }
}
/**
 {
 "data": [
 {
 "username": "user1",
 "reductions": 46080,
 "mqueue_max": 1000,
 "proto_ver": 4,
 "clean_start": true,
 "recv_cnt": 25,
 "created_at": "2025-02-11T06:17:02.972+00:00",
 "send_cnt": 25,
 "send_msg.dropped": 0,
 "expiry_interval": 0,
 "keepalive": 60,
 "recv_oct": 118,
 "mailbox_len": 0,
 "heap_size": 2586,
 "subscriptions_max": 10,
 "awaiting_rel_cnt": 0,
 "recv_msg.dropped.await_pubrel_timeout": 0,
 "ws_cookie": [],
 "send_msg.qos0": 0,
 "connected_at": "2025-02-11T06:17:02.980+00:00",
 "clientid": "mqtt-explorer-9f363690",
 "send_pkt": 25,
 "send_msg": 0,
 "recv_msg.qos1": 0,
 "node": "emqxsl-hz-ala@***********",
 "send_msg.qos1": 0,
 "peersni": "lb11bb12.ala.cn-hangzhou.emqxsl.cn",
 "is_persistent": false,
 "connected": true,
 "inflight_max": 32,
 "send_msg.dropped.too_large": 0,
 "port": 56148,
 "send_msg.dropped.expired": 0,
 "mqueue_dropped": 0,
 "recv_msg.qos2": 0,
 "subscriptions_cnt": 0,
 "send_msg.dropped.queue_full": 0,
 "recv_msg.qos0": 0,
 "proto_name": "MQTT",
 "is_bridge": false,
 "awaiting_rel_max": 100,
 "enable_authn": true,
 "tenant_id_from": "peersni",
 "mqueue_len": 0,
 "recv_msg": 0,
 "send_msg.qos2": 0,
 "send_oct": 58,
 "listener": "ws:default",
 "ip_address": "***************",
 "recv_msg.dropped": 0,
 "inflight_cnt": 0,
 "recv_pkt": 25
 }
 ],
 "meta": {
 "count": 1,
 "hasnext": false,
 "limit": 100,
 "page": 1
 }
 }
 */