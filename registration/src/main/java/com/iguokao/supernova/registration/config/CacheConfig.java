package com.iguokao.supernova.registration.config;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.document.Subject;
import com.iguokao.supernova.registration.response.CandidateStatisticResponse;
import com.iguokao.supernova.registration.response.TemplateDataStatisticResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * <AUTHOR>
 * 缓存配置类
 */
@Configuration
@Slf4j
@ConfigurationProperties("app.redis")
@Getter
@Setter
class CacheConfig {

    private String host;
    private Integer port;
    private String password;
    private Integer database;
    private Integer maxActive;
    private Integer maxIdle;
    private Integer minIdle;

    @Bean
    LettuceConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration redisConfiguration = new RedisStandaloneConfiguration();
        redisConfiguration.setHostName(host);
        redisConfiguration.setPort(port);
        redisConfiguration.setPassword(password);
        redisConfiguration.setDatabase(database);

        GenericObjectPoolConfig<?> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(maxActive);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);

        LettuceClientConfiguration clientConfiguration = LettucePoolingClientConfiguration.
                builder()
                .poolConfig(poolConfig)
                .build();
        return new LettuceConnectionFactory(redisConfiguration, clientConfiguration);
    }

    @Bean
    public StringRedisTemplate stringRedisTemplate(LettuceConnectionFactory factory) {
        final StringRedisTemplate redisTemplate = new StringRedisTemplate();
        redisTemplate.setConnectionFactory(factory);
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, ImageCode> imageCodeRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, ImageCode> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(ImageCode.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, CompanyConfig> companyConfigRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, CompanyConfig> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(CompanyConfig.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, Project> projectRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, Project> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Project.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, Subject> subjectRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, Subject> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Subject.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, AdmissionCard> admissionCardRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, AdmissionCard> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(AdmissionCard.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, TemplateDataStatisticResponse> templateDataStatisticRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, TemplateDataStatisticResponse> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(TemplateDataStatisticResponse.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, CandidateStatisticResponse> candidateStatisticRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, CandidateStatisticResponse> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(CandidateStatisticResponse.class));
        return redisTemplate;
    }
}

