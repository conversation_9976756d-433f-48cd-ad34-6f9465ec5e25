package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.common.util.Md5Util;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Getter
@Setter
@Document
@CompoundIndexes({
        @CompoundIndex(def = "{'companyId': -1, 'idCardNum': -1}", name = "companyId_idCardNum_index"),
        @CompoundIndex(def = "{'companyId': -1, 'loginName': -1, 'loginPassword': -1}", name = "companyId_loginName_loginPassword_index")
})
public class Candidate extends BaseDocument implements UserDetails {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    private String loginName;
    private String loginPassword;
    private String fullName;

    private String city;
    private Integer gender;

    private Integer idCardType;

    private String idCardNum;
    private String mobile;
    private String email;
    private String avatar;

    private List<SendInfo> sendInfoList = new ArrayList<>();
    private List<String> tagList = new ArrayList<>();

    public String genLoginSign(Long expiredAt){
        String str = String.format("%s,%s,%s,%d",
                companyId,
                get_id().toString(),
                loginName,
                expiredAt);
        return Md5Util.genSign(str);
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<SimpleGrantedAuthority> list = new ArrayList<>();
        list.add(new SimpleGrantedAuthority("ROLE_CANDIDATE"));
        return list;
    }

    @Override
    public String getPassword() {
        return loginPassword;
    }

    @Override
    public String getUsername() {
        return get_id().toString();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
