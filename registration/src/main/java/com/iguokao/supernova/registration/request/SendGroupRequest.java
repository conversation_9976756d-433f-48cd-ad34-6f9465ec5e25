package com.iguokao.supernova.registration.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class SendGroupRequest {

    @Schema(description = "公司id")
    private String sn;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目 Id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String projectId;

    @Schema(description = "测试邮件地址")
    private String testEmailAddress;

    @Schema(description = "模板id")
    @Length(min = 6, max = 24, message = "tempId 错误")
    private String tempId;

    @Schema(description = "此组考生")
    @NotEmpty(message = "考生id列表不能为空")
    private List<String> candidateIdList;
}
