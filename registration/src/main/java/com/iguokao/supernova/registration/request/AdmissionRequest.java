package com.iguokao.supernova.registration.request;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class AdmissionRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "projectId")
    @Length(min = 24, max = 24, message = "projectId长度超限")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "身份证号")
    @Length(min = 10, max = 30, message = "身份证号长度超限")
    private String idCardNum;
}
