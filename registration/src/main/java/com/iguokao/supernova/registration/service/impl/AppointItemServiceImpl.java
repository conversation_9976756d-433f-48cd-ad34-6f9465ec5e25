package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.util.Md5Util;
import com.iguokao.supernova.registration.document.AppointItem;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.enums.AppointTypeEnum;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.repository.AppointItemRepository;
import com.iguokao.supernova.registration.repository.ProjectRepository;
import com.iguokao.supernova.registration.service.AppointItemService;
import com.iguokao.supernova.registration.service.CacheService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class AppointItemServiceImpl implements AppointItemService {
    private final AppointItemRepository appointItemRepository;
    private final ProjectRepository projectRepository;
    private final CacheService cacheService;

    @Override
    public void addAllByExcel(List<AppointItem> list, String projectId) {

        Project project = this.projectRepository.findById(new ObjectId(projectId)).orElseThrow();

        for (AppointItem appointItem : list) {
            AppointItem at = null;
            if (appointItem.getSiteId() != null) {
                at = this.appointItemRepository.findAppointItemByProjectIdAndSiteId(new ObjectId(projectId),appointItem.getSiteId());
            } else {
                at = this.appointItemRepository.findAppointItemByProjectIdAndCity(new ObjectId(projectId), appointItem.getCity());
            }
            if (at == null) {
                this.appointItemRepository.insert(appointItem);
                at = appointItem;
            } else {
                at.setQuota(appointItem.getQuota());
                at.setCity(appointItem.getCity());
                at.setSiteName(appointItem.getSiteName());
                at.setSiteAddress(appointItem.getSiteAddress());
                this.appointItemRepository.save(at);
            }

            //更新缓存
            handleAppointCache(project, at);
        }
    }

    @Override
    public List<AppointItem> getAppointItems(String projectId) {
        Project project = this.cacheService.getProject(projectId);
        List<AppointItem> list = this.appointItemRepository.findByProjectId( new ObjectId(projectId));
        if(project.getAppointType().equals(AppointTypeEnum.CITY.getCode())){
            List<String> keyList = list
                    .stream()
                    .map(AppointItem::getCity)
                    .map(Md5Util::genSign)
                    .toList();
            this.setAppointItemLimit(projectId, list, keyList);
        } else if(project.getAppointType().equals(AppointTypeEnum.SITE.getCode())){
            List<String> keyList = list
                    .stream()
                    .map(AppointItem::getSiteId)
                    .map(ObjectId::toString)
                    .toList();
            this.setAppointItemLimit(projectId, list, keyList);
        }
        return list;
    }

    private void setAppointItemLimit(String projectId, List<AppointItem> list, List<String> keyList){
        List<Integer> limitList = this.cacheService.getAppointLimitList(projectId, keyList);
        int i = 0;
        for(AppointItem item : list){
            item.setCurrent(limitList.get(i));
            i++;
        }
    }

    @Override
    public void deleteById(String appointItemId) {
        AppointItem appointItem = this.appointItemRepository.findById(new ObjectId(appointItemId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.APPOINT_ITEM_NOT_FOUND));
        this.appointItemRepository.deleteById(new ObjectId(appointItemId));
        appointItem.setQuota(0);
        Project project = this.projectRepository.findById(appointItem.getProjectId()).orElseThrow();
        handleAppointCache(project,appointItem);
    }

    @Override
    public AppointItem getById(String appointItemId) {
        return this.appointItemRepository.findById(new ObjectId(appointItemId)).orElseThrow(() -> new ServiceException(ExceptionEnum.APPOINT_ITEM_NOT_FOUND));
    }

    @Override
    public void update(AppointItem appointItem) {
        this.appointItemRepository.save(appointItem);
        Project project = this.projectRepository.findById(appointItem.getProjectId()).orElseThrow();

        handleAppointCache(project, appointItem);
    }

    public void handleAppointCache(Project project, AppointItem appointItem) {

        //更新缓存
        if(project.getAppointType().equals(AppointTypeEnum.SITE.getCode())){
            //更新到考站
            this.cacheService.setAppointLimit(project.get_id().toString(), appointItem.getSiteId().toString(), appointItem.getQuota());
        }
        else{
            //更新到城市
            String k = Md5Util.genSign(appointItem.getCity());
            this.cacheService.setAppointLimit(project.get_id().toString(), k, appointItem.getQuota());
        }
    }
}
