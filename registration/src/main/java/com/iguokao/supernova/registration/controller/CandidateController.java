package com.iguokao.supernova.registration.controller;

import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.OssSign;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ExamRemote;
import com.iguokao.supernova.common.response.*;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.enums.SmsTypeEnum;
import com.iguokao.supernova.registration.request.*;
import com.iguokao.supernova.registration.response.*;
import com.iguokao.supernova.registration.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

@RestController
@RequestMapping("/api/v1/candidate")
@RequiredArgsConstructor
public class CandidateController {
    private final CandidateService candidateService;
    private final ProjectService projectService;
    private final SubjectService subjectService;
    private final TemplateService templateService;
    private final JwtService jwtService;
    private final RegistrationService registrationService;
    private final CompanyConfigService companyConfigService;
    private final CacheService cacheService;
    private final AppointItemService appointItemService;
    private final AnnouncementService announcementService;
    private final OssService ossService;
    private final ExamRemote examRemote;
    private final OrderService orderService;
    private final MessageService messageService;
    private final MqttService mqttService;

    @Value(value = "${app.ali.oss-bucket-exam}")
    private String ossBucketExam;

    @Value("${app.login-url}")
    private String loginUrl;

    @Value("${app.service.wechat.app-id}")
    private String appId;

    @Value("${app.service.wechat.app-secret}")
    private String appSecret;

    @Value("${app.dg.pay-page}")
    private String payPage;

    @Value("${app.wechat.app-id}")
    private String wechatAppId;

    @Value("${app.wechat.app-secret}")
    private String wechatAppSecret;

    @Operation(security = @SecurityRequirement(name = "ignore"))
    @GetMapping("/validate_code")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ImageCodeResponse.class)))
    public RestResponse<ImageCodeResponse> imageCode() {
        ImageCode imageCode = this.candidateService.getImageCode();
        return RestResponse.success(ImageCodeResponse.of(imageCode));
    }

    @PostMapping("/login")
    @Operation(summary = "考生登录", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> login(@Validated @RequestBody CandidateLoginRequest request) {
        ImageCode imageCode = new ImageCode();
        imageCode.setImageCode(request.getImageCode());
        imageCode.setKey(request.getKey());

        String token = this.candidateService.login(imageCode, request.getCompanyId(), request.getLoginName(), request.getLoginPassword());
        return RestResponse.success(token);
    }

    @GetMapping("/link/login/{companySn}/{loginName}/{expireAt}/{signature}")
    @Operation(summary = "考生登录 链接方式", security = @SecurityRequirement(name = "ignore"))
    public RedirectView linkLogin(@PathVariable String companySn,
                                          @PathVariable String loginName,
                                          @PathVariable Long expireAt,
                                          @PathVariable String signature) {
        CompanyConfig config = this.cacheService.getCompanyConfig(companySn);
        String token = this.candidateService.login(config.get_id().toString(), loginName, expireAt, signature);
        String url = String.format("https://%s%s%s", companySn, loginUrl, token);
        System.out.println(url);
        return new RedirectView(url);
    }

    @GetMapping("/project/{companyId}")
    @Operation(summary = "公司下——最近的项目列表", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ProjectResponse.class))))
    public RestResponse<List<ProjectResponse>> projectCurrent(@PathVariable @Length(min = 24, max = 24, message = "公司请求数据异常") String companyId) {
        List<Project> page = this.cacheService.getProjectList(companyId);
        List<ProjectResponse> res = ProjectResponse.of(page);
        return RestResponse.success(res);
    }

    @GetMapping("/project/info/{projectId}")
    @Operation(summary = "报名项目信息", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = ProjectResponse.class)))
    public RestResponse<ProjectResponse> info(@PathVariable @Length(min = 24, max = 24, message = "项目请求数据异常") String projectId) {
        Project res = this.projectService.getById(projectId);
        ProjectResponse response = ProjectResponse.of(res);
        return RestResponse.success(response);
    }

    @GetMapping("/subject/list/{projectId}")
    @Operation(summary = "项目下科目列表", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = ProjectResponse.class)))
    public RestResponse<List<SubjectResponse>> list(@PathVariable @Length(min = 24, max = 24, message = "项目请求数据异常") String projectId) {
        List<Subject> subjectList = this.subjectService.getListByProjectId(projectId);
        return RestResponse.success(SubjectResponse.of(subjectList));
    }

    @GetMapping("/template/{templateId}")
    @Operation(summary = "模板详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = TemplateResponse.class)))
    public RestResponse<TemplateResponse> template(@PathVariable @Length(min = 24, max = 24, message = "模版请求数据异常") String templateId) {
        Template template = this.templateService.getById(templateId);
        return RestResponse.success(TemplateResponse.of(template));
    }

    @PostMapping("/submit/template/data")
    @Operation(summary = "提交模板数据")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> templateData(@Validated @RequestBody CandidateTemplateDataRequest request) {
        TemplateData templateData = this.templateService.getDataByProjectIdAndCandidateId(request.getProjectId(), request.getCandidateId(), request.getTemplateId());
        if(null != request.getAvatar()){
            templateData.setAvatar(request.getAvatar());
        }
        if(null != request.getGender()){
            templateData.setGender(request.getGender());
        }
        if(null != request.getBirthday()){
            templateData.setBirthday(request.getBirthday());
        }
        templateData.setTemplateItemList(TemplateItemData.of(request.getTemplateItemList(), templateData.getTemplateItemList()));
        this.templateService.updateTemplateData(templateData,true);
        return RestResponse.success();
    }

    @PostMapping("/submit/template/subject")
    @Operation(summary = "提交所选城市与科目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> templateDataWithSubject(@Validated @RequestBody CandidateTemplateSubjectRequest request) {
        Project project = this.projectService.getById(request.getProjectId());
        TemplateData templateData = this.templateService.findByProjectIdAndCandidateId(request.getProjectId(),request.getCandidateId(),request.getTemplateId());
        //不需要审核 ,并且已经进行了交费
        if(!project.getApprove() && templateData.getSuccessTradeNo() != null){
            request.setSubjectIdList(null);
        }
        templateData.setCity(request.getCity() != null ? request.getCity() : null);
        templateData.setLocation(request.getLocation() != null ? request.getLocation() : null);
        if(null != request.getSubjectIdList()){
            List<ObjectId> idList = new ArrayList<>();
            for(String s : request.getSubjectIdList()){
                idList.add(new ObjectId(s));
            }
            templateData.setSubjectIdList(idList);
        }
        this.templateService.updateTemplateData(templateData,true);
        return RestResponse.success();
    }

    @PostMapping("/submit/template/confirm")
    @Operation(summary = "确认模板数据已经填写完毕", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> templateDataWithSubject(@Validated @RequestBody CandidateTemplateDataFinishRequest request) {
        TemplateData templateData = this.templateService.checkFinished(request.getProjectId(),request.getCandidateId(),request.getTemplateId());
        //TemplateData templateData = this.templateService.findByProjectIdAndCandidateId(request.getProjectId(),request.getCandidateId(),request.getTemplateId());
        templateData.setFinished(request.getFinished());
        templateData.setUpdatedAt(new Date());
        this.templateService.updateTemplateData(templateData,true);
        return RestResponse.success();
    }

    @PostMapping("/template/data")
    @Operation(summary = "获取个人所填写模板数据", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<TemplateDataResponse> candidateTemplateData(@Validated @RequestBody CandidateTemplateDataInfoRequest request) {
        TemplateData templateData = this.templateService.findByProjectIdAndCandidateId(request.getProjectId(),request.getCandidateId(),request.getTemplateId());
        return RestResponse.success(TemplateDataResponse.of(templateData));
    }

    @PostMapping("/sms/login")
    @Operation(summary = "考生短信登录", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> smsLogin(@Validated @RequestBody CandidateSmsLoginRequest request) {
        String token = this.candidateService.smsCodeLogin(request.getCompanyId(), request.getLoginName(),request.getMobile(),request.getSmsCode(),request.getImageCodeKey());
        return RestResponse.success(token);
    }

    @GetMapping("/company/{sn}")
    @Operation(summary = "企业信息SN", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = CompanyConfig.class)))
    public RestResponse<CompanyConfigResponse> companySn(@PathVariable @Length(min = 2, max = 30, message = "请求数据异常") String sn) {
        CompanyConfig config =  this.companyConfigService.getBySn(sn);
        return RestResponse.success(CompanyConfigResponse.of(config));
    }

    @PostMapping("/register")
    @Operation(summary = "考生注册", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> register(@Validated @RequestBody CandidateRegisterRequest request) {
        Candidate candidate = new Candidate();
        BeanUtils.copyProperties(request, candidate);
        candidate.setCompanyId(new ObjectId(request.getCompanyId()));
        candidate.setLoginName(request.getMobile());
        String gender = StringUtil.getGenderByIdCard(request.getIdCardNum());
        if(gender != null && gender.equals("男")){
            candidate.setGender(1);
        } else if(gender != null && gender.equals("女")){
            candidate.setGender(2);
        }
        String token = this.candidateService.register(candidate, request.getSmsCode());
        return RestResponse.success(token);
    }

    @PostMapping("/sms_code")
    @Operation(summary = "短信注册验证码", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> smsCode(@Validated @RequestBody CandidateSmsSendRequest request, HttpServletRequest httpServletRequest) {
        ImageCode imageCode = new ImageCode();
        imageCode.setImageCode(request.getImageCode());
        imageCode.setKey(request.getKey());
        if(request.getType().equals(SmsTypeEnum.REGISTER.getCode())){
            this.candidateService.sendRegisterSms(request.getCompanyId(), imageCode, request.getMobile(), httpServletRequest);
        } else if(request.getType().equals(SmsTypeEnum.LOGIN.getCode())){
            this.candidateService.sendLoginSms(request.getCompanyId(), imageCode, request.getMobile(), httpServletRequest);
        } else {
            throw new ServiceException(ExceptionEnum.SMS_TYPE_NOT_SUPPORT);
        }
        return RestResponse.success();
    }

    @GetMapping("/info")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @Operation(summary = "考生信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = CandidateResponse.class)))
    public RestResponse<CandidateResponse> info() {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        Candidate candidate = this.candidateService.getById(candidateId);
        return RestResponse.success(CandidateResponse.of(candidate,null));
    }

    @PostMapping("/update/info")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @Operation(summary = "修改考生信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> updateInfo(@Validated @RequestBody CandidateMySelfUpdateRequest request) {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        Candidate candidate = this.candidateService.getById(candidateId);
        candidate.setFullName(request.getFullName());
        candidate.setEmail(request.getEmail());
        candidate.setIdCardType(request.getIdCardType());
        candidate.setIdCardNum(request.getIdCardNum());
        this.candidateService.updateInfo(candidate);
        return RestResponse.success();
    }

//    @PostMapping("/project/page")
//    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
//    @Operation(summary = "项目列表")
//    @ApiResponse(content = @Content(schema = @Schema(implementation = PageProjectResponse.class)))
//    public RestResponse<PageResponse<ProjectResponse>> projectPage(@Validated @RequestBody com.iguokao.supernova.common.request.PageRequest request) {
//        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
//        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
//        String companyId =  jwtService.getId(IdConstant.SITE_ID_PREFIX);
//        Tuple2<List<Project>, Integer> page = this.projectService.getPage(companyId, null, pageable);
//        List<ProjectResponse> res = ProjectResponse.of(page.first());
//        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
//    }

    @GetMapping("/subject/my")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @Operation(summary = "我报名的项目")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ProjectResponse.class))))
    public RestResponse<List<CandidateSignUpProjectResponse>> mySubject() {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        List<TemplateData> templateDataList = this.templateService.getDataByCandidateId(candidateId);
        Tuple2<List<Subject>, List<Project>> reg = this.templateService.getCandidateSubject(templateDataList);
        return RestResponse.success(CandidateSignUpProjectResponse.of(templateDataList,reg.first(),reg.second()));
    }

    @PostMapping("/appoint")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @Operation(summary = "科目报名")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> appoint(@Validated @RequestBody SubjectAppointRequest request) {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        this.registrationService.changeRegistration(request.getProjectId(), candidateId, request.getConfirmState(), request.getCity(), request.getSiteId());
        return RestResponse.success();
    }

    @GetMapping("/appoint/list")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @Operation(summary = "预约科目列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ProjectAppointResponse.class))))
    public RestResponse<List<ProjectAppointResponse>> appoint() {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        List<Registration> list = this.registrationService.getListByCandidateId(candidateId);
        return RestResponse.success(ProjectAppointResponse.of(list));
    }

    @GetMapping("/appoint/data/{projectId}")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @Operation(summary = "预约数据")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = AppointItemResponse.class))))
    public RestResponse<List<AppointItemResponse>> appointData(@PathVariable @Length(min = 24, max = 24, message = "请求数据异常") String projectId) {
        List<AppointItem> list = this.appointItemService.getAppointItems(projectId);
        return RestResponse.success(AppointItemResponse.of(list));
    }

    @PostMapping("/announcement/page")
    @Operation(summary = "文章", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageAnnouncementResponse.class)))
    public RestResponse<PageResponse<AnnouncementResponse>> page(@Validated @RequestBody PageAnnouncementRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Announcement>, Integer> page = this.announcementService.page(request.getCompanyId(), request.getCategoryId(), pageable);
        List<AnnouncementResponse> res = AnnouncementResponse.of(page.first());
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }

    @GetMapping("/oss/sign/short")
    @Operation(summary = "获取Oss Sign 180秒")
    @ApiResponse(content = @Content(schema = @Schema(implementation = OssSignResponse.class)))
    public RestResponse<OssSignResponse> ossSign() {
        OssSign sign = this.ossService.getOssSign(ossBucketExam, 180);
        return RestResponse.success(OssSignResponse.of(sign));
    }

    @PostMapping("/oss/sign/url")
    @Operation(summary = "获取Oss Sign URL 180秒")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<List<String>> ossSignUrl(@RequestBody List<String> list) {
        int duration = 180;
        List<String> res = new ArrayList<>();
        for(String path : list){
            String url = this.ossService.generateSignedUrl(this.ossBucketExam, path, duration);
            res.add(url);
        }
        return RestResponse.success(res);
    }

    @GetMapping("/score")
    @Operation(summary = "查询成绩")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ScoreResponse.class))))
    public RestResponse<List<ScoreResponse>> score() {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        List<Score> scoreList = this.candidateService.getScoreList(candidateId);
        List<ScoreResponse> resList = new ArrayList<>();
        Date now = new Date();
        scoreList.forEach(score -> {
            Project project = this.cacheService.getProject(score.getProjectId().toString());
            if(now.getTime() > project.getScoreStartAt().getTime()){
                List<String> subjectIdList = score.getScoreList()
                        .stream()
                        .map(ScoreItem::getSubjectId)
                        .map(ObjectId::toString)
                        .toList();
                List<Subject> subjectList = this.cacheService.getSubjectList(subjectIdList);
                ScoreResponse res = ScoreResponse.of(score, project, subjectList);
                resList.add(res);
            }
        });
        return RestResponse.success(resList.stream()
                .sorted(Comparator.comparing(ScoreResponse::getStartedAt, Comparator.nullsLast(Comparator.reverseOrder())))
                .toList());
    }

    @GetMapping("/admission/project")
    @Operation(summary = "查询准考证 相关项目 ")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = AdmissionResponse.class))))
    public RestResponse<List<AdmissionResponse>> admission() {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        List<Registration> list = this.registrationService.getByCandidateId(candidateId);
        List<String> projectIdList = list
                .stream()
                .map(Registration::getProjectId)
                .map(ObjectId::toString)
                .toList()
                .stream()
                .distinct()
                .toList();
        List<String> subjectIdList = list
                .stream()
                .map(Registration::getSubjectId)
                .map(ObjectId::toString)
                .toList()
                .stream()
                .distinct()
                .toList();
        List<Project> projectList = this.cacheService.getProjectList(projectIdList);
        projectList = projectList
                .stream()
                .filter(p -> p.getAdmission().equals(true))
                .sorted(Comparator.comparing(p -> p.getAdmissionStartAt().getTime()))
                .toList();

        List<Subject> subjectList = this.cacheService.getSubjectList(subjectIdList);
        Date now = new Date();
        List<AdmissionResponse> resList = new ArrayList<>();
        projectList.forEach(project -> {

            AdmissionResponse res = new AdmissionResponse();
            res.setProjectId(project.get_id().toString());
            res.setProjectName(project.getName());
            res.setStartAt(project.getAdmissionStartAt());
            res.setEndAt(project.getAdmissionEndAt());
            if(now.getTime() > project.getAdmissionStartAt().getTime()
                    && now.getTime() < project.getAdmissionEndAt().getTime()){
                res.setOpened(true);
            }
            List<String> subjectNameList = subjectList
                    .stream()
                    .filter(subject -> subject.getProjectId().equals(project.get_id()))
                    .toList()
                    .stream()
                    .map(Subject::getName)
                    .toList();
            res.setSubjectNameList(subjectNameList);
            resList.add(res);
        });
        return RestResponse.success(resList.stream()
                .sorted(Comparator.comparing(AdmissionResponse::getStartAt, Comparator.nullsLast(Comparator.reverseOrder())))
                .toList());
    }

    @PostMapping("/admission/card")
    @Operation(summary = "准考证数据")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = AdmissionCardResponse.class))))
    public RestResponse<AdmissionCardResponse> admissionCard(@Validated @RequestBody AdmissionRequest request) {
        Project project = this.cacheService.getProject(request.getProjectId());
        Date now = new Date();
        if(now.getTime() > project.getAdmissionStartAt().getTime()
                && now.getTime() < project.getAdmissionEndAt().getTime()){
            String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
            Candidate candidate = this.candidateService.getById(candidateId);
            this.examRemote.admissionDownload(project.getExamProjectId().toString(), candidate.getIdCardNum());
            return this.examRemote.admissionExport(project.getExamProjectId().toString(), candidate.getIdCardNum());
        }
        throw new ServiceException(ExceptionEnum.ADMISSION_EXPIRED);
    }

    @PostMapping("/order/add")
    @Operation(summary = "生成订单")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@Validated @RequestBody OrderAddRequest request) {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        String companyId = this.jwtService.getId(IdConstant.COMPANY_ID_PREFIX);
        String tradeNo = this.orderService.add(
                candidateId,
                companyId,
                request.getCompanySn(),
                request.getProjectId(),
                request.getSubjectIdList(),
                request.getSource(),
                request.getFullName(),
                request.getMobile());
        return RestResponse.success(tradeNo);
    }

    @PostMapping("/ali/prepay")
    @Operation(summary = "支付宝 预支付")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> prepay(@Validated @RequestBody PrePayRequest request) {
        String qr = this.orderService.aliPrepay(request.getTradeNo(), request.getCompanySn());
        return RestResponse.success(qr);
    }

    @PostMapping("/wx/authorize")
    @Operation(summary = "获取微信支付授权地址")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> confirmUrl(@RequestBody AuthorizeUrlRequest request) {
        Order order = this.orderService.getByTradeNo(request.getTradeNo());
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        String token = this.cacheService.genShortToken(order.getTradeNo(), candidateId);
        String queryString = String.format("source=%s&price=%s&desc=%s&order_id=%s&sn=%s&expire_at=%d&token=%s",
                request.getSource(),
                StringUtil.toPriceString(order.getAmount()),
                order.getDescription(),
                order.getTradeNo(),
                request.getCompanySn(),
                new Date().getTime()/1000 + 300,
                token);
        String url = String.format("https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&state=%d#wechat_redirect",
                wechatAppId,
                String.format("%s?%s", payPage, URLEncoder.encode(queryString, StandardCharsets.UTF_8)),
                new Random().nextInt() % 10000);
        return RestResponse.success(url);
    }

    @GetMapping("/project/sign-up/{projectId}")
    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @Operation(summary = "报名项目信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = CandidateSignUpProjectResponse.class)))
    public RestResponse<CandidateSignUpProjectResponse> mySubject(@PathVariable @Length(min = 24, max = 24, message = "请求数据异常") String projectId) {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        TemplateData templateData = this.templateService.getDataByProjectIdAndCandidateId(projectId, candidateId);
        List<TemplateData> list = new ArrayList<>();
        list.add(templateData);
        Tuple2<List<Subject>, List<Project>> reg = this.templateService.getCandidateSubject(list);
        return RestResponse.success(CandidateSignUpProjectResponse.of(list, reg.first(),reg.second()).get(0));
    }

    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @PostMapping("/invoice/info")
    @Operation(summary = "发票配置")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> invoiceConfig(@Validated @RequestBody ProjectInvoiceConfigRequest request) {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        TemplateData data = this.templateService.getDataByProjectIdAndCandidateId(request.getProjectId(), candidateId);
        data.setInvoiceTin(request.getInvoiceTin());
        data.setInvoiceTitle(request.getInvoiceTitle());
        data.setInvoiceType(request.getInvoiceType());
        data.setInvoice(request.getInvoice());
        data.setInvoicePersonal(request.getInvoicePersonal());
        this.templateService.updateTemplateData(data,false);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @GetMapping("/invoice/list")
    @Operation(summary = "我的发票")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = InvoiceResponse.class))))
    public RestResponse<List<InvoiceResponse>> invoiceList() {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        List<TemplateData> list = this.templateService.getInvoiceListByCandidateId(candidateId);
        return RestResponse.success(InvoiceResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @PostMapping("/password/change")
    @Operation(summary = "修改密码")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> invoiceConfig(@Validated @RequestBody PasswordChangeRequest request) {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        this.candidateService.passwordChange(candidateId, request.getMobile(), request.getPassword());
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @GetMapping("/message/my")
    @Operation(summary = "我的消息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<MessageResponse>> page() {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        List<Message> list = this.messageService.candidateMessage(candidateId);
        return RestResponse.success(MessageResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @PostMapping("/message/send")
    @Operation(summary = "发送消息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> send(@Validated @RequestBody MessageSendRequest request) {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        Candidate candidate = this.candidateService.getById(candidateId);
        Message message = new Message();
        message.setType(request.getType());
        message.setFullName(candidate.getFullName());
        message.setMobile(candidate.getMobile());
        message.setIdCardNum(candidate.getIdCardNum());
        message.setContent(request.getContent());
        message.setCandidateId(new ObjectId(candidateId));
        message.setReplied(false);
        this.messageService.add(message);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_CANDIDATE')")
    @GetMapping("/message/user")
    @Operation(summary = "用户")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<MqttUserResponse> user() {
        String candidateId = this.jwtService.getId(IdConstant.CANDIDATE_ID_PREFIX);
        MqttUser mqttUser = this.mqttService.getUser(candidateId);
        return RestResponse.success(MqttUserResponse.of(mqttUser));
    }
}
