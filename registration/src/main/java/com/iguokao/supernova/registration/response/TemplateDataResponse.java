package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.registration.document.ApproveItem;
import com.iguokao.supernova.registration.document.SmsHistory;
import com.iguokao.supernova.registration.document.TemplateData;
import com.iguokao.supernova.registration.document.TemplateItemData;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class TemplateDataResponse {
    private String templateDataId;
    private String templateId;
    private String projectId;
    private String candidateId;
    private Boolean passed;
    private Boolean finished;
    private List<String> rejectReasonList = new ArrayList<>();
    private String successTradeNo;

    private String fullName;
    private String mobile;
    private String idCardNum;
    private Integer idCardType;
    private String email;
    private String avatar;
    private Integer gender;

    private String city;
    private String location;
    private List<String> subjectIdList = new ArrayList<>();

    private Boolean invoice;
    private Integer invoiceType;
    private Boolean invoicePersonal;
    private String invoiceTitle;
    private String invoiceTin;
    // 开票后
    private String invoicePrice;
    private String invoiceLink;

    private Boolean refund;
    private List<SmsHistoryResponse> smsHistoryList= new ArrayList<>();

    private String remark1;
    private String remark2;
    private String remark3;

    private List<TemplateItemDataResponse> templateItemList = new ArrayList<>();
    private List<TemplateItemDataResponse> supplementaryItemList = new ArrayList<>();
    private List<ApproveItemResponse> approveItemList = new ArrayList<>();
    private Boolean imported ;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", locale = "zh_CN", timezone = "GMT+8")
    private Date birthday;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date updatedAt;

    @Getter
    @Setter
    public static class TemplateItemDataResponse {
        private String itemId;
        private String value;
    }

    @Getter
    @Setter
    public static class ApproveItemResponse {
        private String operatorId;
        private Boolean passed;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
        private Date createdAt;
        private String message;
    }

    @Getter
    @Setter
    public static class SmsHistoryResponse {
        private String smsTemplateId;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
        private Date createdAt;
        private String errCode;
    }

    public static TemplateDataResponse of(TemplateData obj){
        if(obj == null){
            return null;
        }
        TemplateDataResponse res = new TemplateDataResponse();
        res.setTemplateDataId(obj.get_id().toString());
        res.setCandidateId(obj.getCandidateId().toString());
        res.setProjectId(obj.getProjectId().toString());
        res.setTemplateId(obj.getTemplateId().toString());
        BeanUtils.copyProperties(obj, res);

        if(null != obj.getSubjectIdList()){
            for(ObjectId id : obj.getSubjectIdList()){
                res.getSubjectIdList().add(id.toString());
            }
        }

        for(TemplateItemData d : obj.getTemplateItemList()){
            TemplateItemDataResponse dr = new TemplateItemDataResponse();
            dr.setItemId(d.getItemId().toString());
            dr.setValue(d.getValue());
            res.getTemplateItemList().add(dr);
        }

        for(TemplateItemData t : obj.getSupplementaryItemList()){
            TemplateItemDataResponse tr = new TemplateItemDataResponse();
            tr.setItemId(t.getItemId().toString());
            tr.setValue(t.getValue());
            res.getSupplementaryItemList().add(tr);
        }

        for(ApproveItem a : obj.getApproveList()){
            ApproveItemResponse ar = new ApproveItemResponse();
            ar.setOperatorId(a.getOperatorId().toString());
            ar.setPassed(a.getPassed());
            ar.setCreatedAt(a.getCreatedAt());
            ar.setMessage(a.getMessage());
            res.getApproveItemList().add(ar);
            if(!ar.getPassed()){
                res.getRejectReasonList().add(a.getMessage());
            }
        }

        for(SmsHistory smsHistory : obj.getSmsHistoryList()){
            SmsHistoryResponse sr = new SmsHistoryResponse();
            sr.setSmsTemplateId(smsHistory.getSmsTemplateId());
            sr.setCreatedAt(smsHistory.getCreatedAt());
            sr.setErrCode(smsHistory.getErrCode());
            res.getSmsHistoryList().add(sr);
        }

        return res;
    }


    public static List<TemplateDataResponse> of(List<TemplateData> list){
        if(list == null){
            return new ArrayList<>();
        }
        List<TemplateDataResponse> res = new ArrayList<>();
        for(TemplateData obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
