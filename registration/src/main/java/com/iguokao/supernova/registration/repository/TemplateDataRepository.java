package com.iguokao.supernova.registration.repository;

import com.iguokao.supernova.registration.document.TemplateData;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface TemplateDataRepository extends MongoRepository<TemplateData, ObjectId> {
    Optional<TemplateData> findByProjectIdAndCandidateId(ObjectId projectId, ObjectId candidateId);
    List<TemplateData> findByProjectId(ObjectId projectId);

    List<TemplateData> findByCandidateId(ObjectId candidateId);

    int countByProjectId(ObjectId projectId);
    int countByProjectIdAndSuccessTradeNoExists(ObjectId projectId, Boolean e);
    int countByProjectIdAndPassed(ObjectId projectId, Boolean pass);
    int countByProjectIdAndFinishedAndPassed(ObjectId projectId, Boolean finished, <PERSON>ole<PERSON> passed);

    void deleteByCandidateId(ObjectId candidateId);
}
