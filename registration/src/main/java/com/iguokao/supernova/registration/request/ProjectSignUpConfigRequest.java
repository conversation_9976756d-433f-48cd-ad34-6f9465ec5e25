package com.iguokao.supernova.registration.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ProjectSignUpConfigRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "开启/关闭 报名模块")
    private Boolean signUp;

    @Schema(description = "备注")
    private String signUpAgreement;

    private String signUpAvatarNote;
    private String signUpAreaNote;
    private String signUpSubjectNote;
    private String signUpNoticeNote;

    @Schema(description = "报名模板id")
    private String templateId;

    @Schema(description = "是否需要审核")
    private Boolean approve = false;

    @Schema(description = "科目是否可以多选")
    private Boolean subjectMultiSelect = true;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "开始时间")
    private Date signUpStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "结束时间")
    private Date signUpEndAt;

    @Schema(description = "报名城市列表")
    private List<String> cityList = new ArrayList<>();

}
