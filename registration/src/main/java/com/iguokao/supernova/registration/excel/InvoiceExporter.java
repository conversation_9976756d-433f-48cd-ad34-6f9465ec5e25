package com.iguokao.supernova.registration.excel;

import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.registration.document.Order;
import com.iguokao.supernova.registration.document.TemplateData;
import com.iguokao.supernova.registration.enums.InvoiceEnum;
import com.iguokao.supernova.registration.enums.OrderStateEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class InvoiceExporter {

    public static List<List<String>> head() {
        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("考生姓名");

        List<String> head1 = new ArrayList<>();
        head1.add("考生电话");

        List<String> head2 = new ArrayList<>();
        head2.add("订单号");

        List<String> head3 = new ArrayList<>();
        head3.add("订单状态");

        List<String> head4 = new ArrayList<>();
        head4.add("订单内容");

        List<String> head5 = new ArrayList<>();
        head5.add("支付渠道订单号");

        List<String> head6 = new ArrayList<>();
        head6.add("支付渠道");
        List<String> head7 = new ArrayList<>();
        head7.add("订单金额");
        List<String> head8 = new ArrayList<>();
        head8.add("创建时间");

        List<String> head9 = new ArrayList<>();
        head9.add("发票类型");

        List<String> head12 = new ArrayList<>();
        head12.add("发票主体");


        List<String> head10 = new ArrayList<>();
        head10.add("发票抬头");

        List<String> head11 = new ArrayList<>();
        head11.add("发票税号");

        List<String> head13 = new ArrayList<>();
        head13.add("Email");

        list.add(head0);
        list.add(head1);
        list.add(head2);

        list.add(head3);
        list.add(head4);
        list.add(head5);

        list.add(head6);
        list.add(head7);
        list.add(head8);

        list.add(head9);
        list.add(head12);
        list.add(head10);
        list.add(head11);
        list.add(head13);
        return list;
    }

    public static List<List<Object>> data(List<Order> list, List<TemplateData> dataList) {
        List<List<Object>> res = new ArrayList<>();
        for(Order order : list){
            List<Object> line = new ArrayList<>();
            line.add(order.getFullName());
            line.add(order.getMobile());
            line.add(order.getTradeNo());

            line.add(Objects.equals(order.getState(), OrderStateEnum.SUCCESS.getCode()) ? "支付成功" : "订单完成");
            line.add(order.getDescription());
            line.add(order.getPayOrderId());

            line.add(order.getSource());
            line.add(order.getAmount().toString());
            line.add(DateUtil.dateToStrStd(order.getCreatedAt()));

            TemplateData data = dataList
                    .stream()
                    .filter(item -> item.getCandidateId().equals(order.getCandidateId()))
                    .findFirst()
                    .orElse(null);
            if(data != null){
                if(data.getInvoiceType() != null){
                    if(data.getInvoiceType().equals(InvoiceEnum.VAT_SPECIAL.getCode())){
                        line.add("增值税专用发票");
                    }else if(data.getInvoiceType().equals(InvoiceEnum.VA_GENERAL.getCode())){
                        line.add("增值税普通发票");
                    }else if(data.getInvoiceType().equals(InvoiceEnum.ELECTRONIC.getCode())){
                        line.add("电子发票（增值税普通电子)");
                    }
                } else {
                    line.add("");
                }
                if(data.getInvoicePersonal() == null){
                    line.add("");
                }else {
                    if(data.getInvoicePersonal()){
                        line.add("个人");
                    } else {
                        line.add("单位");
                    }
                }
                line.add(data.getInvoiceTitle());
                line.add(data.getInvoiceTin());
                line.add(data.getEmail());
            } else {
                line.add("");
                line.add("");
                line.add("");
                line.add("");
            }

            res.add(line);
        }
        return res;
    }
}
