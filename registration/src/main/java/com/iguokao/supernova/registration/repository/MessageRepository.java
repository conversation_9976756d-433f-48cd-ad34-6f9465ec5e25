package com.iguokao.supernova.registration.repository;

import com.iguokao.supernova.registration.document.Message;
import com.iguokao.supernova.registration.document.Notification;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface MessageRepository extends MongoRepository<Message, ObjectId> {
    List<Message> findByCandidateId(ObjectId candidateId);
}
