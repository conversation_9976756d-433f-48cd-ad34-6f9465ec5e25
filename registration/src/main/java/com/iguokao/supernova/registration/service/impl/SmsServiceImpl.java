package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.remote.CloopenRemote;
import com.iguokao.supernova.common.remote.cloopen.SmsResponse;
import com.iguokao.supernova.registration.document.SmsProgress;
import com.iguokao.supernova.registration.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class SmsServiceImpl implements SmsService {
    private final List<SmsProgress> smsProgressList = new ArrayList<>();

    @Value(value = "${app.cloopen.account-sid}")
    private String cloopenAccountSid;
    @Value(value = "${app.cloopen.sms-app-id}")
    private String cloopenSmsAppId;
    @Value(value = "${app.cloopen.auth-token}")
    private String cloopenAuthToken;
    private final CloopenRemote cloopenRemote;

    @Override
    public void smsProgressAdd(SmsProgress smsProgress) {
        this.smsProgressList.add(smsProgress);
    }

    @Override
    public SmsProgress smsProgress(String benchId) {
        return this.smsProgressList.stream()
                .filter(s -> s.getBenchId().equals(benchId))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String sendSms(String mobile, String smsTemplateId, List<String> arg) {
        SmsResponse res = CloopenRemote.sendSns(cloopenRemote,
                smsTemplateId,
                mobile,
                arg,
                cloopenSmsAppId,
                cloopenAccountSid,
                cloopenAuthToken);
        log.info(res.toString());
        if(!res.getStatusCode().equals("000000")){
            return res.getStatusCode();
        }
        return null;
    }
}
