package com.iguokao.supernova.registration.repository;

import com.iguokao.supernova.registration.document.AppointItem;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface AppointItemRepository extends MongoRepository<AppointItem, ObjectId> {

    List<AppointItem> findByProjectId(ObjectId projectId);

    AppointItem findAppointItemByProjectIdAndCity(ObjectId projectId,String city);

    AppointItem findAppointItemByProjectIdAndSiteId(ObjectId projectId, ObjectId siteId);
}
