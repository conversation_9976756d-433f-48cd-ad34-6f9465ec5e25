package com.iguokao.supernova.registration.response;

import com.iguokao.supernova.registration.document.Message;
import com.iguokao.supernova.registration.document.MqttUser;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class MqttUserResponse {
    private String username;
    private String password;
    private String candidateId;

    public static MqttUserResponse of(MqttUser obj){
        if(obj==null){
            return null;
        }
        MqttUserResponse res = new MqttUserResponse();
        BeanUtils.copyProperties(obj, res);
        res.setCandidateId(obj.getCandidateId());
        return res;
    }

    public static List<MqttUserResponse> of(List<MqttUser> list){
        if(list == null){
            return new ArrayList<>();
        }
        List<MqttUserResponse> res = new ArrayList<>();
        for(MqttUser obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
