package com.iguokao.supernova.registration.controller;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.iguokao.supernova.common.converter.ValidObjectId;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ExamRemote;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;

import com.iguokao.supernova.registration.document.AppointItem;
import com.iguokao.supernova.registration.document.Project;
import com.iguokao.supernova.registration.enums.CandidateConfirmStateEnum;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.excel.AppointmentItem;
import com.iguokao.supernova.registration.excel.AppointmentItemListener;
import com.iguokao.supernova.registration.excel.CityConfirmExporter;
import com.iguokao.supernova.registration.excel.CityConfirmItem;
import com.iguokao.supernova.registration.request.*;
import com.iguokao.supernova.registration.response.*;
import com.iguokao.supernova.registration.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@RestController
@RequestMapping("/api/v1/project")
@RequiredArgsConstructor
public class
ProjectController {

    private final ExamRemote examRemote;
    private final ProjectService projectService;
    private final RegistrationService registrationService;
    private final AppointItemService appointItemService;
    private final TemplateService templateService;
    private final CacheService cacheService;

    @PostMapping("/add")
    @Operation(summary = "创建项目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody ProjectAddRequest request) {
        Project project = new Project();
        BeanUtils.copyProperties(request, project);
        project.setCompanyId(new ObjectId(request.getCompanyId()));
        String projectId = this.projectService.add(project);
        return RestResponse.success(projectId);
    }

    @GetMapping("/info/{projectId}")
    @Operation(summary = "项目信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ProjectResponse.class)))
    public RestResponse<ProjectResponse> info(@ValidObjectId @PathVariable String projectId) {
        Project res = this.projectService.getById(projectId);
        ProjectResponse response = ProjectResponse.of(res);
        return RestResponse.success(response);
    }

    @GetMapping("/summary/{projectId}")
    @Operation(summary = "项目考生确认状态统计")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ProjectResponse.class)))
    public RestResponse<ProjectSummaryResponse> summary(@ValidObjectId @PathVariable String projectId) {
        ProjectSummaryResponse response = new ProjectSummaryResponse();
        response.setTotal(this.registrationService.countAllByProjectId(projectId));
        response.setConfirmation(this.registrationService.countConfirmationByProjectId(projectId, CandidateConfirmStateEnum.AGREE.getCode()));
        response.setRejection(this.registrationService.countConfirmationByProjectId(projectId,CandidateConfirmStateEnum.REFUSE.getCode()));
        response.setEmail(this.registrationService.countByProjectIdAndEmailState(projectId));
        response.setSms(this.registrationService.countByProjectIdAndSmsState(projectId));
        return RestResponse.success(response);
    }

    @PostMapping("/update")
    @Operation(summary = "修改项目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody ProjectUpdateRequest param){
        Project project = this.projectService.getById(param.getProjectId());
        BeanUtils.copyProperties(param, project);
        this.projectService.update(project);
        return RestResponse.success();
    }

    @GetMapping("/online/{projectId}")
    @Operation(summary = "上线")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Boolean.class)))
    public RestResponse<Boolean> online(@PathVariable String projectId){
        Boolean res = this.projectService.online(projectId);
        return RestResponse.success(res);
    }

    @PostMapping("/page")
    @Operation(summary = "查看项目列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageProjectResponse.class)))
    public RestResponse<PageResponse<ProjectResponse>> list(@RequestBody ProjectPageRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Project>, Integer> page = this.projectService.getPage(request.getCompanyId(), request.getName(), pageable);
        List<ProjectResponse> res = ProjectResponse.of(page.first());
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }

    @PostMapping("/config/signup")
    @Operation(summary = "报名配置")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> payConfig(@RequestBody ProjectSignUpConfigRequest request) {
        Project project = this.projectService.getById(request.getProjectId());
        BeanUtils.copyProperties(request, project);
        if(null != request.getTemplateId()){
            project.setTemplateId(new ObjectId(request.getTemplateId()));
            templateService.updateProjectId(request.getTemplateId(),project.get_id().toString());
        }
        this.projectService.update(project);
        return RestResponse.success();
    }

    @PostMapping("/config/score")
    @Operation(summary = "查询分值配置")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> payConfig(@RequestBody ProjectScoreConfigRequest request) {
        Project project = this.projectService.getById(request.getProjectId());
        BeanUtils.copyProperties(request, project);
        this.projectService.update(project);
        return RestResponse.success();
    }

    @PostMapping("/config/appoint")
    @Operation(summary = "配置预约")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> appointConfig(@RequestBody ProjectAppointConfigRequest request) {
        Project project = this.projectService.getById(request.getProjectId());
        project.setAppoint(request.getAppoint());
        project.setAppointStartAt(request.getAppointStartAt());
        project.setAppointEndAt(request.getAppointEndAt());
        project.setAppointNote(request.getAppointNote());
        project.setAppointType(request.getAppointType());
        this.projectService.update(project);
        return RestResponse.success();
    }

    @PostMapping("/config/pay")
    @Operation(summary = "配置支付")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> payConfig(@RequestBody ProjectPayConfigRequest request) {
        Project project = this.projectService.getById(request.getProjectId());
        project.setPay(request.getPay());
        project.setPayStartAt(request.getPayStartAt());
        project.setPayEndAt(request.getPayEndAt());
        this.projectService.update(project);
        return RestResponse.success();
    }

    @PostMapping("/config/supplementary")
    @Operation(summary = "配置补充")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> supplementaryConfig(@RequestBody ProjectSupplementaryConfigRequest request) {
        Project project = this.projectService.getById(request.getProjectId());
        project.setSupplementary(request.getSupplementary());
        project.setSupplementaryStartAt(request.getSupplementaryStartAt());
        project.setSupplementaryEndAt(request.getSupplementaryEndAt());
        if(request.getSupplementary()){
            project.setSupplementaryTemplateId(new ObjectId(request.getSupplementaryTemplateId()));
        }
        this.projectService.update(project);
        return RestResponse.success();
    }



    @PostMapping("/config/admission")
    @Operation(summary = "准考证")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> admissionConfig(@RequestBody ProjectAdmissionConfigRequest request) {
        Project project = this.projectService.getById(request.getProjectId());
        project.setAdmission(request.getAdmission());
        project.setAdmissionStartAt(request.getAdmissionStartAt());
        project.setAdmissionEndAt(request.getAdmissionEndAt());
        project.setExamProjectId(new ObjectId(request.getExamProjectId()));
        this.projectService.update(project);
        return RestResponse.success();
    }


    @RequestMapping(value = "/import/appoint/item/{projectId}", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "导入各城市与考站配额")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExcelErrResponse.class))))
    public RestResponse<List<ExcelErrResponse>>  importExcel(@ValidObjectId @PathVariable String projectId,
                                                             @RequestPart(value = "file") MultipartFile file) throws IOException {

        Project project = this.projectService.getById(projectId);
        //已经开始预约
        if(new Date().getTime() >= project.getAppointStartAt().getTime()){
            throw new ServiceException(ExceptionEnum.PROJECT_ONLINE);
        }
        List<ExcelErrResponse> errResponseList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), AppointmentItem.class, new AppointmentItemListener(project, errResponseList, appointItemService)).sheet().doRead();
        return RestResponse.success(errResponseList);
    }


    @GetMapping("/appoint/item/list/{projectId}")
    @Operation(summary = "配额列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Boolean.class)))
    public RestResponse<List<AppointItemResponse>> appointItemList(@PathVariable String projectId){
        List<AppointItem> appointItems = this.appointItemService.getAppointItems(projectId);
        return RestResponse.success(AppointItemResponse.of(appointItems));
    }

    @GetMapping("/appoint/item/delete/{appointItemId}")
    @Operation(summary = "删除配额")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Boolean.class)))
    public RestResponse<Boolean> appointItemDelete(@PathVariable String appointItemId){
        this.appointItemService.deleteById(appointItemId);
        return RestResponse.success();
    }

    @PostMapping("/appoint/item/edit")
    @Operation(summary = "编辑配额")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> appointItemUpdate(@RequestBody AppointItemUpdateRequest request) {
        AppointItem appointItem = this.appointItemService.getById(request.getAppointItemId());
        appointItem.setCity(request.getCity());
        appointItem.setSiteName(request.getSiteName());
        appointItem.setQuota(request.getQuota());
        this.appointItemService.update(appointItem);
        return RestResponse.success();
    }


    @GetMapping("/short/url/count/{projectId}")
    @Operation(summary = "已生成短链接数量")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Boolean.class)))
    public RestResponse<Integer> shortUrlCount(@PathVariable String projectId){
        int count = this.projectService.countShortUrl(projectId);
        return RestResponse.success(count);
    }

    @Async
    @PostMapping("/short/url/gen")
    @Operation(summary = "生成短链接")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Boolean.class)))
    public RestResponse<String> genShortUrl(@RequestBody ShortUrlGenRequest request){
        this.projectService.genShortUrl(request.getProjectId(),request.getSn());
        return RestResponse.success();
    }

    @GetMapping("/city/confirm/{projectId}")
    @Operation(summary = "各省市确认与放弃人数统计")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void city(@ValidObjectId @PathVariable String projectId,
                            HttpServletResponse response) throws IOException {

        List<CityConfirmItem> list = new ArrayList<>();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("各省市确认情况表_%s", projectId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<String> cityList = this.registrationService.getCityListByProjectId(projectId);
        for(String city : cityList){
            CityConfirmItem cityConfirmItem = new CityConfirmItem();
            cityConfirmItem.setCity(city);
            cityConfirmItem.setTotal(this.registrationService.countStateByProjectIdAndCity(projectId,city,null));
            cityConfirmItem.setConfirm(this.registrationService.countStateByProjectIdAndCity(projectId,city,20));
            cityConfirmItem.setRefuse(this.registrationService.countStateByProjectIdAndCity(projectId,city,30));
            cityConfirmItem.setUnKnow(this.registrationService.countStateByProjectIdAndCity(projectId,city,0));
            list.add(cityConfirmItem);
        }

        EasyExcel.write(response.getOutputStream())
                .sheet("确认情况")
                .head(CityConfirmExporter.head())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(CityConfirmExporter.data(list));
    }

    @GetMapping("/statistic/{projectId}")
    @Operation(summary = "项目统计")
    @ApiResponse(content = @Content(schema = @Schema(implementation = TemplateDataStatisticResponse.class)))
    public RestResponse<TemplateDataStatisticResponse> statistic(@PathVariable String projectId) {
        TemplateDataStatisticResponse res = this.cacheService.getTemplateDataStatistic(projectId);
        return RestResponse.success(res);
    }

}
