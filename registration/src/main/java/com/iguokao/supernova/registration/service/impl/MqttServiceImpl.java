package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.registration.document.Message;
import com.iguokao.supernova.registration.document.MqttUser;
import com.iguokao.supernova.registration.remote.ClientResponse;
import com.iguokao.supernova.registration.remote.EmqRemote;
import com.iguokao.supernova.registration.remote.PublishRequest;
import com.iguokao.supernova.registration.service.MqttService;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class MqttServiceImpl implements MqttService {

    private List<MqttUser> mqttUserList;
    private final List<String> onlineCandidateIdList = new ArrayList<>();
    private Date onlineUpdatedAt;


//    @Value("classpath:mqtt_auth.csv")  // 读取 resources 下的 CSV 文件
//    private Resource resource;

    private final EmqRemote emqRemote;

    @EventListener(ApplicationReadyEvent.class)
    public void init() throws IOException {
        sync();
    }

    @Override
    public List<String> getOnlineCandidateId() {
//        List<String> list = new ArrayList<>();
//        boolean hasNext = true;
//        int page = 1;
//        while (hasNext){
//            ClientResponse res = this.emqRemote.client(page);
//            List<String> idList = res.getData()
//                    .stream()
//                    .map(ClientResponse.ClientResponseData::getClientid)
//                    .toList();
//            list.addAll(idList);
//            page++;
//            hasNext = res.getMeta().getHasnext();
//        }
        return onlineCandidateIdList;
    }

    @Override
    public void sync() {
        mqttUserList = this.getUserList();
        boolean hasNext = true;
        int page = 1;

        Date now = new Date();
        if(onlineUpdatedAt != null && (now.getTime() - onlineUpdatedAt.getTime()) > 300 * 1000){
            onlineUpdatedAt = now;
            onlineCandidateIdList.clear();
        }

        while (hasNext){
            ClientResponse res = this.emqRemote.client(page);
            if(onlineUpdatedAt != null && (now.getTime() - onlineUpdatedAt.getTime()) > 300 * 1000){
                List<String> idList = res.getData()
                        .stream()
                        .map(ClientResponse.ClientResponseData::getClientid)
                        .toList();
                onlineCandidateIdList.addAll(idList);
            }

            for(ClientResponse.ClientResponseData data : res.getData()){
                this.mqttUserList
                        .stream()
                        .filter(mqttUser -> mqttUser.getUsername().equals(data.getUsername()))
                        .findFirst()
                        .ifPresent(u -> u.setCandidateId(data.getClientid()));
            }
            page++;
            hasNext = res.getMeta().getHasnext();
        }
    }

    @Override
    public MqttUser getUser(String candidateId) {
       MqttUser user = this.mqttUserList
                .stream()
                .filter(mqttUser -> mqttUser.getCandidateId() != null && mqttUser.getCandidateId().equals(candidateId))
                .findFirst()
                .orElse(null);
       if(user == null) {
           user = this.mqttUserList
                   .stream()
                   .filter(mqttUser -> mqttUser.getCandidateId() == null)
                   .findFirst()
                   .orElse(null);
           if(user != null){
               user.setCandidateId(candidateId);
           }
       }
       return user;
    }

    @Override
    public void sendMessage(Message message) {
        PublishRequest request = new PublishRequest();
        request.setTopic(message.getCandidateId().toString());
        request.setPayload(message.toJsonString());
        request.setQos(1);
        this.emqRemote.publish(request);
    }

    public List<MqttUser> getUserList (){
        List<MqttUser> list = new ArrayList<>();
        list.add(new MqttUser("user1","dw9fjv65"));
        list.add(new MqttUser("user2","mhtg2esw"));
        list.add(new MqttUser("user3","q5d6cvj9"));
        list.add(new MqttUser("user4","r5dux36e"));
        list.add(new MqttUser("user5","vrz32w9s"));
        list.add(new MqttUser("user6","qct52rua"));
        list.add(new MqttUser("user7","bpk3r6e5"));
        list.add(new MqttUser("user8","r49nvk8s"));
        list.add(new MqttUser("user9","wezq5p8g"));
        list.add(new MqttUser("user10","r6v7xs59"));
        list.add(new MqttUser("user11","gux4wdt6"));
        list.add(new MqttUser("user12","b87g6zps"));
        list.add(new MqttUser("user13","p3kjbv5h"));
        list.add(new MqttUser("user14","axu9kzrt"));
        list.add(new MqttUser("user15","h9x74m3r"));
        list.add(new MqttUser("user16","x48nu5bs"));
        list.add(new MqttUser("user17","ewhx7mq3"));
        list.add(new MqttUser("user18","yw8dzugr"));
        list.add(new MqttUser("user19","exs75rd6"));
        list.add(new MqttUser("user20","cgvn85sx"));
        list.add(new MqttUser("user21","x9vzc32u"));
        list.add(new MqttUser("user22","qbkc6d94"));
        list.add(new MqttUser("user23","tbk5sy2x"));
        list.add(new MqttUser("user24","k53qfcpy"));
        list.add(new MqttUser("user25","b4j9mvcr"));
        list.add(new MqttUser("user26","ey5a298q"));
        list.add(new MqttUser("user27","k268zty3"));
        list.add(new MqttUser("user28","hndwzk49"));
        list.add(new MqttUser("user29","us6mht7z"));
        list.add(new MqttUser("user30","wcnqu7hz"));
        list.add(new MqttUser("user31","frx9wya6"));
        list.add(new MqttUser("user32","v62n3jp8"));
        list.add(new MqttUser("user33","b6f2qh48"));
        list.add(new MqttUser("user34","tmfyd85z"));
        list.add(new MqttUser("user35","chfa46zq"));
        list.add(new MqttUser("user36","bd9yntx4"));
        list.add(new MqttUser("user37","p9vhbgw4"));
        list.add(new MqttUser("user38","redx9c6h"));
        list.add(new MqttUser("user39","hcasrx6p"));
        list.add(new MqttUser("user40","zhj74wna"));
        list.add(new MqttUser("user41","uyga3wfq"));
        list.add(new MqttUser("user42","xmkqg625"));
        list.add(new MqttUser("user43","d2xksqcy"));
        list.add(new MqttUser("user44","gpcj8xaw"));
        list.add(new MqttUser("user45","sw8f9475"));
        list.add(new MqttUser("user46","wj9grpmy"));
        list.add(new MqttUser("user47","asj48myh"));
        list.add(new MqttUser("user48","exjya6rq"));
        list.add(new MqttUser("user49","qmj9u4y6"));
        list.add(new MqttUser("user50","m4yqds8p"));
        list.add(new MqttUser("user51","zcm4vkgw"));
        list.add(new MqttUser("user52","v3dubtcs"));
        list.add(new MqttUser("user53","d5n9v3hg"));
        list.add(new MqttUser("user54","e9vds36b"));
        list.add(new MqttUser("user55","vjnk8mau"));
        list.add(new MqttUser("user56","h4dfx5am"));
        list.add(new MqttUser("user57","r3qcu7gd"));
        list.add(new MqttUser("user58","w3c47fj2"));
        list.add(new MqttUser("user59","udk2gavm"));
        list.add(new MqttUser("user60","qcm8g4yj"));
        list.add(new MqttUser("user61","paed36nw"));
        list.add(new MqttUser("user62","zs3md57g"));
        list.add(new MqttUser("user63","gc4zyj96"));
        list.add(new MqttUser("user64","pzhqsf7b"));
        list.add(new MqttUser("user65","qt4cv7nm"));
        list.add(new MqttUser("user66","h74pjty5"));
        list.add(new MqttUser("user67","bhkawn3g"));
        list.add(new MqttUser("user68","ad9xu7r2"));
        list.add(new MqttUser("user69","rj6uz3qy"));
        list.add(new MqttUser("user70","eu4d3t52"));
        list.add(new MqttUser("user71","mp86yua5"));
        list.add(new MqttUser("user72","w368n4zm"));
        list.add(new MqttUser("user73","a3g9b6yv"));
        list.add(new MqttUser("user74","b6sr8gex"));
        list.add(new MqttUser("user75","pxz698w5"));
        list.add(new MqttUser("user76","x4v5a6tg"));
        list.add(new MqttUser("user77","uc6aw48j"));
        list.add(new MqttUser("user78","p9kujerz"));
        list.add(new MqttUser("user79","x47mr2jv"));
        list.add(new MqttUser("user80","vfy4x2up"));
        list.add(new MqttUser("user81","fnp825tz"));
        list.add(new MqttUser("user82","mv24q3e7"));
        list.add(new MqttUser("user83","chv9d38u"));
        list.add(new MqttUser("user84","ubk35mcy"));
        list.add(new MqttUser("user85","nbgt9qds"));
        list.add(new MqttUser("user86","trn8hgyf"));
        list.add(new MqttUser("user87","b5v32txp"));
        list.add(new MqttUser("user88","mgrvtd6c"));
        list.add(new MqttUser("user89","p48tbxgz"));
        list.add(new MqttUser("user90","b9h28qga"));
        list.add(new MqttUser("user91","d2g9u3pf"));
        list.add(new MqttUser("user92","rwt28xsp"));
        list.add(new MqttUser("user93","ts6y93d5"));
        list.add(new MqttUser("user94","m5a2c94n"));
        list.add(new MqttUser("user95","afu7br24"));
        list.add(new MqttUser("user96","trj8b6m9"));
        list.add(new MqttUser("user97","d2jve6x4"));
        list.add(new MqttUser("user98","bn2mx5fp"));
        list.add(new MqttUser("user99","ysug39b2"));
        list.add(new MqttUser("user100","buz8qxtw"));
        list.add(new MqttUser("user101","uapbe9r3"));
        list.add(new MqttUser("user102","wvy9jhcx"));
        list.add(new MqttUser("user103","w69cn3yd"));
        list.add(new MqttUser("user104","td4v73gj"));
        list.add(new MqttUser("user105","y7wq2fkh"));
        list.add(new MqttUser("user106","wcmx4gnq"));
        list.add(new MqttUser("user107","ht7mw5ux"));
        list.add(new MqttUser("user108","vxmwud9y"));
        list.add(new MqttUser("user109","vunc58xe"));
        list.add(new MqttUser("user110","b4w53rzj"));
        list.add(new MqttUser("user111","em7atrdu"));
        list.add(new MqttUser("user112","g6re47tn"));
        list.add(new MqttUser("user113","g7vt9cm2"));
        list.add(new MqttUser("user114","z8m4bs2u"));
        list.add(new MqttUser("user115","xu7v8mh3"));
        list.add(new MqttUser("user116","q259kxne"));
        list.add(new MqttUser("user117","mpr6j52d"));
        list.add(new MqttUser("user118","xmbz7w5y"));
        list.add(new MqttUser("user119","a8t3fj9d"));
        list.add(new MqttUser("user120","bhad68f4"));
        list.add(new MqttUser("user121","mx62dqje"));
        list.add(new MqttUser("user122","h6k4b7gm"));
        list.add(new MqttUser("user123","k29hq3jb"));
        list.add(new MqttUser("user124","cw6j3gzp"));
        list.add(new MqttUser("user125","e4tmxjd6"));
        list.add(new MqttUser("user126","ys9k7uf5"));
        list.add(new MqttUser("user127","eh92gcry"));
        list.add(new MqttUser("user128","d7w8hbfc"));
        list.add(new MqttUser("user129","h6gct2yf"));
        list.add(new MqttUser("user130","a59s2hzf"));
        list.add(new MqttUser("user131","cf85n4mx"));
        list.add(new MqttUser("user132","ft3wd4xu"));
        list.add(new MqttUser("user133","p9nvr472"));
        list.add(new MqttUser("user134","n5yczkvg"));
        list.add(new MqttUser("user135","zjsg7ear"));
        list.add(new MqttUser("user136","um2xcf9p"));
        list.add(new MqttUser("user137","kn9z2jr8"));
        list.add(new MqttUser("user138","eg4u865j"));
        list.add(new MqttUser("user139","c4u37hkj"));
        list.add(new MqttUser("user140","m3g9sytz"));
        list.add(new MqttUser("user141","h4v95ans"));
        list.add(new MqttUser("user142","r4exhj2n"));
        list.add(new MqttUser("user143","kht8y96p"));
        list.add(new MqttUser("user144","s5m49ekv"));
        list.add(new MqttUser("user145","vkuh632w"));
        list.add(new MqttUser("user146","f9ng86ah"));
        list.add(new MqttUser("user147","hmd2r7yg"));
        list.add(new MqttUser("user148","rbp576x3"));
        list.add(new MqttUser("user149","s8vh4xzc"));
        list.add(new MqttUser("user150","hnj9bx5c"));
        list.add(new MqttUser("user151","b5wjn7a2"));
        list.add(new MqttUser("user152","f8zb42hs"));
        list.add(new MqttUser("user153","gp5av2r8"));
        list.add(new MqttUser("user154","cy7b2u6v"));
        list.add(new MqttUser("user155","yx7zqv9a"));
        list.add(new MqttUser("user156","jsqzhp9x"));
        list.add(new MqttUser("user157","ybh8r27k"));
        list.add(new MqttUser("user158","xwft3p5k"));
        list.add(new MqttUser("user159","r4w2m6cx"));
        list.add(new MqttUser("user160","rtpyc25m"));
        list.add(new MqttUser("user161","xuzj498g"));
        list.add(new MqttUser("user162","jeguac39"));
        list.add(new MqttUser("user163","aq3h96nj"));
        list.add(new MqttUser("user164","y5sxdmjc"));
        list.add(new MqttUser("user165","w8jmfr75"));
        list.add(new MqttUser("user166","djv3gc7n"));
        list.add(new MqttUser("user167","rt6ykqjw"));
        list.add(new MqttUser("user168","phk7f53y"));
        list.add(new MqttUser("user169","gsc3fjyb"));
        list.add(new MqttUser("user170","dp8a6qtj"));
        list.add(new MqttUser("user171","r5sk9uwb"));
        list.add(new MqttUser("user172","g975kzcq"));
        list.add(new MqttUser("user173","xr4dgnwu"));
        list.add(new MqttUser("user174","jtpn96qh"));
        list.add(new MqttUser("user175","vhzr8spt"));
        list.add(new MqttUser("user176","bgnuy6fj"));
        list.add(new MqttUser("user177","wyav3n6u"));
        list.add(new MqttUser("user178","s6j94nw7"));
        list.add(new MqttUser("user179","uk7mgj4f"));
        list.add(new MqttUser("user180","c2w96ndb"));
        list.add(new MqttUser("user181","ra972s8c"));
        list.add(new MqttUser("user182","f3my9jdv"));
        list.add(new MqttUser("user183","spuc9ta6"));
        list.add(new MqttUser("user184","r4w3mkqz"));
        list.add(new MqttUser("user185","cw7rqg3e"));
        list.add(new MqttUser("user186","m3gqthpk"));
        list.add(new MqttUser("user187","fe437b9m"));
        list.add(new MqttUser("user188","a9prk6uj"));
        list.add(new MqttUser("user189","fwypce83"));
        list.add(new MqttUser("user190","x7ckaz8h"));
        list.add(new MqttUser("user191","kjqx9cuy"));
        list.add(new MqttUser("user192","z6ncja2r"));
        list.add(new MqttUser("user193","n4gp9aqe"));
        list.add(new MqttUser("user194","uzn53f6x"));
        list.add(new MqttUser("user195","ape5s7f9"));
        list.add(new MqttUser("user196","trkq8jex"));
        list.add(new MqttUser("user197","ehvf7384"));
        list.add(new MqttUser("user198","d825f7s9"));
        list.add(new MqttUser("user199","av3ny7pk"));
        list.add(new MqttUser("user200","th375rx8"));
        list.add(new MqttUser("user201","k8pfe3ma"));
        list.add(new MqttUser("user202","nmwf9gju"));
        list.add(new MqttUser("user203","nx63ug74"));
        list.add(new MqttUser("user204","v7gru6we"));
        list.add(new MqttUser("user205","ek9n6dqs"));
        list.add(new MqttUser("user206","ed9qh2vr"));
        list.add(new MqttUser("user207","ej7admt5"));
        list.add(new MqttUser("user208","jscw2f9v"));
        list.add(new MqttUser("user209","r2wf8z3x"));
        list.add(new MqttUser("user210","w68zd732"));
        list.add(new MqttUser("user211","gszwra5e"));
        list.add(new MqttUser("user212","ka8n4rvj"));
        list.add(new MqttUser("user213","xn628uk5"));
        list.add(new MqttUser("user214","d8xqs25v"));
        list.add(new MqttUser("user215","ygz8nr4u"));
        list.add(new MqttUser("user216","r9u6hfjd"));
        list.add(new MqttUser("user217","m9zkfsb6"));
        list.add(new MqttUser("user218","jr72buwf"));
        list.add(new MqttUser("user219","a7vndthy"));
        list.add(new MqttUser("user220","bdhpu5sa"));
        list.add(new MqttUser("user221","hxz2t8sn"));
        list.add(new MqttUser("user222","dqy9ct2x"));
        list.add(new MqttUser("user223","bdfc28pv"));
        list.add(new MqttUser("user224","c248y7sb"));
        list.add(new MqttUser("user225","t9g7zdrk"));
        list.add(new MqttUser("user226","h5mpgt29"));
        list.add(new MqttUser("user227","jrwu2eg6"));
        list.add(new MqttUser("user228","p9a654fe"));
        list.add(new MqttUser("user229","ru493tpa"));
        list.add(new MqttUser("user230","rxe49wzv"));
        list.add(new MqttUser("user231","vq4ac3p9"));
        list.add(new MqttUser("user232","z7wea8u4"));
        list.add(new MqttUser("user233","zu5tr83d"));
        list.add(new MqttUser("user234","ht36z9ev"));
        list.add(new MqttUser("user235","xp4ge3zv"));
        list.add(new MqttUser("user236","d32wjsyx"));
        list.add(new MqttUser("user237","yqteus9z"));
        list.add(new MqttUser("user238","nc26wzvs"));
        list.add(new MqttUser("user239","tsnhb7pm"));
        list.add(new MqttUser("user240","rk4sya9d"));
        list.add(new MqttUser("user241","ew98bqh3"));
        list.add(new MqttUser("user242","sey4f3mc"));
        list.add(new MqttUser("user243","ycgux8r5"));
        list.add(new MqttUser("user244","tgz8a3bj"));
        list.add(new MqttUser("user245","q4rehp5j"));
        list.add(new MqttUser("user246","u9tbjh3m"));
        list.add(new MqttUser("user247","xrgch3qf"));
        list.add(new MqttUser("user248","a6vxf54y"));
        list.add(new MqttUser("user249","cjyh8xtd"));
        list.add(new MqttUser("user250","bfnszx2h"));
        list.add(new MqttUser("user251","nmxa97ec"));
        list.add(new MqttUser("user252","pfek7a5v"));
        list.add(new MqttUser("user253","khz5gfux"));
        list.add(new MqttUser("user254","f53rvu97"));
        list.add(new MqttUser("user255","vk8nj432"));
        list.add(new MqttUser("user256","whymr6kg"));
        list.add(new MqttUser("user257","za6pet9h"));
        list.add(new MqttUser("user258","rz6c5qtd"));
        list.add(new MqttUser("user259","emahc4kq"));
        list.add(new MqttUser("user260","rcye2tn9"));
        list.add(new MqttUser("user261","e7ug2nvx"));
        list.add(new MqttUser("user262","a53ft6q4"));
        list.add(new MqttUser("user263","emy7fpcz"));
        list.add(new MqttUser("user264","exf7hywp"));
        list.add(new MqttUser("user265","p2yr8zq6"));
        list.add(new MqttUser("user266","h2kq9zcg"));
        list.add(new MqttUser("user267","pb4d5vz7"));
        list.add(new MqttUser("user268","rntf65vp"));
        list.add(new MqttUser("user269","fjmx2rzk"));
        list.add(new MqttUser("user270","s869hnmj"));
        list.add(new MqttUser("user271","nskwvm7z"));
        list.add(new MqttUser("user272","egfws4un"));
        list.add(new MqttUser("user273","gk2c37y8"));
        list.add(new MqttUser("user274","mgy9kv2w"));
        list.add(new MqttUser("user275","phxd827f"));
        list.add(new MqttUser("user276","xzt82sa5"));
        list.add(new MqttUser("user277","u6xf7d9j"));
        list.add(new MqttUser("user278","hcps5fzw"));
        list.add(new MqttUser("user279","d7hk9m8f"));
        list.add(new MqttUser("user280","a95x2upc"));
        list.add(new MqttUser("user281","kcp7frv2"));
        list.add(new MqttUser("user282","a6cv8gwr"));
        list.add(new MqttUser("user283","pxa3g76j"));
        list.add(new MqttUser("user284","es9vf354"));
        list.add(new MqttUser("user285","szrf3eac"));
        list.add(new MqttUser("user286","k3u7edvb"));
        list.add(new MqttUser("user287","y6swp4du"));
        list.add(new MqttUser("user288","w7s6pb4e"));
        list.add(new MqttUser("user289","bg7vm4qa"));
        list.add(new MqttUser("user290","fmxkj2a7"));
        list.add(new MqttUser("user291","x5team37"));
        list.add(new MqttUser("user292","e3b9nxps"));
        list.add(new MqttUser("user293","ve7mh9uc"));
        list.add(new MqttUser("user294","fn6v8qaz"));
        list.add(new MqttUser("user295","ndpe8625"));
        list.add(new MqttUser("user296","eg63qd2b"));
        list.add(new MqttUser("user297","y29nt7em"));
        list.add(new MqttUser("user298","q9sx4jzp"));
        list.add(new MqttUser("user299","e29vjfhc"));
        list.add(new MqttUser("user300","vkh2begr"));
        list.add(new MqttUser("user301","gb9n8qv4"));
        list.add(new MqttUser("user302","bex9yf62"));
        list.add(new MqttUser("user303","ujqvk5b3"));
        list.add(new MqttUser("user304","wym37npx"));
        list.add(new MqttUser("user305","bkc8qf9v"));
        list.add(new MqttUser("user306","tvg86fcj"));
        list.add(new MqttUser("user307","ek5x37bq"));
        list.add(new MqttUser("user308","bqg6d72t"));
        list.add(new MqttUser("user309","wdhzus7a"));
        list.add(new MqttUser("user310","r2pakcqd"));
        list.add(new MqttUser("user311","pxt2umrc"));
        list.add(new MqttUser("user312","t2urfwah"));
        list.add(new MqttUser("user313","dvqamu2t"));
        list.add(new MqttUser("user314","u7sbnjfy"));
        list.add(new MqttUser("user315","qvjx8f5s"));
        list.add(new MqttUser("user316","z8sfynej"));
        list.add(new MqttUser("user317","uw37kvsh"));
        list.add(new MqttUser("user318","rdf48ats"));
        list.add(new MqttUser("user319","p5mhw63r"));
        list.add(new MqttUser("user320","avmsy47e"));
        list.add(new MqttUser("user321","nw7r9hkm"));
        list.add(new MqttUser("user322","k8hs5mwg"));
        list.add(new MqttUser("user323","ah9nk38m"));
        list.add(new MqttUser("user324","fzug857r"));
        list.add(new MqttUser("user325","qg3phrk8"));
        list.add(new MqttUser("user326","dnyk9jts"));
        list.add(new MqttUser("user327","fxk3ap9c"));
        list.add(new MqttUser("user328","zmwpv82j"));
        list.add(new MqttUser("user329","nkdt5uy8"));
        list.add(new MqttUser("user330","cq5umb98"));
        list.add(new MqttUser("user331","rmdh8at2"));
        list.add(new MqttUser("user332","ecz72fvm"));
        list.add(new MqttUser("user333","hm2t9swa"));
        list.add(new MqttUser("user334","ry2jt4uk"));
        list.add(new MqttUser("user335","sqgjx2p9"));
        list.add(new MqttUser("user336","d7qgp8m4"));
        list.add(new MqttUser("user337","srat74x2"));
        list.add(new MqttUser("user338","fz7mcs8q"));
        list.add(new MqttUser("user339","br2nmjv6"));
        list.add(new MqttUser("user340","vtnc2a3r"));
        list.add(new MqttUser("user341","c4utkwg3"));
        list.add(new MqttUser("user342","ujya79ds"));
        list.add(new MqttUser("user343","uyftrq7c"));
        list.add(new MqttUser("user344","k9f3raz5"));
        list.add(new MqttUser("user345","px2q7d9v"));
        list.add(new MqttUser("user346","jwypv8k2"));
        list.add(new MqttUser("user347","v5j8eua7"));
        list.add(new MqttUser("user348","fz52d6jc"));
        list.add(new MqttUser("user349","mp7j43x2"));
        list.add(new MqttUser("user350","q9er3xb8"));
        list.add(new MqttUser("user351","tf7yvb6q"));
        list.add(new MqttUser("user352","k3gqw6m8"));
        list.add(new MqttUser("user353","usp9kjfw"));
        list.add(new MqttUser("user354","puxj3wqv"));
        list.add(new MqttUser("user355","f7dha95v"));
        list.add(new MqttUser("user356","snh4ep79"));
        list.add(new MqttUser("user357","dck24tjm"));
        list.add(new MqttUser("user358","dqspym9w"));
        list.add(new MqttUser("user359","xqe4t9n7"));
        list.add(new MqttUser("user360","ebscr268"));
        list.add(new MqttUser("user361","bae3pr8c"));
        list.add(new MqttUser("user362","wf4g5akp"));
        list.add(new MqttUser("user363","tjasp6bx"));
        list.add(new MqttUser("user364","xe7249kz"));
        list.add(new MqttUser("user365","b6c5uhz7"));
        list.add(new MqttUser("user366","z6gm94hf"));
        list.add(new MqttUser("user367","vycn79xe"));
        list.add(new MqttUser("user368","baku95sj"));
        list.add(new MqttUser("user369","t374rep9"));
        list.add(new MqttUser("user370","r6a928sv"));
        list.add(new MqttUser("user371","k5964fqd"));
        list.add(new MqttUser("user372","s36kw9yu"));
        list.add(new MqttUser("user373","cbu24z3m"));
        list.add(new MqttUser("user374","nq2wsv58"));
        list.add(new MqttUser("user375","ebd3q27s"));
        list.add(new MqttUser("user376","sc2k7bha"));
        list.add(new MqttUser("user377","tfap4hyq"));
        list.add(new MqttUser("user378","dsuy2bhw"));
        list.add(new MqttUser("user379","drgft38w"));
        list.add(new MqttUser("user380","mbenk9zd"));
        list.add(new MqttUser("user381","q3v8fzne"));
        list.add(new MqttUser("user382","h6cbn4qe"));
        list.add(new MqttUser("user383","k6u8w4g5"));
        list.add(new MqttUser("user384","jf7gbukq"));
        list.add(new MqttUser("user385","hd3bx9yv"));
        list.add(new MqttUser("user386","d5bm8cnw"));
        list.add(new MqttUser("user387","hbdg6f5e"));
        list.add(new MqttUser("user388","k7xuhypz"));
        list.add(new MqttUser("user389","kfjrp7nv"));
        list.add(new MqttUser("user390","spe3x7q4"));
        list.add(new MqttUser("user391","x5aw8k9b"));
        list.add(new MqttUser("user392","j7sz3d62"));
        list.add(new MqttUser("user393","a25ygp7z"));
        list.add(new MqttUser("user394","m5daj2nh"));
        list.add(new MqttUser("user395","pkyux57d"));
        list.add(new MqttUser("user396","gtjn4f2v"));
        list.add(new MqttUser("user397","f92jus58"));
        list.add(new MqttUser("user398","rkzthp2a"));
        list.add(new MqttUser("user399","aj683mgk"));
        list.add(new MqttUser("user400","h8tf9nda"));
        list.add(new MqttUser("user401","hj9ze7ta"));
        list.add(new MqttUser("user402","ype2g85s"));
        list.add(new MqttUser("user403","aj943hq5"));
        list.add(new MqttUser("user404","k2zmpvnw"));
        list.add(new MqttUser("user405","ydn7saf2"));
        list.add(new MqttUser("user406","w2869ytr"));
        list.add(new MqttUser("user407","eqbf37nx"));
        list.add(new MqttUser("user408","je7qdzm3"));
        list.add(new MqttUser("user409","wjz684vh"));
        list.add(new MqttUser("user410","uzape8t2"));
        list.add(new MqttUser("user411","q5rdkjam"));
        list.add(new MqttUser("user412","t8jhvk6d"));
        list.add(new MqttUser("user413","ngu4f3rk"));
        list.add(new MqttUser("user414","qafjez6k"));
        list.add(new MqttUser("user415","u96v5scx"));
        list.add(new MqttUser("user416","zhqtv97n"));
        list.add(new MqttUser("user417","k4n9w2fe"));
        list.add(new MqttUser("user418","subtc354"));
        list.add(new MqttUser("user419","h2d3s8tg"));
        list.add(new MqttUser("user420","j5x7s4h2"));
        list.add(new MqttUser("user421","z2twyf4n"));
        list.add(new MqttUser("user422","dkxbu37v"));
        list.add(new MqttUser("user423","catvj9nb"));
        list.add(new MqttUser("user424","egv8y3kp"));
        list.add(new MqttUser("user425","adtnyu54"));
        list.add(new MqttUser("user426","w7ypem2d"));
        list.add(new MqttUser("user427","gf2v7h8x"));
        list.add(new MqttUser("user428","fzu3psgd"));
        list.add(new MqttUser("user429","jr65tb3p"));
        list.add(new MqttUser("user430","u8t79y6s"));
        list.add(new MqttUser("user431","bvn98wt6"));
        list.add(new MqttUser("user432","dr38egpj"));
        list.add(new MqttUser("user433","dxmv3ja6"));
        list.add(new MqttUser("user434","ynpu8m3k"));
        list.add(new MqttUser("user435","x9wh3y4k"));
        list.add(new MqttUser("user436","rk3t6h25"));
        list.add(new MqttUser("user437","y2q3t6pv"));
        list.add(new MqttUser("user438","gt8syjua"));
        list.add(new MqttUser("user439","yb5m2adu"));
        list.add(new MqttUser("user440","vh2bnpsx"));
        list.add(new MqttUser("user441","swvtd7a3"));
        list.add(new MqttUser("user442","tkebg8f5"));
        list.add(new MqttUser("user443","a4e3vxhw"));
        list.add(new MqttUser("user444","bhpzg6qf"));
        list.add(new MqttUser("user445","h52uxqd9"));
        list.add(new MqttUser("user446","sj3u9fb6"));
        list.add(new MqttUser("user447","a38m7uc5"));
        list.add(new MqttUser("user448","m6j85dte"));
        list.add(new MqttUser("user449","f7tr2a9g"));
        list.add(new MqttUser("user450","yn4zjp6m"));
        list.add(new MqttUser("user451","rah5uz6b"));
        list.add(new MqttUser("user452","ewzsc5qm"));
        list.add(new MqttUser("user453","yhxk4vfj"));
        list.add(new MqttUser("user454","zhg573fc"));
        list.add(new MqttUser("user455","gcth4973"));
        list.add(new MqttUser("user456","s9utdcmf"));
        list.add(new MqttUser("user457","br9as8m7"));
        list.add(new MqttUser("user458","s25qngt9"));
        list.add(new MqttUser("user459","kf6qz3d4"));
        list.add(new MqttUser("user460","n2x3zsbu"));
        list.add(new MqttUser("user461","ce29qyra"));
        list.add(new MqttUser("user462","t7mz2vu6"));
        list.add(new MqttUser("user463","erukf6by"));
        list.add(new MqttUser("user464","e8a7v3gu"));
        list.add(new MqttUser("user465","bvgdxz9j"));
        list.add(new MqttUser("user466","w5k68bap"));
        list.add(new MqttUser("user467","dp54bw6q"));
        list.add(new MqttUser("user468","saf8dwtn"));
        list.add(new MqttUser("user469","fq8pnzr9"));
        list.add(new MqttUser("user470","mehc4yj6"));
        list.add(new MqttUser("user471","c4ma3he5"));
        list.add(new MqttUser("user472","kvj9ms3z"));
        list.add(new MqttUser("user473","cfmw42je"));
        list.add(new MqttUser("user474","h4adqu2g"));
        list.add(new MqttUser("user475","ngxe37dp"));
        list.add(new MqttUser("user476","knt2dq8g"));
        list.add(new MqttUser("user477","khzeus65"));
        list.add(new MqttUser("user478","p3dkrnyw"));
        list.add(new MqttUser("user479","fsp6tv5q"));
        list.add(new MqttUser("user480","dq695rfj"));
        list.add(new MqttUser("user481","gt7kej4c"));
        list.add(new MqttUser("user482","ane8g5pd"));
        list.add(new MqttUser("user483","w6sjyvrb"));
        list.add(new MqttUser("user484","m6yx8f2j"));
        list.add(new MqttUser("user485","t9q8e4jw"));
        list.add(new MqttUser("user486","wvz54sfp"));
        list.add(new MqttUser("user487","gp8v6y5z"));
        list.add(new MqttUser("user488","rpe6q435"));
        list.add(new MqttUser("user489","atp9rqc5"));
        list.add(new MqttUser("user490","k7tya36f"));
        list.add(new MqttUser("user491","ekp9d6z7"));
        list.add(new MqttUser("user492","e86gqxcn"));
        list.add(new MqttUser("user493","n43pwr9c"));
        list.add(new MqttUser("user494","tbqds5pz"));
        list.add(new MqttUser("user495","w8dz9ycf"));
        list.add(new MqttUser("user496","pe4k8w9a"));
        list.add(new MqttUser("user497","fnxbg7wm"));
        list.add(new MqttUser("user498","bvcxzy8k"));
        list.add(new MqttUser("user499","vh2q4bam"));
        list.add(new MqttUser("user500","cp9brvq7"));
        list.add(new MqttUser("user501","g3vrayjp"));
        list.add(new MqttUser("user502","xnzwu2y5"));
        list.add(new MqttUser("user503","fzcryu9p"));
        list.add(new MqttUser("user504","fxaqjd3y"));
        list.add(new MqttUser("user505","ykgur5x8"));
        list.add(new MqttUser("user506","asx7r9f4"));
        list.add(new MqttUser("user507","u4k9f7wv"));
        list.add(new MqttUser("user508","phna7xc4"));
        list.add(new MqttUser("user509","hbdgy4x8"));
        list.add(new MqttUser("user510","cqx85z2n"));
        list.add(new MqttUser("user511","cb7xv9ap"));
        list.add(new MqttUser("user512","e4yx327r"));
        list.add(new MqttUser("user513","b9a7jnc4"));
        list.add(new MqttUser("user514","ma72zxjf"));
        list.add(new MqttUser("user515","jmt5upd6"));
        list.add(new MqttUser("user516","vgjhu9f7"));
        list.add(new MqttUser("user517","bhgrnj3s"));
        list.add(new MqttUser("user518","f4h3uykw"));
        list.add(new MqttUser("user519","s732b8yc"));
        list.add(new MqttUser("user520","bf2puax6"));
        list.add(new MqttUser("user521","na7zqu5d"));
        list.add(new MqttUser("user522","nhwdv4t9"));
        list.add(new MqttUser("user523","n6tq9m7h"));
        list.add(new MqttUser("user524","t9q2xwvp"));
        list.add(new MqttUser("user525","b482sdfn"));
        list.add(new MqttUser("user526","ywp75frz"));
        list.add(new MqttUser("user527","pz6y3uch"));
        list.add(new MqttUser("user528","yhu46paq"));
        list.add(new MqttUser("user529","wt2c7dyb"));
        list.add(new MqttUser("user530","mgz32cjn"));
        list.add(new MqttUser("user531","m3w6xkgs"));
        list.add(new MqttUser("user532","v946ehqw"));
        list.add(new MqttUser("user533","hu8f3d54"));
        list.add(new MqttUser("user534","fup9t2b5"));
        list.add(new MqttUser("user535","jun3zk8q"));
        list.add(new MqttUser("user536","qfpem8by"));
        list.add(new MqttUser("user537","rtnf6g3s"));
        list.add(new MqttUser("user538","v28ftxh6"));
        list.add(new MqttUser("user539","u76vgexc"));
        list.add(new MqttUser("user540","hn2s84v3"));
        list.add(new MqttUser("user541","dzjt527u"));
        list.add(new MqttUser("user542","xvcf6ryg"));
        list.add(new MqttUser("user543","y4fjs68m"));
        list.add(new MqttUser("user544","vsgrq9f6"));
        list.add(new MqttUser("user545","j96upfmc"));
        list.add(new MqttUser("user546","ube5m8qk"));
        list.add(new MqttUser("user547","wnsh74j8"));
        list.add(new MqttUser("user548","qz7642py"));
        list.add(new MqttUser("user549","mc2q6y5a"));
        list.add(new MqttUser("user550","wjpc5gad"));
        list.add(new MqttUser("user551","mtx73hp9"));
        list.add(new MqttUser("user552","pj8xnrt7"));
        list.add(new MqttUser("user553","rgnu6c4e"));
        list.add(new MqttUser("user554","zhvebw65"));
        list.add(new MqttUser("user555","db3atq47"));
        list.add(new MqttUser("user556","unw37hpg"));
        list.add(new MqttUser("user557","jsdu63yr"));
        list.add(new MqttUser("user558","hrmt68z7"));
        list.add(new MqttUser("user559","w6ryus84"));
        list.add(new MqttUser("user560","humkj9fq"));
        list.add(new MqttUser("user561","t49amx8z"));
        list.add(new MqttUser("user562","z9d8anxb"));
        list.add(new MqttUser("user563","genp7rmu"));
        list.add(new MqttUser("user564","d8a6z3e5"));
        list.add(new MqttUser("user565","whq68np4"));
        list.add(new MqttUser("user566","nyxmj7wb"));
        list.add(new MqttUser("user567","upxzts3h"));
        list.add(new MqttUser("user568","h62tegnv"));
        list.add(new MqttUser("user569","z7vysnuj"));
        list.add(new MqttUser("user570","xas4twy3"));
        list.add(new MqttUser("user571","e642svkg"));
        list.add(new MqttUser("user572","pg2b465d"));
        list.add(new MqttUser("user573","ednhpk8t"));
        list.add(new MqttUser("user574","y4ku7wg8"));
        list.add(new MqttUser("user575","w6fs8hey"));
        list.add(new MqttUser("user576","ncb63t9s"));
        list.add(new MqttUser("user577","vcngqk5b"));
        list.add(new MqttUser("user578","r5u7xfcj"));
        list.add(new MqttUser("user579","xqj54tck"));
        list.add(new MqttUser("user580","zj5wtey8"));
        list.add(new MqttUser("user581","x37ew49h"));
        list.add(new MqttUser("user582","qw57vsn8"));
        list.add(new MqttUser("user583","n4zdxh69"));
        list.add(new MqttUser("user584","w8be4u59"));
        list.add(new MqttUser("user585","v7h8395a"));
        list.add(new MqttUser("user586","rnhej8vf"));
        list.add(new MqttUser("user587","afumwe6y"));
        list.add(new MqttUser("user588","vpe49rc5"));
        list.add(new MqttUser("user589","mzgqa4bk"));
        list.add(new MqttUser("user590","twzr539q"));
        list.add(new MqttUser("user591","wjt6n5e4"));
        list.add(new MqttUser("user592","m3txrew6"));
        list.add(new MqttUser("user593","t43prwks"));
        list.add(new MqttUser("user594","zcmfa54v"));
        list.add(new MqttUser("user595","t5gxe63b"));
        list.add(new MqttUser("user596","gwmy7kax"));
        list.add(new MqttUser("user597","cpu35y74"));
        list.add(new MqttUser("user598","mv5n4scj"));
        list.add(new MqttUser("user599","da9bqhyw"));
        list.add(new MqttUser("user600","kh39z8ge"));
        list.add(new MqttUser("user601","xz7h2db5"));
        list.add(new MqttUser("user602","b6h5c4mj"));
        list.add(new MqttUser("user603","k6s843y5"));
        list.add(new MqttUser("user604","m9fqvus6"));
        list.add(new MqttUser("user605","hr3x9zbw"));
        list.add(new MqttUser("user606","pu5f38wz"));
        list.add(new MqttUser("user607","q6trfzny"));
        list.add(new MqttUser("user608","z2spkrna"));
        list.add(new MqttUser("user609","f9zb87xp"));
        list.add(new MqttUser("user610","eqy59kp2"));
        list.add(new MqttUser("user611","t7rk3g4u"));
        list.add(new MqttUser("user612","nt7jrwva"));
        list.add(new MqttUser("user613","uqj2cm4e"));
        list.add(new MqttUser("user614","hvzyb9rf"));
        list.add(new MqttUser("user615","a5fw2vn9"));
        list.add(new MqttUser("user616","pv4c7xfs"));
        list.add(new MqttUser("user617","na6zpk79"));
        list.add(new MqttUser("user618","k947daeq"));
        list.add(new MqttUser("user619","adw47cgv"));
        list.add(new MqttUser("user620","y3ehtz9f"));
        list.add(new MqttUser("user621","mbtpfr5x"));
        list.add(new MqttUser("user622","wd28bmsu"));
        list.add(new MqttUser("user623","xte753jz"));
        list.add(new MqttUser("user624","b2fcgd8j"));
        list.add(new MqttUser("user625","v4t5ykmj"));
        list.add(new MqttUser("user626","av8t3jz6"));
        list.add(new MqttUser("user627","u2a6j8vy"));
        list.add(new MqttUser("user628","jx73vmyd"));
        list.add(new MqttUser("user629","rydt3gbx"));
        list.add(new MqttUser("user630","eyrmbq2k"));
        list.add(new MqttUser("user631","r7ugxd3q"));
        list.add(new MqttUser("user632","z2cpbxh8"));
        list.add(new MqttUser("user633","fuqsav57"));
        list.add(new MqttUser("user634","k27g8sw3"));
        list.add(new MqttUser("user635","q8v796tp"));
        list.add(new MqttUser("user636","dtw4vzbq"));
        list.add(new MqttUser("user637","qtm8v5z4"));
        list.add(new MqttUser("user638","xdn2thrw"));
        list.add(new MqttUser("user639","f8j27cxe"));
        list.add(new MqttUser("user640","enuz7gbw"));
        list.add(new MqttUser("user641","cy7hsax4"));
        list.add(new MqttUser("user642","d42yfkus"));
        list.add(new MqttUser("user643","b794ku3x"));
        list.add(new MqttUser("user644","jzvx5cwt"));
        list.add(new MqttUser("user645","nuk94d3m"));
        list.add(new MqttUser("user646","bs7umzc9"));
        list.add(new MqttUser("user647","m8zwsb6e"));
        list.add(new MqttUser("user648","tw47yfqk"));
        list.add(new MqttUser("user649","gbp96xdu"));
        list.add(new MqttUser("user650","c5hmja84"));
        list.add(new MqttUser("user651","ep72u3ca"));
        list.add(new MqttUser("user652","ktx95gch"));
        list.add(new MqttUser("user653","x4dhrzme"));
        list.add(new MqttUser("user654","g4c6jwyu"));
        list.add(new MqttUser("user655","waf92q7s"));
        list.add(new MqttUser("user656","zmds5jh9"));
        list.add(new MqttUser("user657","hndg927a"));
        list.add(new MqttUser("user658","h6f2gjt8"));
        list.add(new MqttUser("user659","p6u8rd9q"));
        list.add(new MqttUser("user660","u48cpt9a"));
        list.add(new MqttUser("user661","w4nkxgfs"));
        list.add(new MqttUser("user662","jhqpt46d"));
        list.add(new MqttUser("user663","w5rmc82t"));
        list.add(new MqttUser("user664","sf3yeg64"));
        list.add(new MqttUser("user665","szw8cr9q"));
        list.add(new MqttUser("user666","sgqa32zp"));
        list.add(new MqttUser("user667","h87ynas9"));
        list.add(new MqttUser("user668","wdyrtz9b"));
        list.add(new MqttUser("user669","bj6293wm"));
        list.add(new MqttUser("user670","td9h6wg4"));
        list.add(new MqttUser("user671","gxqv43hp"));
        list.add(new MqttUser("user672","g79mrv5z"));
        list.add(new MqttUser("user673","w62eztd4"));
        list.add(new MqttUser("user674","shpm8vya"));
        list.add(new MqttUser("user675","n4b3drhu"));
        list.add(new MqttUser("user676","ug6f3t95"));
        list.add(new MqttUser("user677","t4n2xfc3"));
        list.add(new MqttUser("user678","unr5364p"));
        list.add(new MqttUser("user679","ngptv7fs"));
        list.add(new MqttUser("user680","fj46t7cw"));
        list.add(new MqttUser("user681","agkr9tfz"));
        list.add(new MqttUser("user682","cb2edg37"));
        list.add(new MqttUser("user683","rvn5d9g3"));
        list.add(new MqttUser("user684","kxveut5m"));
        list.add(new MqttUser("user685","wu2zv736"));
        list.add(new MqttUser("user686","mckqdb3a"));
        list.add(new MqttUser("user687","dgept2sz"));
        list.add(new MqttUser("user688","jrmqv532"));
        list.add(new MqttUser("user689","ue2c7kxt"));
        list.add(new MqttUser("user690","nm6awgse"));
        list.add(new MqttUser("user691","fc7kawnh"));
        list.add(new MqttUser("user692","gb69jrhy"));
        list.add(new MqttUser("user693","q3kmpxhc"));
        list.add(new MqttUser("user694","fm5bj8rp"));
        list.add(new MqttUser("user695","s4e9z658"));
        list.add(new MqttUser("user696","qx8pnz2d"));
        list.add(new MqttUser("user697","yp7s9tgx"));
        list.add(new MqttUser("user698","bjendu64"));
        list.add(new MqttUser("user699","cewbjm24"));
        list.add(new MqttUser("user700","exw7gf95"));
        list.add(new MqttUser("user701","cw5hxmas"));
        list.add(new MqttUser("user702","qdwg6t7u"));
        list.add(new MqttUser("user703","ke3f5p6j"));
        list.add(new MqttUser("user704","dgev43f2"));
        list.add(new MqttUser("user705","chg57mrz"));
        list.add(new MqttUser("user706","spjt2d8u"));
        list.add(new MqttUser("user707","znjx4h2c"));
        list.add(new MqttUser("user708","v7rz8jp6"));
        list.add(new MqttUser("user709","b5j3hd2y"));
        list.add(new MqttUser("user710","mf5pbkzw"));
        list.add(new MqttUser("user711","y35ur9ms"));
        list.add(new MqttUser("user712","ea32umqd"));
        list.add(new MqttUser("user713","ce7qpy9s"));
        list.add(new MqttUser("user714","jy68rzuh"));
        list.add(new MqttUser("user715","r9s43m2q"));
        list.add(new MqttUser("user716","w84bqja2"));
        list.add(new MqttUser("user717","e2ygu3pf"));
        list.add(new MqttUser("user718","ds6urck8"));
        list.add(new MqttUser("user719","trnujh94"));
        list.add(new MqttUser("user720","b6uep7nw"));
        list.add(new MqttUser("user721","qn9u3cv5"));
        list.add(new MqttUser("user722","tq9zr52c"));
        list.add(new MqttUser("user723","zcyg742m"));
        list.add(new MqttUser("user724","m279vnwu"));
        list.add(new MqttUser("user725","c6q9pk4x"));
        list.add(new MqttUser("user726","j4e8m95p"));
        list.add(new MqttUser("user727","a8w2b9d6"));
        list.add(new MqttUser("user728","hsrt5269"));
        list.add(new MqttUser("user729","xhp8n594"));
        list.add(new MqttUser("user730","x3n294gf"));
        list.add(new MqttUser("user731","c54hzv3a"));
        list.add(new MqttUser("user732","zp8g7m59"));
        list.add(new MqttUser("user733","t742hjyb"));
        list.add(new MqttUser("user734","kdcpj9re"));
        list.add(new MqttUser("user735","y72fs6tv"));
        list.add(new MqttUser("user736","h6zdn5p4"));
        list.add(new MqttUser("user737","g8t6n4ah"));
        list.add(new MqttUser("user738","c52hav4s"));
        list.add(new MqttUser("user739","qn8hz75w"));
        list.add(new MqttUser("user740","zcw3yxvk"));
        list.add(new MqttUser("user741","hypxek85"));
        list.add(new MqttUser("user742","jbw8gcya"));
        list.add(new MqttUser("user743","dv28cws9"));
        list.add(new MqttUser("user744","kamcz49t"));
        list.add(new MqttUser("user745","ck9685e7"));
        list.add(new MqttUser("user746","dxwte9ub"));
        list.add(new MqttUser("user747","gs5836ht"));
        list.add(new MqttUser("user748","jrnft85m"));
        list.add(new MqttUser("user749","m5zq2nud"));
        list.add(new MqttUser("user750","w4be6vgm"));
        list.add(new MqttUser("user751","n748wehu"));
        list.add(new MqttUser("user752","j46c2zxg"));
        list.add(new MqttUser("user753","p2qv6cuy"));
        list.add(new MqttUser("user754","u8v54q36"));
        list.add(new MqttUser("user755","vpw62jyg"));
        list.add(new MqttUser("user756","urx5wtk3"));
        list.add(new MqttUser("user757","tw73zk98"));
        list.add(new MqttUser("user758","y3htze7q"));
        list.add(new MqttUser("user759","kc9geu2d"));
        list.add(new MqttUser("user760","dp9c3qaj"));
        list.add(new MqttUser("user761","sf3jdub9"));
        list.add(new MqttUser("user762","b8fcsz3n"));
        list.add(new MqttUser("user763","mtgys7w8"));
        list.add(new MqttUser("user764","n647rwet"));
        list.add(new MqttUser("user765","dybmp843"));
        list.add(new MqttUser("user766","t2gc36j8"));
        list.add(new MqttUser("user767","bt4ykqar"));
        list.add(new MqttUser("user768","h493fkdt"));
        list.add(new MqttUser("user769","v4h2w5nz"));
        list.add(new MqttUser("user770","m5nthkfx"));
        list.add(new MqttUser("user771","y65vensz"));
        list.add(new MqttUser("user772","qsubxp87"));
        list.add(new MqttUser("user773","mhnbjx4q"));
        list.add(new MqttUser("user774","n8vcth7y"));
        list.add(new MqttUser("user775","x5yqm6fe"));
        list.add(new MqttUser("user776","uk4zdcf7"));
        list.add(new MqttUser("user777","qc5hvx4e"));
        list.add(new MqttUser("user778","cvs8nkjy"));
        list.add(new MqttUser("user779","mtse4y7f"));
        list.add(new MqttUser("user780","fp2hgwen"));
        list.add(new MqttUser("user781","qt9vc7uy"));
        list.add(new MqttUser("user782","hazvkd6p"));
        list.add(new MqttUser("user783","zkye8c3x"));
        list.add(new MqttUser("user784","xa6k7tdb"));
        list.add(new MqttUser("user785","npjexg4q"));
        list.add(new MqttUser("user786","azj2596k"));
        list.add(new MqttUser("user787","zsw3prhu"));
        list.add(new MqttUser("user788","kz9eg4fc"));
        list.add(new MqttUser("user789","j5n396bx"));
        list.add(new MqttUser("user790","j42r9af8"));
        list.add(new MqttUser("user791","ka27xuq3"));
        list.add(new MqttUser("user792","fkytd635"));
        list.add(new MqttUser("user793","n4e2pj9m"));
        list.add(new MqttUser("user794","kqhj49uc"));
        list.add(new MqttUser("user795","swu4j6vr"));
        list.add(new MqttUser("user796","b39n8yfx"));
        list.add(new MqttUser("user797","ep43krnz"));
        list.add(new MqttUser("user798","u46t7zyn"));
        list.add(new MqttUser("user799","ubqczh6w"));
        list.add(new MqttUser("user800","uw7qha3x"));
        list.add(new MqttUser("user801","pktx652z"));
        list.add(new MqttUser("user802","tnqzy5ad"));
        list.add(new MqttUser("user803","trbwqf26"));
        list.add(new MqttUser("user804","sraedw6z"));
        list.add(new MqttUser("user805","y7483v6x"));
        list.add(new MqttUser("user806","kc749mhg"));
        list.add(new MqttUser("user807","xw4ya3jm"));
        list.add(new MqttUser("user808","qxzw2fkh"));
        list.add(new MqttUser("user809","s2fkzp5u"));
        list.add(new MqttUser("user810","dqfmx58a"));
        list.add(new MqttUser("user811","r3dh79nm"));
        list.add(new MqttUser("user812","dm5a79p3"));
        list.add(new MqttUser("user813","ne3qg5w4"));
        list.add(new MqttUser("user814","her3bx4a"));
        list.add(new MqttUser("user815","xam95seu"));
        list.add(new MqttUser("user816","rmew9vbp"));
        list.add(new MqttUser("user817","r4eba657"));
        list.add(new MqttUser("user818","vhyxfr2k"));
        list.add(new MqttUser("user819","mwqu3gf4"));
        list.add(new MqttUser("user820","ykqgvj4x"));
        list.add(new MqttUser("user821","qmt6u2bx"));
        list.add(new MqttUser("user822","gdk769xf"));
        list.add(new MqttUser("user823","d3kyug57"));
        list.add(new MqttUser("user824","wcq57rvu"));
        list.add(new MqttUser("user825","g6bmk7sz"));
        list.add(new MqttUser("user826","jpn7kmru"));
        list.add(new MqttUser("user827","ywh7f9t3"));
        list.add(new MqttUser("user828","xc68jsz3"));
        list.add(new MqttUser("user829","mu2gfrpb"));
        list.add(new MqttUser("user830","b72czj6e"));
        list.add(new MqttUser("user831","zmjwe58u"));
        list.add(new MqttUser("user832","m7dk6w92"));
        list.add(new MqttUser("user833","we6qm2f8"));
        list.add(new MqttUser("user834","r69wzmcp"));
        list.add(new MqttUser("user835","wgtpr2ke"));
        list.add(new MqttUser("user836","swd2eg8j"));
        list.add(new MqttUser("user837","erzph5j4"));
        list.add(new MqttUser("user838","nqpedm72"));
        list.add(new MqttUser("user839","k5netf3j"));
        list.add(new MqttUser("user840","u4cxrnt6"));
        list.add(new MqttUser("user841","drg9as5c"));
        list.add(new MqttUser("user842","j32v5tm4"));
        list.add(new MqttUser("user843","g5sxzte2"));
        list.add(new MqttUser("user844","ej543hwg"));
        list.add(new MqttUser("user845","xzgw2han"));
        list.add(new MqttUser("user846","j9yd6qx2"));
        list.add(new MqttUser("user847","sdw92bt7"));
        list.add(new MqttUser("user848","vjd4s2r9"));
        list.add(new MqttUser("user849","zeq2nc58"));
        list.add(new MqttUser("user850","q2f4dpje"));
        list.add(new MqttUser("user851","w59zheg4"));
        list.add(new MqttUser("user852","tgcq5wm3"));
        list.add(new MqttUser("user853","rf9tc287"));
        list.add(new MqttUser("user854","kyj8atec"));
        list.add(new MqttUser("user855","j5vm8rzy"));
        list.add(new MqttUser("user856","fxh7kqte"));
        list.add(new MqttUser("user857","wsnd8gm6"));
        list.add(new MqttUser("user858","fhj3cep5"));
        list.add(new MqttUser("user859","k4p65wts"));
        list.add(new MqttUser("user860","yf4h36wq"));
        list.add(new MqttUser("user861","cmu98pnz"));
        list.add(new MqttUser("user862","xc968vq3"));
        list.add(new MqttUser("user863","x36sda52"));
        list.add(new MqttUser("user864","bqh7sndy"));
        list.add(new MqttUser("user865","hxa9yb7j"));
        list.add(new MqttUser("user866","t35fbk8a"));
        list.add(new MqttUser("user867","pbheuz6j"));
        list.add(new MqttUser("user868","xuawj2th"));
        list.add(new MqttUser("user869","na64chvx"));
        list.add(new MqttUser("user870","s7r6yk9q"));
        list.add(new MqttUser("user871","hys75nfr"));
        list.add(new MqttUser("user872","yh8mgf4j"));
        list.add(new MqttUser("user873","e963ydps"));
        list.add(new MqttUser("user874","aqfrd685"));
        list.add(new MqttUser("user875","fqv4y72j"));
        list.add(new MqttUser("user876","vszu4qhc"));
        list.add(new MqttUser("user877","d62w3xbg"));
        list.add(new MqttUser("user878","n2c6p7fx"));
        list.add(new MqttUser("user879","edu9fhz4"));
        list.add(new MqttUser("user880","zt5e4yqa"));
        list.add(new MqttUser("user881","f5matnyw"));
        list.add(new MqttUser("user882","gkbred3x"));
        list.add(new MqttUser("user883","r5hw76xp"));
        list.add(new MqttUser("user884","kr54udm9"));
        list.add(new MqttUser("user885","jhvyu8xw"));
        list.add(new MqttUser("user886","g2bh94cy"));
        list.add(new MqttUser("user887","n83kzdu5"));
        list.add(new MqttUser("user888","czr5v9mk"));
        list.add(new MqttUser("user889","gkr9wamv"));
        list.add(new MqttUser("user890","cyvqzd96"));
        list.add(new MqttUser("user891","rknjw9bm"));
        list.add(new MqttUser("user892","fka3bhs4"));
        list.add(new MqttUser("user893","q95esdxh"));
        list.add(new MqttUser("user894","a57yk9sf"));
        list.add(new MqttUser("user895","zx9d7ap5"));
        list.add(new MqttUser("user896","q6zba3g8"));
        list.add(new MqttUser("user897","g86khujp"));
        list.add(new MqttUser("user898","s72nq5ha"));
        list.add(new MqttUser("user899","hnqtg29y"));
        list.add(new MqttUser("user900","psecuw6n"));
        list.add(new MqttUser("user901","pb2a37mt"));
        list.add(new MqttUser("user902","dqek2a3s"));
        list.add(new MqttUser("user903","bt27gxup"));
        list.add(new MqttUser("user904","d743zrmg"));
        list.add(new MqttUser("user905","r3pc5496"));
        list.add(new MqttUser("user906","qh7pn62s"));
        list.add(new MqttUser("user907","gjr7wq2x"));
        list.add(new MqttUser("user908","u6jmfx73"));
        list.add(new MqttUser("user909","b7k4hqpu"));
        list.add(new MqttUser("user910","t9q5j7sz"));
        list.add(new MqttUser("user911","qm5pt9c8"));
        list.add(new MqttUser("user912","xu96atcw"));
        list.add(new MqttUser("user913","nu5pje47"));
        list.add(new MqttUser("user914","a9xrb4qm"));
        list.add(new MqttUser("user915","kym43wb5"));
        list.add(new MqttUser("user916","nv2fwg87"));
        list.add(new MqttUser("user917","snb9hjze"));
        list.add(new MqttUser("user918","am397sh2"));
        list.add(new MqttUser("user919","w52cme9f"));
        list.add(new MqttUser("user920","rceu59ak"));
        list.add(new MqttUser("user921","uxvaf9j5"));
        list.add(new MqttUser("user922","cr3pkyxh"));
        list.add(new MqttUser("user923","ty53erdx"));
        list.add(new MqttUser("user924","p6ucr4yj"));
        list.add(new MqttUser("user925","rmy7t96h"));
        list.add(new MqttUser("user926","yeb8pa7s"));
        list.add(new MqttUser("user927","m86ytne5"));
        list.add(new MqttUser("user928","p8htnw42"));
        list.add(new MqttUser("user929","sbu7g4j5"));
        list.add(new MqttUser("user930","k5a3nw2s"));
        list.add(new MqttUser("user931","n6jzev4a"));
        list.add(new MqttUser("user932","f7m8a6b3"));
        list.add(new MqttUser("user933","byv4hzje"));
        list.add(new MqttUser("user934","pz93vtry"));
        list.add(new MqttUser("user935","tynpm32h"));
        list.add(new MqttUser("user936","qt543rh7"));
        list.add(new MqttUser("user937","kzr5u4yf"));
        list.add(new MqttUser("user938","bm8aned3"));
        list.add(new MqttUser("user939","tvfx249c"));
        list.add(new MqttUser("user940","s96ytre3"));
        list.add(new MqttUser("user941","pnmu8vc3"));
        list.add(new MqttUser("user942","a6qbzn2j"));
        list.add(new MqttUser("user943","bjpe57sn"));
        list.add(new MqttUser("user944","mtqe8h4d"));
        list.add(new MqttUser("user945","wrqash9p"));
        list.add(new MqttUser("user946","fw9htpu4"));
        list.add(new MqttUser("user947","nthx5p27"));
        list.add(new MqttUser("user948","mpxn9f3c"));
        list.add(new MqttUser("user949","bar64juc"));
        list.add(new MqttUser("user950","c74dz6ts"));
        list.add(new MqttUser("user951","gu6qmx2e"));
        list.add(new MqttUser("user952","masv6pr7"));
        list.add(new MqttUser("user953","qnj4btr5"));
        list.add(new MqttUser("user954","rvet387n"));
        list.add(new MqttUser("user955","xt79aur4"));
        list.add(new MqttUser("user956","r4n8wf3g"));
        list.add(new MqttUser("user957","xtq6s5pn"));
        list.add(new MqttUser("user958","saf2v84x"));
        list.add(new MqttUser("user959","fenmu7qc"));
        list.add(new MqttUser("user960","v6zh74ra"));
        list.add(new MqttUser("user961","pn2xwa9e"));
        list.add(new MqttUser("user962","e2butpwr"));
        list.add(new MqttUser("user963","e36zumsy"));
        list.add(new MqttUser("user964","ck5dq4zy"));
        list.add(new MqttUser("user965","g8rkuz2t"));
        list.add(new MqttUser("user966","b4zv8mgx"));
        list.add(new MqttUser("user967","qsbu62ax"));
        list.add(new MqttUser("user968","hbg2fe6d"));
        list.add(new MqttUser("user969","wbu2eahf"));
        list.add(new MqttUser("user970","u75bxwkt"));
        list.add(new MqttUser("user971","fpwteq7s"));
        list.add(new MqttUser("user972","pj7t3kgf"));
        list.add(new MqttUser("user973","qu4ydgzw"));
        list.add(new MqttUser("user974","sn4q58w9"));
        list.add(new MqttUser("user975","aq53j7c8"));
        list.add(new MqttUser("user976","hqfxb72c"));
        list.add(new MqttUser("user977","rg94tq3n"));
        list.add(new MqttUser("user978","p5bzehk7"));
        list.add(new MqttUser("user979","y8enjm49"));
        list.add(new MqttUser("user980","pkv32x5u"));
        list.add(new MqttUser("user981","hfdq5wr3"));
        list.add(new MqttUser("user982","v3rxaj2c"));
        list.add(new MqttUser("user983","vfn3zkm4"));
        list.add(new MqttUser("user984","grb43f8e"));
        list.add(new MqttUser("user985","sx8cwgb3"));
        list.add(new MqttUser("user986","m9bw2kdf"));
        list.add(new MqttUser("user987","yn7q2tr6"));
        list.add(new MqttUser("user988","mcv3ur76"));
        list.add(new MqttUser("user989","sep4bt2g"));
        list.add(new MqttUser("user990","e9h6ay2m"));
        list.add(new MqttUser("user991","s5c76pzd"));
        list.add(new MqttUser("user992","n4rdu3et"));
        list.add(new MqttUser("user993","f4ke2u7r"));
        list.add(new MqttUser("user994","pan3d5bs"));
        list.add(new MqttUser("user995","g8suje2n"));
        list.add(new MqttUser("user996","fp96gc3b"));
        list.add(new MqttUser("user997","h2vy64sp"));
        list.add(new MqttUser("user998","vtkdzr7x"));
        list.add(new MqttUser("user999","uz72tavp"));
        list.add(new MqttUser("user1000","weamr5b7"));
        return list;
    }
}
