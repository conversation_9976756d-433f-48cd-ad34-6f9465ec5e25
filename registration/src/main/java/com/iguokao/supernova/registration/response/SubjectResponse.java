package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.registration.document.Subject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class SubjectResponse {

    private String projectId;
    private String templateId;
    private String subjectId;

    private String name;
    private String info;
    private Double price;

    private Integer passScore;
    private Integer candidateCount;
    private Integer scoreImportedCount;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date endAt;

    public static SubjectResponse of(Subject obj){
        if(obj==null){
            return null;
        }
        SubjectResponse res = new SubjectResponse();
        BeanUtils.copyProperties(obj, res);
        res.setProjectId(obj.getProjectId().toString());
        res.setSubjectId(obj.get_id().toString());

        return res;
    }

    public static List<SubjectResponse> of(List<Subject> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<SubjectResponse> res = new ArrayList<>();
        for(Subject obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
