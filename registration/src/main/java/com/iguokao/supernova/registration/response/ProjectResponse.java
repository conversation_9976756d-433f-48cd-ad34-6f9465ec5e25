package com.iguokao.supernova.registration.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.registration.document.Project;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ProjectResponse {
    private String projectId;
    private String examProjectId;

    private String name;
    private String agreement;
    private Integer state = 0;

    @Schema(description = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "结束时间")
    private Date endAt;

    @Schema(description = "模版Id")
    private String templateId;

    @Schema(description = "有限报名")
    private Boolean limited;

    @Schema(description = "状态")
    private Boolean online;

    @Schema(description = "模版Id")
    private String supplementaryTemplateId;

    private Boolean pay;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date payStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date payEndAt;

    // 准考证
    private Boolean admission;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date admissionStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date admissionEndAt;

    private Boolean invoice;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date invoiceEndAt;
    // 报名相关
    private Boolean signUp;
    private Boolean approve;
    private Boolean subjectMultiSelect;
    private String signUpAgreement;
    private String signUpAvatarNote;
    private String signUpAreaNote;
    private String signUpSubjectNote;
    private String signUpNoticeNote;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date signUpStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date signUpEndAt;

    private List<String> cityList = new ArrayList<>();


    // 成绩查询相关
    private Boolean score = false;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date scoreStartAt;


    // 预约
    private Boolean appoint;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date appointStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date appointEndAt;
    private Integer appointType;
    private String appointNote;

    // 补充
    private Boolean supplementary;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date supplementaryStartAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date supplementaryEndAt;

    public static ProjectResponse of(Project obj){
        if(obj==null){
            return null;
        }
        ProjectResponse res = new ProjectResponse();
        BeanUtils.copyProperties(obj, res);
        res.setProjectId(obj.get_id().toString());
        if(obj.getTemplateId() != null){
            res.setTemplateId(obj.getTemplateId().toString());
        }
        if(obj.getSupplementaryTemplateId() != null){
            res.setSupplementaryTemplateId(obj.getSupplementaryTemplateId().toString());
        }

        if(obj.getSignUpStartAt() != null && obj.getSignUpStartAt().getTime() <= new Date().getTime() && obj.getSignUpEndAt().getTime() > new Date().getTime()){
            res.setState(1);
        }
        else if(obj.getSignUpEndAt() != null && new Date().getTime() > obj.getSignUpEndAt().getTime()){
            res.setState(2);
        }

        if(obj.getExamProjectId() != null){
            res.setExamProjectId(obj.getExamProjectId().toString());
        }
        return res;
    }

    public static List<ProjectResponse> of(List<Project> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<ProjectResponse> res = new ArrayList<>();
        for(Project obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
