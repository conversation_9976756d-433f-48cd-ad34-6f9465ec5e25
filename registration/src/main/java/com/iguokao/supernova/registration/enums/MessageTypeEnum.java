package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum MessageTypeEnum implements BaseEnum {
    TEXT(0, "常规"),
    PAY_SUCCESS(10, "支付成功"),
    ;

    MessageTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
