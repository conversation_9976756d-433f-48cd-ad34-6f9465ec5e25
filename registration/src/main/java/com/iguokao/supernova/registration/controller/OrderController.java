package com.iguokao.supernova.registration.controller;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.iguokao.supernova.common.converter.ValidObjectId;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.enums.OrderStateEnum;
import com.iguokao.supernova.registration.excel.*;
import com.iguokao.supernova.registration.request.PageOrderRequest;
import com.iguokao.supernova.registration.request.PageWithdrawOrderRequest;
import com.iguokao.supernova.registration.response.OrderResponse;
import com.iguokao.supernova.registration.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
@RequestMapping("/api/v1/order")
@RequiredArgsConstructor
public class OrderController {
    private final OrderService orderService;
    private final SubjectService subjectService;
    private final JwtService jwtService;
    private final CacheService cacheService;
    private final TemplateService templateService;

    @PostMapping("/page")
    @Operation(summary = "订单列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<PageResponse<OrderResponse>> page(@RequestBody PageOrderRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        String candidateId = request.getCandidateId();
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Order>, Integer> page =  this.orderService.page(request.getCompanyId(),
                request.getProjectId(),
                candidateId,
                request.getState(),
                request.getFullName(),
                request.getMobile(),
                request.getSource(),
                request.getStartAt(),
                request.getEndAt(),
                request.getWithdraw(),
                pageable);
        return RestResponse.success(new PageResponse<>(OrderResponse.of(page.first()), page.second(), pageable));
    }

    @GetMapping("/cancel/{token}/{tradeNo}")
    @Operation(summary = "取消订单")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> cancel(@PathVariable String token, @PathVariable String tradeNo) {
        String content = this.cacheService.getToken(token);
        if(content == null){
            throw new ServiceException(ExceptionEnum.TOKEN_EXPIRED);
        }
        this.orderService.changeState(tradeNo, OrderStateEnum.CANCEL, null, null);
        return RestResponse.success();
    }

    @GetMapping("/export/excel/{projectId}")
    @Operation(summary = "订单导出")
    public void orderExcel(@ValidObjectId @PathVariable String projectId, HttpServletResponse response) throws IOException {

        List<Order> list = this.orderService.successList(projectId);

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("考生订单_%s", projectId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<List<Object>> res = OrderExporter.data(list);

        EasyExcel.write(response.getOutputStream())
                .sheet("考生列表")
                .head(OrderExporter.head())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(res);
    }

    @GetMapping("/export/excel/invoice/{projectId}")
    @Operation(summary = "订单发票导出")
    public void   invoiceExcel(@ValidObjectId @PathVariable String projectId, HttpServletResponse response) throws IOException {

        List<Order> list = this.orderService.successList(projectId);
        List<TemplateData> dataList = this.templateService.getByProjectId(projectId);

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("考生订单_%s", projectId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<List<Object>> res = InvoiceExporter.data(list, dataList);

        EasyExcel.write(response.getOutputStream())
                .sheet("订单和发票信息")
                .head(InvoiceExporter.head())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(res);
    }
}
