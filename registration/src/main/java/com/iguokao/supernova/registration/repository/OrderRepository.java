package com.iguokao.supernova.registration.repository;

import com.iguokao.supernova.registration.document.AppointItem;
import com.iguokao.supernova.registration.document.Order;
import org.aspectj.weaver.ast.Or;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface OrderRepository extends MongoRepository<Order, ObjectId> {
    int countByProjectIdAndCandidateIdAndState(ObjectId projectId, ObjectId candidateId, int state);
    int countByProjectIdAndStateIn(ObjectId projectId, List<Integer> stateList);


    Optional<Order> findByTradeNo(String tradeNo);

    List<Order> findByProjectIdAndCandidateIdAndState(ObjectId projectId, ObjectId candidateId, int state);
    List<Order> findByProjectIdAndStateIn(ObjectId projectId, List<Integer> stateList);
    List<Order> findByCandidateIdAndStateIn(ObjectId candidateId, List<Integer> stateList);
    List<Order> findByCompanyIdAndStateIn(ObjectId projectId, List<Integer> stateList, Sort sort);

    List<Order> findByCandidateId(ObjectId id);
}
