package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.enums.OrderStateEnum;
import com.iguokao.supernova.registration.repository.*;
import com.iguokao.supernova.registration.service.*;
import com.mongodb.client.result.UpdateResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {
    private final OrderRepository orderRepository;
    private final ProjectRepository projectRepository;
    private final CompanyConfigRepository companyConfigRepository;
    private final SmsService smsService;
    private final HfService hfService;
    private final MongoTemplate mongoTemplate;
    private final CacheService cacheService;
    private final BCryptPasswordEncoder bCryptPasswordEncoder;

    private final TemplateService templateService;

    @Value("${app.dg.pay-callback}")
    String payCallbackUrl;

    @Value("${app.dg.refund-callback}")
    String refundCallbackUrl;

    @Value("${app.dg.withdraw-callback}")
    String withdrawCallbackUrl;



    @Override
    public String add(String candidateId, String companyId, String companySn, String projectId, List<String> subjectIdList, String source, String fullName, String mobile) {
        Project project = this.projectRepository.findById(new ObjectId(projectId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
        if(project.getApprove()){
            TemplateData templateData = this.templateService.getDataByProjectIdAndCandidateId(projectId, candidateId);
            if(templateData.getPassed() == null || !templateData.getPassed()){
                throw new ServiceException(ExceptionEnum.TEMPLATE_DATA_NOT_PASS);
            }
        }
        CompanyConfig config = this.cacheService.getCompanyConfig(companySn);
        Date date = new Date();
        if(date.getTime() > project.getPayEndAt().getTime()){
            throw new ServiceException(ExceptionEnum.ORDER_PAID_TIME_OUT);
        }
        if(date.getTime() < project.getPayStartAt().getTime()){
            throw new ServiceException(ExceptionEnum.ORDER_PAID_TIME_EAR);
        }

        if(this.orderRepository.countByProjectIdAndCandidateIdAndState(new ObjectId(projectId), new ObjectId(candidateId),  OrderStateEnum.SUCCESS.getCode()) > 0){
            throw new ServiceException(ExceptionEnum.ORDER_PROJECT_PAID);
        }
        List<Subject> subjectList = new ArrayList<>();
        subjectIdList.forEach(id -> {
            Subject subject = this.cacheService.getSubject(id);
            subjectList.add(subject);
        });
        if(!subjectIdList.isEmpty()){
            Order order = new Order();
            order.setCompanyId(new ObjectId(companyId));
            order.setHfId(config.getHfId());
            BigDecimal amount = BigDecimal.ZERO;
            String desc = "";
            for(Subject subject : subjectList){
                PayItem item = new PayItem();
                item.setSubjectId(subject.get_id());
                item.setInfo(subject.getName());
                item.setAmount(subject.getPrice());
                order.getPayItemList().add(item);
                amount = amount.add(new BigDecimal(subject.getPrice().toString()));
                desc = String.format("%s%s ", desc, subject.getName());
            }
            order.setDescription(String.format("%s 考试费", desc));
            order.setState(OrderStateEnum.NOT_PAY.getCode());
            order.setAmount(amount.doubleValue());
            order.setSource(source);
            order.setFullName(fullName);
            order.setMobile(mobile);
            order.setTradeAmount(amount.doubleValue());
            order.setCandidateId(new ObjectId(candidateId));
            order.setProjectId(new ObjectId(projectId));
            String tradeNo = this.cacheService.getOrderNumber();
            order.setTradeNo(tradeNo);
            this.orderRepository.save(order);
            return tradeNo;
        }
        return null;
    }

    @Override
    public Order getByTradeNo(String tradeNo) {
        return this.orderRepository.findByTradeNo(tradeNo)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.ORDER_NOT_FOUND));
    }

    @Override
    public Order changeState(String tradeNo, OrderStateEnum state, Double feeAmount, String payload) {
//        log.info("changeState - {} - {} - {}", tradeNo, state, payload);

        Order order = this.orderRepository.findByTradeNo(tradeNo)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.ORDER_NOT_FOUND));
        // 防止多次回调
        if(state == OrderStateEnum.CANCEL && order.getPayOrderId() != null){
            return null;
        }
        if(order.getState().equals(OrderStateEnum.SUCCESS.getCode()) && state.equals(OrderStateEnum.SUCCESS)){
            return null;
        }
        if(order.getState().equals(OrderStateEnum.REFUNDED.getCode()) && state.equals(OrderStateEnum.REFUNDED)){
            return null;
        }
        if(payload != null){
            order.getResultList().add(payload);
        }
        order.setState(state.getCode());
//        if(hfSeqId != null){
//            order.setHfSeqId(hfSeqId);
//        }
        if(state.equals(OrderStateEnum.SUCCESS)){
            order.setFeeAmount(feeAmount);
            order.setTradeAmount(order.getAmount() - feeAmount);
            order.setPaidAt(new Date());
        } else if(state.equals(OrderStateEnum.REFUNDED)){
            order.setRefundedAt(new Date());
        } else if(state.equals(OrderStateEnum.WITHDRAWN)){
            order.setUpdatedAt(new Date());
            String[] list = order.getDescription().split(",");
            this.changeOrderListState(list, OrderStateEnum.FINISHED);
        }
        this.orderRepository.save(order);
        Query query = new Query()
                .addCriteria(Criteria.where("projectId").is(order.getProjectId()))
                .addCriteria(Criteria.where("candidateId").is(order.getCandidateId()));
        if(state.equals(OrderStateEnum.SUCCESS)){
            Update update = new Update();
            update.set("successTradeNo", tradeNo);
            update.set("paidAt", new Date());
            update.set("refund", false);
            mongoTemplate.updateFirst(query, update, TemplateData.class);
        }
//        log.info("state - {} - {} ", tradeNo, state);
        if(state.equals(OrderStateEnum.REFUNDED)){
            Update update = new Update();
            update.unset("successTradeNo");
            update.unset("paidAt");
            update.set("refund", true);
            mongoTemplate.updateFirst(query, update, TemplateData.class);
        }
        return order;
    }

    @Async
    @Override
    public void changeOrderListState(String[] tradeNoList, OrderStateEnum state){
        for (String tradeNo : tradeNoList){
            Query query = new Query()
                    .addCriteria(Criteria.where("tradeNo").is(tradeNo));
            Update update = new Update();
            update.set("state", state.getCode());
            mongoTemplate.updateFirst(query, update, Order.class);
        }
    }

    @Override
    public void addPayload(String tradeNo, String payload) {
        Order order = this.orderRepository.findByTradeNo(tradeNo)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.ORDER_NOT_FOUND));
        if(payload != null){
            order.getResultList().add(payload);
        }
        this.orderRepository.save(order);
    }

    public void setPayOrderId(String tradeNo,  OrderStateEnum state, String orderId, String hfSeqId) {
        Query query = new Query()
                .addCriteria(Criteria.where("tradeNo").is(tradeNo));
        Update update = new Update();
        update.set("state", state.getCode());
        update.set("payOrderId", orderId);
        update.set("hfSeqId", hfSeqId);
        mongoTemplate.updateFirst(query, update, Order.class);
    }

    @Override
    public String wxPrepay(String tradeNo, String openid, String companySn) {
        Order order = this.getByTradeNo(tradeNo);
        if(this.orderRepository.countByProjectIdAndCandidateIdAndState(order.getProjectId(), order.getCandidateId(),  OrderStateEnum.SUCCESS.getCode()) > 0){
            throw new ServiceException(ExceptionEnum.ORDER_PROJECT_PAID);
        }
        Project project = this.cacheService.getProject(order.getProjectId().toString());
        if(new Date().getTime() < project.getPayStartAt().getTime() || new Date().getTime() > project.getPayEndAt().getTime()){
            throw new ServiceException(ExceptionEnum.ORDER_PAID_TIME_ERR);
        }
        CompanyConfig companyConfig = this.cacheService.getCompanyConfig(companySn);
        if(companyConfig.getHfId() == null){
            throw new ServiceException(ExceptionEnum.COMPANY_PAY_NOT_SET);
        }
        HfResult res = this.hfService.pay(order, openid, companyConfig.getHfSplitId());
        if (res.getState().equals("P")) {
            this.setPayOrderId(tradeNo, OrderStateEnum.PAYING, res.getResId(), res.getHfSeqId());
            return res.getResult();
        } else {
            this.addPayload(tradeNo, res.getPayload());
        }
        throw new ServiceException(ExceptionEnum.ORDER_PREPAY_FAILED);
    }

    @Override
    public String aliPrepay(String tradeNo, String companySn) {
        Order order = this.getByTradeNo(tradeNo);
        Project project = this.cacheService.getProject(order.getProjectId().toString());
        if(new Date().getTime() < project.getPayStartAt().getTime() || new Date().getTime() > project.getPayEndAt().getTime()){
            throw new ServiceException(ExceptionEnum.ORDER_PAID_TIME_ERR);
        }
        CompanyConfig companyConfig = this.cacheService.getCompanyConfig(companySn);
        if(companyConfig.getHfId() == null){
            throw new ServiceException(ExceptionEnum.COMPANY_PAY_NOT_SET);
        }
        HfResult res = this.hfService.pay(order, null, companyConfig.getHfSplitId());
        if (res.getState().equals("P")) {
            this.setPayOrderId(tradeNo, OrderStateEnum.PAYING, res.getResId(), res.getHfSeqId());
            return res.getResult();
        } else {
            this.addPayload(tradeNo, res.getPayload());
        }
        log.error("支付宝 支付失败 - {}", res);
        throw new ServiceException(ExceptionEnum.ORDER_PREPAY_FAILED);
    }

    @Override
    public Tuple2<List<Order>, Integer> page(String companyId, String projectId, String candidateId, Integer state, String fullName, String mobile, String source, Date startAt, Date endAt, Boolean withdraw, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt", "_id"));
        if(companyId == null && projectId == null){
            return new Tuple2<>(new ArrayList<>(), 0);
        }
        if (companyId != null) {
            query = query.addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));
        }
        if(withdraw){
            query = query.addCriteria(Criteria.where("state").gte(OrderStateEnum.WITHDRAW_REQUEST.getCode()));
        } else {
            if (projectId != null) {
                query = query.addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)));
            }
            if (candidateId != null) {
                query = query.addCriteria(Criteria.where("candidateId").is(new ObjectId(candidateId)));
            }
            if (state != null) {
                query = query.addCriteria(Criteria.where("state").is(state));
            }
            if (fullName != null) {
                query = query.addCriteria(Criteria.where("fullName").is(fullName));
            }
            if (mobile != null) {
                query = query.addCriteria(Criteria.where("mobile").is(mobile));
            }
            if (source != null) {
                query = query.addCriteria(Criteria.where("source").is(source));
            }
            if (startAt != null && endAt != null) {
                query = query.addCriteria(Criteria.where("createdAt").gte(startAt).lte(endAt));
            }
        }

        // 计算总数
        long count = this.mongoTemplate.count(query, Order.class);
        // 分页信息
        query = query.with(pageable);
        List<Order> list = this.mongoTemplate.find(query, Order.class);

        return new Tuple2<>(list, (int) count);    }

    @Override
    public String successTradeNo(String projectId, String candidateId) {
        List<Order> list = this.orderRepository.findByProjectIdAndCandidateIdAndState(new ObjectId(projectId), new ObjectId(candidateId), OrderStateEnum.SUCCESS.getCode());
        if(!list.isEmpty()){
            return list.get(0).getTradeNo();
        }
        return null;
    }

    @Override
    public void cancel() {
        List<Integer> stateList = new ArrayList<>();
        stateList.add(OrderStateEnum.NOT_PAY.getCode());
        stateList.add(OrderStateEnum.PAYING.getCode());
        // 计算 15 分钟前的时间
        Instant expiredTime = Instant.now().minusSeconds(15 * 60);

        Query query = new Query()
                .addCriteria(Criteria.where("state").in(stateList))
                .addCriteria(Criteria.where("createdAt").lt(expiredTime)); // 15 分钟前的订单

        Update update = new Update();
        update.set("state", OrderStateEnum.CANCEL.getCode());
        UpdateResult res = mongoTemplate.updateMulti(query, update, Order.class);
        res.getModifiedCount();
        log.info("已经取消 {} 订单", res.getModifiedCount());
    }

    @Override
    public List<Order> successList(String projectId) {
        List<Integer> stateList = new ArrayList<>();
        stateList.add(OrderStateEnum.SUCCESS.getCode());
        stateList.add(OrderStateEnum.FINISHED.getCode());
        return this.orderRepository.findByProjectIdAndStateIn(new ObjectId(projectId), stateList);
    }

    @Override
    public void refund(String tradeNo, String candidateId) {
        Order order = this.getByTradeNo(tradeNo);
        if(order.getState().equals(OrderStateEnum.FINISHED.getCode())){
            throw new ServiceException(ExceptionEnum.ORDER_SETTLED);
        }
        if(order.getState().equals(OrderStateEnum.REFUNDED.getCode()) || order.getState().equals(OrderStateEnum.REFUNDING.getCode())){
            throw new ServiceException(ExceptionEnum.ORDER_REFUNDED);
        }
        HfResult res = this.hfService.refund(order);
        switch (res.getState()) {
            case "P" , "S" -> {
                order.setState(OrderStateEnum.REFUNDING.getCode());
                this.orderRepository.save(order);
                this.sendRefundSms(order.getCompanyId().toString(), order.getMobile(), order.getFullName());
            }
//            case "S" -> {
//                order.setState(OrderStateEnum.REFUNDED.getCode());
//                this.orderRepository.save(order);
//                this.sendRefundSms(order.getCompanyId().toString(), order.getMobile(), order.getFullName());
//            }
            case "F" -> {
                log.error(res.getPayload());
                order.getResultList().add(res.getPayload());
                this.orderRepository.save(order);
                throw new ServiceException(ExceptionEnum.ORDER_REFUND_ERR, " - " + res.getResult());
            }
        }
    }

    private void sendRefundSms(String companyId, String mobile, String fullName){
        CompanyConfig config = this.companyConfigRepository.findById(new ObjectId(companyId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.COMPANY_CONFIG_NOT_FOUND));
        if(config.getSmsRefundTemplate() != null){
            this.smsService.sendSms(mobile, config.getSmsRefundTemplate(), List.of(fullName));
        }
    }

    @Override
    public String hfBalance(String companySn) {
        CompanyConfig config = this.cacheService.getCompanyConfig(companySn);
        if(config.getHfId() == null){
            throw new ServiceException(ExceptionEnum.COMPANY_HF_ID_NOT_SET);
        }
        return this.hfService.balance(config.getHfSplitId() == null ? config.getHfId() : config.getHfSplitId());
    }

    @Override
    public Balance balance(String companySn, String projectId) {
        CompanyConfig config = this.cacheService.getCompanyConfig(companySn);
        Query query = new Query()
                .addCriteria(Criteria.where("companyId").is(config.get_id()))
                .addCriteria(Criteria.where("state").in(Arrays.asList(OrderStateEnum.SUCCESS.getCode(), OrderStateEnum.FINISHED.getCode())));;
        if(projectId != null){
            query.addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)));
        }
        query.fields()
                .include("state")
                .include("amount")
                .include("feeAmount")
                .include("source")
                .include("tradeAmount");
        List<Order> list = this.mongoTemplate.find(query, Order.class);
        Balance balance = new Balance();
        balance.setWxOrderCount((int)list
                .stream()
                .filter(order -> order.getSource().equals("wx_pc"))
                .count());
        balance.setAliOrderCount((int)list
                .stream()
                .filter(order -> order.getSource().equals("ali_pc"))
                .count());
        balance.setAmount(StringUtil.toPriceString(list
                .stream()
                .mapToDouble(Order::getAmount)
                .sum()));
        balance.setFee(StringUtil.toPriceString(list
                .stream()
                .mapToDouble(Order::getFeeAmount)
                .sum()));
        balance.setTradeAmount(StringUtil.toPriceString(list
                .stream()
                .mapToDouble(Order::getTradeAmount)
                .sum()));
        balance.setOrderCount(String.valueOf(list.size()));

        balance.setNotSettledFee(StringUtil.toPriceString(list
                .stream()
                .filter(o -> o.getState().equals(OrderStateEnum.SUCCESS.getCode()))
                .mapToDouble(Order::getFeeAmount)
                .sum()));
        balance.setNotSettledTradeAmount(StringUtil.toPriceString(list
                .stream()
                .filter(o -> o.getState().equals(OrderStateEnum.SUCCESS.getCode()))
                .mapToDouble(Order::getTradeAmount)
                .sum()));
        balance.setNotSettledAmount(StringUtil.toPriceString(list
                .stream()
                .filter(o -> o.getState().equals(OrderStateEnum.SUCCESS.getCode()))
                .mapToDouble(Order::getAmount)
                .sum()));
        balance.setNotSettledOrderCount(String.valueOf(list
                .stream()
                .filter(o -> o.getState().equals(OrderStateEnum.SUCCESS.getCode()))
                .count()));


        balance.setSettledFee(StringUtil.toPriceString(list
                .stream()
                .filter(o -> o.getState().equals(OrderStateEnum.FINISHED.getCode()))
                .mapToDouble(Order::getFeeAmount)
                .sum()));
        balance.setSettledTradeAmount(StringUtil.toPriceString(list
                .stream()
                .filter(o -> o.getState().equals(OrderStateEnum.FINISHED.getCode()))
                .mapToDouble(Order::getTradeAmount)
                .sum()));
        balance.setSettledAmount(StringUtil.toPriceString(list
                .stream()
                .filter(o -> o.getState().equals(OrderStateEnum.FINISHED.getCode()))
                .mapToDouble(Order::getAmount)
                .sum()));
        balance.setSettledOrderCount(String.valueOf(list
                .stream()
                .filter(o -> o.getState().equals(OrderStateEnum.FINISHED.getCode()))
                .count()));
        return balance;
    }

    @Override
    public Withdraw  withdrawOrder(String projectId, String operatorId, Date startAt, Date endAt) {
        List<Integer> stateList = Arrays.asList(OrderStateEnum.WITHDRAW_REQUEST.getCode(),
                OrderStateEnum.WITHDRAW_CONFIRM.getCode(),
                OrderStateEnum.WITHDRAWING.getCode());
       if(this.orderRepository.countByProjectIdAndStateIn(new ObjectId(projectId), stateList) > 0) {
            throw new ServiceException(ExceptionEnum.ORDER_WITHDRAWING);
       }

        Query query = new Query()
                .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)))
                .addCriteria(Criteria.where("state").is(OrderStateEnum.SUCCESS.getCode()))
                .addCriteria(Criteria.where("createdAt").gte(startAt).lte(endAt));

        query.fields()
                .include("tradeNo")
                .include("state")
                .include("amount")
                .include("feeAmount")
                .include("source")
                .include("tradeAmount");
        List<Order> list = this.mongoTemplate.find(query, Order.class);
        Withdraw withdraw = new Withdraw();

        withdraw.setWxOrderCount((int)list
                .stream()
                .filter(order -> order.getSource().equals("wx_pc"))
                .count());
        withdraw.setAliOrderCount((int)list
                .stream()
                .filter(order -> order.getSource().equals("ali_pc"))
                .count());
        withdraw.setAmount(StringUtil.toPriceString(list
                .stream()
                .mapToDouble(Order::getAmount)
                .sum()));
        withdraw.setFee(StringUtil.toPriceString(list
                .stream()
                .mapToDouble(Order::getFeeAmount)
                .sum()));
        withdraw.setTradeAmount(StringUtil.toPriceString(list
                .stream()
                .mapToDouble(Order::getTradeAmount)
                .sum()));
        withdraw.setOrderCount(String.valueOf(list.size()));
        String orderNo = String.join(",", list.stream().map(Order::getTradeNo).toList());
        Project project = this.cacheService.getProject(projectId);
        CompanyConfig config = this.companyConfigRepository.findById(project.getCompanyId())
                .orElseThrow(()->new ServiceException(ExceptionEnum.COMPANY_CONFIG_NOT_FOUND));
        Order order = new Order();
        order.setState(OrderStateEnum.WITHDRAW_REQUEST.getCode());
        order.setAmount(Double.valueOf(withdraw.getAmount()));
        order.setTradeAmount(Double.valueOf(withdraw.getTradeAmount()));
        order.setFeeAmount(Double.valueOf(withdraw.getFee()));
        order.setSource("withdraw");
        order.setHfId(config.getHfSplitId() == null ? config.getHfId() : config.getHfSplitId());
        order.setProjectId(new ObjectId(projectId));
        order.setCandidateId(new ObjectId(operatorId));
        order.setDescription(orderNo);
        order.setTradeNo(this.cacheService.getOrderNumber());
        order.setCompanyId(project.getCompanyId());
        this.orderRepository.save(order);

        withdraw.setTradeNo(order.getTradeNo());
        return withdraw;
    }

    @Override
    public void withdrawConfirm(String tradeNo) {
        Order order = this.getByTradeNo(tradeNo);
        if(order.getState().equals(OrderStateEnum.WITHDRAW_REQUEST.getCode())){
            this.changeState(tradeNo, OrderStateEnum.WITHDRAW_CONFIRM, null, null);
        }
    }

    @Override
    public void withdrawCancel(String tradeNo) {
        Order order = this.getByTradeNo(tradeNo);
        if(order.getState().equals(OrderStateEnum.WITHDRAW_REQUEST.getCode()) || order.getState().equals(OrderStateEnum.WITHDRAW_CONFIRM.getCode()) ){
            this.changeState(tradeNo, OrderStateEnum.WITHDRAW_CANCEL, null, null);
        }
    }

    @Override
    public void withdrawPass(String tradeNo, String withdrawPassword) {
        Order order = this.getByTradeNo(tradeNo);
        CompanyConfig config = this.companyConfigRepository.findById(order.getCompanyId())
                .orElseThrow(() -> new ServiceException(ExceptionEnum.COMPANY_CONFIG_NOT_FOUND));
        if(config.getWithdrawPassword() == null){
            throw new ServiceException(ExceptionEnum.WITHDRAW_PASSWORD_EMPTY);
        }
        if(!bCryptPasswordEncoder.matches(withdrawPassword, config.getWithdrawPassword())){
            throw new ServiceException(ExceptionEnum.WITHDRAW_PASSWORD_ERROR);
        }
        if(order.getState().equals(OrderStateEnum.WITHDRAW_CONFIRM.getCode())){
            CompanyConfig companyConfig = this.companyConfigRepository.findById(order.getCompanyId())
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.COMPANY_CONFIG_NOT_FOUND));
            if(companyConfig.getHfTokenNo() == null){
                throw new ServiceException(ExceptionEnum.COMPANY_TOKEN_NO_NOT_SET);
            }
            HfResult res = this.hfService.withdraw(order, config.getHfTokenNo());
            if(res.getState().equals("P")){
                this.changeState(tradeNo, OrderStateEnum.WITHDRAWING, null, null);
            } else {
                log.error(res.getPayload());
                this.addPayload(tradeNo, res.getPayload());
            }
        }
    }


    private String getExpireTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureTime = now.plusHours(24);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return futureTime.format(formatter);
    }

}
