package com.iguokao.supernova.registration.response;
import com.iguokao.supernova.registration.document.AppointItem;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AppointItemResponse {

    private String appointItemId;
    private String projectId;
    private String siteId;
    private String siteName;
    private String siteAddress;

    private String city;
    private Integer current; // 当前人数
    private Integer quota;  // 配额


    public static AppointItemResponse of(AppointItem obj){
        if(obj==null){
            return null;
        }
        AppointItemResponse res = new AppointItemResponse();
        BeanUtils.copyProperties(obj, res);
        res.setProjectId(obj.getProjectId().toString());
        res.setAppointItemId(obj.get_id().toString());

        if(obj.getSiteId() != null){
            res.setSiteId(obj.getSiteId().toString());
        }

        return res;
    }

    public static List<AppointItemResponse> of(List<AppointItem> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<AppointItemResponse> res = new ArrayList<>();
        for(AppointItem obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
