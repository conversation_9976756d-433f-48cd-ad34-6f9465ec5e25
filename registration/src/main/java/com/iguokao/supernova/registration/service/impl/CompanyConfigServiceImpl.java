package com.iguokao.supernova.registration.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.registration.document.AnnouncementCategory;
import com.iguokao.supernova.registration.document.CompanyConfig;
import com.iguokao.supernova.registration.document.SmsTemplate;
import com.iguokao.supernova.registration.enums.ExceptionEnum;
import com.iguokao.supernova.registration.repository.CompanyConfigRepository;
import com.iguokao.supernova.registration.service.CacheService;
import com.iguokao.supernova.registration.service.CompanyConfigService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class CompanyConfigServiceImpl implements CompanyConfigService {
    private final CompanyConfigRepository companyConfigRepository;
    private final MongoTemplate mongoTemplate;
    private final CacheService cacheService;

    @Override
    public void register(CompanyConfig config) {
        int count = this.companyConfigRepository.countBy_id(config.get_id());
        if(count > 0){
            throw new ServiceException(ExceptionEnum.COMPANY_CONFIG_EXIST);
        }
        this.companyConfigRepository.save(config);
    }

    @Override
    public void update(CompanyConfig config) {
        CompanyConfig existConfig = this.companyConfigRepository.findById(config.get_id())
                .orElseThrow(() -> new ServiceException(ExceptionEnum.COMPANY_CONFIG_NOT_FOUND));
        BeanUtils.copyProperties(config, existConfig, "announcementCategoryList", "hfId", "hfTokenNo", "smsTemplateList");
        this.companyConfigRepository.save(existConfig);
        this.cacheService.deleteCompany(config.getSn());
    }

    @Override
    public void update(String companyId, List<AnnouncementCategory> list) {
        Query query = new Query()
                .addCriteria(Criteria.where("_id").is(new ObjectId(companyId)));
        Update candidateUpdate = new Update();
        candidateUpdate.set("announcementCategoryList", list);
        mongoTemplate.updateFirst(query, candidateUpdate, CompanyConfig.class);
    }

    @Override
    public CompanyConfig getById(String companyId) {
        return this.companyConfigRepository.findById(new ObjectId(companyId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.COMPANY_CONFIG_NOT_FOUND));
    }

    @Override
    public CompanyConfig getBySn(String sn) {
        return this.cacheService.getCompanyConfig(sn);
    }

    @Override
    public void addSmsTemplate(String companyId, SmsTemplate smsTemplate) {
        CompanyConfig config = this.getById(companyId);
        if(config.getSmsTemplateList()
                .stream()
                .filter(item -> item.getSmsTemplateId().equals(smsTemplate.getSmsTemplateId()))
                .findFirst()
                .orElse(null) == null){
            config.getSmsTemplateList().add(smsTemplate);
        }
        this.companyConfigRepository.save(config);
        this.cacheService.deleteCompany(config.getSn());
    }

    @Override
    public void removeSmsTemplate(String companyId, String smsTemplateId) {
        CompanyConfig config = this.getById(companyId);
        if(config.getSmsTemplateList()
                .removeIf(item -> item.getSmsTemplateId().equals(smsTemplateId))){
            this.companyConfigRepository.save(config);
            this.cacheService.deleteCompany(config.getSn());
        }
    }
}
