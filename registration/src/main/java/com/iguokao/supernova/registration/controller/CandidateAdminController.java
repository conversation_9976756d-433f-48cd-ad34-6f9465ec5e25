package com.iguokao.supernova.registration.controller;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.iguokao.supernova.common.converter.ValidObjectId;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.enums.CredentialCategoryEnum;
import com.iguokao.supernova.common.enums.ImportTypeEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.registration.document.*;
import com.iguokao.supernova.registration.excel.*;
import com.iguokao.supernova.registration.excel.CandidateItem;
import com.iguokao.supernova.registration.excel.CandidateItemListener;
import com.iguokao.supernova.registration.request.*;
import com.iguokao.supernova.registration.response.CandidateResponse;
import com.iguokao.supernova.registration.response.CandidateStatisticResponse;
import com.iguokao.supernova.registration.response.PageCandidateResponse;
import com.iguokao.supernova.registration.response.TemplateDataStatisticResponse;
import com.iguokao.supernova.registration.service.*;
import com.iguokao.supernova.registration.service.CandidateService;
import com.iguokao.supernova.registration.service.ProjectService;
import com.iguokao.supernova.registration.service.RegistrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/candidate")
@RequiredArgsConstructor
public class CandidateAdminController {
    private final CandidateService candidateService;
    private final TemplateService templateService;
    private final RegistrationService registrationService;
    private final ProjectService projectService;
    private final SubjectService subjectService;
    private final CacheService cacheService;

    @GetMapping("/basic/info/{candidateId}")
    @Operation(summary = "考生详情")
    public RestResponse<CandidateResponse> info(@PathVariable String candidateId){
        Candidate candidate = this.candidateService.getById(candidateId);
        return RestResponse.success(CandidateResponse.of(candidate,null));
    }

    @PostMapping("/edit")
    @Operation(summary = "考生编辑")
    public RestResponse<String> edit(@RequestBody CandidateEditRequest param){
        this.candidateService.edit(param.getSn(), param.getProjectId() ,param.getCandidateId(),param.getFullName(),param.getMobile(),param.getEmail(),
                param.getIdCardNum(),param.getIdCardType(),param.getGender());
        return RestResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "考生删除")
    public RestResponse<String> delete(@RequestBody CandidateDeleteRequest param){
        this.candidateService.delete(param.getProjectId(),param.getSubjectId(),param.getCandidateIdList());
        return RestResponse.success();
    }

    @RequestMapping(value = "/import/{projectId}/{subjectId}", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "批量导入考生")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExcelErrResponse.class))))
    public RestResponse<List<ExcelErrResponse>>  importExcel(@ValidObjectId @PathVariable String projectId,
                                                             @ValidObjectId @PathVariable String subjectId,
                                                             @RequestPart(value = "file") MultipartFile file) throws IOException {
        List<ExcelErrResponse> errResponseList = new ArrayList<>();
        Project project = this.projectService.getById(projectId);

        String lockKey = String.format("can_imp_%s", projectId);
        if(this.cacheService.getLock(lockKey) != null){
            throw new ServiceException(BaseExceptionEnum.IMPORT_PENDING);
        }
        this.cacheService.setLock(lockKey);

        try {
            List<String> existsLoginName = this.candidateService.getExistsLoginNameByCompanyId(project.getCompanyId().toString());
            List<String> existsReg = this.registrationService.getExistsLoginNameBySubjectId(subjectId);

            EasyExcel.read(file.getInputStream(), CandidateItem.class, new CandidateItemListener(project.getCompanyId().toString(),
                    project.get_id().toString(), subjectId, existsLoginName, existsReg,errResponseList, candidateService)).sheet().doRead();
        } finally {
            this.cacheService.deleteLock(lockKey);
        }

        return RestResponse.success(errResponseList);
    }


//    @RequestMapping(value = "/import/template/clear", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
//    @Operation(summary = "批量导入考生并填写报名表数据")
//    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExcelErrResponse.class))))
//    public RestResponse<List<ExcelErrResponse>>  clearTemplate( @RequestPart(value = "file") MultipartFile file) throws IOException {
//
//        EasyExcel.read(file.getInputStream(), CandidateTemplateClearItem.class, new CandidateTemplateClearItemListener(candidateService,templateService,registrationService)).sheet().doRead();
//
//        return RestResponse.success();
//    }
//
//    @RequestMapping(value = "/import/template/group", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
//    @Operation(summary = "批量导入考生并填写报名表数据")
//    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExcelErrResponse.class))))
//    public RestResponse<List<ExcelErrResponse>>  importTemplateExcel( @RequestPart(value = "file") MultipartFile file) throws IOException {
//        List<ExcelErrResponse> errResponseList = new ArrayList<>();
//        String projectId = "67eb794d2ee28d38f6ef7be0";
//        Project project = this.projectService.getById(projectId);
//
//        EasyExcel.read(file.getInputStream(), CandidateTemplateItem.class, new CandidateTemplateItemListener(project.getCompanyId().toString(),
//                project.get_id().toString(),errResponseList, candidateService,templateService)).sheet().doRead();
//
//        return RestResponse.success(errResponseList);
//    }

    @PostMapping("/registration/page")
    @Operation(summary = "考生列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageCandidateResponse.class)))
    public RestResponse<PageResponse<CandidateResponse>> list(@Validated @RequestBody CandidatePageRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Registration>, Integer> page = this.candidateService.getRegistrationPage(request.getProjectId(),
                request.getSubjectId(),
                request.getFullName(),
                request.getMobile(),
                request.getEmailState(),
                request.getSmsState(),
                request.getConfirmState(),
                pageable);

        List<CandidateResponse> candidateResponses = new ArrayList<>();
        for(Registration registration : page.first()){
            Candidate candidate = this.candidateService.getById(registration.getCandidateId().toString());
            CandidateResponse res = CandidateResponse.of(candidate,registration);

            int startIndex;
            int endIndex;
            int length;

            if(res.getIdCardType().equals(CredentialCategoryEnum.ID_CARD.getCode())){
                startIndex = 6;
                endIndex = 12;
            }
            else {
                startIndex = 3;
                endIndex = res.getIdCardNum().length() - 2;
            }
            length = endIndex - startIndex;
            StringBuilder sb = new StringBuilder(res.getIdCardNum());
            sb.replace(startIndex, endIndex, "X".repeat(length));
            String result = sb.toString();
            res.setIdCardNum(result);

            candidateResponses.add(res);
        }

        return RestResponse.success(new PageResponse<>(candidateResponses, page.second(), pageable));
    }

    @PostMapping("/page")
    @Operation(summary = "全部考生列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageCandidateResponse.class)))
    public RestResponse<PageResponse<CandidateResponse>> page(@Validated @RequestBody CandidatePageRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Candidate>, Integer> page = this.candidateService.page(request.getCompanyId(),
                request.getFullName(),
                request.getMobile(),
                pageable);

        return RestResponse.success(new PageResponse<>(CandidateResponse.of(page.first()), page.second(), pageable));
    }

    @GetMapping("/export/excel/{subjectId}")
    @Operation(summary = "导出全部考生")
    public void export(@PathVariable String subjectId, HttpServletResponse response) throws IOException {
        Subject subject = this.cacheService.getSubject(subjectId);
        List<Candidate> confirmedList = this.candidateService.getImportCandidate(subjectId, ImportTypeEnum.CONFIRMED.getCode());
        List<Candidate> notConfirmedList = this.candidateService.getImportCandidate(subjectId, ImportTypeEnum.NOT_CONFORM.getCode());
        List<Candidate> refusedList = this.candidateService.getImportCandidate(subjectId, ImportTypeEnum.REFUSED.getCode());

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("考生列表_%s", subject.getName()), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<List<String>> res = CandidateExporter.data(confirmedList, notConfirmedList, refusedList);

        EasyExcel.write(response.getOutputStream())
                .sheet("考生列表")
                .head(CandidateExporter.head())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(res);
    }

    @GetMapping("/statistic/{companyId}")
    @Operation(summary = "项目统计")
    @ApiResponse(content = @Content(schema = @Schema(implementation = TemplateDataStatisticResponse.class)))
    public RestResponse<CandidateStatisticResponse> statistic(@PathVariable String companyId) {
        CandidateStatisticResponse res = this.cacheService.getCandidateStatistic(companyId);
        return RestResponse.success(res);
    }

}
