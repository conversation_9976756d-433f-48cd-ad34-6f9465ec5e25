package com.iguokao.supernova.registration.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Document
public class Project extends BaseDocument {
    @Indexed(name = "companyId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId examProjectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId supplementaryTemplateId;

    @Indexed(name = "name_index")
    private String name;
    private Boolean online = false; // 离线 在线 结束

    private Date startAt;
    private Date endAt;
    private Long loginExpireTime = 0L;

    // 报名
    private Boolean signUp = false;
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId templateId;
    private Boolean approve = false;     // 是否需要审核
    private Boolean subjectMultiSelect = true;     // 科目是否可以多选
    private Date signUpStartAt;
    private Date signUpEndAt;
    private String signUpAgreement;
    private String signUpAvatarNote;
    private String signUpAreaNote;
    private String signUpSubjectNote;
    private String signUpNoticeNote;


    private List<String> cityList = new ArrayList<>();

    // 支付
    private Boolean pay = false;
    private Date payStartAt;
    private Date payEndAt;

    // 准考证
    private Boolean admission = false;
    private Date admissionStartAt;
    private Date admissionEndAt;
    private Date admissionGeneratedAt;

    private Boolean invoice = false;
    private Date invoiceEndAt;

    // 预约
    private Boolean appoint = false;
    private Date appointStartAt;
    private Date appointEndAt;
    private Integer appointType;
    private String appointNote;

    // 补充
    private Boolean supplementary = false;
    private Date supplementaryStartAt;
    private Date supplementaryEndAt;

    //成绩查询相关，开始、结束时间
    private Boolean score = false;
    private Date scoreStartAt;

}
