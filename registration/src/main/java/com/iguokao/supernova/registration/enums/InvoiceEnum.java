package com.iguokao.supernova.registration.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum InvoiceEnum implements BaseEnum {
    VAT_SPECIAL(1, "增值税专用发票"),
    VA_GENERAL(2, "增值税普通发票"),
    ELECTRONIC(3, "电子发票（增值税普通电子)"),
    ;

    InvoiceEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
