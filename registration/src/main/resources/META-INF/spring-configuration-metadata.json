{"properties": [{"name": "app.app.cloopen.api", "type": "java.lang.String"}, {"name": "app.app.cloopen.account-sid", "type": "java.lang.String"}, {"name": "app.app.cloopen.auth-token", "type": "java.lang.String"}, {"name": "app.app.cloopen.sms-app-id", "type": "java.lang.String"}, {"name": "app.app.cloopen.template.operator-login", "type": "java.lang.String"}, {"name": "app.app.cloopen.template.operator-register", "type": "java.lang.String"}, {"name": "app.app.cloopen.template.password-reset", "type": "java.lang.String"}, {"name": "app.service.exam", "type": "java.lang.String"}, {"name": "app.service.report", "type": "java.lang.String"}, {"name": "app.security.jwt.secret-key", "type": "java.lang.String"}, {"name": "app.security.jwt.expiration", "type": "java.lang.Integer"}, {"name": "app.security.jwt.expiration", "type": "java.lang.Integer"}, {"name": "app.security.jwt.refresh-token.expiration", "type": "java.lang.Integer"}, {"name": "app.redis.database", "type": "java.lang.Integer"}, {"name": "app.redis.host", "type": "java.lang.String"}, {"name": "app.redis.password", "type": "java.lang.String"}, {"name": "app.redis.port", "type": "java.lang.Integer"}, {"name": "app.redis.max-active", "type": "java.lang.Integer"}, {"name": "app.redis.max-idle", "type": "java.lang.Integer"}, {"name": "app.redis.min-idle", "type": "java.lang.Integer"}]}