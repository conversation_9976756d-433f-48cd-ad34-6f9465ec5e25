#!/bin/bash
project="management"
profile="prod"
containerPort="8001"
hostPort="8001"
image="registry.cn-beijing.aliyuncs.com/freewolf/supernova"
xms="Xms128m"
xmx="Xmx512m"
logPath="/root/log"

podman login --username=freew01f --password=spectre27 registry.cn-beijing.aliyuncs.com
podman pull $image:$profile
podman images

podman stop $project
podman rm $project
podman run --name=$project \
    -p $hostPort:$containerPort \
    -v $logPath/$project:/opt/log \
    -e APP_NAME=$project  \
    -e TZ=Asia/Shanghai \
    -e PROFILE=$profile \
    -e XMS=$xms \
    -e XMX=$xmx \
    -d $image:$profile

rm -f /etc/systemd/system/container-$project.service
podman generate systemd --restart-policy=always -t 1 --name -f  $project
mv container-"$project".service /etc/systemd/system/container-"$project".service
systemctl daemon-reload
systemctl enable container-"$project".service

for i in \| \/ \- \\ \| \/ \- \\; do
  echo -n -e "\r Starting $project @ $profile ...... $1  $i  "
  sleep 0.5
done

echo -e "\n"

podman ps
systemctl start container-$project.service
systemctl --no-pager status container-$project.service