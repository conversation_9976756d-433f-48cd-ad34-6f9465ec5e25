apiVersion: v1
kind: Pod
metadata:
  creationTimestamp: "2023-07-20T10:27:50Z"
  labels:
    app: app
  name: app
spec:
  containers:
  - name: management
    image: registry.cn-beijing.aliyuncs.com/freewolf/supernova:test
    ports:
    - containerPort: 8001
      hostPort: 8001
    network:
    - slirp4netns:allow_host_loopback=true
    env:
    - name: APP_NAME
      value: management
    - name: TZ
      value: Asia/Shanghai
    - name: PROFILE
      value: test
    - name: XMS
      value: Xms128m
    - name: XMX
      value: Xmx512m
  - name: exam
    image: localhost/freewolf/stone-service:latest
    ports:
    - containerPort: 8002
      hostPort: 8002
    network:
    - slirp4netns:allow_host_loopback=true
    env:
    - name: APP_NAME
      value: exam
    - name: TZ
      value: Asia/Shanghai
    - name: PROFILE
      value: test
    - name: XMS
      value: Xms128m
    - name: XMX
      value: Xmx512m
    volumeMounts:
    - mountPath: /opt/log
      name: log
  - name: report
    image: localhost/freewolf/stone-service:latest
    ports:
    - containerPort: 8003
      hostPort: 8003
    network:
    - slirp4netns:allow_host_loopback=true
    env:
    - name: APP_NAME
      value: report
    - name: TZ
      value: Asia/Shanghai
    - name: PROFILE
      value: test
    - name: XMS
      value: Xms128m
    - name: XMX
      value: Xmx512m
  volumes:
  - name: log
    hostPath:
      path: /home/<USER>/data/log
      type: Directory