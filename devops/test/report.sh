#!/bin/bash
project="report"
profile="test"
containerPort="8003"
hostPort="8003"
image="registry.cn-beijing.aliyuncs.com/freewolf/supernova:test"
xms="Xms128m"
xmx="Xmx512m"
password="iguokao.123"

echo $password | sudo -S podman login --username=freew01f --password=spectre27 registry.cn-beijing.aliyuncs.com
echo $password | sudo -S podman pull $image
echo $password | sudo -S podman images

echo $password | sudo -S podman stop $project
echo $password | sudo -S podman rm $project
echo $password | sudo -S podman run --name=$project \
    -p $hostPort:$containerPort \
    -v /home/<USER>/data/log/$project:/opt/log \
    -e APP_NAME=$project  \
    -e TZ=Asia/Shanghai \
    -e PROFILE=$profile \
    -e XMS=$xms \
    -e XMX=$xmx \
    -d $image

echo $password | sudo -S rm -f /etc/systemd/system/container-$project.service
echo $password | sudo -S podman generate systemd --restart-policy=always -t 1 --name -f  $project
echo $password | sudo -S mv container-"$project".service /etc/systemd/system/container-"$project".service
echo $password | sudo -S systemctl daemon-reload
echo $password | sudo -S systemctl enable container-"$project".service

for i in \| \/ \- \\ \| \/ \- \\; do
  echo -n -e "\r Starting $project @ $profile ...... $1  $i  "
  sleep 0.5
done

echo -e "\n"

echo $password | sudo -S podman ps
echo $password | sudo -S systemctl start container-$project.service
echo $password | sudo -S systemctl --no-pager status container-$project.service