FROM openjdk:17-alpine

USER root

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

RUN apk update
RUN apk add --no-cache imagemagick
RUN apk add --no-cache curl
RUN apk add --no-cache tzdata
RUN apk --no-cache add msttcorefonts-installer fontconfig && \
    update-ms-fonts && \
    fc-cache -f
ENV TZ Asia/Hong_Kong
RUN curl -O https://gitee.com/phperliu/msyh.ttf/raw/master/msyh.ttf -o /usr/share/fonts/msyh.ttf

RUN wget https://arthas.aliyun.com/arthas-boot.jar -P /opt


ARG APP_NAME
ARG XMS
ARG XMX
ARG PROFILE

ENV APP_NAME $APP_NAME
ENV XMS $XMS
ENV XMX $XMX
ENV PROFILE $PROFILE
ENV VERSION 0.8

RUN mkdir /opt/tmp
RUN mkdir /opt/tmp/pdf
RUN mkdir /opt/upload
RUN mkdir /opt/log

COPY management.jar /opt/management.jar
COPY exam.jar /opt/exam.jar
COPY report.jar /opt/report.jar
#COPY gateway.jar /opt/gateway.jar
RUN ls /opt

CMD ["sh", "-c", "java -${XMS} -${XMX} -jar /opt/${APP_NAME}.jar --spring.profiles.active=${PROFILE}"]

EXPOSE 8001 8002 8003
# 8080 gateway
# 8001 management
# 8002 exam
# 8003 report
