server:
  port: 8080
spring:
  application:
    name: gateway
  cloud:
    gateway:
      routes:
        - id: management
          uri: http://localhost:8001
          predicates:
            - Path=/management/**
          filters:
            - StripPrefix=1
        - id: exam
          uri: http://localhost:8002
          predicates:
            - Path=/exam/**
          filters:
            - StripPrefix=1
        - id: report
          uri: http://localhost:8003
          predicates:
            - Path=/report/**
          filters:
            - StripPrefix=1
        - id: registration
          uri: http://localhost:8004
          predicates:
            - Path=/registration/**
          filters:
            - StripPrefix=1
      globalcors:
        cors-configurations:
          '[/**]':
            allow-credentials: true
            allowedOriginPatterns: "*.iguokao.com"
            allowedMethods: "*"
            allowedHeaders: "*"
management:
  endpoint:
    health:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health
