spring:
  application:
    name: gateway
  cloud:
    gateway:
      routes:
        - id: short-code
          uri: http://localhost:8002/api/short
          predicates:
            - Host=gkao.cc
        - id: management
          uri: http://localhost:8001
          predicates:
            - Path=/management/**
          filters:
            - StripPrefix=1
        - id: exam
          uri: http://localhost:8002
          predicates:
            - Path=/exam/**
          filters:
            - StripPrefix=1
        - id: report
          uri: http://localhost:8003
          predicates:
            - Path=/report/**
          filters:
            - StripPrefix=1
        - id: oj
          uri: http://localhost:8004
          predicates:
            - Path=/oj/**
          filters:
            - StripPrefix=1
        - id: hr
          uri: http://localhost:5000
          predicates:
            - Path=/hr/**
          filters:
            - StripPrefix=1
        - id: short-code
          uri: http://localhost:8002
          predicates:
            - Path=/*
      globalcors:
        cors-configurations:
          '[/**]':
            # 允许携带认证信息
            allow-credentials: true
            # 允许跨域的源(网站域名/ip)，设置*为全部
            allowedOriginPatterns: "*"
            # 允许跨域的method， 默认为GET和OPTIONS，设置*为全部
            allowedMethods: "*"
            # 允许跨域请求里的head字段，设置*为全部
            allowedHeaders: "*"
management:
  endpoint:
    health:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health
