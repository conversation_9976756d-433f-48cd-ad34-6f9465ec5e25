server:
  port: 8001
  undertow:
    threads:
      io: 4
      worker: 128
  forward-headers-strategy: framework
springdoc:
  api-docs:
    enabled: false
spring:
  data:
    mongodb:
      uri: mongodb://app:<EMAIL>:3717,dds-2ze3f7fd58d45bb41.mongodb.rds.aliyuncs.com:3717/management?replicaSet=mgset-76207179&slaveOk=true&readPreference=secondaryPreferred&maxPoolSize=200
      auto-index-creation: true
app:
  service:
    report: http://127.0.0.1:8003
    exam: http://127.0.0.1:8002
    registration: http://127.0.0.1:8004
  security:
    jwt:
      secret-key: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
      expiration: 86400000 # a day
      refresh-token:
        expiration: 86400000
    remote-ip: 10.0.0.0/8
  redis:
    database: 1
    host: r-2zetxv1tyf5hfd778y.redis.rds.aliyuncs.com
    password: av49J1WhTickA8GZ
    port: 6379
    max-active: 8
    max-idle: 8
    min-idle: 0
  ali:
    access-key-id: LTAI5t5sGHZy1GGQjJVUoqfq
    access-key-secret: ******************************
    oss-endpoint: oss-cn-beijing.aliyuncs.com
    oss-endpoint-internal: oss-cn-beijing-internal.aliyuncs.com
    oss-bucket-exam: iguokao-supernova-exam
management:
  endpoint:
    health:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health
# 日志配置
logging:
  file:
    path: /opt/log
  config: classpath:logback-prod.xml
  level:
    com:
      iguokao:
        supernova:
          common:
            remote:
              RegistrationRemote: trace

