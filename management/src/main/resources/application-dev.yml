server:
  port: 8001
spring:
  data:
    mongodb:
      uri: ***************************************************
      auto-index-creation: true
  jpa:
    show-sql: true
    generate-ddl: true
#logging:
#  level:
#    com:
#      iguokao:
#        supernova:
#          common:
#            remote:
#              CloopenRemote: TRACE
app:
  service:
    exam: http://127.0.0.1:8002
    report: http://127.0.0.1:8003
    registration: http://127.0.0.1:8004
  security:
    jwt:
      secret-key: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
      expiration: 86400000 # a day
      refresh-token:
        expiration: 86400000
    remote-ip: ************
  redis:
    database: 1
    host: ***********
    password: wVtB7vKkqZggAbyJ
    port: 6379
    max-active: 8
    max-idle: 8
    min-idle: 0
  ali:
    access-key-id: LTAI5tBsx8tiNMdspnQehbDS
    access-key-secret: ******************************
    oss-endpoint: oss-cn-beijing.aliyuncs.com
    oss-endpoint-internal: oss-cn-beijing-internal.aliyuncs.com
    oss-bucket-exam: stone-exam-test