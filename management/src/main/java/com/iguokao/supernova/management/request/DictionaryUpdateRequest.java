package com.iguokao.supernova.management.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
@Schema(description = "字典值编辑")
public class DictionaryUpdateRequest {
    @NotNull(message = "字典 Id 不能为空")
    @Length(min = 24, max = 24, message = "字典 Id 是24位")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "字典 Id")
    private String dictId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "字典key")
    private String dictKey;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "节点值")
    private String value;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "节点补充备注")
    private String info;
}
