package com.iguokao.supernova.management.respone;

import com.iguokao.supernova.management.document.Action;
import com.iguokao.supernova.management.document.Company;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;


@Getter
@Setter
public class ActionResponse {
    private String actionId;
    private String operatorId;
    private String companyId;

    private Integer type;
    private Integer value;
    private String text;

    public static ActionResponse of(Action obj){
        if(obj==null){
            return null;
        }
        ActionResponse res = new ActionResponse();
        BeanUtils.copyProperties(obj, res);
        if(obj.getCompanyId() != null){
            res.setCompanyId(obj.getCompanyId().toString());
        }
        if(obj.getOperatorId() != null){
            res.setOperatorId(obj.getOperatorId().toString());
        }
        res.setActionId(obj.get_id().toString());
        return res;
    }

    public static List<ActionResponse> of(List<Action> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<ActionResponse> res = new ArrayList<>();
        for(Action obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
