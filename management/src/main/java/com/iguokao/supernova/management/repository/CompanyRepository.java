package com.iguokao.supernova.management.repository;

import com.iguokao.supernova.management.document.Company;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface CompanyRepository extends MongoRepository<Company, ObjectId> {
    int countByName(String name);

    List<Company> findBy_idIn(List<ObjectId> list);
}
