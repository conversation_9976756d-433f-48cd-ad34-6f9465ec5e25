package com.iguokao.supernova.management.respone;

import com.iguokao.supernova.management.document.Dictionary;
import com.iguokao.supernova.management.document.Menu;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class MenuResponse {
    @Schema(description = "节点的 ID")
    private String menuId;

    @Schema(description = "父 ID")
    private String parentId;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "模版名称")
    private String template;

    @Schema(description = "菜单地址")
    private String path;

    @Schema(description = "菜单编号（用于显示时排序）")
    private Integer sort;

    @Schema(description = "菜单编号（用于显示时排序）")
    private Boolean hidden;

    @Schema(description = "级别")
    private Integer level;

    @Schema(description = "图标")
    private String icon;

    public static MenuResponse of(Menu obj){
        if(obj==null){
            return null;
        }
        MenuResponse res = new MenuResponse();
        BeanUtils.copyProperties(obj, res);
        res.setMenuId(obj.get_id().toString());
        if(obj.getParentId() != null){
            res.setParentId(obj.getParentId().toString());
        }
        return res;
    }

    public static List<MenuResponse> of(List<Menu> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<MenuResponse> res = new ArrayList<>();
        for(Menu obj : list){
            res.add(of(obj));
        }
        return res;
    }
}

