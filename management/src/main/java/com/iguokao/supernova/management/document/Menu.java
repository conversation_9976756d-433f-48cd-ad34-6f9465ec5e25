package com.iguokao.supernova.management.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Getter
@Setter
public class Menu extends BaseDocument {
    @Indexed(name = "parentId_index")
    private ObjectId parentId;
    // 菜单名称
    private String name;
    // 模版名称
    private String template;
    // 菜单地址
    private String path;
    // 菜单编号（用于显示时排序）
    private Integer sort;
    // 隐藏
    private Boolean hidden;
    // 级别
    private Integer level = 0;
    // 图标
    private String icon;
}
