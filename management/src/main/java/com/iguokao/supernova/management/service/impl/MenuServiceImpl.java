package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.management.document.Menu;
import com.iguokao.supernova.management.document.Operator;
import com.iguokao.supernova.management.document.Role;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.repository.MenuRepository;
import com.iguokao.supernova.management.repository.OperatorRepository;
import com.iguokao.supernova.management.repository.RoleRepository;
import com.iguokao.supernova.management.service.MenuService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
@RequiredArgsConstructor
public class MenuServiceImpl implements MenuService {
    private final MenuRepository menuRepository;
    private final RoleRepository roleRepository;
    private final OperatorRepository operatorRepository;

    @Override
    public void add(Menu menu) {
        if(null != menu.getParentId()){
            this.menuRepository.findById(menu.getParentId())
                    .ifPresent(parent -> menu.setLevel(parent.getLevel() + 1));
        }
        this.menuRepository.insert(menu);
    }

    @Override
    public void update(Menu menu) {
        Menu exist = this.getById(menu.get_id().toString());
        if(null != menu.getParentId()){
            this.menuRepository.findById(menu.getParentId())
                    .ifPresent(parent -> menu.setLevel(parent.getLevel() + 1));
        }
        BeanUtils.copyProperties(menu, exist);
        this.menuRepository.save(exist);
    }

    @Override
    public Menu remove(String menuId) {
        int count = this.menuRepository.countByParentId(new ObjectId(menuId));
        if(count > 0){
            throw new ServiceException(ExceptionEnum.MENU_USED_BY_OPERATOR);
        }
        Menu menu = this.getById(menuId);
        this.menuRepository.deleteById(new ObjectId(menuId));
        return menu;
    }

    @Override
    public Menu getById(String menuId) {
        return this.menuRepository.findById(new ObjectId(menuId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.MENU_NOT_FOUND));
    }

    @Override
    public List<Menu> getAll() {
        return this.menuRepository.findAll();
    }

    @Override
    public List<Menu> getByRoleId(String roleId) {
        Role role = this.roleRepository.findById(new ObjectId(roleId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.ROLE_NOT_FOUND));
        return this.menuRepository.findBy_idIn(role.getMenuIdList());
    }

    @Override
    public List<Menu> operatorMenuList(String operatorId) {
        Operator operator = this.operatorRepository.findById(new ObjectId(operatorId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.OPERATOR_NOT_FOUND));
        if(!operator.getRoleKeyList().isEmpty()){
            List<Role> roleList = this.roleRepository.findByKeyIn(operator.getRoleKeyList());
            List<ObjectId> list = roleList.stream().map(Role::getMenuIdList).flatMap(Collection::stream).toList();
            if(!list.isEmpty()){
                return this.menuRepository.findBy_idIn(list);
            }
        }
        return new ArrayList<>();
    }
}
