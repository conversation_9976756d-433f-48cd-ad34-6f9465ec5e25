package com.iguokao.supernova.management.config;

import com.iguokao.supernova.common.remote.CloopenRemote;
import com.iguokao.supernova.common.remote.RegistrationRemote;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import feign.slf4j.Slf4jLogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RemoteConfig {
    @Bean
    public CloopenRemote cloopenRemote(@Value("${app.cloopen.api}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .logger(new Slf4jLogger(CloopenRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(CloopenRemote.class, service);
    }

    @Bean
    public RegistrationRemote registrationRemoteRemote(@Value("${app.service.registration}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .logger(new Slf4jLogger(RegistrationRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(RegistrationRemote.class, service);
    }
}
