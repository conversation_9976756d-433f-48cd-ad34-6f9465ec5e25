package com.iguokao.supernova.management.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter
@Setter
@Document
public class Dictionary extends BaseDocument {
    // 父节点
    @Indexed(name = "parentId_index")
    private ObjectId parentId;
    // 节点名称
    @Indexed(name = "key_index")
    private String key;
    // 节点值
    private String value;
    // 节点说明
    private String info;
    // 节点深度
    private Integer level;
}
