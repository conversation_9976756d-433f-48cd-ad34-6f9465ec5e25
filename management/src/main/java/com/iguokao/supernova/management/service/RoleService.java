package com.iguokao.supernova.management.service;

import com.iguokao.supernova.management.document.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface RoleService {
    // 添加
    void add(Role role);
    // 修改 Role
    void update(Role role);
    // 删除 Role
    Role remove(String roleId);
    // 获取 角色 分页
    Role getByName(String roleName);
    // 获取 根据Id
    Role getById(String roleId);
    // 获取根据 key
    Role getByRoleKey(String key);
    // 获取全部
    List<Role> getAll();
    List<Role> getByRoleKeyList(List<String> roleKeyList);

    // 角色与菜单相关联
    void linkRoleMenu(String roleId, List<String> menuIdList);
    // 角色与权限相关联
    void linkRolePermission(String roleId, List<String> permissionKeyList);

}
