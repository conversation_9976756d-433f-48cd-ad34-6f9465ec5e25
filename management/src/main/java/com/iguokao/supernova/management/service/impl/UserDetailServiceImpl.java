package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.repository.OperatorRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserDetailServiceImpl implements UserDetailsService {
    private final OperatorRepository operatorRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return operatorRepository.findByLoginName(username)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.OPERATOR_NOT_FOUND));
    }

}
