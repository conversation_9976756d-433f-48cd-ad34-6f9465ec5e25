package com.iguokao.supernova.management.document;

import com.iguokao.supernova.common.document.BaseDocument;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Getter
@Setter
public class Permission extends BaseDocument {
    @Indexed(name = "key_index")
    private String key;

    @Indexed(name = "name_index")
    private String name;
}
