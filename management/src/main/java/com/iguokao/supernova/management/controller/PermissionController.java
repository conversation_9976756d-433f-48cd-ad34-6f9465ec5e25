package com.iguokao.supernova.management.controller;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.management.document.Permission;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.request.PermissionAddRequest;
import com.iguokao.supernova.management.request.PermissionUpdateRequest;
import com.iguokao.supernova.management.respone.PagePermissionResponse;
import com.iguokao.supernova.management.respone.PermissionResponse;
import com.iguokao.supernova.management.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.prefs.Preferences;

@RestController
@RequestMapping("/api/v1/permission")
@RequiredArgsConstructor
public class PermissionController {
    private final PermissionService permissionService;
    private final JwtService jwtService;
    private final AuditService auditService;

    @GetMapping("/my")
    @Operation(summary = "当前用户相关权限")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = PermissionResponse.class))))
    public RestResponse<List<PermissionResponse>> myPermission() {
        List<Permission> list = this.permissionService.getByOperatorId(jwtService.currentOperatorId());
        return RestResponse.success(PermissionResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/all")
    @Operation(summary = "全部权限")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = PermissionResponse.class))))
    public RestResponse<List<PermissionResponse>> all() {
        List<Permission> list = this.permissionService.getAll();
        return RestResponse.success(PermissionResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/add")
    @Operation(summary = "添加新权限")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody PermissionAddRequest request) {
        Permission permission = new Permission();
        permission.setKey(request.getKey());
        permission.setName(request.getName());
        this.permissionService.add(permission);

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.PERMISSION_ADD.getCode(), permission);
        this.auditService.add(item);

        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/update")
    @Operation(summary = "更新权限")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody PermissionUpdateRequest request) {
        Permission permission = this.permissionService.getById(request.getId());
        permission.setKey(request.getKey());
        permission.setName(request.getName());
        this.permissionService.update(permission);

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.PERMISSION_UPDATE.getCode(), permission);
        this.auditService.add(item);

        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/remove/{permissionId}")
    @Operation(summary = "移除权限")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@PathVariable(value = "permissionId") String permissionId) {
        Permission permission = this.permissionService.remove(permissionId);

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.PERMISSION_DELETE.getCode(), permission);
        this.auditService.add(item);

        return RestResponse.success();
    }
}
