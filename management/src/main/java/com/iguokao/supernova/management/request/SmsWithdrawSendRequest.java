package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class SmsWithdrawSendRequest {
    @NotNull(message = "图片验证码不能为空")
    @Length(max = 4, min = 4, message = "验证码必须是4位")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "图形验证码字符串")
    private String imageCode;

    @NotNull(message = "验证码key不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "验证码的token")
    private String key;

    @NotNull(message = "手机号不能为空")
    @Length(max = 11, min = 11, message = "手机号必须是11位")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "手机号不能为空")
    private String mobile;

    private String price;
}
