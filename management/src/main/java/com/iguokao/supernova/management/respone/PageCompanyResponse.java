package com.iguokao.supernova.management.respone;

import com.iguokao.supernova.common.response.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;


public class PageCompanyResponse extends PageResponse<CompanyResponse> {
    public PageCompanyResponse(List<CompanyResponse> list, int total, Pageable pageable) {
        super(list, total, pageable);
    }
}
