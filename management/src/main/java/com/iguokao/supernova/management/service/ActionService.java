package com.iguokao.supernova.management.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.management.document.Action;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ActionService {
    void add(Action action);

    // 分页获取
    Tuple2<List<Action>, Integer> getPage(String companyId, String operatorId, Integer type, Pageable pageable);
}
