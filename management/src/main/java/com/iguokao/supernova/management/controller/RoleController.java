package com.iguokao.supernova.management.controller;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.management.document.Menu;
import com.iguokao.supernova.management.document.Operator;
import com.iguokao.supernova.management.document.Permission;
import com.iguokao.supernova.management.document.Role;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.request.RoleAddRequest;
import com.iguokao.supernova.management.request.RoleMenuLinkRequest;
import com.iguokao.supernova.management.request.RolePermissionLinkRequest;
import com.iguokao.supernova.management.request.RoleUpdateRequest;
import com.iguokao.supernova.management.respone.*;
import com.iguokao.supernova.management.service.MenuService;
import com.iguokao.supernova.management.service.OperatorService;
import com.iguokao.supernova.management.service.PermissionService;
import com.iguokao.supernova.management.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/role")
@RequiredArgsConstructor
public class RoleController {
    private final RoleService roleService;
    private final OperatorService operatorService;
    private final PermissionService permissionService;
    private final MenuService menuService;
    private final JwtService jwtService;
    private final AuditService auditService;

    @GetMapping("/all")
    @Operation(summary = "所有角色")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoleResponse.class))))
    public RestResponse<List<RoleResponse>> all() {
        List<Role> list = this.roleService.getAll();
        List<RoleResponse> res = RoleResponse.of(list);
        return RestResponse.success(res);
    }

    @PostMapping("/my")
    @Operation(summary = "当前用户相关角色")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoleResponse.class))))
    public RestResponse<List<RoleResponse>> my() {
        Operator operator = operatorService.getById(jwtService.currentOperatorId());
        List<Role> list = this.roleService.getByRoleKeyList(operator.getRoleKeyList());
        return RestResponse.success(RoleResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/menu/{roleId}")
    @Operation(summary = "获取某个角色的菜单列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = MenuResponse.class))))
    public RestResponse<List<MenuResponse>> menuList(@PathVariable String roleId) {
        List<Menu> list = this.menuService.getByRoleId(roleId);
        return RestResponse.success(MenuResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/add")
    @Operation(summary = "添加新角色")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> add(@RequestBody RoleAddRequest request) {
        Role role = new Role();
        role.setKey(request.getRoleKey());
        role.setName(request.getName());
        this.roleService.add(role);

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.ROLE_ADD.getCode(), role);
        this.auditService.add(item);

        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/update")
    @Operation(summary = "更新角色")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> update(@RequestBody RoleUpdateRequest request) {
        Role role = new Role();
        role.set_id(new ObjectId(request.getRoleId()));
        role.setKey(request.getKey());
        role.setName(request.getName());
        this.roleService.update(role);

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.ROLE_UPDATE.getCode(), role);
        this.auditService.add(item);

        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/list/{operatorId}")
    @Operation(summary = "获取一个用户的角色列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoleResponse.class))))
    public RestResponse<List<RoleResponse>> update(@PathVariable(value = "operatorId") String operatorId) {
        Operator operator = this.operatorService.getById(operatorId);
        List<Role> list = this.roleService.getByRoleKeyList(operator.getRoleKeyList());
        return RestResponse.success(RoleResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/remove/{roleId}")
    @Operation(summary = "移除角色")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> remove(@PathVariable(value = "roleId") String roleId) {
        Role role = this.roleService.remove(roleId);

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.ROLE_DELETE.getCode(), role);
        this.auditService.add(item);

        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/permission/{roleId}")
    @Operation(summary = "获取某个角色的权限列表）")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = PermissionResponse.class))))
    public RestResponse<List<PermissionResponse>> permissionList(@PathVariable String roleId) {
        List<Permission> list = this.permissionService.getByRoleId(roleId);
        return RestResponse.success(PermissionResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/link/menu")
    @Operation(summary = "角色与其相应的菜单相关联")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> linkMenu(@RequestBody RoleMenuLinkRequest request) {
        this.roleService.linkRoleMenu(request.getRoleId(), request.getMenuIdList());

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.OPERATOR_LINK_ROLE.getCode(), request);
        this.auditService.add(item);

        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/link/permission")
    @Operation(summary = "角色与权限相关联")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> linkPermission(@RequestBody RolePermissionLinkRequest request) {
        this.roleService.linkRolePermission(request.getRoleId(), request.getPermissionIdList());

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.ROLE_LINK_MENU.getCode(), request);
        this.auditService.add(item);

        return RestResponse.success();
    }
}
