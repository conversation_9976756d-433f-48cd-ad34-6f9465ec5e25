package com.iguokao.supernova.management.controller;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.response.ActionRemoteResponse;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.management.document.Action;
import com.iguokao.supernova.management.request.ActionPageRequest;
import com.iguokao.supernova.management.respone.ActionResponse;
import com.iguokao.supernova.management.respone.CompanyResponse;
import com.iguokao.supernova.management.service.ActionService;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/action")
@RequiredArgsConstructor
public class ActionController {
    private final JwtService jwtService;
    private final ActionService actionService;

    @PostMapping("/page")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<PageResponse<ActionResponse>> page(@RequestBody ActionPageRequest request) {
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
        Tuple2<List<Action>, Integer> page = this.actionService.getPage(request.getCompanyId(), request.getOperatorId(), request.getType(), pageable);
        List<ActionResponse> res = ActionResponse.of(page.first());
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }
}
