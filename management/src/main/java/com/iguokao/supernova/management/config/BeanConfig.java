package com.iguokao.supernova.management.config;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.iguokao.supernova.common.component.ApiLogAspect;
import com.iguokao.supernova.common.exception.SecurityAccessDeniedHandler;
import com.iguokao.supernova.common.exception.SecurityAuthenticationEntryPoint;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.common.service.impl.JwtServiceImpl;
import com.iguokao.supernova.common.service.impl.OssServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.filter.ForwardedHeaderFilter;

@Configuration
@RequiredArgsConstructor
public class BeanConfig {
    private final UserDetailsService userService;

    /**
     * 阿里云 ACS 验证用
     * @param acsKeyId ID
     * @param acsKeySecret 密钥
     * @return ACS Client
     */
    @Bean
    public IAcsClient client(@Value("${app.ali.acs-key-id}") String acsKeyId,
                             @Value("${app.ali.acs-key-secret}") String acsKeySecret){
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", acsKeyId, acsKeySecret);
        DefaultProfile.addEndpoint( "cn-hangzhou", "afs", "afs.aliyuncs.com");
        return new DefaultAcsClient(profile);
    }

    // 日志框架
    @Profile({"test", "dev"})
    @Bean
    ApiLogAspect apiLogAspect(){
        return new ApiLogAspect();
    }

    @Bean
    OssService ossService(Environment environment){
        return new OssServiceImpl(environment);
    }

    @Bean
    public AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityAuthenticationEntryPoint securityAuthenticationEntryPoint(){
        return new SecurityAuthenticationEntryPoint();
    }

    @Bean
    public SecurityAccessDeniedHandler securityAccessDeniedHandler(){
        return new SecurityAccessDeniedHandler();
    }

    /**
     * 处理header转发  主要是 swagger
     *
     */
    @Bean
    ForwardedHeaderFilter forwardedHeaderFilter() {
        return new ForwardedHeaderFilter();
    }

    @Bean
    JwtService jwtService(){
        return new JwtServiceImpl();
    }
}
