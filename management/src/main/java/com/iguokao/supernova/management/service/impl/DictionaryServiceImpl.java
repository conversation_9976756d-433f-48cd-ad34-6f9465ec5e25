package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.management.document.Dictionary;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.repository.DictionaryRepository;
import com.iguokao.supernova.management.service.DictionaryService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class DictionaryServiceImpl implements DictionaryService {

    private final DictionaryRepository dictionaryRepository;

    @Override
    public void add(Dictionary dictionary) {
        int count = this.dictionaryRepository.countByKey(dictionary.getKey());
        if(count > 0){
            throw new ServiceException(ExceptionEnum.DICTIONARY_KEY_EXIST);
        }
        this.dictionaryRepository.insert(dictionary);
    }

    @Override
    public void remove(String id) {
        if(this.dictionaryRepository.countByParentId(new ObjectId(id)) != 0){
            throw new ServiceException(ExceptionEnum.DICTIONARY_CONTAIN_SUB_NODE);
        }
        this.dictionaryRepository.deleteById(new ObjectId(id));
    }

    @Override
    public void update(Dictionary dictionary) {
        Dictionary exist = this.getById(dictionary.get_id().toString());
        BeanUtils.copyProperties(dictionary, exist);
        this.dictionaryRepository.save(exist);
    }

    @Override
    public Dictionary getById(String id) {
        return this.dictionaryRepository.findById(new ObjectId(id))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.DICTIONARY_NOT_FOUND));
    }

    @Override
    public Dictionary getByKey(String key) {
        return this.dictionaryRepository.findByKey(key)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.DICTIONARY_NOT_FOUND));
    }

    @Override
    public List<Dictionary> getAll() {
        return this.dictionaryRepository.findAll();
    }

    @Override
    public List<Dictionary> subListByKey(String key) {
        Dictionary dictionary = this.getByKey(key);
        return this.dictionaryRepository.findByParentId(dictionary.get_id());
    }
}
