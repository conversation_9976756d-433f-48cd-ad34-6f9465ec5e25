package com.iguokao.supernova.management.repository;

import com.iguokao.supernova.management.document.Dictionary;
import com.iguokao.supernova.management.document.Role;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface RoleRepository extends MongoRepository<Role, ObjectId> {
    int countByKey(String key);

    Role findByName(String name);
    Role findByKey(String key);
    List<Role> findBy_idIn(List<ObjectId> idList);
    List<Role> findByKeyIn(List<String> keyList);
}
