package com.iguokao.supernova.management.respone;

import com.iguokao.supernova.management.document.Permission;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class PermissionResponse {
    @Schema(description = "permissionId")
    private String permissionId;

    @Schema(description = "权限名称")
    private String name;

    @Schema(description = "权限标识", example = "p_admin, p_role_delete")
    private String key;

    public static PermissionResponse of(Permission obj){
        if(obj==null){
            return null;
        }
        PermissionResponse res = new PermissionResponse();
        BeanUtils.copyProperties(obj, res);
        res.setPermissionId(obj.get_id().toString());
        res.setKey(obj.getKey());
        return res;
    }

    public static List<PermissionResponse> of(List<Permission> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<PermissionResponse> res = new ArrayList<>();
        for(Permission obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
