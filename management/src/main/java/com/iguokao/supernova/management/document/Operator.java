package com.iguokao.supernova.management.document;

import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Document
@Getter
@Setter
public class Operator extends BaseDocument implements UserDetails {
    private ObjectId relatedId;

    @Indexed(name = "loginName_index")
    private String loginName;
    private String loginPassword;
    private String loginKey;

    @Indexed(name = "mobile_index")
    private String mobile;

    @Indexed(name = "fullName_index")
    private String fullName;
    private String note;

    private Integer type;
    private String salt;
    private String avatar;
    private Date expiredAt;
    private Boolean enabled = true;
    private Boolean accountNonLocked = true;
    private List<String> roleKeyList = new ArrayList<>();
    private List<ObjectId> companyIdList = new ArrayList<>();
    private List<Role> roleList = new ArrayList<>();

    public OperatorRemoteResponse toRemoteResponse(){
        OperatorRemoteResponse dto = new OperatorRemoteResponse();
        BeanUtils.copyProperties(this, dto, "loginPassword");
        dto.setOperatorId(this.get_id().toString());
        if(this.relatedId != null){
            dto.setRelatedId(this.getRelatedId().toString());
        }
        this.getAuthorities()
                .forEach(grantedAuthority -> dto.getRoleList().add(grantedAuthority.getAuthority()));
        return dto;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if(roleList.isEmpty()){
            return new ArrayList<>();
        }
        return this.roleList
                .stream()
                .map(Role::getAuthorities)
                .flatMap(Collection::stream)
                .toList();
    }

    @Override
    public String getPassword() {
        return loginPassword;
    }

    @Override
    public String getUsername() {
        return get_id().toString();
    }

    @Override
    public boolean isAccountNonExpired() {
        if(this.expiredAt == null){
            return true;
        }
        return new Date().getTime() > this.expiredAt.getTime();
    }

    @Override
    public boolean isAccountNonLocked() {
        return this.accountNonLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return this.enabled;
    }
}
