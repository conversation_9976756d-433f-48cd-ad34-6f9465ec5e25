package com.iguokao.supernova.management.service;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.management.document.Operator;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface OperatorService {

    boolean acsValidate(String sessionId, String sig, String token, String ip);

    // 创建 Operator
    String add(Operator operator);

    // 修改
    void update(Operator operator);
    // 移除
    Operator remove(String operatorId);
    ImageCode getImageCode();

    String login(String loginName, String loginPassword);


    // 发短信 登录
    void sendSmsLogin(ImageCode imageCode, String mobile, HttpServletRequest request);

    // 发短信 找回密码
    void sendSmsPassword(ImageCode imageCode, String mobile, HttpServletRequest request);
    void sendSmsRefund(ImageCode imageCode, String mobile, List<String> argList, String smsCode, HttpServletRequest request);
    void sendSmsWithdraw(ImageCode imageCode, String mobile, List<String> argList, String smsCode, HttpServletRequest request);

    // 获取 Operator
    Operator getById(String operatorId);
    Operator getByIdWithRoleList(String operatorId);


    Tuple2<List<Operator>, Integer> getPageData(String companyId, String fullName, String mobile, Pageable pageable);

    // 根据登录名取得操作员对象
    Operator getByLoginName(String loginName);

    // 获取 Operator
    Operator getByMobile(String mobile);
    List<Operator> getOperatorList(List<String> operatorIdList);

    // 给操作员赋予权限
    void linkOperatorRole(String operatorId, List<String> roleKeyList);
    void linkOperatorCompany(String operatorId, List<String> companyIdList);
    // 给操作员重新设置密码
    void changePassword(String operatorId, String oldPassword, String newPassword);
    // 重置密码
    void resetPassword(String operatorId, String newPassword);

    // 企业 管理员
    List<Operator> companyOperatorList(String companyId);

    // 锁定
    void lock(String operatorId);

    String login2fa(String mobile, String smsCode, String key2fa);
}
