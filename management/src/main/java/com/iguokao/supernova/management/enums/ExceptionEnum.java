package com.iguokao.supernova.management.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum ExceptionEnum implements BaseEnum {
    DICTIONARY_CONTAIN_SUB_NODE(1001, "字典包含，子节点，无法删除"),
    DICTIONARY_NOT_FOUND(1002, "字典没有找到"),
    DICTIONARY_KEY_EXIST(1003, "字典KEY 存在"),

    OPERATOR_PASSWORD_WRONG(1101, "账号不存在或者密码错误"),
    OPERATOR_LOCKED(1102, "该操作员账号被锁定"),
    OPERATOR_NOT_FOUND(1103, "该操作员未找到"),
    OPERATOR_LOGIN_AUTH_FAILED(1104, "登录验证失败"),
    OPERATOR_EXIST(1105, "管理员已经存在"),
    OPERATOR_MOBILE_EXIST(1106, "管理员手机号已经占用"),
    OPERATOR_ACS_VALIDATE_FAILED(1107, "阿里云ACS认证失败"),
    OPERATOR_CAN_NOT_ADD_ROLE(1108, "操作员类型不允许修改角色"),

    MENU_NOT_FOUND(1201, "菜单未找到"),
    MENU_USED_BY_OPERATOR(1202, "菜单被使用中"),

    ROLE_NOT_FOUND(1301, "角色未找到"),
    ROLE_USED_BY_OPERATOR(1302, "角色被使用中"),
    ROLE_KEY_NAME_FORMAT_EXCEPTION(1303, "角色名称错误"),
    ROLE_NOT_NULL(1304, "管理员角色不能为空"),
    ROLE_KEY_EXIST_EXCEPTION(1305, "角色Key已经存在"),

    COMPANY_EXIST(1401, "企业已经存在"),
    COMPANY_NOT_FOUND(1402, "企业未找到"),

    PERMISSION_NOT_FOUND(1501, "权限未找到"),
    PERMISSION_EXIST(1502, "权限存在"),

    SMS_CODE_NOT_VALID(1701, "短信验证码验证失败"),
    SMS_CODE_NOT_FOUND(1702, "短信验证码未找到或者已过期"),
    SMS_TIMES_BEYOND_LIMIT(1703, "手机号或者IP超出当日发送限制，请明日再试"),
    TIMEOUT_2FA(1704, "未找到token，请重新登录，账密验证5分钟内，通过二次验证可登录系统"),

    ;

    ExceptionEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
