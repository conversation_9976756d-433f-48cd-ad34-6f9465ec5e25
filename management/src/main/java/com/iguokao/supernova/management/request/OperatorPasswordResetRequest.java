package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class OperatorPasswordResetRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "操作员 Id")
    private String operatorId;

    @Length(min = 7 ,max = 12 ,message = "密码格式错误")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "新密码")
    private String newPassword;
}
