package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class OperatorRoleLinkRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "operatorId")
    private String operatorId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "权限 Key 数组")
    private List<String> roleKeyList;
}

