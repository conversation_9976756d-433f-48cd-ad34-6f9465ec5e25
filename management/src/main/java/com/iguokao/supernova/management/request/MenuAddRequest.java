package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class MenuAddRequest {
    @Length(min = 2, max = 16, message = "菜单名称 2-16")
    @NotNull(message = "菜单名称 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单名称")
    private String name;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单路径")
    private String path;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单编号（用于显示时排序）")
    private Integer sort;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单隐藏")
    private Boolean hidden;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "父菜单uuid")
    private String parentId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单图标")
    private String icon;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单模板")
    private String template;
}
