package com.iguokao.supernova.management.service;

import com.iguokao.supernova.management.document.Menu;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface MenuService {
    // 创建 Menu
    void add(Menu menu);
    // 删除 Menu
    Menu remove(String menuId);
    // 修改 Menu
    void update(Menu menu);
    // 获取一个
    Menu getById(String menuId);
    // 获取所有
    List<Menu> getAll();
    //
    List<Menu> getByRoleId(String roleId);

    List<Menu> operatorMenuList(String operatorId);
}
