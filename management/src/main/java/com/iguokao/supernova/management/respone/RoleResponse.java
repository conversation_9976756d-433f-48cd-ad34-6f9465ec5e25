package com.iguokao.supernova.management.respone;

import com.iguokao.supernova.management.document.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RoleResponse {
    @Schema(description = "角色的uuid")
    private String roleId;

    @Schema(description = "角色编码", example = "r_sys_admin, r_user")
    private String key;

    @Schema(description = "名称")
    private String name;

    public static RoleResponse of(Role obj){
        if(obj==null){
            return null;
        }
        RoleResponse res = new RoleResponse();
        BeanUtils.copyProperties(obj, res);
        res.setRoleId(obj.get_id().toString());
        res.setKey(obj.getKey());
        return res;
    }

    public static List<RoleResponse> of(List<Role> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<RoleResponse> res = new ArrayList<>();
        for(Role obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
