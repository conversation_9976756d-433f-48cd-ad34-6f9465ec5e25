package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.management.document.Role;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.repository.OperatorRepository;
import com.iguokao.supernova.management.repository.RoleRepository;
import com.iguokao.supernova.management.service.MenuService;
import com.iguokao.supernova.management.service.RoleService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {
    private final RoleRepository roleRepository;
    private final OperatorRepository operatorRepository;

    @Override
    public void add(Role role) {
        if(this.roleRepository.countByKey(role.getKey()) > 0){
            throw new ServiceException(ExceptionEnum.ROLE_KEY_EXIST_EXCEPTION);
        }
        this.roleRepository.insert(role);
    }

    @Override
    public void update(Role role) {
        Role exist = this.getById(role.get_id().toString());
        BeanUtils.copyProperties(role, exist, "permissionKeyList", "menuIdList");
        this.roleRepository.save(exist);
    }

    @Override
    public Role remove(String roleId) {
        Role role = this.getById(roleId);
        int count = this.operatorRepository.countByRoleKeyListIn(Collections.singletonList(role.getKey()));
        if(count > 0){
            throw new ServiceException(ExceptionEnum.ROLE_USED_BY_OPERATOR);
        }
        this.roleRepository.delete(role);
        return role;
    }

    @Override
    public List<Role> getAll() {
        return this.roleRepository.findAll();
    }

    @Override
    public List<Role> getByRoleKeyList(List<String> roleKeyList) {
        return this.roleRepository.findByKeyIn(roleKeyList);
    }

    @Override
    public void linkRoleMenu(String roleId, List<String> menuIdList) {
        Role role = this.getById(roleId);

        List<ObjectId> idList = menuIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        role.getMenuIdList().clear();
        role.getMenuIdList().addAll(idList);
        this.roleRepository.save(role);
    }

    @Override
    public void linkRolePermission(String roleId, List<String> permissionKeyList) {
        Role role = this.getById(roleId);
        role.getPermissionKeyList().clear();
        role.getPermissionKeyList().addAll(permissionKeyList);
        this.roleRepository.save(role);
    }

    @Override
    public Role getByName(String name) {
        return this.roleRepository.findByName(name);
    }

    @Override
    public Role getById(String roleId) {
        return this.roleRepository.findById(new ObjectId(roleId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.ROLE_NOT_FOUND));
    }

    @Override
    public Role getByRoleKey(String key) {
        return this.roleRepository.findByKey(key);
    }
}
