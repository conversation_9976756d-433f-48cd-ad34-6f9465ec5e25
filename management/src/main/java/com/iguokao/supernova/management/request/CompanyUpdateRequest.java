package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class CompanyUpdateRequest {
    @Length(min = 24, max = 24, message = "ID 24位")
    @NotNull(message="ID 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "ID")
    private String companyId;

    @Length(min = 4, max = 32, message = "公司名称长度必须是4-16位")
    @NotNull(message="公司名不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司名称")
    private String name;

    @Length(min = 2, max = 8, message = "公司简称长度必须是2-8位")
    @NotNull(message="公司简称不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司简称")
    private String shortName;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "logo")
    private String logo;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "theme")
    private String theme;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "备注")
    private String note;



}
