package com.iguokao.supernova.management.request;

import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OperatorPageRequest extends PageRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "企业ID")
    String companyId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "姓名")
    private String fullName;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "手机号")
    private String mobile;
}
