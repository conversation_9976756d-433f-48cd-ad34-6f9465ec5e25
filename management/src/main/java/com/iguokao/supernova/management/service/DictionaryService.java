package com.iguokao.supernova.management.service;

import com.iguokao.supernova.management.document.Dictionary;

import java.util.List;

public interface DictionaryService {
    // 添加
    void add(Dictionary dictionary);
    // 移除
    void remove(String id);
    // 更新
    void update(Dictionary dictionary);
    // 获取
    Dictionary getById(String id);
    // 获取 根据key
    Dictionary getByKey(String key);
    // 获取所有
    List<Dictionary> getAll();
    // 获取 根据父key 获取列表
    List<Dictionary> subListByKey(String key);
}
