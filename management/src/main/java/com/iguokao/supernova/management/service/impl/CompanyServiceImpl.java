package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.management.document.Company;
import com.iguokao.supernova.management.document.Operator;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.repository.CompanyRepository;
import com.iguokao.supernova.management.repository.OperatorRepository;
import com.iguokao.supernova.management.service.ActionService;
import com.iguokao.supernova.management.service.CompanyService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class CompanyServiceImpl implements CompanyService {
    private final CompanyRepository companyRepository;
    private final MongoTemplate mongoTemplate;

    @Override
    public void add(Company company) {
        int count = this.companyRepository.countByName(company.getName());
        if(count > 0){
            throw new ServiceException(ExceptionEnum.COMPANY_EXIST);
        }
        this.companyRepository.insert(company);
    }

    @Override
    public void update(Company company) {
        Company exist = this.getById(company.get_id().toString());
        BeanUtils.copyProperties(company, exist,"registrationEnabled", "createdAt");
        this.companyRepository.save(exist);
    }

    @Override
    public Company getById(String companyId) {
        return this.companyRepository.findById(new ObjectId(companyId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.COMPANY_NOT_FOUND));
    }

    @Override
    public List<Company> getByIdList(List<ObjectId> list) {
        return this.companyRepository.findBy_idIn(list);
    }

    @Override
    public Tuple2<List<Company>, Integer> getPageData(String name, String shortName, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "_id", "createdAt"));
        if(null != name){
            query = query.addCriteria(Criteria.where("name").is(name));
        }
        if(null != shortName){
            query = query.addCriteria(Criteria.where("shortName").is(shortName));
        }
        // 计算总数
        long count = this.mongoTemplate.count(query, Company.class);
        // 分页信息
        query = query.with(pageable);
        List<Company> list = this.mongoTemplate.find(query, Company.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public void enableRegistration(String companyId) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(companyId)));
        Update update = new Update();
        update.set("registrationEnabled", true);
        mongoTemplate.updateFirst(query, update, Company.class);
    }
}
