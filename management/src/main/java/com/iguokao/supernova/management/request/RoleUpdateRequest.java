package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RoleUpdateRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "角色的uuid")
    private String roleId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "角色编码", example = "r_sys_admin, r_user")
    private String key;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "角色名称")
    private String name;
}
