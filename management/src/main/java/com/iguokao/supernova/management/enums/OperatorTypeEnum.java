package com.iguokao.supernova.management.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum OperatorTypeEnum implements BaseEnum {
    SYS_OPERATOR(1, "系统管理员"),
    STANDARD(2, "操作员"),
    SITE_OPERATOR(3, "考点管理员"),
    ROOM_OPERATOR(4, "管理机操作员"),
    ;

    OperatorTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
