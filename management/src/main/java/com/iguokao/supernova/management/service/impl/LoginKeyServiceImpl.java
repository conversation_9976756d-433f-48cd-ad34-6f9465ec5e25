package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.service.LoginKeyService;
import com.iguokao.supernova.management.service.CacheService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LoginKeyServiceImpl implements LoginKeyService {

    private final CacheService cacheService;

    @Override
    public boolean check(String operatorId, String key) {
        String loginKey = cacheService.getLoginKey(operatorId);
        if(loginKey != null){
            return loginKey.equals(key);
        }
        return false;
    }
}
