package com.iguokao.supernova.management.respone;

import com.iguokao.supernova.management.document.Company;
import com.iguokao.supernova.management.document.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class CompanyResponse {
    @Schema(description = "企业 Id")
    private String companyId;

    @Schema(description = "企业 名称")
    private String name;

    @Schema(description = "企业 简称")
    private String shortName;

    @Schema(description = "logo")
    private String logo;

    @Schema(description = "封面")
    private String theme;

    @Schema(description = "备注")
    private String note;

    private Boolean registrationEnabled;

    public static CompanyResponse of(Company obj){
        if(obj==null){
            return null;
        }
        CompanyResponse res = new CompanyResponse();
        BeanUtils.copyProperties(obj, res);
        res.setCompanyId(obj.get_id().toString());
        return res;
    }

    public static List<CompanyResponse> of(List<Company> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<CompanyResponse> res = new ArrayList<>();
        for(Company obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
