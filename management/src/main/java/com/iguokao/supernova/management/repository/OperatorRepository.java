package com.iguokao.supernova.management.repository;

import com.iguokao.supernova.management.document.Operator;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.CountQuery;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface OperatorRepository extends MongoRepository<Operator, ObjectId> {
    int countByLoginName(String loginName);
    int countByMobile(String mobile);

//    @CountQuery("{ 'roleKeyList' : { $elemMatch : {$eq: ?0 } } }")
//    int countByRoleKey(String roleKey);
    int countByRoleKeyListIn(List<String> list);

    Optional<Operator> findByLoginName(String loginName);
    Optional<Operator> findByMobile(String mobile);
    List<Operator> findBy_idIn(List<ObjectId> list);

    List<Operator> findByCompanyIdListIn(List<ObjectId> list);
}
