package com.iguokao.supernova.management.controller;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.ImageCodeResponse;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.common.util.RequestUtil;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.request.Login2faRequest;
import com.iguokao.supernova.management.request.OperatorLoginRequest;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.management.request.SmsSendRequest;
import com.iguokao.supernova.management.service.OperatorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
public class AuthenticationController {

    private final OperatorService operatorService;
    private final AuditService auditService;

    @Operation(security = @SecurityRequirement(name = "ignore"))
    @GetMapping("/validate_code")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ImageCodeResponse.class)))
    public RestResponse<ImageCodeResponse> imageCode() {
        ImageCode imageCode = this.operatorService.getImageCode();
        return RestResponse.success(ImageCodeResponse.of(imageCode));
    }

    @Operation(security = @SecurityRequirement(name = "ignore"))
    @PostMapping("/login")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> login(@RequestBody OperatorLoginRequest request, HttpServletRequest req) {
        boolean passed = this.operatorService.acsValidate(request.getSessionId(),
                request.getSig(),
                request.getToken(),
                RequestUtil.getClientIp(req));
        if(!passed){
            throw new ServiceException(ExceptionEnum.OPERATOR_ACS_VALIDATE_FAILED);
        }
        String token = this.operatorService.login(request.getUsername(), request.getPassword());

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.LOGIN.getCode(), request.getUsername());
        this.auditService.add(item);
        return RestResponse.success(token);
    }

    @Operation(security = @SecurityRequirement(name = "ignore"))
    @PostMapping("/sms/login/send")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ImageCodeResponse.class)))
    public RestResponse<String> smsSendLogin(@Validated @RequestBody SmsSendRequest request, HttpServletRequest req) {
        ImageCode imageCode = new ImageCode();
        imageCode.setImageCode(request.getImageCode());
        imageCode.setKey(request.getKey());
        this.operatorService.sendSmsLogin(imageCode, request.getMobile(), req);
        return RestResponse.success();
    }

    @Operation(security = @SecurityRequirement(name = "ignore"))
    @PostMapping("/2fa/login")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ImageCodeResponse.class)))
    public RestResponse<String> token2fa(@RequestBody Login2faRequest request) {
        String token = this.operatorService.login2fa(request.getMobile(), request.getSmsCode(), request.getKey2fa());
        return RestResponse.success(token);
    }

    @Operation(security = @SecurityRequirement(name = "ignore"))
    @PostMapping("/sms/password/send")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ImageCodeResponse.class)))
    public RestResponse<String> smsSendPassword(@Validated @RequestBody SmsSendRequest request, HttpServletRequest req) {
        ImageCode imageCode = new ImageCode();
        imageCode.setImageCode(request.getImageCode());
        imageCode.setKey(request.getKey());
        this.operatorService.sendSmsPassword(imageCode, request.getMobile(), req);
        return RestResponse.success();
    }

    @Operation(security = @SecurityRequirement(name = "ignore"))
    @GetMapping("/test")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> test() {
        return RestResponse.success();
    }
}