package com.iguokao.supernova.management.document;

import com.iguokao.supernova.common.document.BaseDocument;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Document
@Getter
@Setter
public class Role extends BaseDocument {

    @Indexed(name = "key_index")
    private String key;

    @Indexed(name = "name_index")
    private String name;

    private List<String> permissionKeyList = new ArrayList<>();
    private List<ObjectId> menuIdList = new ArrayList<>();

    public List<SimpleGrantedAuthority> getAuthorities() {
        var authorities = permissionKeyList
                .stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
        authorities.add(new SimpleGrantedAuthority("ROLE_" + this.key.toUpperCase()));
        return authorities;
    }
}
