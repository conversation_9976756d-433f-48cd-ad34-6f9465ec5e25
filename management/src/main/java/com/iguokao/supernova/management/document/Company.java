package com.iguokao.supernova.management.document;

import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Document
@Getter
@Setter
public class Company extends BaseDocument {
    @Indexed(name = "name_index")
    private String name;

    @Indexed(name = "shortName_index")
    private String shortName;
    private String logo;
    private String theme;
    private String note;

    private Boolean registrationEnabled = false;

    public CompanyRemoteResponse getRemoteResponse(){
        CompanyRemoteResponse res = new CompanyRemoteResponse();
        BeanUtils.copyProperties(this, res);
        res.setCompanyId(this.get_id().toString());
        return res;
    }
}
