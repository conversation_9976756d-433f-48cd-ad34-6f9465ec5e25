package com.iguokao.supernova.management.request;

import com.iguokao.supernova.common.request.PageRequest;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class CompanyPageRequest extends PageRequest {
    @Length(min = 4, max = 32, message = "公司名称长度必须是4-16位")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司名称 模糊搜索")
    private String name;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司简称 模糊搜索")
    private String shorName;
}
