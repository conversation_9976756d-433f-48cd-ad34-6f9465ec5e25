package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;

import com.iguokao.supernova.management.service.CacheService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class CacheServiceImpl implements CacheService {
    public static Integer DURATION_MINUTE = 60;
    public static Integer DURATION_FIVE_MINUTE = 300;
    public static Integer DURATION_QUARTER = 900;

    public static String PREFIX_IMAGE_CODE = "ic";
    public static String PREFIX_SMS_CODE = "sms";
    public static String PREFIX_LOGIN_KEY = "lk";
    public static String PREFIX_TOKEN = "tk";


    private final RedisTemplate<String, ImageCode> imageCodeRedisTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    @Override
    public ImageCode getImageCode(String key) {
        String k = String.format("%s:%s", PREFIX_IMAGE_CODE, key);
        return imageCodeRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setImageCode(ImageCode imageCode) {
        String k = String.format("%s:%s", PREFIX_IMAGE_CODE, imageCode.getKey());
        imageCodeRedisTemplate.opsForValue().set(k, imageCode, DURATION_MINUTE, TimeUnit.SECONDS);
    }

    @Override
    public void deleteImageCode(String key) {
        String k = String.format("%s:%s", PREFIX_IMAGE_CODE, key);
        imageCodeRedisTemplate.delete(k);
    }

    @Override
    public String getSmsCode(String key) {
        String k = String.format("%s:%s", PREFIX_SMS_CODE, key);
        return this.stringRedisTemplate.opsForValue().get(k);
    }

//    @Override
//    public void setSmsCode(String key, String code) {
//        String k = String.format("%s:%s", PREFIX_SMS_CODE, key);
//        this.stringRedisTemplate.opsForValue().set(k, code, DURATION_QUARTER, TimeUnit.SECONDS);
//    }

    @Override
    public void setSmsCode(String key, String code, Integer seconds) {
        String k = String.format("%s:%s", PREFIX_SMS_CODE, key);
        this.stringRedisTemplate.opsForValue().set(k, code, seconds, TimeUnit.SECONDS);
    }

    @Override
    public void removeSmsCode(String key) {
        String k = String.format("%s:%s", PREFIX_SMS_CODE, key);
        this.stringRedisTemplate.delete(k);
    }

    @Override
    public void setToken(String key, String token) {
        String k = String.format("%s:%s", PREFIX_TOKEN, key);
        this.stringRedisTemplate.opsForValue().set(k, token, DURATION_FIVE_MINUTE, TimeUnit.SECONDS);
    }

    @Override
    public String getToken(String key) {
        String k = String.format("%s:%s", PREFIX_TOKEN, key);
        return this.stringRedisTemplate.opsForValue().get(k);
    }

    @Override
    public String getLoginKey(String operatorId) {
        String k = String.format("%s:%s", PREFIX_LOGIN_KEY, operatorId);
        return this.stringRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setLoginKey(String operatorId, String key) {
        String k = String.format("%s:%s", PREFIX_LOGIN_KEY, operatorId);
        this.stringRedisTemplate.opsForValue().set(k, key);
    }

    @Override
    public Tuple2<Integer, Integer> getSmsCount(String ip, String mobile) {
        String keyIp = String.format("%s:ip:%s", PREFIX_SMS_CODE, ip);
        String ipCount = this.stringRedisTemplate.opsForValue().get(keyIp);
        String keyMobile = String.format("%s:mb:%s", PREFIX_SMS_CODE, mobile);
        String mobileCount = this.stringRedisTemplate.opsForValue().get(keyMobile);

        return new Tuple2<>(ipCount == null ? 0: Integer.parseInt(ipCount),
                mobileCount == null ? 0: Integer.parseInt(mobileCount));
    }

    @Override
    public void increaseSmsCount(String ip, String mobile) {
        int time =  86400 - (int)(new Date().getTime()/1000 + 8*3600) % 86400;

        String keyIp = String.format("%s:ip:%s", PREFIX_SMS_CODE, ip);
        this.stringRedisTemplate.opsForValue().increment(keyIp);
        this.stringRedisTemplate.expire(keyIp, time, TimeUnit.SECONDS);

        String keyMobile = String.format("%s:mb:%s", PREFIX_SMS_CODE, mobile);
        this.stringRedisTemplate.opsForValue().increment(keyMobile);
        this.stringRedisTemplate.expire(keyMobile, time, TimeUnit.SECONDS);
    }
}
