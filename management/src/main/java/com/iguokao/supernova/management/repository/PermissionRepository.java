package com.iguokao.supernova.management.repository;

import com.iguokao.supernova.management.document.Permission;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface PermissionRepository extends MongoRepository<Permission, ObjectId> {
    int countByName(String name);
    int countByKey(String key);

    List<Permission> findByKeyIn(List<String> keyList);
}
