package com.iguokao.supernova.management;

import com.iguokao.supernova.management.service.OperatorService;
import feign.Headers;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.security.core.context.SecurityContextHolder;

@RequiredArgsConstructor
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
public class ManagementApplication implements CommandLineRunner {

	public static void main(String[] args) {
		SpringApplication.run(ManagementApplication.class, args);
	}

	private final OperatorService operatorService;

	@Override
	public void run(String... args) throws Exception {
//		this.operatorService.login("15092359696", "AWDZXCawdzxc11");
	}
}
