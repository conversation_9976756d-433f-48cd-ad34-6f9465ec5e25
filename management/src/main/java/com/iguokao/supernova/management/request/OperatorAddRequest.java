package com.iguokao.supernova.management.request;

import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OperatorAddRequest extends PageRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登录名")
    private String loginName;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登录密码")
    private String loginPassword;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "全名")
    private String fullName;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "全名")
    private String mobile;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "备注")
    private String note;
}
