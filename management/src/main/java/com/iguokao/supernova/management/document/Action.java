package com.iguokao.supernova.management.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Getter
@Setter
public class Action extends BaseDocument {
    @Indexed(name = "operatorId_index")
    private ObjectId operatorId;
    @Indexed(name = "companyId_index")
    private ObjectId companyId;
    @Indexed(name = "type_index")
    private Integer type;

    private Integer value;

    private String text;
}
