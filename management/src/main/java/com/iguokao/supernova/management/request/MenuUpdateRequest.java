package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MenuUpdateRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单的uuid")
    private String menuId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单名称")
    private String name;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单路径")
    private String path;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单编号（用于显示时排序）")
    private Integer sort;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单隐藏")
    private Boolean hidden;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "父菜单uuid")
    private String parentId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单图标")
    private String icon;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "菜单模板")
    private String template;
}
