package com.iguokao.supernova.management.repository;

import com.iguokao.supernova.management.document.Dictionary;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface DictionaryRepository extends MongoRepository<Dictionary, ObjectId> {
    int countByKey(String key);
    int countByParentId(ObjectId id);
    Optional<Dictionary> findByKey(String key);
    List<Dictionary> findByParentId(ObjectId id);
}
