package com.iguokao.supernova.management.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.RegistrationRemote;
import com.iguokao.supernova.common.request.CompanyConfigRegisterParam;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.management.document.Company;
import com.iguokao.supernova.management.request.CompanyAddRequest;
import com.iguokao.supernova.management.request.CompanyPageRequest;
import com.iguokao.supernova.management.request.CompanyUpdateRequest;
import com.iguokao.supernova.management.respone.CompanyResponse;
import com.iguokao.supernova.management.respone.PageCompanyResponse;
import com.iguokao.supernova.management.service.CompanyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v1/company")
@RequiredArgsConstructor
public class CompanyController {
    private final CompanyService companyService;
    private final RegistrationRemote registrationRemote;
    private final AuditService auditService;

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/page")
    @Operation(summary = "所有企业")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageCompanyResponse.class)))
    public RestResponse<PageResponse<CompanyResponse>> page(@RequestBody CompanyPageRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Company>, Integer> page = this.companyService.getPageData(request.getName(), request.getShorName(), pageable);
        List<CompanyResponse> res = CompanyResponse.of(page.first());
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }

    @GetMapping("/info/{companyId}")
    @Operation(summary = "企业信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = CompanyResponse.class)))
    public RestResponse<CompanyResponse> info(@PathVariable String companyId) {
        Company company = this.companyService.getById(companyId);
        return RestResponse.success(CompanyResponse.of(company));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/add")
    @Operation(summary = "添加企业")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody CompanyAddRequest request) {
        Company company = new Company();
        BeanUtils.copyProperties(request, company);
        this.companyService.add(company);
        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.COMPANY_ADD.getCode(), company);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PostMapping("/update")
    @Operation(summary = "更新企业")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody CompanyUpdateRequest request) {
        Company company = new Company();
        BeanUtils.copyProperties(request, company);
        company.set_id(new ObjectId(request.getCompanyId()));
        this.companyService.update(company);

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.COMPANY_ADD.getCode(), company);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/registration/enable/{companyId}")
    @Operation(summary = "开启报名系统使用权限")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> registration(@PathVariable String companyId) {
        Company company = this.companyService.getById(companyId);
        this.companyService.enableRegistration(companyId);
        CompanyConfigRegisterParam param = new CompanyConfigRegisterParam();
        param.setCompanyId(companyId);
        param.setName(company.getName());
        param.setLogo(company.getLogo());
        RestResponse<String> res = this.registrationRemote.register(param);
        if(!res.getCode().equals(0)){
            log.error("远程 {} - {}", res.getCode(), res.getMessage() != null ? res.getMessage() : "Null");
            throw new ServiceException(BaseExceptionEnum.REMOTE_COMMUNICATION_FAILED);
        }

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.COMPANY_REGISTRATION_ENABLE.getCode(), null);
        this.auditService.add(item);
        return RestResponse.success();
    }
}
