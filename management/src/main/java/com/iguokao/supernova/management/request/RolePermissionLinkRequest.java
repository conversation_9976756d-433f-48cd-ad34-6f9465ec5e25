package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class RolePermissionLinkRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "角色Id")
    private String roleId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "权限Id 组成的数组")
    private List<String> permissionIdList;
}

