package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.management.document.Action;
import com.iguokao.supernova.management.service.ActionService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@RequiredArgsConstructor
public class AuditServiceImpl implements AuditService {

    private final ActionService actionService;
    private final JwtService jwtService;

    @Override
    public void add(AuditItem item) {
        Action action = new Action();
        String operatorId = this.jwtService.currentOperatorId();
        if(!operatorId.equals("anonymousUser")){
            action.setOperatorId(new ObjectId(operatorId));
        }
        if(item.getOperatorId() != null){
            action.setOperatorId(new ObjectId(item.getOperatorId()));
        }
        if(item.getCompanyId() != null){
            action.setCompanyId(new ObjectId(item.getCompanyId()));
        }
        BeanUtils.copyProperties(item, action);
        this.actionService.add(action);
    }
}
