package com.iguokao.supernova.management.request;

import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ActionPageRequest extends PageRequest {
    @Schema(description = "企业ID")
    private String companyId;

    @Schema( description = "类型")
    private Integer type;

    @Schema(description = "手机号")
    private String operatorId;
}
