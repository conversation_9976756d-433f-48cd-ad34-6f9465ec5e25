package com.iguokao.supernova.management.respone;

import com.iguokao.supernova.management.document.Dictionary;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Schema(title = "Dictionary 字典节点")
public class DictionaryResponse {
    
    @Schema(description = "节点的 ID")
    private String dictionaryId;

    @Schema(description = "字典key")
    private String dictKey;

    @Schema(description = "节点值")
    private String value;

    @Schema(description = "节点补充备注")
    private String info;

    @Schema(description = "节点级别")
    private Integer level;

    @Schema(description = "父节点")
    private String parentUuid;

    public static DictionaryResponse of(Dictionary obj){
        if(obj==null){
            return null;
        }
        DictionaryResponse res = new DictionaryResponse();
        BeanUtils.copyProperties(obj, res);
        res.setDictionaryId(obj.get_id().toString());
        res.setDictKey(obj.getKey());
        if(obj.getParentId() != null){
            res.setParentUuid(obj.getParentId().toString());
        }
        return res;
    }

    public static List<DictionaryResponse> of(List<Dictionary> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<DictionaryResponse> res = new ArrayList<>();
        for(Dictionary obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
