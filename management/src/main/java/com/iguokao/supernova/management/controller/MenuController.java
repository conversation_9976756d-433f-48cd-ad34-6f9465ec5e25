package com.iguokao.supernova.management.controller;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.management.document.Menu;
import com.iguokao.supernova.management.request.MenuAddRequest;
import com.iguokao.supernova.management.request.MenuUpdateRequest;
import com.iguokao.supernova.management.respone.MenuResponse;
import com.iguokao.supernova.management.service.MenuService;
import com.iguokao.supernova.management.service.OperatorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/menu")
@RequiredArgsConstructor
public class MenuController {
    private final MenuService menuService;
    private final OperatorService operatorService;
    private final JwtService jwtService;
    private final AuditService auditService;

    @PostMapping("/my")
    @Operation(summary = "当前用户菜单")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = MenuResponse.class))))
    public RestResponse<List<MenuResponse>> my() {
        List<Menu> list = this.menuService.operatorMenuList(jwtService.currentOperatorId());
        return RestResponse.success(MenuResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/add")
    @Operation(summary = "添加菜单")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody MenuAddRequest request) {
        Menu menu = new Menu();
        BeanUtils.copyProperties(request, menu);
        if(null != request.getParentId()){
            menu.setParentId(new ObjectId(request.getParentId()));
        } else {
            menu.setLevel(1);
        }
        this.menuService.add(menu);
        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.MENU_ADD.getCode(), menu);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/update")
    @Operation(summary = "更新菜单")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody MenuUpdateRequest param) {
        Menu menu = this.menuService.getById(param.getMenuId());
        BeanUtils.copyProperties(param, menu);
        if(param.getParentId()!=null){
            menu.setParentId(new ObjectId(param.getParentId()));
        }
        this.menuService.update(menu);

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.MENU_UPDATE.getCode(), menu);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/remove/{menuId}")
    @Operation(summary = "移除菜单")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@PathVariable(value = "menuId") String menuId) {
        Menu menu = this.menuService.remove(menuId);
        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.MENU_DELETE.getCode(), menu);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/all")
    @Operation(summary = "所有菜单")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = MenuResponse.class))))
    public RestResponse<List<MenuResponse>> all() {
        List<Menu> list = this.menuService.getAll();
        List<MenuResponse> voList = MenuResponse.of(list);
        return RestResponse.success(voList);
    }
}
