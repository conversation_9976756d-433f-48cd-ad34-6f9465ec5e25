package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
@Schema(description = "字典值添加")
public class DictionaryAddRequest {
    @NotNull(message = "字典key 不能为空")
    @Length(min = 4, max = 16, message = "字典key 长度必须大于3 小于16")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "字典key")
    private String dictKey;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "节点值")
    private String value;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "节点补充备注")
    private String info;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "父节点")
    private String parentId;
}
