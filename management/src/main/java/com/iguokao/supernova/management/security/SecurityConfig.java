package com.iguokao.supernova.management.security;

import com.iguokao.supernova.common.exception.SecurityAccessDeniedHandler;
import com.iguokao.supernova.common.exception.SecurityAuthenticationEntryPoint;
import com.iguokao.supernova.common.security.JwtAuthorizationFilter;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.LoginKeyService;
import com.iguokao.supernova.common.util.CorsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.expression.WebExpressionAuthorizationManager;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.cors.CorsConfigurationSource;

import static com.iguokao.supernova.common.constant.SecurityConstant.ENDPOINTS_WHITELIST;

@Configuration
@Slf4j
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = false, jsr250Enabled = false, prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtService jwtService;
    private final AuthenticationConfiguration config;
    private final SecurityAuthenticationEntryPoint authenticationEntryPoint;
    private final SecurityAccessDeniedHandler accessDeniedHandler;
    private final LoginKeyService loginKeyService;

    @Value("${app.security.remote-ip}")
    private String remoteIp;

//    @Bean
//    @Profile("prod")
//    CorsConfigurationSource corsConfigurationSource() {
//        return CorsUtil.getConfiguration();
//    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http)throws Exception{
        AntPathRequestMatcher[] matchers = new AntPathRequestMatcher[ENDPOINTS_WHITELIST.length];
        for (int i = 0; i < ENDPOINTS_WHITELIST.length; i++) {
            matchers[i] = new AntPathRequestMatcher(ENDPOINTS_WHITELIST[i]);
        }

        // 远程
        String remoteServer = String.format("hasIpAddress('%s') or hasIpAddress('127.0.0.1/32')", remoteIp);
        log.info("允许远程 - {}", remoteServer);
        WebExpressionAuthorizationManager remoteManager = new WebExpressionAuthorizationManager(remoteServer);

        http
//                .cors(Customizer.withDefaults())
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/auth/**")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/actuator/**")).permitAll()
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/remote/**")).access(remoteManager)
                        .requestMatchers(matchers).permitAll()
                        .anyRequest().authenticated()
                )
//                .authenticationProvider(authenticationProvider)
                .addFilterBefore(new JwtAuthorizationFilter(jwtService, loginKeyService, config.getAuthenticationManager()), UsernamePasswordAuthenticationFilter.class)
                .sessionManagement(sessionManagement -> sessionManagement.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .headers(httpSecurityHeadersConfigurer -> httpSecurityHeadersConfigurer.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable))
                .exceptionHandling(exceptionHandlingConfigurer -> exceptionHandlingConfigurer
                        .authenticationEntryPoint(authenticationEntryPoint)
                        .accessDeniedHandler(accessDeniedHandler)
                );
        return http.build();
    }
}