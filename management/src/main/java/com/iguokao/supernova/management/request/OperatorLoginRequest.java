package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OperatorLoginRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登录用户名")
    private String username;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登陆密码")
    private String password;

    @Schema(description = "会话 ID")
    private String sessionId;

    @Schema(description = "签名")
    private String sig;

    @Schema(description = "令牌")
    private String token;

    @Schema(description = "短信验证码")
    private String smsCode;
}
