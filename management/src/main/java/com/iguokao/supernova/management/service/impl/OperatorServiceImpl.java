package com.iguokao.supernova.management.service.impl;

import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigRequest;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigResponse;
import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.CloopenRemote;
import com.iguokao.supernova.common.remote.cloopen.SmsResponse;
import com.iguokao.supernova.common.util.RequestUtil;
import com.iguokao.supernova.management.document.Operator;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.enums.OperatorTypeEnum;
import com.iguokao.supernova.management.repository.OperatorRepository;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.management.repository.RoleRepository;
import com.iguokao.supernova.management.service.CacheService;
import com.iguokao.supernova.management.service.OperatorService;
import com.wf.captcha.SpecCaptcha;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class OperatorServiceImpl implements OperatorService {
    private final OperatorRepository operatorRepository;
    private final RoleRepository roleRepository;
    private final BCryptPasswordEncoder bCryptPasswordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;
    private final MongoTemplate mongoTemplate;
    private final CacheService cacheService;
    private final CloopenRemote cloopenRemote;
    private final IAcsClient acsClient;

    @Value(value = "${app.cloopen.account-sid}")
    private String cloopenAccountSid;
    @Value(value = "${app.cloopen.sms-app-id}")
    private String cloopenSmsAppId;
    @Value(value = "${app.cloopen.auth-token}")
    private String cloopenAuthToken;
    @Value(value = "${app.cloopen.template.operator-login}")
    private String smsTemplateLogin;
    @Value(value = "${app.cloopen.template.password-reset}")
    private String smsTemplatePasswordReset;
    @Value(value = "${app.cloopen.template.refund}")
    private String smsTemplateRefund;
    @Value(value = "${app.cloopen.template.withdraw}")
    private String smsTemplateWithdraw;

    @Value(value = "${app.ali.acs-key}")
    private String aliAcsKey;
    @Value(value = "${app.ali.acs-scene}")
    private String aliAcsScene;

    @Override
    public boolean acsValidate(String sessionId, String sig, String token, String ip) {
        AuthenticateSigRequest request = new AuthenticateSigRequest();
        request.setSessionId(sessionId); // 会话ID。必填参数，从前端获取，不可更改。
        request.setSig(sig); // 签名串。必填参数，从前端获取，不可更改。
        request.setToken(token); // 请求唯一标识。必填参数，从前端获取，不可更改。
        request.setScene(aliAcsScene); // 场景标识。必填参数，从前端获取，不可更改。
        request.setAppKey(aliAcsKey); // 应用类型标识。必填参数，后端填写。
        request.setRemoteIp(ip); // 客户端IP。必填参数，后端填写。
        try {
            //response的code枚举：100验签通过，900验签失败。
            AuthenticateSigResponse response = acsClient.getAcsResponse(request);
            if (response.getCode() == 100) {
                // System.out.println("验签通过");
                return true;
            }
        } catch (Exception e) {
            log.error("验签失败");
            return false;
        }
        return false;
    }

    @Override
    public String add(Operator operator) {
        int countByLoginName = this.operatorRepository.countByLoginName(operator.getLoginName());
        if(countByLoginName > 0) {
            throw new ServiceException(ExceptionEnum.OPERATOR_EXIST);
        }
        int countByMobile = this.operatorRepository.countByMobile(operator.getMobile());
        if(countByMobile > 0) {
            throw new ServiceException(ExceptionEnum.OPERATOR_MOBILE_EXIST);
        }
        operator.setLoginPassword(bCryptPasswordEncoder.encode(operator.getLoginPassword().trim()));
        Operator res = operatorRepository.insert(operator);
        return res.get_id().toString();
    }

    @Override
    public void update(Operator operator) {
        Operator exist = this.getById(operator.get_id().toString());
        if(!operator.getMobile().equals(exist.getMobile())){
            if(this.operatorRepository.countByMobile(operator.getMobile()) > 0){
                throw new ServiceException(ExceptionEnum.OPERATOR_MOBILE_EXIST);
            }
        }
        BeanUtils.copyProperties(operator, exist,
                "_id",
                "relatedId",
                "salt",
                "loginPassword",
                "expiredAt",
                "enabled",
                "accountNonLocked",
                "roleKeyList",
                "companyIdList",
                "roleList");
        operatorRepository.save(exist);
    }

    @Override
    public Operator remove(String operatorId) {
        Operator operator = this.getById(operatorId);
        if(operator.getType().equals(OperatorTypeEnum.STANDARD.getCode())){
            operatorRepository.deleteById(new ObjectId(operatorId));
        }
        return operator;
    }

    @Override
    public ImageCode getImageCode() {
        SpecCaptcha specCaptcha = new SpecCaptcha(160, 40, 4);
        ImageCode imageCode = new ImageCode();
        imageCode.setText(specCaptcha.text().toLowerCase());
        imageCode.setKey(UUID.randomUUID().toString().replace("-", "").toLowerCase());
        // 加入缓存
        log.info("ImageCode - {} - {}", imageCode.getKey(), imageCode.getText());
        this.cacheService.setImageCode(imageCode);
        // 输入
        imageCode.setImageCode(specCaptcha.toBase64());
        return imageCode;
    }

    public String login(String loginName, String loginPassword) {
        try {
            authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(loginName, loginPassword));
        } catch (AuthenticationException e){
            throw new ServiceException(ExceptionEnum.OPERATOR_LOGIN_AUTH_FAILED);
        }
        Operator operator = this.getByLoginName(loginName);
        operator.setRoleList(this.roleRepository.findByKeyIn(operator.getRoleKeyList()));
        List<String> idList = new ArrayList<>();
        if(operator.getType().equals(OperatorTypeEnum.SITE_OPERATOR.getCode())){
            idList.add(String.format("%s_%s", IdConstant.SITE_ID_PREFIX, operator.getRelatedId()));
        }

        // loginKey 单一登录检测
//        String loginKey = StringUtil.genPassword(8);
//        idList.add(String.format("%s_%s", IdConstant.LOGIN_KEY_PREFIX, loginKey));
//
//        Query query = new Query(Criteria.where("_id").is(operator.get_id()));
//        Update update = new Update();
//        update.set("loginKey", loginKey);
//        mongoTemplate.updateFirst(query, update, Operator.class);
//        this.cacheService.setLoginKey(operator.get_id().toString(), loginKey);
        String token = jwtService.generateToken(operator.getAuthorities(), operator, idList);
        if(operator.getRoleKeyList().contains("2fa")){
            String key = new ObjectId().toString();
            this.cacheService.setToken(key, token);
            return key;
        }
        return token;
    }

    @Override
    public void sendSmsLogin(ImageCode imageCode, String mobile, HttpServletRequest request) {
        int smsCode = new Random().nextInt(8888) + 1000;
        List<String> argList = new ArrayList<>();
        argList.add(String.valueOf(smsCode));
        argList.add("15");
        String ip = this.checkSmsLimit(mobile, request);
        this.sendSms(imageCode,
                mobile,
                smsTemplateLogin,
                argList,
                String.valueOf(smsCode),
                900,
                ip);
    }


    @Override
    public void sendSmsPassword(ImageCode imageCode, String mobile, HttpServletRequest request) {
        int smsCode = new Random().nextInt(8888) + 1000;
        List<String> argList = new ArrayList<>();
        argList.add(String.valueOf(smsCode));
        argList.add("15");
        String ip = this.checkSmsLimit(mobile, request);
        this.sendSms(imageCode,
                mobile,
                smsTemplatePasswordReset,
                argList,
                String.valueOf(smsCode),
                900,
                ip);
    }

    @Override
    public void sendSmsRefund(ImageCode imageCode, String mobile, List<String> argList, String smsCode, HttpServletRequest request) {
        this.sendSms(imageCode,
                mobile,
                smsTemplateRefund,
                argList,
                smsCode,
                90,
                null);
    }

    @Override
    public void sendSmsWithdraw(ImageCode imageCode, String mobile, List<String> argList, String smsCode, HttpServletRequest request) {
        this.sendSms(imageCode,
                mobile,
                smsTemplateWithdraw,
                argList,
                smsCode,
                90,
                null);
    }

    public String checkSmsLimit(String mobile, HttpServletRequest request){
        String ip = RequestUtil.getClientIp(request).split(",")[0];
        Tuple2<Integer, Integer> count = this.cacheService.getSmsCount(ip, mobile);
        //一个ip一天30次上限  ，一个手机号一天10次上限
        if(count.first() >= 30 || count.second() >= 10){
            throw new ServiceException(ExceptionEnum.SMS_TIMES_BEYOND_LIMIT);
        }
        return ip;
    }

    public void sendSms(ImageCode imageCode, String mobile, String template, List<String> argList, String smsCode, Integer seconds, String ip) {
        ImageCode code =  this.cacheService.getImageCode(imageCode.getKey());
        if(null == code){
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_FOUND);
        } else if(code.getText().equals(imageCode.getImageCode().toLowerCase())) {
            SmsResponse res = CloopenRemote.sendSns(cloopenRemote,
                    template,
                    mobile,
                    argList,
                    cloopenSmsAppId,
                    cloopenAccountSid,
                    cloopenAuthToken);
            log.info(res.toString());
            if(ip != null){
                this.cacheService.increaseSmsCount(ip, mobile);
            }
            this.cacheService.setSmsCode(mobile, smsCode, seconds);
        } else {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_VALID);
        }
        this.cacheService.deleteImageCode(imageCode.getKey());
    }

    @Override
    public Operator getById(String operatorId) {
        return this.operatorRepository.findById(new ObjectId(operatorId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.OPERATOR_NOT_FOUND));
    }

    @Override
    public Operator getByIdWithRoleList(String operatorId) {
        Operator operator = this.getById(operatorId);
        operator.setRoleList(this.roleRepository.findByKeyIn(operator.getRoleKeyList()));
        return operator;
    }

    @Override
    public Tuple2<List<Operator>, Integer> getPageData(String companyId, String fullName, String mobile, Pageable pageable) {
        Query query = new Query();



        if(fullName != null){
            query.addCriteria(Criteria.where("fullName").is(fullName));
        }
        if(mobile != null){
            query.addCriteria(Criteria.where("mobile").is(mobile));
        }
        if(companyId != null) {
            query.addCriteria(Criteria.where("companyIdList").in(new ObjectId(companyId)));
        }

        long count = mongoTemplate.count(query, Operator.class);
        query.with(pageable);
        List<Operator> list = mongoTemplate.find(query, Operator.class);

        return new Tuple2<>(list, (int)count);
    }

    @Override
    public Operator getByLoginName(String loginName) {
        return this.operatorRepository.findByLoginName(loginName)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.OPERATOR_NOT_FOUND));
    }

    @Override
    public Operator getByMobile(String mobile) {
        return this.operatorRepository.findByMobile(mobile)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.OPERATOR_NOT_FOUND));
    }

    @Override
    public List<Operator> getOperatorList(List<String> operatorIdList) {
        List<ObjectId> idList = operatorIdList
                .stream()
                .filter(Objects::nonNull)
                .map(ObjectId::new)
                .toList();
        return this.operatorRepository.findBy_idIn(idList);
    }

    @Override
    public void linkOperatorRole(String operatorId, List<String> roleKeyList) {
        this.operatorRepository.findById(new ObjectId(operatorId)).ifPresent(operator -> {
            if(operator.getType().equals(OperatorTypeEnum.SITE_OPERATOR.getCode())
                    || operator.getType().equals(OperatorTypeEnum.ROOM_OPERATOR.getCode())){
                throw new ServiceException(ExceptionEnum.OPERATOR_PASSWORD_WRONG);
            }
        });

        Query query = new Query(Criteria.where("_id").is(new ObjectId(operatorId)));
        Update update = new Update();
        update.set("roleKeyList", roleKeyList);
        mongoTemplate.updateFirst(query, update, Operator.class);
    }

    @Override
    public void linkOperatorCompany(String operatorId, List<String> companyIdList) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(operatorId)));
        List<ObjectId> idList = companyIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        Update update = new Update();
        update.set("companyIdList", idList);
        mongoTemplate.updateFirst(query, update, Operator.class);
    }

    @Override
    public void changePassword(String operatorId, String oldPassword, String newPassword) {
        Operator operator = this.getById(operatorId);
        if (!this.bCryptPasswordEncoder.matches(oldPassword, operator.getLoginPassword())) {
            throw new ServiceException(ExceptionEnum.OPERATOR_PASSWORD_WRONG);
        }
        operator.setLoginPassword(bCryptPasswordEncoder.encode(newPassword));
        operatorRepository.save(operator);
    }

    @Override
    public void resetPassword(String operatorId, String newPassword) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(operatorId)));
        Update update = new Update();
        update.set("loginPassword", bCryptPasswordEncoder.encode(newPassword));
        mongoTemplate.updateFirst(query, update, Operator.class);
    }

    @Override
    public List<Operator> companyOperatorList(String companyId) {
        List<ObjectId> idList = Collections.singletonList(new ObjectId(companyId));
        return this.operatorRepository.findByCompanyIdListIn(idList);
    }

    @Override
    public void lock(String operatorId) {
        Operator operator = this.getById(operatorId);
        Query query = new Query(Criteria.where("operatorId").is(new ObjectId(operatorId)));
        Update update = new Update();
        update.set("accountNonLocked", !operator.getAccountNonLocked());
        mongoTemplate.updateFirst(query, update, Operator.class);
    }

    @Override
    public String login2fa(String mobile, String smsCode, String key2fa) {
        String code = this.cacheService.getSmsCode(mobile);
        if (null == code) {
            throw new ServiceException(ExceptionEnum.SMS_CODE_NOT_FOUND);
        } else if(code.equals(smsCode)){
            String token = this.cacheService.getToken(key2fa);
            if(token == null){
                throw new ServiceException(ExceptionEnum.TIMEOUT_2FA);
            }
            return token;
        } else {
            throw new ServiceException(ExceptionEnum.SMS_CODE_NOT_VALID);
        }
    }
}
