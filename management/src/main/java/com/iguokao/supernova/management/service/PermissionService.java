package com.iguokao.supernova.management.service;

import com.iguokao.supernova.management.document.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface PermissionService {
    // 创建 Permission
    void add(Permission permission);
    // 修改 Permission
    void update(Permission permission);
    // 根据ID删除一个操作员，同时删除与该操作员关联的角色关联信息. type="admin"的超级管理员不能删除.
    Permission remove(String permissionId);
    // 根据uuid获取数据 PermissionAction
    Permission getById(String permissionId);
    // 权限 根据类别

    List<Permission> getByOperatorId(String operatorId);
    List<Permission> getAll();

    List<Permission> getByRoleId(String roleId);
}
