package com.iguokao.supernova.management.respone;

import com.iguokao.supernova.common.response.PageResponse;
import org.springframework.data.domain.Pageable;

import java.util.List;


public class PagePermissionResponse extends PageResponse<PermissionResponse> {
    public PagePermissionResponse(List<PermissionResponse> list, int total, Pageable pageable) {
        super(list, total, pageable);
    }
}
