package com.iguokao.supernova.management.controller;

import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.management.document.Dictionary;
import com.iguokao.supernova.management.request.DictionaryAddRequest;
import com.iguokao.supernova.management.request.DictionaryUpdateRequest;
import com.iguokao.supernova.management.respone.DictionaryResponse;
import com.iguokao.supernova.management.service.DictionaryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/v1/dict")
@RequiredArgsConstructor
public class DictionaryController {
    private final DictionaryService dictionaryService;

    @GetMapping("/all")
    @Operation(summary = "获取全部节点")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = DictionaryResponse.class))))
    public RestResponse<List<DictionaryResponse>> all() {
        List<Dictionary> list = dictionaryService.getAll();
        List<DictionaryResponse> res = DictionaryResponse.of(list);
        return RestResponse.success(res);
    }

    @GetMapping("/value/{dictKey}")
    @Operation(summary = "获取当前节点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<DictionaryResponse> value(@PathVariable String dictKey) {
        Dictionary dictionary = dictionaryService.getByKey(dictKey);
        return RestResponse.success(DictionaryResponse.of(dictionary));
    }

    @GetMapping("/values/{dictKey}")
    @Operation(summary = "获取节点的子节点")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = DictionaryResponse.class))))
    public RestResponse<List<DictionaryResponse>> listByKey(@PathVariable String dictKey) {
        List<Dictionary> list = dictionaryService.subListByKey(dictKey);
        return RestResponse.success(DictionaryResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/add")
    @Operation(summary = "添加字典节点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody DictionaryAddRequest req) {
        Dictionary dictionary = new Dictionary();
        BeanUtils.copyProperties(req, dictionary);
        if(null != req.getParentId()){
            dictionary.setParentId(new ObjectId(req.getParentId()));
            Dictionary parentDict = dictionaryService.getById(req.getParentId());
            dictionary.setLevel(parentDict.getLevel() + 1);
        }
        else {
            dictionary.setLevel(1);
        }
        dictionary.setKey(req.getDictKey());
        this.dictionaryService.add(dictionary);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/update")
    @Operation(summary = "修改字典节点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody DictionaryUpdateRequest req) {
        Dictionary dictionary = dictionaryService.getById(req.getDictId());
        BeanUtils.copyProperties(req, dictionary);
        dictionary.setKey(req.getDictKey());
        dictionary.setUpdatedAt(new Date());
        this.dictionaryService.update(dictionary);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/remove/{id}")
    @Operation(summary = "删除字典节点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@PathVariable String id) {
        dictionaryService.remove(id);
        return RestResponse.success();
    }
}
