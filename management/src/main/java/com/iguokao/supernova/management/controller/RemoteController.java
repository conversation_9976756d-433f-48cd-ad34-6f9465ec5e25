package com.iguokao.supernova.management.controller;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.request.LoginKeyCheckRequest;
import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.management.document.Company;
import com.iguokao.supernova.management.document.Operator;
import com.iguokao.supernova.management.enums.OperatorTypeEnum;
import com.iguokao.supernova.management.respone.CompanyResponse;
import com.iguokao.supernova.management.service.CacheService;
import com.iguokao.supernova.management.service.CompanyService;
import com.iguokao.supernova.management.service.OperatorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/remote")
@RequiredArgsConstructor
public class RemoteController {
    private final OperatorService operatorService;
    private final CompanyService companyService;
    private final CacheService cacheService;
    private final AuditService auditService;

    @GetMapping("/operator/info/{operatorId}")
    @Operation(summary = "管理员信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = CompanyResponse.class)))
    public RestResponse<OperatorRemoteResponse> info(@PathVariable String operatorId) {
        Operator operator = this.operatorService.getByIdWithRoleList(operatorId);
        return RestResponse.success(operator.toRemoteResponse());
    }

    @PostMapping("/site/operator/add")
    @Operation(summary = "管理员信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> info(@RequestBody OperatorRemoteResponse request) {
        Operator operator = new Operator();
        BeanUtils.copyProperties(request, operator);
        operator.setRelatedId(new ObjectId(request.getRelatedId()));
        operator.setType(OperatorTypeEnum.SITE_OPERATOR.getCode());
        operator.getRoleKeyList().add("site");
        String operatorId = this.operatorService.add(operator);
        return RestResponse.success(operatorId);
    }

    @GetMapping("/company/info/{companyId}")
    @Operation(summary = "企业信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = CompanyRemoteResponse.class)))
    public RestResponse<CompanyRemoteResponse> company(@PathVariable String companyId) {
        Company company = this.companyService.getById(companyId);
        return RestResponse.success(company.getRemoteResponse());
    }

    @PostMapping("/login/key/check")
    @Operation(summary = "登录Key验证")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Boolean.class)))
    public RestResponse<Boolean> loginKeyCheck(@RequestBody LoginKeyCheckRequest request) {
        String key = this.cacheService.getLoginKey(request.getOperatorId());
        if(key != null){
            return RestResponse.success(key.equals(request.getLoginKey()));
        }
        return RestResponse.success(false);
    }

    @PostMapping("/audit")
    @Operation(summary = "audit")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> audit(@RequestBody AuditItem request) {
        this.auditService.add(request);
        return RestResponse.success();
    }

    @GetMapping("/sms/code/{operatorId}")
    @Operation(summary = "企业信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = CompanyRemoteResponse.class)))
    public RestResponse<String> smsCode(@PathVariable String operatorId) {
        Operator operator = this.operatorService.getById(operatorId);
        String smsCode = this.cacheService.getSmsCode(operator.getLoginName());
        return RestResponse.success(smsCode);
    }
}
