package com.iguokao.supernova.management.controller;

import com.iguokao.supernova.common.document.AuditItem;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.AuditTypeEnum;
import com.iguokao.supernova.common.response.ImageCodeResponse;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.AuditService;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.management.document.Company;
import com.iguokao.supernova.management.document.Operator;
import com.iguokao.supernova.management.enums.OperatorTypeEnum;
import com.iguokao.supernova.management.request.*;
import com.iguokao.supernova.management.respone.CompanyResponse;
import com.iguokao.supernova.management.respone.OperatorResponse;
import com.iguokao.supernova.management.respone.PageOperatorResponse;
import com.iguokao.supernova.management.service.CompanyService;
import com.iguokao.supernova.management.service.OperatorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@RestController
@RequestMapping("/api/v1/operator")
@RequiredArgsConstructor
public class OperatorController {
    private final OperatorService operatorService;
    private final JwtService jwtService;
    private final OssService ossService;
    private final CompanyService companyService;
    private final AuditService auditService;

    @Value("${app.ali.oss-bucket-exam}")
    private String ossBucketExam;
    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/page")
    @Operation(summary = "所有管理员")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageOperatorResponse.class)))
    public RestResponse<PageResponse<OperatorResponse>> page(@RequestBody OperatorPageRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Operator>, Integer> page = this.operatorService.getPageData(request.getCompanyId(),
                request.getFullName(),
                request.getMobile(),
                pageable);
        List<OperatorResponse> res = OperatorResponse.of(page.first());
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/info/{operatorId}")
    @Operation(summary = "操作员信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = OperatorResponse.class)))
    public RestResponse<OperatorResponse> info(@PathVariable String operatorId) {
        Operator operator = this.operatorService.getById(operatorId);
        return RestResponse.success(OperatorResponse.of(operator));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/remove/{operatorId}")
    @Operation(summary = "操作员删除")
    @ApiResponse(content = @Content(schema = @Schema(implementation = OperatorResponse.class)))
    public RestResponse<OperatorResponse> remove(@PathVariable String operatorId) {
        Operator operator = this.operatorService.remove(operatorId);
        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.OPERATOR_DELETE.getCode(), operator);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @GetMapping("/info")
    @Operation(summary = "操作员信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = OperatorResponse.class)))
    public RestResponse<OperatorResponse> myInfo() {
        Operator operator = this.operatorService.getById(jwtService.currentOperatorId());
        return RestResponse.success(OperatorResponse.of(operator));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/add")
    @Operation(summary = "添加操作员")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody OperatorAddRequest request) {
        Operator operator = new Operator();
        BeanUtils.copyProperties(request, operator);
        operator.setType(OperatorTypeEnum.STANDARD.getCode());
        this.operatorService.add(operator);
        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.OPERATOR_ADD.getCode(), operator);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/update")
    @Operation(summary = "更新操作员")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody OperatorUpdateRequest request) {
        Operator operator = this.operatorService.getById(request.getOperatorId());
        operator.setFullName(request.getFullName());
        operator.setMobile(request.getMobile());
        operator.setNote(request.getNote());
        this.operatorService.update(operator);
        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.OPERATOR_UPDATE.getCode(), operator);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/link/role")
    @Operation(summary = "管理员 角色关联")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> linkRole(@RequestBody OperatorRoleLinkRequest request) {
        this.operatorService.linkOperatorRole(request.getOperatorId(), request.getRoleKeyList());

        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.OPERATOR_LINK_ROLE.getCode(), request);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/link/company")
    @Operation(summary = "管理员 企业关联")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> linkCompany(@RequestBody OperatorCompanyLinkRequest request) {
        this.operatorService.linkOperatorCompany(request.getOperatorId(), request.getCompanyIdList());
        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.OPERATOR_LINK_COMPANY.getCode(), request);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/list")
    @Operation(summary = "管理员列表 通过Id")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = OperatorResponse.class))))
    public RestResponse<List<OperatorResponse>> operatorName(@RequestBody List<String> operatorIdList) {
        List<Operator> list = this.operatorService.getOperatorList(operatorIdList);
        return RestResponse.success(OperatorResponse.of(list));
    }

    @PostMapping("/password/change")
    @Operation(summary = "管理员密码修改")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> passwordChange(@Validated @RequestBody OperatorPasswordChangeRequest request) {
        this.operatorService.changePassword(jwtService.currentOperatorId(), request.getOldPassword(), request.getNewPassword());
        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.OPERATOR_PASSWORD_CHANGE.getCode(), request);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/password/reset")
    @Operation(summary = "管理员的的密码重置")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> passwordReset(@RequestBody OperatorPasswordResetRequest request) {
        this.operatorService.resetPassword(request.getOperatorId(),  request.getNewPassword());
        // 审计
        AuditItem item = new AuditItem(null, AuditTypeEnum.OPERATOR_PASSWORD_RESET.getCode(), request);
        this.auditService.add(item);
        return RestResponse.success();
    }

    @GetMapping("/company")
    @Operation(summary = "我关联的企业")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = CompanyResponse.class))))
    public RestResponse<List<CompanyResponse>> companyList() {
        Operator operator = this.operatorService.getById(this.jwtService.currentOperatorId());
        List<Company> list = this.companyService.getByIdList(operator.getCompanyIdList());
        return RestResponse.success(CompanyResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/company/{operatorId}")
    @Operation(summary = "某个操作员的企业信息")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = CompanyResponse.class))))
    public RestResponse<List<CompanyResponse>> companyList(@PathVariable String operatorId) {
        Operator operator = this.operatorService.getById(operatorId);
        List<Company> list = this.companyService.getByIdList(operator.getCompanyIdList());
        return RestResponse.success(CompanyResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_FINANCE')")
    @PostMapping("/sms/send/refund")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ImageCodeResponse.class)))
    public RestResponse<String> refundSms(@Validated @RequestBody SmsRefundSendRequest request, HttpServletRequest req) {
        ImageCode imageCode = new ImageCode();
        imageCode.setImageCode(request.getImageCode());
        imageCode.setKey(request.getKey());

        int smsCode = new Random().nextInt(8888) + 1000;
        List<String> argList = new ArrayList<>();
        argList.add(request.getCandidateName());
        argList.add(request.getPrice());
        argList.add(String.valueOf(smsCode));
        this.operatorService.sendSmsRefund(imageCode, request.getMobile(), argList, String.format("R%s", smsCode), req);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_FINANCE')")
    @PostMapping("/sms/send/withdraw")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ImageCodeResponse.class)))
    public RestResponse<String> withdrawSms(@Validated @RequestBody SmsWithdrawSendRequest request, HttpServletRequest req) {
        ImageCode imageCode = new ImageCode();
        imageCode.setImageCode(request.getImageCode());
        imageCode.setKey(request.getKey());

        int smsCode = new Random().nextInt(8888) + 1000;
        List<String> argList = new ArrayList<>();
        argList.add(request.getPrice());
        argList.add(String.valueOf(smsCode));
        this.operatorService.sendSmsWithdraw(imageCode, request.getMobile(), argList, String.format("W%s", smsCode), req);
        return RestResponse.success();
    }
}
