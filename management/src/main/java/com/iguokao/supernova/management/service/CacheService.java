package com.iguokao.supernova.management.service;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.service.LoginKeyService;

public interface CacheService {
    // 图片验证码
    ImageCode getImageCode(String key);
    // 保存图片验证码
    void setImageCode(ImageCode imageCode);
    // 删除图片验证码
    void deleteImageCode(String key);
    // 获取短信验证码
    String getSmsCode(String key);
    // 设置短信验证码
    void setSmsCode(String key, String code, Integer seconds);

    // 删除短信验证码
    void removeSmsCode(String key);

    void setToken(String key, String token);
    String getToken(String key);

    String getLoginKey(String operatorId);
    void setLoginKey(String operatorId, String key);

    Tuple2<Integer, Integer> getSmsCount(String ip, String mobile);
    void increaseSmsCount(String ip, String mobile);
}
