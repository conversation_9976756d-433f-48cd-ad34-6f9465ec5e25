package com.iguokao.supernova.management.respone;

import com.iguokao.supernova.management.document.Operator;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Schema(title = "Operator 管理员")
public class OperatorResponse {
    @Schema(description = "管理员的uuid")
    private String operatorId;

    @Schema(description = "登录名")
    private String loginName;

    @Schema(description = "管理员姓名")
    private String fullName;

    @Schema(description = "管理员手机号")
    private String mobile;

    @Schema(description = "Email")
    private String email;

    @Schema(description = "管理员类型 admin:超级管理员，common:普通操作员")
    private Integer type;

    @Schema(description = "备注")
    private String note;

    public static OperatorResponse of(Operator obj){
        if(obj==null){
            return null;
        }
        OperatorResponse res = new OperatorResponse();
        BeanUtils.copyProperties(obj, res);
        res.setOperatorId(obj.get_id().toString());

        return res;
    }

    public static List<OperatorResponse> of(List<Operator> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<OperatorResponse> res = new ArrayList<>();
        for(Operator obj : list){
            res.add(of(obj));
        }
        return res;
    }

}
