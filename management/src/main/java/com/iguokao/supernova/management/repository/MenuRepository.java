package com.iguokao.supernova.management.repository;

import com.iguokao.supernova.management.document.Menu;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface MenuRepository extends MongoRepository<Menu, ObjectId> {
    int countByParentId(ObjectId parentId);
    List<Menu> findBy_idIn(List<ObjectId> menuIds);
}
