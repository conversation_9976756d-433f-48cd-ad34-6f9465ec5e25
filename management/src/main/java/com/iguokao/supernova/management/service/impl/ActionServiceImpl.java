package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.management.document.Action;
import com.iguokao.supernova.management.repository.ActionRepository;
import com.iguokao.supernova.management.service.ActionService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ActionServiceImpl implements ActionService {
    private final ActionRepository actionRepository;
    private final MongoTemplate mongoTemplate;


    @Override
    public void add(Action action) {
        this.actionRepository.insert(action);
    }

    @Override
    public Tuple2<List<Action>, Integer> getPage(String companyId, String operatorId, Integer type, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "_id"));
        if(null != companyId){
            query = query.addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));
        }
        if(null != operatorId){
            query = query.addCriteria(Criteria.where("operatorId").is(new ObjectId(operatorId)));
        }
        // 类型
        if(null != type){
            query = query.addCriteria(Criteria.where("type").is(type));
        }
        // 计算总数
        long count = this.mongoTemplate.count(query, Action.class);
        // 分页信息
        query = query.with(pageable);
        List<Action> list = this.mongoTemplate.find(query, Action.class);
        return new Tuple2<>(list, (int)count);
    }
}
