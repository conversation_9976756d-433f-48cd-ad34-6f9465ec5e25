package com.iguokao.supernova.management.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PermissionAddRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "权限名称")
    private String name;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "权限标识", example = "p_admin, p_role_delete")
    private String key;
}
