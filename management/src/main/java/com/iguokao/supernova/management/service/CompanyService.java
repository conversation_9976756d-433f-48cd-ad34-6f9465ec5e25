package com.iguokao.supernova.management.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.management.document.Company;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface CompanyService {
    void add(Company company);
    void update(Company company);
    Company getById(String companyId);
    List<Company> getByIdList(List<ObjectId> list);
    Tuple2<List<Company>, Integer> getPageData(String name, String shortName, Pageable pageable);

    void enableRegistration(String companyId);
}
