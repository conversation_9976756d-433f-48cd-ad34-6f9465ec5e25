package com.iguokao.supernova.management.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.management.document.Operator;
import com.iguokao.supernova.management.document.Permission;
import com.iguokao.supernova.management.document.Role;
import com.iguokao.supernova.management.enums.ExceptionEnum;
import com.iguokao.supernova.management.repository.OperatorRepository;
import com.iguokao.supernova.management.repository.PermissionRepository;
import com.iguokao.supernova.management.repository.RoleRepository;
import com.iguokao.supernova.management.service.PermissionService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {
    private final PermissionRepository permissionRepository;
    private final OperatorRepository operatorRepository;
    private final RoleRepository roleRepository;

    @Override
    public void add(Permission permission) {
        if(this.permissionRepository.countByKey(permission.getKey()) > 0 ||
        this.permissionRepository.countByName(permission.getName()) > 0) {
            throw new ServiceException(ExceptionEnum.PERMISSION_EXIST);
        }
        this.permissionRepository.insert(permission);
    }

    @Override
    public void update(Permission permission) {
        permissionRepository.save(permission);
    }


    @Override
    public List<Permission> getByOperatorId(String operatorId) {
        Operator operator = this.operatorRepository.findById(new ObjectId(operatorId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.OPERATOR_NOT_FOUND));
        if(!operator.getRoleKeyList().isEmpty()){
            List<Role> roleList = this.roleRepository.findByKeyIn(operator.getRoleKeyList());
            if(!roleList.isEmpty()){
                return this.permissionRepository.findByKeyIn(roleList
                        .stream()
                        .map(Role::getPermissionKeyList)
                        .flatMap(Collection::stream)
                        .toList());
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<Permission> getAll() {
        return this.permissionRepository.findAll();
    }

    @Override
    public List<Permission> getByRoleId(String roleId) {
        Role role = this.roleRepository.findById(new ObjectId(roleId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.ROLE_NOT_FOUND));
        return this.permissionRepository.findByKeyIn(role.getPermissionKeyList());
    }


    @Override
    public Permission remove(String permissionId) {
        Permission permission = this.getById(permissionId);
        this.permissionRepository.deleteById(new ObjectId(permissionId));
        return permission;
    }

    @Override
    public Permission getById(String permissionId) {
        return this.permissionRepository.findById(new ObjectId(permissionId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERMISSION_NOT_FOUND));

    }
}
