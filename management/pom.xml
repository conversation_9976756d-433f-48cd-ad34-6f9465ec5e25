<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.iguokao</groupId>
		<artifactId>supernova</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<groupId>com.iguokao.supernova</groupId>
	<artifactId>management</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>management</name>
	<description>Demo project for Spring Boot</description>
	<properties>
		<java.version>17</java.version>
		<aliyun-sdk-afs-version>1.0.1</aliyun-sdk-afs-version>
		<aliyun-sdk-version>4.6.4</aliyun-sdk-version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.iguokao.supernova</groupId>
			<artifactId>common</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<!--  V1.0 SDK  -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>${aliyun-sdk-version}</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-afs</artifactId>
			<version>${aliyun-sdk-afs-version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
			</plugin>
		</plugins>
	</build>

</project>
