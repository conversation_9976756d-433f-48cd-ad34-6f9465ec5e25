FROM registry.cn-beijing.aliyuncs.com/freewolf/jdk17:latest

USER root

ARG APP_NAME
ARG XMS
ARG XMX
ARG PROFILE

ENV APP_NAME $APP_NAME
ENV XMS $XMS
ENV XMX $XMX
ENV PROFILE $PROFILE
ENV VERSION 0.8

RUN mkdir /opt/tmp
RUN mkdir /opt/tmp/pdf
RUN mkdir /opt/upload
RUN mkdir /opt/log

COPY management.jar /opt/management.jar
COPY exam.jar /opt/exam.jar
COPY report.jar /opt/report.jar
COPY gateway.jar /opt/gateway.jar
COPY registration.jar /opt/registration.jar
RUN ls /opt

CMD ["sh", "-c", "java -${XMS} -${XMX} -jar /opt/${APP_NAME}.jar --spring.profiles.active=${PROFILE}"]

EXPOSE 8001 8002 8003 8004 8080
# 8080 gateway
# 8001 management
# 8002 exam
# 8003 report
