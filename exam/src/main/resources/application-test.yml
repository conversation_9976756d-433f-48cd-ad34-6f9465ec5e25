server:
  port: 8002
spring:
  data:
    mongodb:
      uri: ***************************************************
      auto-index-creation: true
  jpa:
    show-sql: true
    generate-ddl: true
app:
  tmp-path: /opt/tmp
  test-paper-id: "676b6fc2da40d12d26cc89b3"
  test-paper-password: "c8d7f5fa"
  test-period-id: "673afba5a45f9418f7dc4274"
  test-subject-id: "673afd10a45f9418f7dc429e"
  admission-url: "https://supernova-admission.igky.cc"
  candidate-confirm-url: "https://supernova-api.igky.cc/exam/api/v1/candidate/email/confirm"
  short-url-prefix: "https://test.igky.cc/"
  service:
    management: http://***********:8001
    report: http://***********:8003
    exam: http://***********:8002
    task: http://***********:3000
    registration: http://***********:8004
    hr: http://***********:5000
    stone: http://***********:8002
    task-callback: http://***********:8002
  security:
    jwt:
      secret-key: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
      expiration: 86400000 # a day
      refresh-token:
        expiration: 86400000
    remote-ip: hasIpAddress('10.0.0.0/8') or hasIpAddress('**********/16')
  redis:
    database: 0
    host: ***********
    password: wVtB7vKkqZggAbyJ
    port: 6379
    max-active: 8
    max-idle: 8
    min-idle: 0
  ali:
    access-key-id: LTAI5tBFNBDyB1JfXqfG85Za
    access-key-secret: ******************************
    oss-endpoint: oss-cn-beijing.aliyuncs.com
    oss-endpoint-internal: oss-cn-beijing-internal.aliyuncs.com
    oss-bucket-exam: supernova-exam-test
  tencent:
    secret-id: AKID1rRloa5NPnvmNhjW5NO8bolSt51h6F7i
    secret-key: LQEl1zh8H9l7C0YppYz93g27vTlnYS6T
    merchant-id: 0NSJ2203141444073205
    merchant-id-lite: 0NSJ2207111622402921
    callback-password: Q2leXoBh1CTWBcoF
    rtc-app-supernova-id: 1600041630
    rtc-app-supernova-key: f05e3291b76f40615d56bd7d12e6c618c14b94bde8093e3e8eb237a6d4757828
    vod-sub-app-id: 1500009069
  mqtt:
    broker: tcp://***********:1883
    username: exam_service
    password: ECeHvG95qcT7X1LPdep1