server:
  port: 8002
  undertow:
    threads:
      io: 4
      worker: 128
spring:
  data:
    mongodb:
      uri: mongodb://app:<EMAIL>:3717/exam?replicaSet=mgset-76207179
      auto-index-creation: true
app:
  tmp-path: /Users/<USER>/tmp
#  tmp-path: /opt/tmp
  test-paper-id: "676b6fc2da40d12d26cc89b3"
  test-paper-password: "c8d7f5fa"
  test-period-id: "673afba5a45f9418f7dc4274"
  test-subject-id: "673afd10a45f9418f7dc429e"
  admission-url: "https://admission.iguokao.com"
  candidate-confirm-url: "https://supernova-exam-api.iguokao.com/api/v1/candidate/email/confirm"
  short-url-prefix: "https://igky.cc/"
  service:
    management: http://127.0.0.1:8001
    report: http://127.0.0.1:8003
    exam: http://127.0.0.1:8002
    task: http://***********
    registration: http://127.0.0.1:8004
    hr: http://127.0.0.1:5000
    stone: http://***********:8080
    task-callback: http://************:8002
  security:
    jwt:
      secret-key: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
      expiration: 86400000 # a day
      refresh-token:
        expiration: 86400000
    remote-ip: hasIpAddress('10.0.0.0/8') or hasIpAddress('**********/16')
  redis:
    database: 0
    host: r-2zetxv1tyf5hfd778ypd.redis.rds.aliyuncs.com
    password: av49J1WhTickA8GZ
    port: 6379
    max-active: 8
    max-idle: 8
    min-idle: 0
  ali:
    access-key-id: LTAI5t5sGHZy1GGQjJVUoqfq
    access-key-secret: ******************************
    oss-endpoint: oss-accelerate.aliyuncs.com
    oss-endpoint-internal: oss-cn-beijing-internal.aliyuncs.com
    oss-bucket-exam: iguokao-supernova-exam
  tencent:
    secret-id: AKID1rRloa5NPnvmNhjW5NO8bolSt51h6F7i
    secret-key: LQEl1zh8H9l7C0YppYz93g27vTlnYS6T
    merchant-id: 0NSJ2203141444073205
    merchant-id-lite: 0NSJ2207111622402921
    callback-password: Q2leXoBh1CTWBcoF
    rtc-app-supernova-id: 1400795330
    rtc-app-supernova-key: 874788148b4d77c0ecc5254a3a54c9da59b03d44a086c4b07565330ba87e3cbe
    vod-sub-app-id: 1500009069
  mqtt:
    broker: tcp://***********:1883
    username: exam_service
    password: ECeHvG95qcT7X1LPdep1