package com.iguokao.supernova.exam.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.exam.document.Period;
import com.iguokao.supernova.exam.document.Subject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class PeriodResponse {
    @Schema(description = "period Id")
    private String periodId;

    @Schema(description = "project Id")
    private String projectId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "状态")
    private Integer state;

    @Schema(description = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startAt;

    @Schema(description = "时长")
    private Integer duration;

    @Schema(description = "结束")
    private Boolean finished;

    @Schema(description = "编排状态")
    private Boolean arranged;

    @Schema(description = "就绪")
    private Boolean ready;

    @Schema(description = "准考证数量")
    private Integer admissionCardCount;


    @Schema(description = "科目列表")
    private List<SubjectResponse> subjectList = new ArrayList<>();

    public static PeriodResponse of(Period obj){
        if(obj == null){
            return null;
        }
        PeriodResponse res = new PeriodResponse();
        BeanUtils.copyProperties(obj, res);
        res.setPeriodId(obj.get_id().toString());
        res.setProjectId(obj.getProjectId().toString());
        for(Subject subject : obj.getSubjectList()){
            res.getSubjectList().add(SubjectResponse.of(subject,null));
        }
        return res;
    }

    public static List<PeriodResponse> of(List<Period> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<PeriodResponse> res = new ArrayList<>();
        for(Period obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
