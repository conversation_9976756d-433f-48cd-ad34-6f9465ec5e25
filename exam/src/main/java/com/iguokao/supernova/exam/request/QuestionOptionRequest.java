package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QuestionOptionRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "选项描述")
    private String title;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "选项序号")
    private Integer value;
}




