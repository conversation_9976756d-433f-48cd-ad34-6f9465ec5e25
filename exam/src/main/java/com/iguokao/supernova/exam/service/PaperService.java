package com.iguokao.supernova.exam.service;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.exam.document.Paper;
import com.iguokao.supernova.exam.document.Subject;
import com.iguokao.supernova.exam.response.PaperResponse;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface PaperService {

    void generatePaper(String periodId, String projectId, String subjectId);

    void clearPaper(String periodId, String projectId, String subjectId);

    Paper previewPaper(String periodId, String projectId, String subjectId);

    List<Paper> getPeriodPaperList(String periodId);

    Paper getSubjectPaper(String subjectId);

}
