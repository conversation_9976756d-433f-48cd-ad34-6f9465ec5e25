package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.exam.document.Confirm;

import java.util.List;

public interface ConfirmService {
    void add(Confirm confirm);
    void updateCandidateCount(String confirmId, Integer candidateCount);

    void remove(String confirmId);

    Confirm getById(String periodId);
    List<Confirm> getListByPeriodId(String periodId);

    List<Confirm> getListBySiteId(String siteId);

    int managerSubmit(String confirmId, String operatorId, List<String> roomIdList);
    int operatorSubmit(String confirmId, String operatorId, List<String> roomIdList);
}
