package com.iguokao.supernova.exam.service.impl;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.enums.ProjectStateEnum;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.PartService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;

import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class PartServiceImpl implements PartService {
    private final PartRepository partRepository;
    private final QuestionRepository questionRepository;
    private final PeriodRepository periodRepository;
    private final ProjectRepository projectRepository;
    private final MongoTemplate mongoTemplate;
    private final CandidateRepository candidateRepository;
    private final CacheService cacheService;

    @Override
    public String add(Part part) {
        List<Part> parts = this.partRepository.findBySubjectId(part.getSubjectId());
        part.setSort(parts.size() + 1);
        parts.add(part);
        handleSubject(part.getPeriodId().toString(), part.getSubjectId().toString(), parts);
        part = this.partRepository.insert(part);
        return part.get_id().toString();
    }

    @Override
    public void update(Part part) {
        this.partRepository.save(part);
    }

    @Override
    public void deletePart(String partId, String periodId, String subjectId) {
        Part part = getById(partId);
        List<Part> parts = getPartsButOne(part.getSubjectId(), part.get_id().toString());
        handleSubject(part.getPeriodId().toString(), part.getSubjectId().toString(), parts);
        this.partRepository.delete(part);
    }

    @Override
    public Part getById(String partId) {
        return this.partRepository.findById(new ObjectId(partId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PART_NOT_FOUND));
    }

    @Override
    public List<Part> getByPeriodId(String periodId) {
        return this.partRepository.findByPeriodId(new ObjectId(periodId));
    }

    @Override
    public void sort(String periodId, String subjectId, List<String> partList) {

        for(int i=0;i<partList.size();i++){
            Part part = this.partRepository.findBy_idAndSubjectId(new ObjectId(partList.get(i)),new ObjectId(subjectId))
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.PART_NOT_FOUND));
            part.setSort(i+1);
            this.partRepository.save(part);
        }
    }

    @Override
    public List<Question> questionList(String partId) {
        List<Question> questionList = new ArrayList<>();
        Part part = this.partRepository.findById(new ObjectId(partId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PART_NOT_FOUND));

        for(PartQuestion pq : part.getQuestionList()){
            Question question = this.questionRepository.findById(pq.getQuestionId())
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.QUESTION_NOT_FOUND));
            question.setQuestionScore(pq.getScore());
            questionList.add(question);
        }
        return questionList;
    }

    @Override
    public void questionFill(List<PartQuestion> partQuestionList, String partId, Set<String> questionIds) {
        List<ObjectId> questionObjectIds = new ArrayList<>();
        for(String s : questionIds){
            questionObjectIds.add(new ObjectId(s));
        }
        Part part = getById(partId);
        part.getQuestionList().clear();
        Double partScore = 0D;

        Query query = new Query(Criteria.where("subjectId").is(part.getSubjectId()));
        query.addCriteria(Criteria.where("_id").ne(new ObjectId(partId)));
        query.addCriteria(Criteria.where("questionList.questionId").in(questionObjectIds));
        long checkRepeat = mongoTemplate.count(query,Part.class);
        if(checkRepeat > 0){
            throw new ServiceException(ExceptionEnum.SUBJECT_QUESTION_REPEAT);
        }

        for(PartQuestion partQuestion : partQuestionList){
            partScore += partQuestion.getScore();
            part.getQuestionList().add(partQuestion);
        }

        part.setPartScore(partScore);

        List<Part> parts = getPartsButOne(part.getSubjectId(), part.get_id().toString());
        parts.add(part);
        handleSubject(part.getPeriodId().toString(), part.getSubjectId().toString(), parts);
        this.partRepository.save(part);
    }

    @Override
    public List<Part> getBySubjectId(String subjectId) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "sort"));
        query = query.addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        return this.mongoTemplate.find(query, Part.class);
    }


    private List<Part> getPartsButOne(ObjectId subjectId, String partId){
        Query query = new Query(Criteria.where("subjectId").is(subjectId));
        query.addCriteria(Criteria.where("_id").ne(new ObjectId(partId)));
        return mongoTemplate.find(query,Part.class);
    }

    private void handleSubject(String periodId, String subjectId, List<Part> parts){
        Double scoreSum = 0D;
        int questionSum = 0;
        for(Part one : parts){
            scoreSum += one.getPartScore();
            questionSum += one.getQuestionList().size();
        }

        //检查时段的时间是否 满足填充
        Period period = getPeriod(periodId);
        for(Subject subject : period.getSubjectList()){
            if(subject.get_id().toString().equals(subjectId)){
                subject.setScore(scoreSum);
                subject.setQuestionCount(questionSum);
            }
        }
        this.periodRepository.save(period);
        cacheService.deletePeriod(periodId);
    }

    private Period getPeriod(String periodId){
        return this.periodRepository.findById(new ObjectId(periodId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
    }
}
