package com.iguokao.supernova.exam.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum ExceptionEnum implements BaseEnum {

    SITE_EXIST(2001, "考点已经存在"),
    SITE_NOT_FOUND(2002, "考点未找到"),
    SITE_CONTAINS_ROOM(2003, "考点中含有考场，请删除全部考场"),
    SITE_LOCKED(2004, "考点锁定，无法修改考场"),

    ROOM_EXIST(2011, "考场已经存在"),
    ROOM_NOT_FOUND(2012, "考场未找到"),
    ROOM_DO_NOT_CONTAIN_ALL_CANDIDATE(2013, "房间中不存在要移除的全部考生"),
    ROOM_CONTAIN_CANDIDATE(2014, "房间已经存在考生"),
    ROOM_HAS_NO_CANDIDATE(2015, "房间内无考生"),
    ROOM_AVAILABLE_BEYOND_LIMIT(2016, "房间可用人数不能超过容量"),
    ROOM_CANDIDATE_BEYOND_LIMIT(2017, "房间人数不能超过可用的1.2倍"),
    ROOM_USING(2018, "当前该房间在使用中，存在使用该房间并且没有结束的项目"),

    PROJECT_EXIST(2021, "项目已经存在"),
    PROJECT_NOT_FOUND(2022, "项目未找到"),
    PROJECT_ONLINE(2024, "项目已经上线，无法进行此操作"),
    PROJECT_ATTACHMENT_EXIST(2025, "项目附件名称重复！"),
    PROJECT_NO_CONFIRM(2026, "项目无考点需求，请先添加需求并确认！"),
    PROJECT_HAS_NOT_CONFIRM(2027, "项目包含未确认需求！"),
    PROJECT_NOT_ONLINE(2028, "项目没有上线，无法进行此操作"),
    PROJECT_EXPIRED(2029, "项目已结束，无法进行相关操作"),

    PERIOD_EXIST(2031, "时段已经存在"),
    PERIOD_NOT_FOUND(2031, "时段未找到"),
    PERIOD_ARRANGED(2032, "时段已经排过座位了"),
    PERIOD_FINISHED(2033, "时段结束"),
    PERIOD_ADMISSION_CARD_EXISTS(2034, "已经有科目生成了准考证，无法进行此操作"),
    PERIOD_FAIL(2036, "项目状态异常或已导入考生，无法进行此操作"),
    PERIOD_READY(2037, "项目已经就绪，无法进行此操作"),
    PERIOD_NOT_ARRANGED(2038, "时段没有编排过座位"),
    PERIOD_NOT_READY(2039, "项目未就绪，无法进行此操作"),
    PERIOD_CANCEL_ARRANGE_ERR(2040, "无法取消此时段的编排，考生已经下载了准考证"),
    PERIOD_CLONE_ARRANGE_ERR(2041, "无法进行参考编排，部分房间已经到达限制"),

    CANDIDATE_NOT_FOUND(2051, "考生不存在"),
    CANDIDATE_BEYOND_SEAT(2052, "考生数量超过了机位数量"),
    CANDIDATE_IN_SEAT(2053, "选择了已经编排的考生"),
    CANDIDATE_ID_CARD_ERR(2054, "考生身份证号不合规"),
    CANDIDATE_NOT_IN_SEAT(2055, "有考生未分配考场"),
    CANDIDATE_IN_SAME_ROOM(2056, "换房间不能选择相同房间"),
    CANDIDATE_ID_CARD_REPEAT(2057, "身份证号重复"),
    CANDIDATE_COUNT_NOT_EQUAL_ADMISSION_CARD(2058, "考生数量和准考证数量不一致！"),
    CANDIDATE_SITE_NOT_IN_SAME_CITY(2059, "考生和考点不在同一个城市"),
    CANDIDATE_EXIST(2060, "项目中存在考生"),

    PAPER_NOT_FOUND(2061, "试卷未找到"),
    PAPER_PASSWORD_NOT_ON_TIME(2062, "试卷密码获取时间异常，开考前60分钟获取"),
    PAPER_GEN_ERR(2063, "试卷生成出现异常"),
    PAPER_GEN_EMPTY(2064, "科目或者小卷异常，无法生成试卷"),
    PAPER_NOT_ON_TIME(2065, "试卷获取时间异常，开考前4小时获取"),
    PAPER_GENERATED(2066, "试卷已经生成"),
    PAPER_NOT_GENERATED(2067, "试卷没有生成"),

    QUESTION_NOT_FOUND(2071,"题目未找到"),
    QUESTION_NOT_CORRECT(2072,"题目不合规，提示：复合题已经加入到试卷中后无法编辑"),

    SUBJECT_NOT_FOUND(2082,"科目未找到"),
    SUBJECT_UNABLE_IMPORT(2083,"科目未加入到项目中，无法导入考生"),
    SUBJECT_PERIOD_EXISTS(2084,"科目已经加入到项目中，无法重复添加"),
    SUBJECT_DURATION_FAIL(2085,"科目时长异常，无法加入时段"),
    SUBJECT_QUESTION_REPEAT(2086,"科目中出现重复试题"),
    SUBJECT_CONTAINS_CANDIDATE(2087,"已经存在考生"),
    SUBJECT_EMPTY(2088,"项目中无科目"),

    PERIOD_ROOM_NOT_ENOUGH(2091,"时段数据不完整"),
    PERIOD_ROOM_EXIST(2092,"时段房间数据已经存在"),
    PERIOD_ROOM_NOT_FOUND(2093,"时段房间未找到"),
    PERIOD_ROOM_STATE_NOT_ANSWER_UPLOADED(2094,"时段房间状态是已经上传答案才能更新日志"),
    PERIOD_ROOM_INDEX_EXIST(2095,"时段房间号码已经存在"),
    PERIOD_ROOM_EXPIRED(2096,"找不到时段房间数据，请确认数据是否已经过期"),


    EMAIL_NOT_EXIST(2101,"邮件不存在"),

    PART_NOT_FOUND(2111,"小卷未找到"),


    ADMISSION_CARD_LOGIN_FAILED(2121, "未查询到相关准考证，请检查姓名和身份证号码！"),
    ADMISSION_CARD_EXIST(2122, "准考证已经存在，身份证号码 - "),

    TRANSFER_NOT_SAME_ROOM(2131, "房间不一致"),
    TRANSFER_ONLY_ONCE(2132, "考生只能只转场一次"),

    PROCTOR_NOT_EXIST(2141, "督考官未找到"),

    TENCENT_EID_TOKEN_GET_FAILED(2202, "腾讯E证通令牌获取失败"),
    TENCENT_EID_RESULT_GET_FAILED(2203, "腾讯E证通结果获取失败"),
    MONITOR_ROOM_NOT_FOUND(2301,"房间监控信息未找到"),
    MONITOR_ROOM_EXIST(2302,"房间监控信息已存在"),
    MONITOR_TEST_NOT_FOUND(2303,"房间测试数据未找到"),
    MONITOR_TEST_EXIST(2304,"房间测试信息已存在"),


    CONFIRM_EXIST(2401, "该考点已经添加过，请不要重复添加"),
    CONFIRM_NOT_FOUND(2402, "考点确认未找到"),
    CONFIRM_NOT_ENOUGH(2403, "考点容量不足"),
    CONFIRMED(2404, "考点已经确认了"),
    CONFIRM_ROOM_SIZE_LESS_AVG(2405, "考点中有考场座位数量小于平均预留，无法执行该操作"),
    ;

    public static final BaseEnum AUDIO_EXECUTE_FAILED = null;

    ExceptionEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
