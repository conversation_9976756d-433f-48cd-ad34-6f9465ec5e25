package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SiteUpdateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考点Id")
    private String siteId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考点名称")
    private String name;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "省份")
    private String province;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "城市")
    private String city;

    @Schema(description = "街道")
    private String district;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "地址")
    private String address;

    @Schema(description = "交通方式")
    private String traffic;

    @Schema(description = "考点负责人")
    private String manager; // 考点负责人

    @Schema(description = "考点负责人手机号")
    private String managerMobile; // 考点负责人手机号

    @Schema(description = "考点技术老师")
    private String itContract;

    @Schema(description = "考点技术老师手机号")
    private String itMobile;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "邮寄地址")
    private String expressAddress;

    @Schema(description = "容量")
    private Integer capacity;

    @Schema(description = "房间数量")
    private Integer roomCount;

    @Schema(description = "电子屏幕")
    private Boolean screen;
}
