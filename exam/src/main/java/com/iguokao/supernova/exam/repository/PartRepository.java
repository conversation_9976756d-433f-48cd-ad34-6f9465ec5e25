package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Part;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface PartRepository extends MongoRepository<Part, ObjectId> {

    Optional<Part> findBy_idAndSubjectId(ObjectId _id, ObjectId subjectId);
    List<Part> findBySubjectId(ObjectId subjectId);
    List<Part> findBy_idInOrderBySortAsc(List<ObjectId> list);
    void deleteBySubjectId(ObjectId subjectId);

    List<Part> findByPeriodId(ObjectId periodId);
}
