package com.iguokao.supernova.exam.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.remote.TaskRemote;
import com.iguokao.supernova.common.request.EmailTaskAddRequest;
import com.iguokao.supernova.common.request.PaperTaskAddRequest;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.util.TeaUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.NotificationTypeEnum;
import com.iguokao.supernova.exam.request.*;
import com.iguokao.supernova.exam.response.*;
import com.iguokao.supernova.exam.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/v1/subject")
@RequiredArgsConstructor
public class SubjectController {

    private final PeriodService periodService;
    private final PartService partService;
    private final QuestionService questionService;
    private final PaperService paperService;
    private final CacheService cacheService;
    private final ProjectService projectService;
    private final NotificationBatchService notificationBatchService;
    private final CandidateService candidateService;
    private final TaskRemote taskRemote;

    @Value(value = "${app.service.task-callback}")
    private String notificationPrefix;

    @PostMapping("/add")
    @Operation(summary = "添加科目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@Validated @RequestBody SubjectAddRequest request) {
        Subject subject = new Subject();
        subject.set_id(new ObjectId());
        subject.setName(request.getName());
        subject.setDuration(request.getDuration());
        subject.setNote(request.getNote());
        subject.setCalculatorEnabled(request.getCalculatorEnabled());
        subject.setShowScore(request.getShowScore());
        subject.setSubmitSecond(request.getSubmitSecond());
        this.periodService.addSubject(request.getPeriodId(),subject);
        return RestResponse.success(subject.get_id().toString());
    }

    @PostMapping("/update")
    @Operation(summary = "更新科目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@Validated @RequestBody SubjectUpdateRequest request) {
        Subject subject = new Subject();
        subject.set_id(new ObjectId(request.getSubjectId()));
        subject.setName(request.getName());
        subject.setDuration(request.getDuration());
        subject.setNote(request.getNote());
        subject.setCalculatorEnabled(request.getCalculatorEnabled());
        subject.setShowScore(request.getShowScore());
        subject.setSubmitSecond(request.getSubmitSecond());
        this.periodService.updateSubject(request.getPeriodId(), subject);
        return RestResponse.success();
    }

    @PostMapping("/info")
    @Operation(summary = "科目详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<SubjectResponse> find(@Validated @RequestBody SubjectInfoRequest request) {
        Subject subject = this.periodService.getSubject(request.getSubjectId(),request.getPeriodId());
        List<Part> partList = this.partService.getBySubjectId(subject.get_id().toString());
        SubjectResponse subjectResponse = SubjectResponse.of(subject,partList);
        return RestResponse.success(subjectResponse);
    }

    @PostMapping("/remove")
    @Operation(summary = "删除科目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@Validated @RequestBody SubjectInfoRequest request) {
        this.periodService.removeSubject(request.getSubjectId(), request.getPeriodId());
        return RestResponse.success();
    }

    @PostMapping("/sort")
    @Operation(summary = "科目中小卷排序")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> sortPart(@Validated @RequestBody PartSortRequest request) {
        this.partService.sort(request.getPeriodId(), request.getSubjectId(),request.getPartList());
        return RestResponse.success();
    }

    @PostMapping("/generate/paper")
    @Operation(summary = "科目-生成试卷")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> genPaper(@Validated @RequestBody PaperGenRequest request) {
        this.paperService.generatePaper(request.getPeriodId(), request.getProjectId(), request.getSubjectId());
        return RestResponse.success();
    }

    @PostMapping("/clear/paper")
    @Operation(summary = "科目-清除试卷")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> clearPaper(@Validated @RequestBody PaperGenRequest request) {
        this.paperService.clearPaper(request.getPeriodId(), request.getProjectId(), request.getSubjectId());
        return RestResponse.success();
    }

    @PostMapping("/preview/paper")
    @Operation(summary = "科目-预览试卷")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<PaperResponse> previewPaper(@RequestBody PaperGenRequest request) {
        Period period = this.cacheService.getPeriod(request.getPeriodId());
        Paper paper = this.paperService.previewPaper(request.getPeriodId(), request.getProjectId(), request.getSubjectId());
        List<Part> partList = this.partService.getBySubjectId(request.getSubjectId());
        List<Question> questionList = this.questionService.getByPaper(paper);
        PaperGroup paperGroup = new PaperGroup();
        paperGroup.setPaper(paper);
        paperGroup.setQuestionList(questionList);
        paperGroup.setPartList(partList);
        List<PaperGroup> paperGroupList = new ArrayList<>();
        paperGroupList.add(paperGroup);
        PaperResponse paperResponse = PaperResponse.of(period,paperGroupList);
        return RestResponse.success(paperResponse);
    }

    @PostMapping("/download/paper")
    @Operation(summary = "科目-下载pdf试卷")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<DownloadPaperResponse> downloadPaper(@RequestBody PaperDownloadRequest request) {

        String path = "pdf/paper" + request.getPeriodId() + "/" + request.getSubjectId() + "_" +  new Date().getTime() + ".pdf";
        NotificationBatch notificationBatch = this.notificationBatchService.add(request.getPeriodId(),request.getSubjectId(),NotificationTypeEnum.PDF.getCode(),1);

        PaperTaskAddRequest task = new PaperTaskAddRequest();
        task.setUrl(request.getUrl());
        task.setCallbackUrl(notificationPrefix + "/api/v1/remote/paper/pdf/result");
        task.setPath(path);
        task.setJobId(request.getSubjectId());
        task.setCandidateId("");
        taskRemote.addPdfTask(task);

        return RestResponse.success(DownloadPaperResponse.of(path,notificationBatch.get_id().toString()));
    }

    @GetMapping("/gen/paper/result/{batchId}")
    @Operation(summary = "查询要下载的pdf试卷是否完成")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<DownloadPaperResultResponse> pdfPaperResult(@PathVariable String batchId) {
        NotificationBatch notificationBatch = this.notificationBatchService.findById(batchId);
        DownloadPaperResultResponse paperResultResponse = new DownloadPaperResultResponse();
        paperResultResponse.setCompleted(notificationBatch.getCompleted());
        paperResultResponse.setSuccess(notificationBatch.getSuccess());
        return RestResponse.success(paperResultResponse);
    }

    @RequestMapping(value = "/paper/import/{periodId}/{subjectId}", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "试卷导入")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExcelErrResponse.class))))
    public RestResponse<String>  importExcel(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId,
                                                             @PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String subjectId,
                                                             @RequestPart(value = "file") MultipartFile file) throws IOException {
        String jsonString = TeaUtil.decryptToString(file.getBytes(), "iguokao.123");
        ObjectMapper mapper = new ObjectMapper();
        PaperSubjectResponse paper = mapper.readValue(jsonString, PaperSubjectResponse.class);
        Period period = this.periodService.getById(periodId);
        Project project = this.projectService.getById(period.getProjectId().toString());
        Long candidateCount = this.candidateService.countBySubjectId(periodId,subjectId);
        for(Subject subject : period.getSubjectList()){
            if(subject.get_id().toString().equals(subjectId)){
                subject.setNote(paper.getNote());
                subject.setScore(paper.getScore());
                subject.setQuestionCount(paper.getQuestionCount());
                subject.setCandidateCount(candidateCount.intValue());
                subject.setCalculatorEnabled(paper.getCalculatorEnabled());
                subject.setShowScore(paper.getShowScore());
            }
        }
        this.periodService.update(period,true);

        int partSort =1;
        for(PartResponse partResponse: paper.getPartList()){
            Part part = Part.of(partResponse,periodId,subjectId);
            part.setSort(partSort);
            for(QuestionResponse questionResponse : partResponse.getQuestionList()){
                Question question = Question.of(questionResponse,project.getCompanyId().toString());
                String questionId = this.questionService.addQuestion(question);
                question.set_id(new ObjectId(questionId));

                PartQuestion partQuestion = new PartQuestion();
                partQuestion.setQuestionId(question.get_id());
                partQuestion.setScore(question.getQuestionScore());
                partQuestion.setGroupScore(questionResponse.getGroupScore());
                part.getQuestionList().add(partQuestion);
            }
            partSort++;
            this.partService.add(part);
        }

        return RestResponse.success();
    }
}
