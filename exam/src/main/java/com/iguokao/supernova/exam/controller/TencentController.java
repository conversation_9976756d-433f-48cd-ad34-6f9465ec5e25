package com.iguokao.supernova.exam.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.request.TencentRtcRecordCallbackRequest;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.exam.request.EidResultRequest;
import com.iguokao.supernova.exam.request.EidTokenRequest;
import com.iguokao.supernova.exam.service.PeriodRoomService;
import com.iguokao.supernova.exam.service.TencentService;
import com.tencentcloudapi.faceid.v20180301.models.GetEidResultResponse;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;

@RestController
@RequestMapping("/api/v1/rtc")
@RequiredArgsConstructor
public class TencentController {

    private final PeriodRoomService periodRoomService;
    private final TencentService tencentService;

    @PostMapping("/callback/record")
    @Operation(summary = "腾讯 RTC回调 录制 机考监控")
    public RestResponse<String> recordCallbackOffline(@RequestBody TencentRtcRecordCallbackRequest request) throws JsonProcessingException, UnsupportedEncodingException {
        System.out.println(new ObjectMapper().writeValueAsString(request));

        if(request.getEventInfo() != null &&
                request.getEventInfo().getPayload() != null &&
                request.getEventInfo().getPayload().getTencentVod() != null){
            String[] data = request
                    .getEventInfo()
                    .getPayload()
                    .getTencentVod()
                    .getUserId()
                    .split("_");
            String periodId = data[0];
            String roomId = data[1];

            this.periodRoomService.setVideoUrl(periodId,
                    roomId,
                    request.getEventInfo().getTaskId(),
                    data[2],
                    request.getEventInfo().getPayload().getTencentVod().getVideoUrl());
        }
        return RestResponse.success();
    }
}

