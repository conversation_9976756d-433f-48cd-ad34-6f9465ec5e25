package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import com.iguokao.supernova.exam.document.*;

import java.util.List;

public interface RedisService {
    // 图片验证码
    ImageCode getImageCode(String key);
    // 保存图片验证码
    void setImageCode(ImageCode imageCode);
    // 删除图片验证码
    void deleteImageCode(String key);

    Integer getNextRoomNumber();

    Site getSite(String siteId);
    void setSite(Site site);

    Room getRoom(String roomId);
    List<Room> getRoom(List<String> list);

    void setRoom(Room room);
    OperatorRemoteResponse getOperator(String operatorId);
    void setOperator(OperatorRemoteResponse operator);

    List<PeriodRoom> getPeriodRoomList(String roomId);
    void setPeriodRoomList(List<PeriodRoom> list);
    void deletePeriodRoom(String roomId);

//    List<Candidate> getRoomCandidateList(String periodId, String roomId);
//    void setRoomCandidateList(List<Candidate> list);
//    void deleteRoomCandidateList(String periodId, String roomId);
//    void deleteRoomCandidateList(String periodId, List<String> roomIdList);

    List<MonitorSignature> getMonitorSignatureList(String periodId, String roomId);
    void setMonitorSignature(MonitorSignature monitorSignature);
    void deleteMonitorSignature(MonitorSignature monitorSignature);

//    List<MonitorRoom> getMonitorRoomList(String periodId);
//    MonitorRoom getMonitorRoom(String periodId, String roomId);
//    void setMonitorRoom(MonitorRoom monitorRoom);
//    void deleteMonitorRoom(String periodId);

//    List<MonitorTest> getRoomTest(String projectId);
//    MonitorTest getRoomTest(String projectId, String roomId);
//    void setRoomTest(MonitorTest test);

    List<Paper> getPaperList(String periodId);
    void setPaperList(String periodId, List<Paper> list);

    Project getProject(String projectId);
    List<Project> getProject(List<String> list);
    void setProject(Project project);
    void deleteProject(String projectId);

    Period getPeriod(String periodId);
    List<Period> getPeriod(List<String> list);
    void setPeriod(Period period);
    void deletePeriod(String periodId);

    List<RoomProjectTest> getRoomProjectTest(String roomId);
    void setRoomProjectTest(List<RoomProjectTest> list);
    void deleteRoomProjectTest(String roomId);

    List<AdmissionCard> getAdmissionCardByIdCardNum(String projectId, String idCardNum);
    void setAdmissionCard(List<AdmissionCard> list);
    void deleteAdmissionCardByIdCardNum(String projectId, String idCardNum);

    void setClientVersion(String ver);
    String getClientVersion();

    CompanyRemoteResponse getCompany(String companyId);
    void setCompany(CompanyRemoteResponse company);

    String getToken(String key);
    void setToken(String key, String token);

    String getLock(String key);
    void setLock(String key);
    void deleteLock(String key);
}
