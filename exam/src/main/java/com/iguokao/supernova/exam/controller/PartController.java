package com.iguokao.supernova.exam.controller;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.exam.document.Part;
import com.iguokao.supernova.exam.document.PartQuestion;
import com.iguokao.supernova.exam.document.Question;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.request.*;
import com.iguokao.supernova.exam.response.PartResponse;
import com.iguokao.supernova.exam.response.QuestionResponse;
import com.iguokao.supernova.exam.service.PartService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/api/v1/part")
@RequiredArgsConstructor
public class PartController {

    private final PartService partService;

    @PostMapping("/add")
    @Operation(summary = "添加小卷")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@Validated @RequestBody PartAddRequest request) {
        Part part = new Part();
        BeanUtils.copyProperties(request, part);
        part.setPeriodId(new ObjectId(request.getPeriodId()));
        part.setSubjectId(new ObjectId(request.getSubjectId()));
        String partId = partService.add(part);
        return RestResponse.success(partId);
    }

    @PostMapping("/update")
    @Operation(summary = "更新小卷")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody PartUpdateRequest request) {
        Part part = this.partService.getById(request.getPartId());
        BeanUtils.copyProperties(request, part);
        partService.update(part);
        return RestResponse.success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除小卷")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> delete(@RequestBody PartInfoRequest request) {
        this.partService.deletePart(request.getPartId(),request.getPeriodId(),request.getSubjectId());
        return RestResponse.success();
    }

    @PostMapping("/info")
    @Operation(summary = "小卷详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<PartResponse> find(@RequestBody PartInfoRequest request) {
        Part part = this.partService.getById(request.getPartId());
        return RestResponse.success(PartResponse.of(part,null,null));
    }

    @GetMapping("/question/{partId}")
    @Operation(summary = "已添加到此小卷中的所有试题")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = QuestionResponse.class))))
    public RestResponse<List<QuestionResponse>> questionList(@PathVariable String partId) {
        List<Question> questionList = this.partService.questionList(partId);
        Part part = this.partService.getById(partId);
        return RestResponse.success(PartResponse.of(part, questionList,null)
                .getQuestionList());
    }

    @PostMapping("/fill")
    @Operation(summary = "小卷试题的添加,删除，排序,设置分值")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> fillQuestion(@RequestBody PartFillRequest request) {
        List<PartQuestion> partQuestionList = new ArrayList<>();
        Set<String> questionIds = new HashSet<>();
        for(PartQuestionRequest partQuestionRequest : request.getQuestionList()){
            PartQuestion partQuestion = new PartQuestion();
            partQuestion.setQuestionId(new ObjectId(partQuestionRequest.getQuestionId()));
            partQuestion.setScore(partQuestionRequest.getScore());
            partQuestionList.add(partQuestion);
            questionIds.add(partQuestionRequest.getQuestionId());
            if(!partQuestionRequest.getGroupScore().isEmpty()){
                partQuestion.setGroupScore(partQuestionRequest.getGroupScore());
            }
        }
        //有重复试题，
        if(questionIds.size() != request.getQuestionList().size()){
            throw new ServiceException(ExceptionEnum.QUESTION_NOT_CORRECT);
        }
        this.partService.questionFill(partQuestionList, request.getPartId(), questionIds);
        return RestResponse.success();
    }
}
