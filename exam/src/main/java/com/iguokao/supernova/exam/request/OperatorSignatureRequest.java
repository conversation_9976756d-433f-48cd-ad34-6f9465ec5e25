package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OperatorSignatureRequest {
    @Schema(description = "时段")
    private String periodId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "姓名")
    private String fullName;

    @Schema(description = "手机号")
    private String mobile;
}
