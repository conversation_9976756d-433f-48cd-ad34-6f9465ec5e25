package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.exam.document.Candidate;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

@Getter
@Setter
public class CandidateArrangedItemListener {
    private String candidateId;
    private String siteId;
    private String roomId;
    private String periodId;
    private String fullName;
    private String city;
    private String siteName;
    private String roomName;
    private String seatNum;
    private Integer round;
    private String idCardNum;
    private String subjectName;
    private String email;
    private String mobile;
    private String custom1;
    private String custom2;
    private String custom3;

    public static CandidateArrangedItemListener of(Candidate obj, String siteName, String roomName){
        if(obj == null){
            return null;
        }
        CandidateArrangedItemListener res = new CandidateArrangedItemListener();
        BeanUtils.copyProperties(obj, res);
        res.setSiteName(siteName);
        res.setRoomName(roomName);
        res.setCandidateId(obj.get_id().toString());
        if(obj.getSiteId() != null){
            res.setSiteId(obj.getSiteId().toString());
        }
        if(obj.getRoomId() != null){
            res.setRoomId(obj.getRoomId().toString());
        }
        if(obj.getPeriodId() != null){
            res.setPeriodId(obj.getPeriodId().toString());
        }
        return res;
    }
}
