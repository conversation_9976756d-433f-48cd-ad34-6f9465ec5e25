package com.iguokao.supernova.exam.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum NotificationTypeEnum implements BaseEnum {

    EMAIL(1, "邮件"),
    SMS(2, "短信"),
    PDF(3, "pdf"),

            ;

    NotificationTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
