package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.exam.document.CandidateManualArrangeItem;
import com.iguokao.supernova.exam.document.Period;
import com.iguokao.supernova.exam.document.Project;
import com.iguokao.supernova.exam.document.Subject;
import com.iguokao.supernova.exam.excel.CandidateArrangedItem;
import org.bson.types.ObjectId;

import java.util.List;

public interface PeriodService {
    void add(Period period);
    void update(Period period, Boolean isOffline);
    Period getById(String periodId);
    List<Period> getByProjectIdList(List<ObjectId> idList);
    List<Period> getByProjectList(List<Project> first);
    void autoArrange(String periodId, boolean random);
    void siteArrange(String periodId, boolean random, List<String> siteIdList);
    List<CandidateArrangedItem> arrangeData(String periodId);
    int arrangeCancel(String periodId);
    List<String> getPeriodIds(String projectId);
    List<Period> getByProjectId(String projectId);
    void remove(String projectId, String periodId);

    boolean ready(String periodId);

    void readyV2(Period period);

    void addSubject(String periodId, Subject subject);

    void updateSubject(String periodId, Subject subject);

    void removeSubject(String subjectId, String periodId);

    Subject getSubject(String subjectId, String periodId);

    Period getBySubjectId(String periodId, String subjectId);

    Period getTestPeriod(Project project, String testPaperId);

    void done(String periodId);

    List<Period> reviewPeriodList(int day);

    void arrangeManual(String periodId, List<CandidateManualArrangeItem> list);

    List<Period> recent(String companyId);
}
