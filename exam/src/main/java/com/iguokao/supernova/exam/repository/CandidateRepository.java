package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Candidate;
import com.iguokao.supernova.exam.document.Period;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.CountQuery;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface CandidateRepository extends MongoRepository<Candidate, ObjectId> {
    int countByProjectId(ObjectId projectId);
    int countByProjectIdAndPeriodId(ObjectId projectId, ObjectId periodId);

    int countByPeriodId(ObjectId periodId);
    int countByPeriodIdAndStateGreaterThanEqual(ObjectId period, Integer state);
    int countByPeriodIdAndRoomId(ObjectId period, ObjectId roomId);
    int countByPeriodIdAndSubjectIdAndIdCardNum(ObjectId periodId ,ObjectId subjectId, String idCard);
    int countBySubjectId (ObjectId subjectId);

    @CountQuery("{ periodId:?0, roomId: { $ne: null } }")
    int countArranged(ObjectId periodId);

    @CountQuery("{ periodId:?0, roomId: null }")
    int countNotArranged(ObjectId periodId);

    @CountQuery("{ subjectId:?0, avatar: { $ne: null } }")
    int countAvatar(ObjectId subjectId);

    Optional<Candidate> findFirstByProjectId(ObjectId projectId, Sort sort);
    Optional<Candidate> findByPeriodIdAndIdCardNum(ObjectId periodId, String num);

    List<Candidate> findByPeriodIdAndTransRoomId(ObjectId periodId, ObjectId transRoomId);
    List<Candidate> findByPeriodId(ObjectId periodId);
    List<Candidate> findByProjectIdAndPeriodId(ObjectId projectId, ObjectId periodId);
    List<Candidate> findBySubjectIdAndPeriodId(ObjectId subjectId, ObjectId periodId);
    List<Candidate> findByProjectId(ObjectId projectId);
    List<Candidate> findBySubjectId(ObjectId subjectId);
    List<Candidate> findByProjectIdAndIdCardNumIn(ObjectId projectId, List<String> idCardNumList);

    List<Candidate> findByPeriodIdAndRoomId(ObjectId periodId, ObjectId roomId);
    List<Candidate> findByProjectIdAndPeriodIdAndSiteId(ObjectId projectId, ObjectId periodId, ObjectId siteId);

    List<Candidate> findBy_idIn(List<ObjectId> list);

    @Query("{ projectId:?0, periodId:?1, roomId: null }")
    List<Candidate> findNotArranged(ObjectId projectId ,ObjectId periodId);

    int countBySubjectIdAndIdCardNum(ObjectId subjectId, String idCardNum);
}
