package com.iguokao.supernova.exam.service;


import com.iguokao.supernova.exam.document.NotificationBatch;

import java.util.List;

public interface NotificationBatchService {

    void pdfResult(String subjectId, Boolean result);

    List<NotificationBatch> latestList(String periodId, String subjectId ,Integer count);

    NotificationBatch add(String periodId, String subjectId , Integer type, Integer count);

    NotificationBatch findById(String batchId);
}
