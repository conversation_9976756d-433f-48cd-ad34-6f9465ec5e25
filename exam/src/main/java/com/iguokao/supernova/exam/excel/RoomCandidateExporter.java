package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.exam.document.Candidate;

import java.util.ArrayList;
import java.util.List;

public class RoomCandidateExporter {

    public static List<List<Object>> data(List<String> headerLines, List<Candidate> list, boolean includeSignature) {
        List<List<Object>> res = new ArrayList<>();
        for(String s : headerLines){
            List<Object> line = new ArrayList<>();
            line.add(s);
            line.add("");
            line.add("");
            line.add("");
            res.add(line);
        }

        List<Object> header = new ArrayList<>();
        header.add("座位号");
        header.add("考生姓名");
        header.add("准考证号");
        header.add("身份证号");
        res.add(header);

        for(Candidate candidate : list){
            List<Object> line = new ArrayList<>();
            line.add(candidate.getSeatNum());
            line.add(candidate.getFullName());
            line.add(candidate.getNum());
            line.add(StringUtil.idCardNumSecure(candidate.getIdCardNum()));
            res.add(line);
        }
        return res;
    }
}
