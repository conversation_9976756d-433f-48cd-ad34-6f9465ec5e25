package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class PaperSubjectResponse {

    @Schema(description = "id")
    private String subjectId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = " 时长")
    private Integer duration;

    @Schema(description = "报名人数")
    private Integer candidateCount;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "总分")
    private Double score;

    @Schema(description = "总试题数量")
    private Integer questionCount;

    @Schema(description = "计算器是否开启")
    private Boolean calculatorEnabled;

    @Schema(description = "考生可以查看题分")
    private Boolean showScore;

    @Schema(description = "提前交卷时间")
    private Integer submitSecond = 0;

    @Schema(description = "小卷列表")
    private List<PartResponse> partList = new ArrayList<>();

    public static PaperSubjectResponse of(Period period, PaperGroup paperGroup){
        PaperSubjectResponse res = new PaperSubjectResponse();
        for(Subject subject : period.getSubjectList()){
            if(subject.get_id().toString().equals(paperGroup.getPaper().getSubjectId().toString())){
                BeanUtils.copyProperties(subject, res);
                res.setSubjectId(subject.get_id().toString());
                break;
            }
        }
        res.setPartList(PartResponse.of(paperGroup.getPartList(),paperGroup.getQuestionList(),paperGroup.getPaper()));
        return res;
    }

}
