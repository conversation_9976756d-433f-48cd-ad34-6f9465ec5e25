package com.iguokao.supernova.exam.excel;

import java.util.ArrayList;
import java.util.List;

public class RoomInfoItemExporter {

    public static List<List<String>> head() {

        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("省份");

        List<String> head1 = new ArrayList<>();
        head1.add("城市");

        List<String> head2 = new ArrayList<>();
        head2.add("考点名称");

        List<String> head3 = new ArrayList<>();
        head3.add("房间名称");

        List<String> head4 = new ArrayList<>();
        head4.add("房间地址");

        List<String> head5 = new ArrayList<>();
        head5.add("考场号");

        List<String> head6 = new ArrayList<>();
        head6.add("机位总数");

        List<String> head7 = new ArrayList<>();
        head7.add("可用机位");

        List<String> head8 = new ArrayList<>();
        head8.add("编排考生");

        List<String> head9 = new ArrayList<>();
        head9.add("电脑摄像头");

        List<String> head10 = new ArrayList<>();
        head10.add("云机房");

        List<String> head11 = new ArrayList<>();
        head11.add("挡板");

        List<String> head12 = new ArrayList<>();
        head12.add("备注");


        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        list.add(head6);
        list.add(head7);
        list.add(head8);
        list.add(head9);
        list.add(head10);
        list.add(head11);
        list.add(head12);
        return list;
    }

    public static List<List<Object>> data(List<RoomInfoItem> list) {
        List<List<Object>> res = new ArrayList<>();
        for(RoomInfoItem item : list){
            List<Object> line = new ArrayList<>();
            line.add(item.getProvince());
            line.add(item.getCity());
            line.add(item.getSiteName());
            line.add(item.getName());
            line.add(item.getAddress());
            line.add(item.getRoomIndexName());

            line.add(item.getCapacity());
            line.add(item.getAvailable());
            line.add(item.getCandidateCount());

            line.add(item.getPcCamera());
            line.add(item.getCloudSystem());
            line.add(item.getBlocked());
            line.add(item.getNote());
            res.add(line);
        }
        return res;
    }
}
