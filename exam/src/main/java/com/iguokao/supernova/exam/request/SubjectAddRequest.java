package com.iguokao.supernova.exam.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class SubjectAddRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "公司 Id")
    @Length(min = 24, max = 24, message = "companyId 错误")
    private String companyId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private String periodId;

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "备注")
    private Integer duration;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "使用计算器")
    private Boolean calculatorEnabled;

    @Schema(description = "考生可以查看题分")
    private Boolean showScore;

    @Schema(description = "允许交卷的秒数 超出 0不限")
    private Integer submitSecond;
}