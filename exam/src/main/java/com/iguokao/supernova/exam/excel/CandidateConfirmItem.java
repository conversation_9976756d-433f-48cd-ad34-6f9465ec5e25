package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.exam.document.Candidate;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class CandidateConfirmItem {

    private String fullName;
    private String idCardNum;
    private String mobile;
    private String city;
    private Long num;
    private Integer confirmState;
    private Integer state;


    public static List<CandidateConfirmItem> of(List<Candidate> list) {
        List<CandidateConfirmItem> res = new ArrayList<>();
        for(Candidate candidate : list){
            CandidateConfirmItem candidateConfirmItem = new CandidateConfirmItem();
            candidateConfirmItem.setFullName(candidate.getFullName());
            candidateConfirmItem.setMobile(candidate.getMobile());
            candidateConfirmItem.setIdCardNum(candidate.getIdCardNum());
            candidateConfirmItem.setConfirmState(candidate.getConfirmState());
            candidateConfirmItem.setCity(candidate.getCity());
            candidateConfirmItem.setNum(candidate.getNum());
            candidateConfirmItem.setState(candidate.getState());
            res.add(candidateConfirmItem);
        }
        return res;
    }

}
