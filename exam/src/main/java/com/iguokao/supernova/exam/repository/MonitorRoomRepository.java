package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.MonitorRoom;
import com.iguokao.supernova.exam.document.Period;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.CountQuery;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface MonitorRoomRepository extends MongoRepository<MonitorRoom, ObjectId> {
    int countByPeriodId(ObjectId periodId);
    int countByPeriodIdAndRoomId(ObjectId periodId, ObjectId roomId);

    @CountQuery("{ periodId:?0, answerUploaded: true }")
    int countByPeriodFinished(ObjectId periodId);

    Optional<MonitorRoom> findByPeriodIdAndRoomId(ObjectId periodId, ObjectId roomId);
    List<MonitorRoom> findByPeriodId(ObjectId periodId);

    void deleteByPeriodId(ObjectId periodId);

}
