package com.iguokao.supernova.exam.controller;

import cn.idev.excel.EasyExcel;
import com.iguokao.supernova.common.constant.RoleConstant;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.util.Md5Util;
import com.iguokao.supernova.exam.document.PeriodRoom;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.document.Site;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.excel.RoomItem;
import com.iguokao.supernova.exam.excel.RoomItemListener;
import com.iguokao.supernova.exam.request.RoomAddRequest;
import com.iguokao.supernova.exam.request.RoomListRequest;
import com.iguokao.supernova.exam.request.RoomUpdateRequest;
import com.iguokao.supernova.exam.response.RoomResponse;
import com.iguokao.supernova.exam.response.SiteResponse;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.PeriodService;
import com.iguokao.supernova.exam.service.RoomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/room")
@RequiredArgsConstructor
public class RoomController {

    private final RoomService roomService;
    private final CacheService cacheService;
    private final JwtService jwtService;

    @PostMapping("/list")
    @Operation(summary = "当前考点所有考场")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoomResponse.class))))
    public RestResponse<List<RoomResponse>> list(@RequestBody RoomListRequest request) {
        List<Room> list = this.roomService.getList(request.getSiteId(), request.getName());
        List<RoomResponse> res = List.of();
        if(jwtService.currentRole().contains(RoleConstant.SITE)){
            res = RoomResponse.of(list, true);
        } else {
            res = RoomResponse.of(list, false);
        }
        return RestResponse.success(res);
    }

    @PostMapping("/add")
    @Operation(summary = "添加考场")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody RoomAddRequest request) {
        if(request.getAvailable() > request.getCapacity()){
            throw new ServiceException(ExceptionEnum.ROOM_AVAILABLE_BEYOND_LIMIT);
        }
        Room room = new Room();
        BeanUtils.copyProperties(request, room);
        room.setSiteId(new ObjectId(request.getSiteId()));
        this.roomService.add(room);
        return RestResponse.success();
    }

    @PostMapping("/update")
    @Operation(summary = "更新考场")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody RoomUpdateRequest request) {
        if(request.getAvailable() > request.getCapacity()){
            throw new ServiceException(ExceptionEnum.ROOM_AVAILABLE_BEYOND_LIMIT);
        }
        Room room = new Room();
        BeanUtils.copyProperties(request, room);
        room.set_id(new ObjectId(request.getRoomId()));
        room.setSiteId(new ObjectId(request.getSiteId()));
        if(this.roomService.usingRoom(request.getRoomId())){
            throw new ServiceException(ExceptionEnum.ROOM_USING);
        }
        this.roomService.update(room);
        this.cacheService.setRoom(room);
        return RestResponse.success();
    }

    @GetMapping("/remove/{roomId}")
    @Operation(summary = "删除考点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String roomId) {
        this.roomService.remove(roomId);
        return RestResponse.success();
    }

    @RequestMapping(value = "/import/{siteId}", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "批量倒入考场")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExcelErrResponse.class))))
    public RestResponse<List<ExcelErrResponse>>  excel(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String siteId,
                                                       @RequestPart(value = "file") MultipartFile file) throws IOException {
        List<ExcelErrResponse> errResponseList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), RoomItem.class, new RoomItemListener(siteId, roomService, errResponseList)).sheet().doRead();
        return RestResponse.success(errResponseList);
    }

    @GetMapping("/info/{roomId}")
    @Operation(summary = "房间详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = RoomResponse.class)))
    public RestResponse<RoomResponse> info(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String roomId) {
        Room room = this.roomService.getById(roomId);
        return RestResponse.success(RoomResponse.of(room,null, false));
    }

    @GetMapping("/token/{key}")
    @Operation(summary = "获取 token")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> token(@PathVariable String key) {
        String token = this.cacheService.getToken(key);
        return RestResponse.success(token);
    }
}
