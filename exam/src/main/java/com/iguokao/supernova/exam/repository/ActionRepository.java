package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Action;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ActionRepository extends MongoRepository<Action, ObjectId> {
    List<Action> findByCandidateId(ObjectId candidateId);
    List<Action> findByPeriodIdAndRoomId(ObjectId periodId, ObjectId roomId);
    List<Action> findByPeriodIdAndType(ObjectId periodId,  int type);

    List<Action> findByProjectIdAndRoomId(ObjectId projectId, ObjectId roomId);
    List<Action> findByRoomIdAndProjectIdInAndTypeIn(ObjectId roomId, List<ObjectId> projectIdList, List<Integer> typeList);
}
