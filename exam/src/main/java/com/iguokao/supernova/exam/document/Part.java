package com.iguokao.supernova.exam.document;

import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.exam.response.PartResponse;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@CompoundIndex(name = "questionId_index", def = "{'questionList.questionId' : 1}")
public class Part extends BaseDocument {
    @Indexed(name = "periodId_index")
    private ObjectId periodId;

    @Indexed(name = "subjectId_index")
    private ObjectId subjectId;
    private String name;  // 名称
    private String note; // 备注
    private Double partScore = 0D; // 子卷得分
    private Integer sort = 0; // 子卷排序号
    private List<PartQuestion> questionList = new ArrayList<>();
    private Boolean questionRandomized; // 试题乱序
    private Boolean optionRandomized; // 选项乱序


    public static Part of(PartResponse partResponse,String periodId, String subjectId){
        Part res = new Part();
        if(null != periodId){
            res.setPeriodId(new ObjectId(periodId));
        }
        if(null != subjectId){
            res.setSubjectId(new ObjectId(subjectId));
        }
        res.setName(partResponse.getName());
        res.setNote(partResponse.getNote());
        res.setPartScore(partResponse.getPartScore());
        res.setQuestionRandomized(partResponse.getQuestionRandomized());
        res.setOptionRandomized(partResponse.getOptionRandomized());
        return res;
    }

}

