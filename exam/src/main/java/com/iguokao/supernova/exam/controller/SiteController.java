package com.iguokao.supernova.exam.controller;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.metadata.style.WriteFont;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ManagementRemote;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.excel.*;
import com.iguokao.supernova.exam.request.*;
import com.iguokao.supernova.exam.response.PageSiteResponse;
import com.iguokao.supernova.exam.response.SiteResponse;
import com.iguokao.supernova.exam.service.PeriodRoomService;
import com.iguokao.supernova.exam.service.RoomService;
import com.iguokao.supernova.exam.service.SiteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/v1/site")
@RequiredArgsConstructor
public class SiteController {

    private final SiteService siteService;
    private final JwtService jwtService;
    private final ManagementRemote managementRemote;
    private final PeriodRoomService periodRoomService;
    private final RoomService roomService;

    @PostMapping("/page")
    @Operation(summary = "所有考点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageSiteResponse.class)))
    public RestResponse<PageResponse<SiteResponse>> page(@RequestBody SitePageRequest request) {
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize());
        Tuple2<List<Site>, Integer> page = this.siteService.getPage(request.getCity(),
                request.getName(),
                pageable);
        List<SiteResponse> res = SiteResponse.of(page.first());
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }

    @PostMapping("/add")
    @Operation(summary = "添加考点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody SiteAddRequest request) {
        Site site = new Site();
        BeanUtils.copyProperties(request, site);
        String siteId = this.siteService.add(site);

        OperatorRemoteResponse operator = new OperatorRemoteResponse();
        operator.setLoginName(request.getManagerMobile());
        operator.setFullName(request.getManager());
        operator.setRelatedId(siteId);
        operator.setMobile(request.getManagerMobile());
        operator.setLoginPassword("i2345678");
        operator.setNote(site.getName());
        RestResponse<String> res = this.managementRemote.addSiteOperator(operator);
        if(res.getCode() != 0){
            this.siteService.remove(siteId);
            throw ServiceException.remote(res);
        }
        this.siteService.updateOperatorId(siteId, res.getData());
        return RestResponse.success();
    }

    @PostMapping("/update")
    @Operation(summary = "更新考点信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody SiteUpdateRequest request) {
        Site site = new Site();
        BeanUtils.copyProperties(request, site);
        site.set_id(new ObjectId(request.getSiteId()));
        this.siteService.update(site);
        return RestResponse.success();
    }

    @PostMapping("/priority")
    @Operation(summary = "更新考点信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> priority(@RequestBody SitePriorityRequest request) {
        this.siteService.setPriority(request.getSiteId(), request.getPriority());
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SYS_ADMIN')")
    @PostMapping("/remove/{siteId}")
    @Operation(summary = "删除考点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String siteId) {
        this.siteService.remove(siteId);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_SITE_ADMIN')")
    @GetMapping("/lock/{siteId}")
    @Operation(summary = "锁定考点 锁定后 考点管理员无法更新房间")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<Boolean> lock(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String siteId) {
        boolean res = this.siteService.lock(siteId);
        return RestResponse.success(res);
    }

    @GetMapping("/info/{siteId}")
    @Operation(summary = "考点详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = SiteResponse.class)))
    public RestResponse<SiteResponse> info(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String siteId) {
        Site site = this.siteService.getById(siteId);
        return RestResponse.success(SiteResponse.of(site));
    }

    @PreAuthorize("hasAuthority('ROLE_SITE')")
    @GetMapping("/info")
    @Operation(summary = "考点 管理员 考点详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = SiteResponse.class)))
    public RestResponse<SiteResponse> info() {
        String siteId =  this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        Site site = this.siteService.getById(siteId);
        return RestResponse.success(SiteResponse.of(site));
    }

    @GetMapping("/site/list/{periodId}")
    @Operation(summary = "考点列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = SiteResponse.class))))
    public RestResponse<List<SiteResponse>> siteList(@PathVariable String periodId) {
        List<Site> list = this.periodRoomService.getSiteListByPeriodId(periodId);
        return RestResponse.success(SiteResponse.of(list));
    }

    @GetMapping("/excel/{periodId}")
    @Operation(summary = "考点excel")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void excelStatistics(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("时段所有考点信息_%s", periodId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<Site> list = this.periodRoomService.getSiteListByPeriodId(periodId);

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        EasyExcel.write(response.getOutputStream())
                .sheet("考点信息")
                .head(PeriodSiteItemExporter.head())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(PeriodSiteItemExporter.data(list));
    }

    @GetMapping("/excel/room/{siteId}")
    @Operation(summary = "考点下各房间的信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void excelRoom(@PathVariable String siteId, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("考点下各房间的登录信息_%s", siteId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<Room> list = this.roomService.getBySiteId(siteId);

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteFont font = new WriteFont();
        font.setFontHeightInPoints((short) 15); // 设置字体大小为15
        contentStyle.setWriteFont(font);

        EasyExcel.write(response.getOutputStream())
                .sheet("房间登录信息")
                .head(SiteRoomLoginItemExporter.head())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(SiteRoomLoginItemExporter.data(list));
    }

    @PreAuthorize("hasAnyAuthority('ROLE_SYS_ADMIN')")
    @GetMapping("/excel/all/{category}")
    @Operation(summary = "所有考点的excel列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void allSite(@PathVariable String category ,HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("所有考点信息_%s", new Date().getTime()), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<Site> list = this.siteService.getAll();

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        EasyExcel.write(response.getOutputStream())
                .sheet("考点信息")
                .head(SiteItemExporter.head())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(SiteItemExporter.data(list,category));
    }

    @PostMapping("/agent/add")
    @Operation(summary = "添加考点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> addAgent(@RequestBody SiteAgentAddRequest request) {
        Agent agent = new Agent();
        BeanUtils.copyProperties(request, agent);
        agent.setLoginCode(StringUtil.genUpperCode(20));
        this.siteService.addAgent(request.getSiteId(), agent);
        return RestResponse.success();
    }

    @PostMapping("/agent/update")
    @Operation(summary = "更新考点信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody SiteAgentUpdateRequest request) {
        Agent agent = new Agent();
        BeanUtils.copyProperties(request, agent);
        this.siteService.updateAgent(request.getSiteId(), agent);
        return RestResponse.success();
    }

    @GetMapping("/agent/remove/{siteId}/{loginCode}")
    @Operation(summary = "删除")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@PathVariable String siteId, @PathVariable String loginCode) {
        this.siteService.removeAgent(siteId, loginCode);
        return RestResponse.success();
    }

}
