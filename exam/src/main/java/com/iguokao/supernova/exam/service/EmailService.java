package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.exam.document.Email;
import org.bson.types.ObjectId;

import java.util.List;

public interface EmailService {

    void add(String companyId, String name, String title, String content);

    void remove(String emailId);

    void edit(String emailId, String name, String title, String content);

    List<Email> list(String companyId);

    void sendGroup(String periodId, String subjectId, String emailTempId, List<String> candidateIdList, String testEmail);

    void sendResult(String candidateId, String batchName, Integer sendType, Integer sendResult, String err);

    void sendAll(String periodId, String subjectId, String emailTempId, Integer confirmState, Integer emailState, Integer state);

    void sendSmsGroup(String periodId, String subjectId, String tempId, List<String> candidateIdList);

    void sendSmsAll(String periodId, String subjectId, String tempId, Integer confirmState, Integer smsState, Integer state);

}
