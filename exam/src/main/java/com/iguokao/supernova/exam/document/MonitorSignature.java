package com.iguokao.supernova.exam.document;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class MonitorSignature implements Serializable {
    private String roomId;
    private String siteId;
    private String periodId;
    private String candidateId;
    private String avatar;
    private String fullName;
    private String note;
    private Date createdAt = new Date();
}
