package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.Agent;
import com.iguokao.supernova.exam.document.Site;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.repository.RoomRepository;
import com.iguokao.supernova.exam.repository.SiteRepository;
import com.iguokao.supernova.exam.service.SiteService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SiteServiceImpl implements SiteService {
    private final SiteRepository siteRepository;
    private final RoomRepository roomRepository;
    private final MongoTemplate mongoTemplate;

    @Override
    public String add(Site site) {
        int count = this.siteRepository.countByName(site.getName());
        if(count > 0){
            throw new ServiceException(ExceptionEnum.SITE_EXIST);
        }
        Site res = this.siteRepository.insert(site);
        return res.get_id().toString();
    }

    @Override
    public void update(Site site) {
        Site exist = this.getById(site.get_id().toString());
        if(exist.getLocked()){
            throw new ServiceException(ExceptionEnum.SITE_LOCKED);
        }
        BeanUtils.copyProperties(site, exist, "operatorId", "locked", "available");
        this.siteRepository.save(exist);
    }

    @Override
    public void setPriority(String siteId, Integer priority) {
        Site exist = this.getById(siteId);
        if(exist.getLocked()){
            throw new ServiceException(ExceptionEnum.SITE_LOCKED);
        }
        exist.setPriority(priority);
        this.siteRepository.save(exist);
    }

    @Override
    public void updateOperatorId(String siteId, String operatorId) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(siteId)));
        Update update = new Update();
        update.set("operatorId", new ObjectId(operatorId));
        mongoTemplate.updateFirst(query, update, Site.class);
    }

    @Override
    public void remove(String siteId) {
        Site site = this.getById(siteId);
        if(site.getLocked()){
            throw new ServiceException(ExceptionEnum.SITE_LOCKED);
        }
        if(this.roomRepository.countBySiteId(new ObjectId(siteId))>0){
            throw new ServiceException(ExceptionEnum.SITE_CONTAINS_ROOM);
        }
        this.siteRepository.deleteById(new ObjectId(siteId));
    }

    @Override
    public Site getById(String siteId) {
        return this.siteRepository.findById(new ObjectId(siteId))
                .orElseThrow(()->new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
    }

    @Override
    public List<Site> getByIdList(List<ObjectId> list) {
        return this.siteRepository.findBy_idIn(list);
    }

    @Override
    public Tuple2<List<Site>, Integer> getPage(String city, String siteName, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC,"priority", "createdAt"));
        if(null != city){
            query = query.addCriteria(Criteria.where("city").is(city));
        }
        if(null != siteName){
            query = query.addCriteria(Criteria.where("name").regex(siteName));
        }
        // 计算总数
        long count = this.mongoTemplate.count(query, Site.class);
        // 分页信息
        query = query.with(pageable);
        List<Site> list = this.mongoTemplate.find(query, Site.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public boolean lock(String siteId) {
        Site site = this.getById(siteId);
        Query query = new Query(Criteria.where("_id").is(new ObjectId(siteId)));
        Update update = new Update();
        update.set("locked", !site.getLocked());
        mongoTemplate.updateFirst(query, update, Site.class);
        return !site.getLocked();
    }

    @Override
    public List<Site> getAll() {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC,"city", "available"));
        return this.mongoTemplate.find(query, Site.class);
    }

    @Override
    public void addAgent(String siteId, Agent agent) {
        Site site = this.getById(siteId);
        site.getAgentList()
                .removeIf(a -> a.getLoginCode().equals(agent.getLoginCode()));
        site.getAgentList().add(agent);
        this.siteRepository.save(site);
    }

    @Override
    public void updateAgent(String siteId, Agent agent) {
        Site site = this.getById(siteId);
        site.getAgentList()
                .removeIf(a -> a.getLoginCode().equals(agent.getLoginCode()));
        site.getAgentList().add(agent);
        this.siteRepository.save(site);
    }

    @Override
    public void removeAgent(String siteId, String loginCode) {
        Site site = this.getById(siteId);
        site.getAgentList()
                .removeIf(a -> a.getLoginCode().equals(loginCode));
        this.siteRepository.save(site);
    }
}
