package com.iguokao.supernova.exam.controller;


import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.util.RequestUtil;
import com.iguokao.supernova.exam.document.Period;
import com.iguokao.supernova.exam.document.PeriodRoom;
import com.iguokao.supernova.exam.document.Project;
import com.iguokao.supernova.exam.document.Site;
import com.iguokao.supernova.exam.request.RoomLoginRequest;
import com.iguokao.supernova.exam.response.AgentLoginResponse;
import com.iguokao.supernova.exam.response.PeriodRoomResponse;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.ProjectService;
import com.iguokao.supernova.exam.service.RoomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/agent")
@RequiredArgsConstructor
public class AgentController {
    private final RoomService roomService;
    private final CacheService cacheService;
    private final JwtService jwtService;
    private final ProjectService projectService;

    @PostMapping("/login")
    @Operation(summary = "房间登录码登录", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = AgentLoginResponse.class)))
    public RestResponse<AgentLoginResponse> login(@RequestBody RoomLoginRequest request, HttpServletRequest req) {
        ImageCode imageCode = new ImageCode();
        BeanUtils.copyProperties(request, imageCode);
        Tuple2<Site, String> loginRes =  this.roomService.agentLogin(imageCode, request.getLoginCode(), request.getVersion(), RequestUtil.getClientIp(req));
        AgentLoginResponse res = new AgentLoginResponse();
        res.setToken(loginRes.second());
        res.setSiteName(loginRes.first().getName());
        loginRes.first().getAgentList()
                .stream()
                .filter(a -> a.getLoginCode().equals(request.getLoginCode()))
                .findFirst().ifPresent(a -> res.setAgentName(a.getName()));
        return RestResponse.success(res);
    }

    @GetMapping("/period/list")
    @Operation(summary = "查看所有项目时段")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = PeriodRoomResponse.class))))
    public RestResponse<List<PeriodRoomResponse>> periodList() {
        String siteId =  this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        Tuple2<List<Project>, List<PeriodRoom>> data = this.projectService.getAgentProjectList(siteId);
        List<String> periodIdList = data.second()
                .stream()
                .map(PeriodRoom::getPeriodId)
                .map(ObjectId::toString)
                .distinct()
                .toList();
        List<Period> periodList = this.cacheService.getPeriod(periodIdList);
        return RestResponse.success(PeriodRoomResponse.of(data.second(), data.first(), periodList));
    }
}
