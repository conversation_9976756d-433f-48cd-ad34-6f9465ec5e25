package com.iguokao.supernova.exam;

import com.iguokao.supernova.common.enums.ActionTypeEnum;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.NotificationSendStateEnum;
import com.iguokao.supernova.exam.repository.ActionRepository;
import com.iguokao.supernova.exam.repository.CandidateRepository;
import com.iguokao.supernova.exam.repository.PeriodRoomRepository;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.CandidateService;
import com.iguokao.supernova.exam.service.PeriodRoomService;
import com.iguokao.supernova.exam.service.PeriodService;
import com.mongodb.client.result.UpdateResult;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.EnableAsync;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.io.IOException;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;


@EnableAsync
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
@RequiredArgsConstructor
public class ExamApplication implements CommandLineRunner {

	public static void main(String[] args) {
		SpringApplication.run(ExamApplication.class, args);
	}

	private final CandidateRepository candidateRepository;
	private final OssService ossService;
	private final PeriodRoomRepository periodRoomRepository;
	private final CacheService cacheService;
	private final ActionRepository actionRepository;

	@Override
	public void run(String... args) throws Exception {



//		List<PeriodRoom> list = this.periodRoomRepository.findByProjectId(new ObjectId("6718d4a9f470ad4ae1569396"));
//		int i = 0;
//		for(PeriodRoom pr : list) {
//			MonitorRoom monitorRoom = this.cacheService.getMonitorRoom(pr.getPeriodId().toString(), pr.getRoomId().toString());
//			System.out.println(i++);
//			monitorRoom.setCandidateFinishedCount(monitorRoom.getCandidateArrivedCount());
//			this.cacheService.setMonitorRoom(monitorRoom);
//
//		}
//		System.out.println("ok");

//		List<PeriodRoom> list = this.periodRoomRepository.findByProjectId(new ObjectId("6718d4a9f470ad4ae1569396"));
//		for(PeriodRoom pr : list) {
//			MonitorRoom monitorRoom = this.cacheService.getMonitorRoom(pr.getPeriodId().toString(), pr.getRoomId().toString());
//			if(monitorRoom != null && !monitorRoom.getPaperDownloaded()){
//				List<Action> actionList = this.actionRepository
//						.findByPeriodIdAndRoomId(new ObjectId(pr.getPeriodId().toString()), new ObjectId(pr.getRoomId().toString()));
//				if(actionList.stream().anyMatch(a -> a.getType().equals(ActionTypeEnum.DATA_PAPER.getCode()))){
//					System.out.println(monitorRoom.getRoomId());
//					monitorRoom.setPaperDownloaded(true);
//					this.cacheService.setMonitorRoom(monitorRoom);
//				}
//			}
//		}
//		System.out.println("ok");

//		//迷药
//	 List<PeriodRoom> list = this.periodRoomRepository.findByProjectId(new ObjectId("6718d4a9f470ad4ae1569396"));
//	 for(PeriodRoom pr : list) {
//		 MonitorRoom monitorRoom = this.cacheService.getMonitorRoom(pr.getPeriodId().toString(), pr.getRoomId().toString());
//		 if(monitorRoom != null && !monitorRoom.getPasswordGet()){
//			 List<Action> actionList = this.actionRepository
//					 .findByPeriodIdAndRoomId(new ObjectId(pr.getPeriodId().toString()), new ObjectId(pr.getRoomId().toString()));
//			 if(actionList.stream().anyMatch(a -> a.getType().equals(ActionTypeEnum.PASSWORD_GET.getCode()))){
//				 System.out.println(monitorRoom.getRoomId());
//				 monitorRoom.setPasswordGet(true);
//				 this.cacheService.setMonitorRoom(monitorRoom);
//			 }
//		 }
//	 }
//	 System.out.println("ok");

//		List<String> list2 = this.ossService.fileList("iguokao-supernova-exam", "avatar/6721b0710bd21c528ffa12db");
//		List<Candidate> canList = this.candidateRepository.findBySubjectId(new ObjectId("6721b0710bd21c528ffa12db"));
//		int i = 0;
//		for(Candidate c: canList){
//			System.out.println(i++);
//			boolean find = false;
//			for(String s : list2){
//				if(s.contains(c.getIdCardNum())){
//					find = true;
//					break;
//				}
//			}
//			if(!find){
//				System.out.println(c.getIdCardNum());
//			}
//		}
//		System.out.println("done");
//		List<PeriodRoom> list = this.periodRoomRepository.findByPeriodIdAndSiteId(new ObjectId("6721b05c081a1f04d15334cb"), new ObjectId("67048e60da300501b8ced1db"));
//		list.forEach(pr -> {
//			long count = candidateRepository.countByPeriodIdAndRoomId(new ObjectId("6721b05c081a1f04d15334cb"), pr.getRoomId());
//			pr.setCandidateCount((int)count);
//			this.periodRoomRepository.save(pr);
//			System.out.println(count);
//		});


//		ObjectId subjectId = new ObjectId("6721b0710bd21c528ffa12db");
//		try {
//			List<String> files = getFilesInDirectory("/Users/<USER>/Downloads/candidate");
//			for(int i=0; i<files.size(); i++){
//				String idnum = files.get(i).replace(".jpg", "").toUpperCase();
//				if(idnum.startsWith("._")){
//					continue;
//				}
//				int count = this.candidateRepository.countBySubjectIdAndIdCardNum(subjectId, idnum);
//				if(count>0){
//					Query query = new Query(Criteria.where("subjectId").is(subjectId))
//							.addCriteria(Criteria.where("idCardNum").is(idnum.toUpperCase()));
//					String path = String.format("avatar/%s/%s.jpg", subjectId, idnum);
//					Update update = new Update()
//							.set("avatar", path);
//					UpdateResult res = mongoTemplate.updateFirst(query, update, Candidate.class);
//					if(res.getModifiedCount() == 0){
//						System.out.printf("失败 %d - %s\n", i, files.get(i));
//					}
//				}else {
//					System.out.println(i + " - " + idnum);
//					Path sourcePath = Paths.get(String.format("/Users/<USER>/Downloads/candidate/%s", files.get(i)));  // 源文件路径
//					Path targetPath = Paths.get(String.format("/Volumes/FREEWOLF/other/%s", files.get(i)));  // 目标文件路径
//					Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
//				}
//			}
//			System.out.println("ok");
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//	}
//
//	public static List<String> getFilesInDirectory(String directoryPath) throws IOException {
//		List<String> fileNames = new ArrayList<>();
//
//		try (DirectoryStream<Path> stream = Files.newDirectoryStream(Paths.get(directoryPath))) {
//			for (Path entry : stream) {
//				if (Files.isRegularFile(entry)) {
//					fileNames.add(entry.getFileName().toString());
//				}
//			}
//		}
//
//		return fileNames;
	}
}
