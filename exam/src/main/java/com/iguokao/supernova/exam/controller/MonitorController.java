package com.iguokao.supernova.exam.controller;

import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ShortUrlRequest;
import com.iguokao.supernova.common.remote.StoneExamRemote;
import com.iguokao.supernova.common.response.OssSignResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.common.service.impl.OssServiceImpl;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.request.SendAllRequest;
import com.iguokao.supernova.exam.response.*;
import com.iguokao.supernova.exam.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/v1/monitor")
@RequiredArgsConstructor
public class MonitorController {
    private final CacheService cacheService;
    private final PeriodRoomService periodRoomService;
    private final CandidateService candidateService;
    private final TencentService tencentService;
    private final RoomService roomService;
    private final StoneExamRemote stoneExamRemote;
    private final OssService ossService;
    private final MonitorService monitorService;

    @Value(value = "${app.short-url-prefix}")
    private String shortUrl;

    @Value(value = "${app.ali.oss-bucket-exam}")
    private String ossBucketExam;

    @GetMapping("/room/{periodId}")
    @Operation(summary = "全部房间监控状态")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = MonitorRoomResponse.class))))
    public RestResponse<List<MonitorRoomResponse>> room(@PathVariable String periodId) {
        List<MonitorRoom> list = this.monitorService.getMonitorRoomList(periodId);
        if(list.isEmpty()){
            throw new ServiceException(ExceptionEnum.PERIOD_ROOM_EXPIRED);
        }
        List<MonitorTest> roomTestList = this.monitorService.getRoomTest(list.get(0).getProjectId().toString());
        return RestResponse.success(MonitorRoomResponse.of(list, roomTestList));
    }

    @GetMapping("/site/list/{periodId}")
    @Operation(summary = "考点列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = SiteResponse.class))))
    public RestResponse<List<SiteResponse>> siteList(@PathVariable String periodId) {
        List<Site> list = this.periodRoomService.getSiteListByPeriodId(periodId);
        return RestResponse.success(SiteResponse.of(list));
    }

    @GetMapping("/room/list/{periodId}/{siteId}")
    @Operation(summary = "考场列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = PeriodRoomResponse.class))))
    public RestResponse<List<PeriodRoomResponse>> roomList(@PathVariable String periodId, @PathVariable String siteId) {
        List<PeriodRoom> list = this.periodRoomService.getRoomListByPeriodId(periodId, siteId);
        List<ObjectId> idList = list
                .stream()
                .map(PeriodRoom::getRoomId)
                .distinct()
                .toList();
        List<PeriodRoomResponse> res = new ArrayList<>();
        if(!idList.isEmpty()){
            List<Room> roomList = this.roomService.getByIdList(idList);

            for(PeriodRoom periodRoom : list){
                PeriodRoomResponse periodRoomResponse = PeriodRoomResponse.of(periodRoom);
                roomList
                        .stream()
                        .filter(room -> room.get_id().toString().equals(periodRoomResponse.getRoomId()))
                        .findFirst()
                        .ifPresent(room -> periodRoomResponse.setRoom(RoomResponse.of(room, periodRoom, false)));
                res.add(periodRoomResponse);
            }
        }
        return RestResponse.success(res);
    }

    @GetMapping("/candidate/list/{periodId}/{roomId}")
    @Operation(summary = "考生列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = CandidateResponse.class))))
    public RestResponse<List<CandidateSignResponse>> candidateList(@PathVariable String periodId, @PathVariable String roomId) {
        List<MonitorSignature> monitorSignatureList = this.cacheService.getMonitorSignatureList(periodId, roomId);
        List<Candidate> list = this.candidateService.getMinitorRoomCandidateList(periodId, roomId);
        List<CandidateSignResponse> res = CandidateSignResponse.of(list, monitorSignatureList);
        if(!res.isEmpty()){
            Project project = this.cacheService.getProject(list.get(0).getProjectId().toString());
            int duration = (int)((project.getEndAt().getTime() - new Date().getTime()) / 1000);
            res.forEach(c -> {
                monitorSignatureList
                        .stream()
                        .filter(ms -> ms.getCandidateId() != null && ms.getCandidateId().equals(c.getCandidateId()))
                        .findFirst()
                        .ifPresent(ms -> {
                            c.setAvatarUrl(this.ossService.generateSignedUrl(ossBucketExam, ms.getAvatar(), duration));
                        });
            });
        }
        return RestResponse.success(res);
    }

    @GetMapping("/monitor/sig/{periodId}/{siteId}")
    @Operation(summary = "获取啦流的Sig")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RtcSigResponse.class))))
    public RestResponse<List<RtcSigResponse>> monitorSig(@PathVariable String periodId, @PathVariable String siteId) {
        List<PeriodRoom> list = this.periodRoomService.getRoomListByPeriodId(periodId, siteId);
        List<RtcSigResponse> res = new ArrayList<>();
        int userId = this.cacheService.getNextRoomNumber();
        for(PeriodRoom periodRoom : list){
            RtcSig rtcSig = this.tencentService.getRtcSig(String.valueOf(userId), periodRoom.getStreamRoomNumber());
            RtcSigResponse item = RtcSigResponse.of(rtcSig);
            item.setRoomId(periodRoom.getRoomId().toString());
            res.add(item);
        }
        return RestResponse.success(res);
    }

    @GetMapping("/time")
    @Operation(summary = "获取服务器时间")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Long.class)))
    public RestResponse<Long> time() {
        return RestResponse.success(new Date().getTime() / 1000);
    }


    @PostMapping("/gen/short/url")
    @Operation(summary = "生成监考地址的短链接")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> genMonitorShort(@RequestBody ShortUrlRequest request) {
        RestResponse<String> res = stoneExamRemote.genShortUrl(request);
        return RestResponse.success(shortUrl + res.getData());
    }
}
