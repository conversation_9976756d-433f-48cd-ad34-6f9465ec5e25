package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.exam.document.RtcSig;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.GetEidResultResponse;
import com.tencentcloudapi.faceid.v20180301.models.GetEidTokenConfig;
import com.tencentcloudapi.faceid.v20180301.models.GetEidTokenRequest;
import com.tencentcloudapi.faceid.v20180301.models.GetEidTokenResponse;

public interface TencentService {
    RtcSig getRtcSig(String userId, Integer roomNumber);
    String getEidToken(String candidateId, String fullName,  String idNum);

    GetEidResultResponse getEidResult(String token);
}
