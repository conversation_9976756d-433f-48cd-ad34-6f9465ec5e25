package com.iguokao.supernova.exam.excel;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.iguokao.supernova.common.document.QuestionOption;
import com.iguokao.supernova.common.enums.QuestionTypeEnum;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.util.ScoreUtil;
import com.iguokao.supernova.exam.document.Question;
import com.iguokao.supernova.exam.service.QuestionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public class QuestionItemListener extends AnalysisEventListener<QuestionItem> {

    private final String companyId;
    private final List<ExcelErrResponse> errList;
    private final List<Question> questionList;
    private final QuestionService questionService;
    //成功试题的数组
    private final List<Question> successList = new ArrayList<>();
    //失败试题的数组
    private final List<Question> failList = new ArrayList<>();

    @Override
    public void invoke(QuestionItem item, AnalysisContext analysisContext) {
        Integer currentRow = analysisContext.readRowHolder().getRowIndex();

        //可能是第一行 表头
        if((item.getBody() != null && item.getBody().contains("题干(必填)"))){
            return;
        }

        try {
            Question question = new Question();
            if(null == item.getBody() || item.getBody().length() <= 2 || item.getBody().length() >= 20000){
                handleErr(currentRow,"题干长度错误!");
                return;
            }
            //处理题干
            question.setBody(item.getBody());

            //处理解析
            if(null != item.getAnalysis()){
                question.setAnalysis(item.getAnalysis());
            }

            //处理备注
            if(null != item.getNote()){
                question.setNote(item.getNote());
            }

            //处理题型
            switch (item.getTypeName()) {
                case "单选题" -> question.setType(QuestionTypeEnum.SINGLE_SELECTION.getCode());
                case "多选题" -> question.setType(QuestionTypeEnum.MULTI_SELECTION.getCode());
                case "判断题" -> question.setType(QuestionTypeEnum.YES_NO.getCode());
                case "问答题" -> question.setType(QuestionTypeEnum.QA.getCode());
                case "不定项选择题" -> question.setType(QuestionTypeEnum.FREE_SELECTION.getCode());
                default -> question.setType(-100);
            }
            if(question.getType() < 0){
                handleErr(currentRow,"题目类型错误!");
                return;
            }

            //处置分值
            question.setQuestionScore(ScoreUtil.validQuestionScore(item.getScore()));
            if(question.getQuestionScore() < 0.1){
                handleErr(currentRow,"分值错误!，试题分值应大于0.1");
                return;
            }

            //处理答案
            if(null == item.getCorrectAnswer()){
                //非问答题
                if(!question.getType().equals(QuestionTypeEnum.QA.getCode())){
                    handleErr(currentRow,"试题答案为必填选项");
                    return;
                }
                //问答题
                item.setCorrectAnswer("");
            }
            item.setCorrectAnswer(item.getCorrectAnswer().replaceAll("<","&lt;"));
            item.setCorrectAnswer(item.getCorrectAnswer().replaceAll(">","&gt;"));

            //处理选项
            // 如果是单选多选不定项，或者判断题
            if(question.getType().equals(QuestionTypeEnum.SINGLE_SELECTION.getCode()) || question.getType().equals(QuestionTypeEnum.MULTI_SELECTION.getCode())
                    || question.getType().equals(QuestionTypeEnum.YES_NO.getCode()) || question.getType().equals(QuestionTypeEnum.FREE_SELECTION.getCode())) {
                int optionNum = Integer.parseInt(item.getOptionNum());
                if (optionNum < 2) {
                    handleErr(currentRow, "试题选项数量错误");
                    return;
                }

                String reg = "^[A-Z]+$";
                if (!item.getCorrectAnswer().matches(reg)) {
                    handleErr(currentRow, "答案存在错误！注意正确答案后不要有空格等隐藏字符");
                    return;
                }

                if (question.getType().equals(1) && item.getCorrectAnswer().length() != 1) {
                    handleErr(currentRow, "单选题答案为1个，其中包含错误！");
                    return;
                }

                if (question.getType().equals(3) && item.getCorrectAnswer().length() != 1) {
                    handleErr(currentRow, "判断题答案为1个，其中包含错误！");
                    return;
                }

                if (question.getType().equals(2) && item.getCorrectAnswer().length() < 2) {
                    handleErr(currentRow, "多选题答案至少为2个，其中包含错误！");
                    return;
                }

                //判断题
                if(question.getType().equals(QuestionTypeEnum.YES_NO.getCode())){
                    List<QuestionOption> questionOptionList = new ArrayList<>();
                    if(item.getCorrectAnswer().equals("A")){
                        //格式正确
                        question.getCorrectValue().add("0");
                    }
                    else if(item.getCorrectAnswer().equals("B")){
                        //格式正确
                        question.getCorrectValue().add("1");
                    }
                    else{
                        //格式错误
                        handleErr(currentRow, "判断题答案错误!");
                        return;
                    }

                    if(null == item.getOption1()){
                        item.setOption1("");
                    }
                    if(null == item.getOption2()){
                        item.setOption2("");
                    }
                    QuestionOption questionOption1 = new QuestionOption();
                    questionOption1.setTitle(item.getOption1());
                    questionOption1.setValue(0);

                    QuestionOption questionOption2 = new QuestionOption();
                    questionOption2.setTitle(item.getOption2());
                    questionOption2.setValue(1);

                    questionOptionList.add(questionOption1);
                    questionOptionList.add(questionOption2);

                    question.setOptionList(questionOptionList);
                }
                //多种选择题的
                else {
                    Map<String, String> map = new HashMap<>();
                    map.put("A","0");
                    map.put("B","1");
                    map.put("C","2");
                    map.put("D","3");
                    map.put("E","4");
                    map.put("F","5");
                    map.put("G","6");
                    map.put("H","7");

                    List<QuestionOption> questionOptionList = new ArrayList<>();
                    handleOption(questionOptionList,item);
                    List<QuestionOption> correctOptionList = questionOptionList.subList(0,optionNum);
                    question.setOptionList(correctOptionList);

                    for (int k = 0; k < item.getCorrectAnswer().length(); k++) {
                        char one = item.getCorrectAnswer().charAt(k);
                        String select = String.valueOf(one);
                        for(String key : map.keySet()){
                            if(select.equals(key)){
                                question.getCorrectValue().add(map.get(key));
                            }
                        }
                    }
                }
            }

            //问答题
            if(question.getType().equals(5)){
                if(item.getCorrectAnswer().equals("")){
                    item.setCorrectAnswer("未填写答案");
                }
                question.getCorrectValue().add(item.getCorrectAnswer());
            }

            //最后一步设置 公司id
            question.setCompanyId(new ObjectId(companyId));
            //预插入数据库
            Boolean result = this.questionService.preAddQuestion(question);
            //试题不合法
            if(!result){
                handleErr(currentRow, "试题不合法，请检查敏感词");
            }
            //预插入成功
            else {
                questionList.add(question);
            }
        }
        catch (Exception e){
            handleErr(currentRow,"导入非法");
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

        for(int i = questionList.size() -1 ; i >= 0 ; i--){
            this.questionService.addQuestion(questionList.get(i));
        }
        log.info("试题导入完成");
    }

    private void handleErr(Integer i, String info){
        ExcelErrResponse errBody = new ExcelErrResponse();
        errBody.setRow(i + 1);
        errBody.setError("请检查：第" + (i + 1) + "行题目," + info);
        errList.add(errBody);
    }

    private void handleOption(List<QuestionOption> questionOptionList, QuestionItem item){
        QuestionOption a = new QuestionOption();
        a.setTitle(item.getOption1());
        a.setValue(0);
        questionOptionList.add(a);

        QuestionOption b = new QuestionOption();
        b.setTitle(item.getOption2());
        b.setValue(1);
        questionOptionList.add(b);

        QuestionOption c = new QuestionOption();
        c.setTitle(item.getOption3());
        c.setValue(2);
        questionOptionList.add(c);

        QuestionOption d = new QuestionOption();
        d.setTitle(item.getOption4());
        d.setValue(3);
        questionOptionList.add(d);

        QuestionOption e = new QuestionOption();
        e.setTitle(item.getOption5());
        e.setValue(4);
        questionOptionList.add(e);

        QuestionOption f = new QuestionOption();
        f.setTitle(item.getOption6());
        f.setValue(5);
        questionOptionList.add(f);

        QuestionOption g = new QuestionOption();
        g.setTitle(item.getOption7());
        g.setValue(6);
        questionOptionList.add(g);

        QuestionOption h = new QuestionOption();
        h.setTitle(item.getOption8());
        h.setValue(7);
        questionOptionList.add(h);

    }
}