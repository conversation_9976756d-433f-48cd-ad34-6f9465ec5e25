package com.iguokao.supernova.exam.document;

import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.common.document.QuestionGroupOption;
import com.iguokao.supernova.common.document.QuestionOption;
import com.iguokao.supernova.common.response.QuestionGroupOptionRemoteResponse;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;
import com.iguokao.supernova.exam.response.PartResponse;
import com.iguokao.supernova.exam.response.QuestionResponse;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
public class Question extends BaseDocument {
    @Indexed(name = "companyId_index")
    private ObjectId companyId;
    private ObjectId additionalId;

    @Indexed(name = "type_index")
    private Integer type;
    private String body;
    private Double questionScore;
    private String analysis;
    private Integer scoreType = 1;  // 默认是 严格积分-全对才给分
    private Integer wordLimit;  // 字符限制
    private String note;
    private List<String> correctValue = new ArrayList<>();
    private List<QuestionOption> optionList = new ArrayList<>();
    private List<QuestionGroupOption> groupOptionList = new ArrayList<>();

    public static QuestionJudgeRemoteResponse toRemoteResponse(Question obj){
        if(obj==null){
            return null;
        }
        QuestionJudgeRemoteResponse res = new QuestionJudgeRemoteResponse();
        BeanUtils.copyProperties(obj, res);
        res.setQuestionId(obj.get_id().toString());
        res.setOptionCount(obj.getOptionList().size());
        for(QuestionGroupOption questionGroupOption : obj.getGroupOptionList()){
            QuestionGroupOptionRemoteResponse q = new QuestionGroupOptionRemoteResponse();
            BeanUtils.copyProperties(questionGroupOption, q);
            res.getGroupOptionList().add(q);
        }

        return res;
    }

    public static List<QuestionJudgeRemoteResponse> toRemoteResponse(List<Question> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<QuestionJudgeRemoteResponse> res = new ArrayList<>();
        for(Question obj : list){
            res.add(toRemoteResponse(obj));
        }
        return res;
    }

    public static Question of(QuestionResponse questionResponse, String companyId){
        Question res = new Question();
        res.setCompanyId(new ObjectId(companyId));
        res.setType(questionResponse.getType());
        res.setBody(questionResponse.getBody());
        res.setNote(questionResponse.getNote());
        res.setQuestionScore(questionResponse.getQuestionScore());

        res.setAnalysis(questionResponse.getAnalysis());
        res.setScoreType(questionResponse.getScoreType());
        res.setCorrectValue(questionResponse.getCorrectValue());
        res.setWordLimit(questionResponse.getWordLimit());

        if(questionResponse.getOptionList().size() >0){
            for(QuestionOption questionOption : questionResponse.getOptionList()){
                QuestionOption questionOption1 = new QuestionOption();
                questionOption1.setValue(questionOption.getValue());
                questionOption1.setTitle(questionOption.getTitle());
                res.getOptionList().add(questionOption1);
            }
        }

        if(questionResponse.getGroupOptionList().size() >0){
            for(QuestionGroupOption questionGroupOption : questionResponse.getGroupOptionList()){
                QuestionGroupOption questionGroupOption1 = new QuestionGroupOption();
                questionGroupOption1.setScoreType(questionGroupOption.getScoreType());
                questionGroupOption1.setType(questionGroupOption.getType());
                questionGroupOption1.setQuestionScore(questionGroupOption.getQuestionScore());
                questionGroupOption1.setBody(questionGroupOption.getBody());

                questionGroupOption1.setIsShowAnswerBox(questionGroupOption.getIsShowAnswerBox());
                questionGroupOption1.setAnalysis(questionGroupOption.getAnalysis());
                questionGroupOption1.setCorrectValue(questionGroupOption.getCorrectValue());

                for(QuestionOption questionOption : questionGroupOption.getOptionList()){
                    QuestionOption questionOption1 = new QuestionOption();
                    questionOption1.setTitle(questionOption.getTitle());
                    questionOption1.setValue(questionOption.getValue());
                    questionGroupOption1.getOptionList().add(questionOption1);
                }
                res.getGroupOptionList().add(questionGroupOption1);
            }
        }

        return res;
    }
}
