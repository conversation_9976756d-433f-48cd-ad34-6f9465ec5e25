package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.response.CandidateRemoteResponse;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter
@Setter
@Document
@CompoundIndexes({
        @CompoundIndex(def = "{'periodId': -1, 'roomId': -1}", name = "periodId_roomId_index")
})
public class Candidate extends BaseDocument {
    @Indexed(name = "companyId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;

    @Indexed(name = "projectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId periodId;

    @Indexed(name = "subjectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId subjectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId siteId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId roomId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId transRoomId;

    private Integer state = CandidateStateEnum.INIT.getCode();

    private Long num;  // 准考证号
    private String fullName;
    private String loginPassword;
    private Integer confirmState = 0;
    private SendInfo emailState = new SendInfo();
    private SendInfo smsState = new SendInfo();
    private Integer idCardType;
    private Integer gender;
    private String avatar;
    private String city;

    @Indexed(name = "idCardNum_index")
    private String idCardNum;
    private String mobile;
    private String email;
    private Integer seatNum;
    private Integer paperIndex;
    private String note;
    private String custom1;
    private String custom2;
    private String custom3;

    public CandidateRemoteResponse toRemoteResponse(){
        CandidateRemoteResponse dto = new CandidateRemoteResponse();
        BeanUtils.copyProperties(this, dto);
        dto.setCandidateId(this.get_id().toString());

        if(this.getCompanyId()!=null){
            dto.setCompanyId(this.getCompanyId().toString());
        }
        if(this.getProjectId()!=null){
            dto.setProjectId(this.getProjectId().toString());
        }
        if(this.getPeriodId()!=null){
            dto.setPeriodId(this.getPeriodId().toString());
        }
        if(this.getSubjectId()!=null){
            dto.setSubjectId(this.getSubjectId().toString());
        }
        if(this.getSiteId()!=null){
            dto.setSiteId(this.getSiteId().toString());
        }
        if(this.getRoomId()!=null){
            dto.setRoomId(this.getRoomId().toString());
        }
        return dto;
    }
}













