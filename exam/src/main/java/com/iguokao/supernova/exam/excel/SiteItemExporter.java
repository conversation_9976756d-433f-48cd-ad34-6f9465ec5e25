package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.exam.document.Site;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SiteItemExporter {

    public static List<List<String>> head() {

        List<List<String>> list = new ArrayList<>();

        List<String> head0 = new ArrayList<>();
        head0.add("考点id");

        List<String> head1 = new ArrayList<>();
        head1.add("考点名称");

        List<String> head2 = new ArrayList<>();
        head2.add("考点地址");

        List<String> head3 = new ArrayList<>();
        head3.add("城市");

        List<String> head4 = new ArrayList<>();
        head4.add("可用机位");


        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        return list;
    }

    public static List<List<Object>> data(List<Site> list, String category) {
        List<List<Object>> res = new ArrayList<>();
        int type = Integer.parseInt(category);
        //全量
        if(type == 2){
            for(Site item : list){
                List<Object> line = new ArrayList<>();
                line.add(item.get_id().toString());
                line.add(item.getName());
                line.add(item.getCity() + item.getDistrict() + item.getAddress());
                line.add(item.getCity());
                line.add(item.getAvailable());
                res.add(line);
            }
        }
        //城市
        else {
            Map<String,Integer> map = new HashMap<>();
            for(Site item : list){
                map.merge(item.getCity(), item.getAvailable(), Integer::sum);
            }

            for (String key : map.keySet()) {
                List<Object> line = new ArrayList<>();
                line.add("");
                line.add("");
                line.add("");
                line.add("      " + key + "     ");
                line.add(map.get(key));
                res.add(line);
            }
        }

        return res;
    }
}
