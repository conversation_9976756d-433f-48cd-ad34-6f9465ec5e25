package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class CandidateRoomExchangeRequest {
    @Schema(requiredMode = Schema.RequiredMode.NOT_REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private String periodId;

    @NotNull(message = "目标ID 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "目标房间 Id")
    @Length(min = 24, max = 24, message = "roomId 错误")
    private String roomId;

    @Schema(description = "考生 Id")
    @Length(min = 24, max = 24, message = "candidateId 错误")
    private String candidateId;


}
