package com.iguokao.supernova.exam.request;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class PaperDownloadRequest {

    @Schema(description = "时段id")
    @Length(min = 24, max = 24, message = "24位id")
    private String periodId;

    @Schema(description = "项目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Schema(description = "科目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String subjectId;

    @Schema(description = "url")
    @Length(min = 1, max = 10000, message = "url")
    private String url;

}