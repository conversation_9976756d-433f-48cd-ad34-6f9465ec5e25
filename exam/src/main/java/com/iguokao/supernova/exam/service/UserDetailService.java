package com.iguokao.supernova.exam.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

public interface UserDetailService {
    UserDetails loadUserByUsername(String username, String password, String imageCode, String key, String examId, String ip, Integer device) throws UsernameNotFoundException, JsonProcessingException;
}
