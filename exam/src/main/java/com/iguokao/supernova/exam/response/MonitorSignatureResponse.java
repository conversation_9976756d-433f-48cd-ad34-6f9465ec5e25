package com.iguokao.supernova.exam.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.exam.document.MonitorRoom;
import com.iguokao.supernova.exam.document.MonitorSignature;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class MonitorSignatureResponse implements Serializable {
    private String roomId;
    private String siteId;
    private String periodId;
    private String candidateId;
    private String avatar;
    private String fullName;
    private String note;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date createdAt;


    public static MonitorSignatureResponse of(MonitorSignature obj){
        if(obj==null){
            return null;
        }
        MonitorSignatureResponse res = new MonitorSignatureResponse();
        BeanUtils.copyProperties(obj, res);
        return res;
    }

    public static List<MonitorSignatureResponse> of(List<MonitorSignature> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<MonitorSignatureResponse> res = new ArrayList<>();
        for(MonitorSignature obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
