package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.exam.document.*;

import java.util.List;

public interface ActionService {
    void add(Action action);

    List<Action> getListByCandidate(String candidateId);
    List<Action> getListByPeriodIdAndRoomId(String periodId, String roomId);

    List<Action> getListByPeriodAndType(String periodId, int type);

    List<Action> getListByProjectIdAndRoomId(String projectId, String roomId);

}
