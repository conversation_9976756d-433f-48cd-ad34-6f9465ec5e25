package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Email;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class EmailResponse {
    private String emailId;
    private String name;
    private String title;
    private String content;

    public static EmailResponse of(Email obj){
        if(obj==null){
            return null;
        }
        EmailResponse res = new EmailResponse();
        BeanUtils.copyProperties(obj, res);
        res.setEmailId(obj.get_id().toString());
        return res;
    }

    public static List<EmailResponse> of(List<Email> list){
        if(list == null){
            return new ArrayList<>();
        }
        List<EmailResponse> res = new ArrayList<>();
        for(Email obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
