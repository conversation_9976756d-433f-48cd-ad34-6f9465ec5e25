package com.iguokao.supernova.exam.document;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;


@Getter
@Setter
public class Email extends BaseDocument {
    @Indexed(name = "companyId_index")
    private ObjectId companyId;
    private String name;  // 名称
    private String title;
    private String content;  // 内容
}
