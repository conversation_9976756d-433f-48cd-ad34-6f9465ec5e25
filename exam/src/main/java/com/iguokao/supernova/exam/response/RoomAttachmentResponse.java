package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.PeriodRoom;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.document.Site;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RoomAttachmentResponse {

    @Schema(description = "考点Id")
    private String siteId;

    @Schema(description = "考场Id")
    private String roomId;

    @Schema(description = "考场名称")
    private String siteName;

    @Schema(description = "考场编号")
    private String roomName;

    @Schema(description = "附件")
    private List<String> attachmentList;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "房间地址")
    private String roomAddress;

    public static RoomAttachmentResponse of(PeriodRoom pr, Site site, Room room){
        if(pr==null){
            return null;
        }
        RoomAttachmentResponse res = new RoomAttachmentResponse();
        res.setSiteId(pr.getSiteId().toString());
        res.setRoomId(pr.getRoomId().toString());
        res.setSiteName(site.getName());
        res.setRoomName(room.getName());
        res.setAttachmentList(pr.getAttachmentList());
        res.setCity(site.getCity());
        res.setRoomAddress(room.getAddress());
        return res;
    }

    public static List<RoomAttachmentResponse> of(List<PeriodRoom> list, List<Site> siteList, List<Room> roomList){
        if(list.isEmpty() || siteList.isEmpty() || roomList.isEmpty()){
            return new ArrayList<>();
        }
        List<RoomAttachmentResponse> res = new ArrayList<>();
        for(PeriodRoom obj : list){
            Site site = siteList
                    .stream()
                    .filter(s -> s.get_id().equals(obj.getSiteId()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
            Room room = roomList
                    .stream()
                    .filter(s -> s.get_id().equals(obj.getRoomId()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
            res.add(of(obj, site, room));
        }
        return res;
    }
}
