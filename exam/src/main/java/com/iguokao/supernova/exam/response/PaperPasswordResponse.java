package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Paper;
import com.iguokao.supernova.exam.document.Part;
import com.iguokao.supernova.exam.document.Period;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class PaperPasswordResponse {
    private String periodId;
    private String password;

    public static PaperPasswordResponse of(Period obj){
        if(obj==null){
            return null;
        }
        PaperPasswordResponse res = new PaperPasswordResponse();
        res.setPeriodId(obj.get_id().toString());
        res.setPassword(obj.getPaperPassword());
        return res;
    }
}
