package com.iguokao.supernova.exam.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Getter
@Setter
public class PeriodUpdateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private String periodId; // 项目ID

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @NotNull(message = "开始时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startAt;

    @Schema(description = "时长")
    @Min(value = 60, message = "时长错误")
    @Max(value = 21600, message = "时长错误")
    private Integer duration;

    @Schema(description = "允许迟到的秒数 超出 0不限")
    private Integer lateSecond;

    @Schema(description = "允许交卷的秒数 超出 0不限")
    private Integer submitSecond;
}
