package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Document("monitor_test")
@CompoundIndexes({
        @CompoundIndex(def = "{'projectId': -1, 'roomId': -1}", name = "projectId_roomId_index")
})
public class MonitorTest extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId roomId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId siteId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    private Boolean examTested;
    private Boolean cameraTested;
    private Boolean envTested;
    private String envError;
    private String examError;
}
