package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class IdCheckRequest {

    @Length(min = 24, max = 24, message = "项目Id 24位")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目Id不能为空")
    private String projectId;


    @NotNull(message = "图片验证码不能为空")
    @Length(max = 4, min = 4, message = "验证码必须是4位")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "图形验证码字符串")
    private String imageCode;

    @NotNull(message = "验证码key不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "验证码的token")
    private String key;

    @NotNull(message = "身份证号码不能为空")
    @Length(max = 18, min = 18, message = "身份证号码18位")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "身份证号码")
    private String idCardNum;

    @NotNull(message = "姓名不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "姓名")
    private String fullName;


}
