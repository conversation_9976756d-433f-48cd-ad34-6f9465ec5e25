package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Document
public class Project extends BaseDocument {
    @Indexed(name = "companyId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId companyId;
    private Integer num;

    @Indexed(name = "name_index")
    private String name;
    private String shortName;
    private Date startAt;
    private Date endAt;
    private Integer type = 1; // 1 考试 2 三级
    private Integer state = 0;
    private Boolean online = false;
    private Boolean offlineMode = false;
    private Boolean fixedPosition = true;
    private Boolean idNumLoginOnly = false;
    private Integer candidateCount = 0;
    private Integer periodCount = 0;
    private Integer lateSecond = 0;
    private Integer submitSecond = 0;
    private Integer faceDiff = 0;
    private String note;
    private String shortCode;
    private String requirement; // 考试须知
    private String admissionCardRequirement; //准考证考试须知
    private String clientVersion;
    private List<InputItem> inputList = new ArrayList<>(); // 输入法
    private Double oversize; // 超编比例
    private List<Attachment> attachmentList = new ArrayList<>();
    private Date admissionCardExpiredAt;
}
