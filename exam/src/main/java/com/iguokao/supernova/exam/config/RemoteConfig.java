package com.iguokao.supernova.exam.config;

import com.iguokao.supernova.common.remote.*;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import feign.slf4j.Slf4jLogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RemoteConfig {
    @Bean
    public ManagementRemote cloopenRemote(@Value("${app.service.management}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(ManagementRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(ManagementRemote.class, service);
    }

    @Bean
    public TaskRemote taskRemote(@Value("${app.service.task}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(TaskRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(TaskRemote.class, service);
    }

    @Bean
    public StoneExamRemote examRemote(@Value("${app.service.stone}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .logger(new Slf4jLogger(TaskRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(StoneExamRemote.class, service);
    }

    @Bean
    public RegistrationRemote registrationRemoteRemote(@Value("${app.service.registration}") String service) {
        return Feign.builder()
                .client(new OkHttpClient())
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .logger(new Slf4jLogger(RegistrationRemote.class))
                .logLevel(feign.Logger.Level.FULL)
                .target(RegistrationRemote.class, service);
    }
}
