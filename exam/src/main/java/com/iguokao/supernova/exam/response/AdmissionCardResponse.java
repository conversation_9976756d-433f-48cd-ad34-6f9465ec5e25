package com.iguokao.supernova.exam.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.common.document.AdmissionCard;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class AdmissionCardResponse {

    @Schema(description = "考生姓名")
    private String fullName;

    @Schema(description = "准考证号")
    private Long num;

    @Schema(description = "证件号")
    private String idCardNum;

    @Schema(description = "电子邮件")
    private String email;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "头像")
    private String avatar;

    private String avatarUrl;

    private String candidateId;
    private String periodId;
    private String subjectId;
    private String siteId;
    private String roomId;
    private Integer seatNum;
    private String roomNum;

    private String subjectName;
    private String siteName;
    private String siteAddress;

    private String roomName;
    private String roomAddress;
//    private String traffic;
    private String city;
    private String district;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startAt;

    private Integer duration;

    public static AdmissionCardResponse of(AdmissionCard obj){
        if(obj == null){
            return null;
        }
        AdmissionCardResponse res = new AdmissionCardResponse();
        BeanUtils.copyProperties(obj, res);
        return res;
    }

    public static List<AdmissionCardResponse> of(List<AdmissionCard> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<AdmissionCardResponse> res = new ArrayList<>();
        for(AdmissionCard obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
