package com.iguokao.supernova.exam.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Getter
@Setter
public class ProjectClientVersionUpdateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Schema(description = "版本号")
    @NotBlank(message = "版本号 不能为空")
    private String clientVersion;
}
