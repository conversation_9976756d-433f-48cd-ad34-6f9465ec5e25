package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.CredentialCategoryEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.exam.enums.CandidateConfirmStateEnum;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.excel.CandidateConfirmItem;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.CandidateService;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.schema.JsonSchemaObject;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
@RequiredArgsConstructor
public class CandidateServiceImpl implements CandidateService {
    private final CandidateRepository candidateRepository;
    private final ProjectRepository projectRepository;
    private final PeriodRepository periodRepository;
    private final PeriodRoomRepository periodRoomRepository;
    private final CacheService cacheService;
    private final MongoTemplate mongoTemplate;
    private final SiteRepository siteRepository;
    private final RoomRepository roomRepository;

    @Override
    public void add(Candidate candidate) {
        long num = this.getNextNumOfCandidate(candidate.getProjectId().toString());
        candidate.setNum(num);

        //验证身份证号重复
        int count = this.candidateRepository.countByPeriodIdAndSubjectIdAndIdCardNum(candidate.getPeriodId(),candidate.getSubjectId(),candidate.getIdCardNum());
        if(count > 0){
            throw new ServiceException(ExceptionEnum.CANDIDATE_ID_CARD_REPEAT);
        }

        this.insertCandidate(candidate);

        Project project = this.projectRepository.findById(candidate.getProjectId()).orElseThrow(
                () -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));

        //处理考生人数
        handleCandidateCountInProjectAndSubject(project.get_id().toString(),
                candidate.getPeriodId().toString(),
                candidate.getSubjectId().toString());
    }

    @Override
    public int addAll(List<Candidate> list) {
        if(list.isEmpty()){
            return 0;
        }
        long num = this.getNextNumOfCandidate(list.get(0).getProjectId().toString());
        AtomicInteger count = new AtomicInteger(0);
        list.removeIf(candidate ->
             this.candidateRepository.countByPeriodIdAndSubjectIdAndIdCardNum(candidate.getPeriodId(),
                     candidate.getSubjectId(), candidate.getIdCardNum()) > 0
        );
        list.forEach(candidate -> {
            if(candidate.getNum() == null){
                candidate.setNum(num + count.get());
            }
            this.insertCandidate(candidate);
            count.getAndIncrement();
        });
        if(list.isEmpty()){
            return 0;
        }

        Project project = this.projectRepository.findById(list.get(0).getProjectId()).orElseThrow(
                () -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));

        //处理考生人数
        handleCandidateCountInProjectAndSubject(project.get_id().toString(),
                list.get(0).getPeriodId().toString(),
                list.get(0).getSubjectId().toString());
        return list.size();
    }

    private void insertCandidate(Candidate candidate){
        candidate.setIdCardNum(candidate.getIdCardNum().toUpperCase());
        //证件类型是身份证号码
        if(candidate.getIdCardType().equals(CredentialCategoryEnum.ID_CARD.getCode())){
            if(!StringUtil.validIdCard(candidate.getIdCardNum())){
                throw new ServiceException(ExceptionEnum.CANDIDATE_ID_CARD_ERR);
            }
            //根据身份证号处理性别
            if(Integer.parseInt(candidate.getIdCardNum().substring(16,17)) %2 == 0){
                candidate.setGender(2);
            }
            else {
                //男
                candidate.setGender(1);
            }
        }

        this.candidateRepository.insert(candidate);
    }

    private Long getNextNumOfCandidate(String projectId){
        Project project = this.cacheService.getProject(projectId);
        Sort sort = Sort.by("num").descending();
        Candidate candidate = this.candidateRepository.findFirstByProjectId(new ObjectId(projectId), sort)
                .orElse(null);
        if(candidate != null && candidate.getNum() != null){
            return candidate.getNum() + 1;
        }else {
            return (long)project.getNum() * 100000 + 1;
        }
    }

    @Override
    public void remove(String candidateId) {
        this.candidateRepository.deleteById(new ObjectId(candidateId));
    }

    @Override
    public void setState(String candidateId, CandidateStateEnum state) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(candidateId)))
                .addCriteria(Criteria.where("state").is(CandidateStateEnum.INIT.getCode()));
        Update update = new Update()
                .set("state", state.getCode());
        this.mongoTemplate.updateFirst(query, update, Candidate.class);
    }

    @Override
    public void setStateByIdCardNum(String projectId, String idCardNum, CandidateStateEnum state) {
        Query query = new Query(Criteria.where("projectId").is(new ObjectId(projectId)))
                .addCriteria(Criteria.where("idCardNum").is(idCardNum))
                .addCriteria(Criteria.where("state").is(CandidateStateEnum.INIT.getCode()));
        Update update = new Update()
                .set("state", state.getCode());
        this.mongoTemplate.updateFirst(query, update, Candidate.class);
    }

    @Override
    public Tuple2<List<Candidate>, Integer> getPage(String companyId,
                                                    String projectId,
                                                    String subjectId,
                                                    String fullName,
                                                    String mobile,
                                                    Integer emailState,
                                                    Integer smsState,
                                                    Integer confirm,
                                                    Integer state,
                                                    Boolean arranged,
                                                    Boolean avatarNull,
                                                    Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt"))
                .addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)))
                .addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)))
                .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)));


        if(null != fullName){
            query = query.addCriteria(Criteria.where("fullName").is(fullName));
        }
        if(null != mobile){
            query = query.addCriteria(Criteria.where("mobile").is(mobile));
        }
        if(null != emailState){
            query = query.addCriteria(Criteria.where("emailState.state").is(emailState));
        }
        if(null != smsState){
            query = query.addCriteria(Criteria.where("smsState.state").is(smsState));
        }
        if(null != confirm){
            query = query.addCriteria(Criteria.where("confirmState").is(confirm));
        }
        if(null != state){
            query = query.addCriteria(Criteria.where("state").is(state));
        }
        if(null != arranged){
            //已安排
            if(arranged){
                query = query.addCriteria(Criteria.where("roomId").type(JsonSchemaObject.Type.OBJECT_ID));
            }
            //未安排
            else {
                query.addCriteria(new Criteria().orOperator(Criteria.where("roomId").isNull(), Criteria.where("roomId").exists(false)));
            }

        }
        if(avatarNull != null && avatarNull){
            query = query.addCriteria(Criteria.where("avatar").isNull());
        }
        // 计算总数
        long count = this.mongoTemplate.count(query, Candidate.class);
        // 分页信息
        query = query.with(pageable);
        List<Candidate> list = this.mongoTemplate.find(query, Candidate.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public List<Candidate> getRoomCandidateList(String periodId, String roomId) {
        return this.candidateRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId));
    }

    @Override
    public XWPFDocument getRoomCandidateDoc(Project project, Period period, Room room, String periodId, String roomId) {
        List<Candidate> list = this.candidateRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId));
        if(list.isEmpty()){
            return null;
        }
        list = list.stream()
                .sorted(Comparator.comparing(Candidate::getSeatNum))
                .toList();
        Date end = new Date(period.getStartAt().getTime() + period.getDuration() * 1000);

        PeriodRoom periodRoom = this.periodRoomRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId))
                .orElseThrow(()-> new ServiceException(ExceptionEnum.PERIOD_ROOM_NOT_FOUND));

        XWPFDocument document = new XWPFDocument();
        // 创建一个段落
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);

        XWPFRun title = titleParagraph.createRun();
        title.setFontSize(20);
        title.setBold(true);
        title.setText(project.getName());
        title.addBreak(BreakType.TEXT_WRAPPING);

        XWPFRun second = titleParagraph.createRun();
        second.setFontSize(20);
        second.setBold(true);
        second.setText("考生名单");
        second.addBreak(BreakType.TEXT_WRAPPING);

        XWPFParagraph timeParagraph = document.createParagraph();
        timeParagraph.setAlignment(ParagraphAlignment.LEFT);

        XWPFRun info = timeParagraph.createRun();
        info.setFontSize(14);
        info.setBold(false);
        info.setText(String.format("考场号: 第 %d 考场", periodRoom.getRoomIndex() + 1));
        info.addBreak(BreakType.TEXT_WRAPPING);
        info.setText(String.format("考场地址: %s", room.getAddress()));
        info.addBreak(BreakType.TEXT_WRAPPING);
        info.setText(String.format("时间： %s - %s",
                DateUtil.dateToStr(period.getStartAt(), "yyyy/MM/dd HH:mm"),
                DateUtil.dateToStr(end, "yyyy/MM/dd HH:mm")));
        info.addBreak(BreakType.TEXT_WRAPPING);
        info.setText("总人数： " + list.size() + " 人");
        info.addBreak(BreakType.TEXT_WRAPPING);

        XWPFTable table = document.createTable(list.size() + 1, 4);
        table.setWidth(getCellWidth(100));

        XWPFTableRow header = table.getRow(0);
        header.setHeight(400);

        header.getCell(0).setText("座位号");
        header.getCell(0).setWidth("10%");
        header.getCell(0)
                .getParagraphArray(0)
                .setAlignment(ParagraphAlignment.CENTER);

        header.getCell(1).setText("考生姓名");
        header.getCell(1).setWidth("25%");
        header.getCell(1)
                .getParagraphArray(0)
                .setAlignment(ParagraphAlignment.CENTER);

        header.getCell(2).setText("准考证号");
        header.getCell(2).setWidth("30%");
        header.getCell(2)
                .getParagraphArray(0)
                .setAlignment(ParagraphAlignment.CENTER);

        header.getCell(3).setWidth("35%");
        header.getCell(3).setText("身份证号");
        header.getCell(3)
                .getParagraphArray(0)
                .setAlignment(ParagraphAlignment.CENTER);

        for(int i=0; i<list.size(); i++){
            XWPFTableRow row = table.getRow(i+1);
            Candidate candidate = list.get(i);
            row.setHeight(100);

            row.getCell(0).setWidth("10%");
            if(candidate.getSeatNum() != null){
                row.getCell(0).setText(candidate.getSeatNum().toString());
                row.getCell(0)
                        .getParagraphArray(0)
                        .setAlignment(ParagraphAlignment.CENTER);
            }

            row.getCell(1).setWidth("25%");
            row.getCell(1).setText(candidate.getFullName());
            row.getCell(1)
                    .getParagraphArray(0)
                    .setAlignment(ParagraphAlignment.CENTER);

            row.getCell(2).setWidth("30%");
            row.getCell(2).setText(candidate.getNum().toString());
            row.getCell(2)
                    .getParagraphArray(0)
                    .setAlignment(ParagraphAlignment.CENTER);

            row.getCell(3).setWidth("35%");
            row.getCell(3).setText(StringUtil.idCardNumSecure(candidate.getIdCardNum()));
            row.getCell(3)
                    .getParagraphArray(0)
                    .setAlignment(ParagraphAlignment.CENTER);
        }
        setAllBorders(table);
        return document;
    }

    void setAllBorders(XWPFTable table) {
        int size = 1;
        int space = 1;

        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, size, space, "000000");
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, size, space, "000000");
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, size, space, "000000");
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, size, space, "000000");
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, size, space, "000000");
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, size, space, "000000");
    }

    int getCellWidth(int width){
        return (int)(0.01 * width * 8000);
    }

    @Override
    public Integer roomIn(String periodId, String roomId, List<String> idList) {
        PeriodRoom periodRoom = this.periodRoomRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
        Period period = this.periodRepository.findById(periodRoom.getPeriodId())
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));

        if(period.getReady()){
            throw new ServiceException(ExceptionEnum.PERIOD_READY);
        }
        List<Candidate> list = this.getRoomCandidateList(periodId, roomId);

        int count = this.getSameCount(list, idList);
        if(count > 0){
            throw new ServiceException(ExceptionEnum.ROOM_CONTAIN_CANDIDATE);
        }

        /*
         处理 进入房间
         1 考生退出房间 roomId 取消
         2 PeriodRoom candidateCount 减去退出人数
         */
        this.setCandidateRoomId(list, new ObjectId(roomId));
        this.setPeriodRoomCandidateCount(periodRoom.get_id(), periodRoom.getCandidateCount() + idList.size());

        return idList.size();
    }

    @Override
    public Integer roomOut(String periodId, String roomId, List<String> idList) {
        PeriodRoom periodRoom = this.periodRoomRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
        Period period = this.periodRepository.findById(periodRoom.getPeriodId())
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
        // 项目是否就绪
        if(period.getReady()){
            throw new ServiceException(ExceptionEnum.PERIOD_READY);
        }
        List<Candidate> list = this.getRoomCandidateList(periodId, roomId);

        int count = this.getSameCount(list, idList);
        if(count != idList.size()){
            throw new ServiceException(ExceptionEnum.ROOM_DO_NOT_CONTAIN_ALL_CANDIDATE);
        }

        /*
         处理 出房间
         1 考生退出房间 roomId 取消
         2 PeriodRoom candidateCount 减去退出人数
         */
        this.setCandidateRoomId(list, null);
        this.setPeriodRoomCandidateCount(periodRoom.get_id(), periodRoom.getCandidateCount() - idList.size());

        return idList.size();
    }

    @Override
    public Long removeBatch(String companyId, String projectId, String periodId, String subjectId, List<String> candidateIdList) {
        Period period = this.cacheService.getPeriod(periodId);
        if(period.getReady()){
            throw new ServiceException(ExceptionEnum.PERIOD_READY);
        }
        List<ObjectId> idList = candidateIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        long count = this.mongoTemplate.count(new Query()
                .addCriteria(Criteria.where("_id").in(idList))
                .addCriteria(Criteria.where("roomId").ne(null)), Candidate.class);
        if(count > 0){
            throw new ServiceException(ExceptionEnum.CANDIDATE_IN_SEAT);
        }
        Query query = new Query()
                .addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)))
                .addCriteria(Criteria.where("projectId").is(new ObjectId(projectId)))
                .addCriteria(Criteria.where("_id").in(idList));

        DeleteResult result = this.mongoTemplate.remove(query, Candidate.class);
        Project project = this.projectRepository.findById(period.getProjectId()).orElseThrow();
        handleCandidateCountInProjectAndSubject(project.get_id().toString(), periodId, subjectId);
        return result.getDeletedCount();
    }

    @Override
    public void update(Candidate candidate) {
        this.candidateRepository.save(candidate);
    }

    @Override
    public Candidate getById(String candidateId) {
        return this.candidateRepository.findById(new ObjectId(candidateId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
    }

    @Override
    public List<Candidate> getPeriodSiteCandidateList(String projectId, String periodId, String siteId) {
        return this.candidateRepository.findByProjectIdAndPeriodIdAndSiteId(new ObjectId(projectId), new ObjectId(periodId), new ObjectId(siteId));
    }

    @Override
    public List<Candidate> getTestCandidateList(Project project, Period period, int count, String testPaperId, String siteId, String roomId) {
        List<Candidate> candidateList = new ArrayList<>();
        for(int i =0;i<count;i++){
            Candidate candidate = new Candidate();
            candidate.set_id(new ObjectId());
            candidate.setCompanyId(new ObjectId());
            candidate.setProjectId(project.get_id());
            candidate.setPeriodId(period.get_id());
            candidate.setSubjectId(period.getSubjectList().get(0).get_id());
            candidate.setSiteId(new ObjectId(siteId));
            candidate.setRoomId(new ObjectId(roomId));

            candidate.setNum((long) i + 1);
            candidate.setFullName("测试考生" + (i + 1));
            candidate.setLoginPassword("test" + (i + 1));
            candidate.setMobile("13901234567");
            candidate.setEmail((i+1) + "@qq.com");
            candidate.setSeatNum(i + 1);

            candidateList.add(candidate);
        }
        return candidateList;
    }

    @Override
    public Tuple2<Integer, Integer> countPeriodCandidate(String periodId) {
        Period period = this.periodRepository.findById(new ObjectId(periodId)).orElse(null);
        if(null == period){
            throw new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND);
        }
        int total = this.candidateRepository.countByProjectIdAndPeriodId(period.getProjectId(), new ObjectId(periodId));
        int arranged = this.candidateRepository.countArranged(new ObjectId(periodId));
        return new Tuple2<>(arranged, total);
    }

    @Override
    public List<Candidate> getNotArrangedCandidateList(String periodId) {
        Period period = this.periodRepository.findById(new ObjectId(periodId)).orElse(null);
        if(null == period){
            throw new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND);
        }
        return this.candidateRepository.findNotArranged(period.getProjectId(), new ObjectId(periodId));
    }

    @Override
    public void setCandidateListSiteAndRoom(List<String> idList, String periodId, String siteId, String roomId) {
        Site site = this.siteRepository.findById(new ObjectId(siteId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
        Room room = this.roomRepository.findById(new ObjectId(roomId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));

        int existCount = this.candidateRepository.countByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId));
        if(room.getAvailable() * 1.2 < (existCount + idList.size())){
            throw new ServiceException(ExceptionEnum.ROOM_CANDIDATE_BEYOND_LIMIT);
        }

        List<Candidate> notArrangedList = this.candidateRepository.findBy_idIn(idList
                .stream()
                .map(ObjectId::new)
                .toList());
        int diffCityCount = notArrangedList
                .stream()
                .filter(c -> !c.getCity().equals(site.getCity()))
                .toList()
                .size();
        if(diffCityCount > 0){
            throw new ServiceException(ExceptionEnum.CANDIDATE_SITE_NOT_IN_SAME_CITY);
        }

        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Candidate.class);
        idList.forEach(id -> {
            notArrangedList
                    .stream()
                    .filter(c -> c.get_id().toString().equals(id))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_IN_SEAT));

            Query query = new Query(Criteria.where("_id").is(new ObjectId(id)));
            Update update = new Update()
                    .set("siteId", new ObjectId(siteId))
                    .set("roomId", new ObjectId(roomId));
            bulkOperations.updateOne(query, update);
        });
        if (!idList.isEmpty()) {
            BulkWriteResult result = bulkOperations.execute();
            log.info("Room {} 添加考生 - {} - 人", roomId, result.getModifiedCount());
        }
        // 重新排序
        Project project = this.cacheService.getProject(notArrangedList.get(0).getProjectId().toString());
        if(project.getFixedPosition()){
            this.setCandidateSeatNum(periodId, roomId);
        }
        //更新periodRoom的考生人数
        this.calculatePeriodRoomCandidateCount(periodId, roomId);
    }

    @Override
    public void cancelCandidateListSiteAndRoom(List<String> idList, String periodId, String roomId) {
        //清除 考生列表中这些人mongodb中的 seatNum  siteId roomId 字段
        Period period = this.cacheService.getPeriod(periodId);
        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Candidate.class);
        idList.forEach(id -> {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(id)));
            Update update = new Update()
                    .set("seatNum", null)
                    .set("siteId", null)
                    .set("roomId", null);
            bulkOperations.updateOne(query, update);
        });
        if (!idList.isEmpty()) {
            BulkWriteResult result = bulkOperations.execute();
            log.info("Room {} 移除考生 - {} - 人", roomId, result.getModifiedCount());
        }
        // 删除准考证
        if(period.getAdmissionCardCount()>0){
            List<Candidate> list = this.candidateRepository.findBy_idIn(idList
                    .stream()
                    .map(ObjectId::new)
                    .toList());
            for(Candidate candidate : list){
               this.cacheService.deleteAdmissionCardByIdCardNum(period.getProjectId().toString(), candidate.getIdCardNum());
            }
            // 更新  mongodb and redis
            period.setAdmissionCardCount(period.getAdmissionCardCount() - idList.size());
            this.periodRepository.save(period);
            this.cacheService.deletePeriod(periodId);
        }
        // 重新排序
        Project project = this.cacheService.getProject(period.getProjectId().toString());
        if(project.getFixedPosition()){
            this.setCandidateSeatNum(periodId, roomId);
        }
        //更新periodRoom的考生人数
        this.calculatePeriodRoomCandidateCount(periodId, roomId);
    }

    @Override
    public List<Candidate> getMinitorRoomCandidateList(String periodId, String roomId) {
        return this.candidateRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId));
    }

    @Override
    public List<Candidate> getByPeriodId(String periodId) {
        Period period = this.periodRepository.findById(new ObjectId(periodId)).orElse(null);
        if(null == period){
            throw new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND);
        }
        return this.candidateRepository.findByProjectIdAndPeriodId(period.getProjectId(), new ObjectId(periodId));
    }

    @Override
    public List<CandidateConfirmItem> getConfirmStateByPeriodId(String periodId) {
        List<Candidate> candidateList = this.getByPeriodId(periodId);
        return CandidateConfirmItem.of(candidateList);
    }

    @Override
    public void exchange(String candidateId, String roomId, String siteId) {
        Candidate candidate = this.getById(candidateId);
        if(candidate.getRoomId().toString().equals(roomId)){
            throw new ServiceException(ExceptionEnum.CANDIDATE_IN_SAME_ROOM);
        }
        candidate.setRoomId(new ObjectId(roomId));
        this.candidateRepository.save(candidate);
    }

    @Override
    public List<String> getExistsIdCardsInSubject(String periodId, String subjectId) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));
        return mongoTemplate.findDistinct(query, "idCardNum", Candidate.class, String.class);
    }

    @Override
    public List<Long> getExistsNumInSubject(String periodId, String subjectId) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));
        return mongoTemplate.findDistinct(query, "num", Candidate.class, Long.class);
    }

    @Override
    public List<String> setAvatar(String subjectId, List<String> idList) {
        List<String> failedList = new ArrayList<>();
        for(String id : idList){
            Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)))
                    .addCriteria(Criteria.where("idCardNum").is(id));
            Update update = new Update()
                    .set("avatar", String.format("avatar/%s/%s.jpg", subjectId, id));
            UpdateResult res = mongoTemplate.updateFirst(query, update, Candidate.class);
            if(res.getMatchedCount() == 0){
                failedList.add(id);
            }
        }
        return failedList;
    }

    @Override
    public void confirm(String candidateId, Integer confirmState) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(candidateId)));
        Update update = new Update()
                .set("confirmState", confirmState);
        mongoTemplate.updateFirst(query, update, Candidate.class);
    }

    @Override
    public Long countBySubjectId(String periodId, String subjectId) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));
        return mongoTemplate.count(query, Candidate.class);
    }

    @Override
    public Long countNotifiedBySubjectId(String periodId, String subjectId) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));
        query.addCriteria(new Criteria().orOperator(Criteria.where("emailState.state").gt(0), Criteria.where("smsState.state").gt(0)));
        return mongoTemplate.count(query, Candidate.class);
    }

    @Override
    public Long countStateGreaterBySubjectId(String periodId, String subjectId, Integer state) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));
        query.addCriteria(Criteria.where("state").gte(state));
        return mongoTemplate.count(query, Candidate.class);
    }


    @Override
    public void setCandidateSeatNum(String periodId, String roomId){
        List<Candidate> list = this.candidateRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId));
        if(list.isEmpty()){
            //throw new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND);
            return;
        }
        int max = list
                .stream()
                .filter(c -> c.getSeatNum() != null)
                .mapToInt(Candidate::getSeatNum)
                .max()
                .orElse(0);
        max = Math.max(max, list.size());

        for(int i=1; i<=max; i++){
            final int current = i;
            Candidate exist = list
                    .stream()
                    .filter(c -> c.getSeatNum() != null && c.getSeatNum() == current)
                    .findFirst()
                    .orElse(null);
            if(exist == null){
                Candidate willIn = list
                        .stream()
                        .filter(c -> c.getSeatNum() == null)
                        .findFirst()
                        .orElse(null);
                if(willIn == null){
                    break;
                }
                willIn.setSeatNum(i);
                Query query = new Query(Criteria.where("_id").is(willIn.get_id()));
                Update update = new Update()
                        .set("seatNum", i);
                mongoTemplate.updateFirst(query, update, Candidate.class);
                System.out.printf("%s - %d%n", willIn.get_id().toString(), i);
            }
        }
    }

    @Override
    public int avatarCount(String subjectId) {
        return this.candidateRepository.countAvatar(new ObjectId(subjectId));
    }

    @Override
    public Long countRefused(String periodId, String subjectId) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));
        query.addCriteria(Criteria.where("confirmState").is( CandidateConfirmStateEnum.REFUSE.getCode()));
        return mongoTemplate.count(query, Candidate.class);
    }

    @Override
    public Long countConfirmed(String periodId, String subjectId) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));
        Criteria or = new Criteria();
        or.orOperator(Criteria.where("confirmState").is( CandidateConfirmStateEnum.AGREE.getCode()), Criteria.where("state").is(CandidateStateEnum.DOWNLOADED.getCode()));
        query.addCriteria(or);

        List<ObjectId> list = mongoTemplate.findDistinct(query, "_id", Candidate.class, ObjectId.class);
        return (long) list.size();
    }

    @Override
    public Long countUnconfirmed(String periodId, String subjectId) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)));
        query.addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));
        query.addCriteria(Criteria.where("confirmState").is(0));
        query.addCriteria(Criteria.where("state").is(CandidateStateEnum.INIT.getCode()));
        return mongoTemplate.count(query, Candidate.class);
    }

    @Override
    public void test() {
        List<String> list = new ArrayList<>();

        list.add("6728c2a7e48e1123e88290ac");
        list.add("6728c2a7e48e1123e882912e");
        list.add("6728c2a8e48e1123e882918b");
        list.add("6728c2a8e48e1123e88291ef");
        list.add("6728c2a8e48e1123e882930d");
        list.add("6728c2a8e48e1123e882934a");
        list.add("6728c2a8e48e1123e8829352");
        list.add("6728c2a9e48e1123e88293d0");
        list.add("6728c2a9e48e1123e88294ae");
        list.add("6728c2a9e48e1123e8829518");
        list.add("6728c2a9e48e1123e88295cc");
        list.add("6728c2aae48e1123e88295e3");
        list.add("6728c2aae48e1123e882963f");
        list.add("6728c2aae48e1123e882964e");
        list.add("6728c2aae48e1123e8829804");
        list.add("6728c2abe48e1123e8829828");
        list.add("6728c2ace48e1123e8829b93");
        list.add("6728c2ace48e1123e8829bb3");
        list.add("6728c2ace48e1123e8829bdf");
        list.add("6728c2ace48e1123e8829c6b");
        list.add("6728c2ace48e1123e8829c93");
        list.add("6728c2ace48e1123e8829ca2");
        list.add("6728c2ade48e1123e8829d2a");
        list.add("6728c2ade48e1123e8829da0");
        list.add("6728c2ade48e1123e8829dbd");
        list.add("6728c2ade48e1123e8829dca");
        list.add("6728c2ade48e1123e8829e32");
        list.add("6728c2afe48e1123e882a17c");
        list.add("6728c2afe48e1123e882a1c8");
        list.add("6728c2afe48e1123e882a2a5");
        list.add("6728c2afe48e1123e882a2be");
        list.add("6728c2b0e48e1123e882a4d4");
        list.add("6728c2b0e48e1123e882a52c");
        list.add("6728c2b1e48e1123e882a661");
        list.add("6728c2b1e48e1123e882a6f0");
        list.add("6728c2b1e48e1123e882a6ff");
        list.add("6728c2b2e48e1123e882aa0b");
        list.add("6728c2b2e48e1123e882aa2a");
        list.add("6728c2b3e48e1123e882aabc");
        list.add("6728c2b3e48e1123e882abfc");
        list.add("6728c2b3e48e1123e882ac27");
        list.add("6728c2b3e48e1123e882ac28");
        list.add("6728c2b3e48e1123e882ac5f");
        list.add("6728c2b4e48e1123e882ad2a");
        list.add("6728c2b4e48e1123e882ad2e");
        list.add("6728c2b5e48e1123e882aeaf");
        list.add("6728c2b6e48e1123e882b0f0");
        list.add("6728c2b6e48e1123e882b10e");
        list.add("6728c2b6e48e1123e882b2d5");
        list.add("6728c2b7e48e1123e882b3cb");
        list.add("6728c2b7e48e1123e882b446");
        list.add("6728c2d1e48e1123e882b7a1");
        list.add("6728c2d1e48e1123e882b801");
        list.add("6728c2d3e48e1123e882bc9d");
        list.add("6728c2d4e48e1123e882be78");
        list.add("6728c2d5e48e1123e882bf50");
        list.add("6728c2d5e48e1123e882bfd8");
        list.add("6728c2d5e48e1123e882c05f");
        list.add("6728c2d5e48e1123e882c0b5");
        list.add("6728c2d5e48e1123e882c166");
        list.add("6728c2d6e48e1123e882c210");
        list.add("6728c2d6e48e1123e882c2fa");
        list.add("6728c2d6e48e1123e882c309");
        list.add("6728c2d6e48e1123e882c335");
        list.add("6728c2d6e48e1123e882c337");
        list.add("6728c2d7e48e1123e882c4a9");
        list.add("6728c2d7e48e1123e882c4f5");
        list.add("6728c2d8e48e1123e882c614");
        list.add("6728c2d8e48e1123e882c6fc");
        list.add("6728c2d8e48e1123e882c73c");
        list.add("6728c2d8e48e1123e882c75d");
        list.add("6728c2d9e48e1123e882c93d");
        list.add("6728c2d9e48e1123e882c94d");
        list.add("6728c2d9e48e1123e882ca84");
        list.add("6728c2dae48e1123e882cab8");
        list.add("6728c2dae48e1123e882cae1");
        list.add("6728c2dae48e1123e882ccd0");
        list.add("6728c2dbe48e1123e882cf13");
        list.add("6728c2dce48e1123e882d046");
        list.add("6728c2dce48e1123e882d157");
        list.add("6728c2dde48e1123e882d1f4");
        list.add("6728c2dde48e1123e882d253");
        list.add("6728c2dde48e1123e882d297");
        list.add("6728c2dee48e1123e882d42d");
        list.add("6728c2dfe48e1123e882d75f");
        list.add("6728c2e0e48e1123e882d9a7");
        list.add("6728c2e0e48e1123e882da02");
        list.add("6728c2e0e48e1123e882da48");
        list.add("6728c2e0e48e1123e882da51");
        list.add("6728c2e1e48e1123e882db13");
        list.add("6728c2fbe48e1123e882dba1");
        list.add("6728c2fbe48e1123e882dc09");
        list.add("6728c2fbe48e1123e882dca3");
        list.add("6728c2fce48e1123e882ddae");
        list.add("6728c2fce48e1123e882ddfb");
        list.add("6728c2fce48e1123e882de10");
        list.add("6728c2fce48e1123e882de1b");
        list.add("6728c2fce48e1123e882de79");
        list.add("6728c2fce48e1123e882de93");
        list.add("6728c2fce48e1123e882deb0");
        list.add("6728c2fde48e1123e882e023");
        list.add("6728c2fde48e1123e882e02c");
        list.add("6728c2fde48e1123e882e13a");
        list.add("6728c2fee48e1123e882e20f");
        list.add("6728c2fee48e1123e882e2a4");
        list.add("6728c2fee48e1123e882e2ad");
        list.add("6728c2fee48e1123e882e364");
        list.add("6728c2ffe48e1123e882e4a1");
        list.add("6728c2ffe48e1123e882e4ba");
        list.add("6728c2ffe48e1123e882e4ff");
        list.add("6728c2ffe48e1123e882e60b");
        list.add("6728c2ffe48e1123e882e635");
        list.add("6728c2ffe48e1123e882e647");
        list.add("6728c300e48e1123e882e6db");
        list.add("6728c300e48e1123e882e73a");
        list.add("6728c300e48e1123e882e7d5");
        list.add("6728c300e48e1123e882e804");
        list.add("6728c300e48e1123e882e814");
        list.add("6728c300e48e1123e882e82e");
        list.add("6728c301e48e1123e882e8ea");
        list.add("6728c301e48e1123e882e96c");
        list.add("6728c301e48e1123e882e9b7");
        list.add("6728c301e48e1123e882e9fb");
        list.add("6728c301e48e1123e882ea16");
        list.add("6728c301e48e1123e882eaf5");
        list.add("6728c302e48e1123e882ed2b");
        list.add("6728c302e48e1123e882ed47");
        list.add("6728c303e48e1123e882edb1");
        list.add("6728c303e48e1123e882eddf");
        list.add("6728c303e48e1123e882ee23");
        list.add("6728c303e48e1123e882ef9c");
        list.add("6728c304e48e1123e882f0b5");
        list.add("6728c304e48e1123e882f11c");
        list.add("6728c304e48e1123e882f137");
        list.add("6728c304e48e1123e882f151");
        list.add("6728c304e48e1123e882f153");
        list.add("6728c304e48e1123e882f162");
        list.add("6728c304e48e1123e882f1ae");
        list.add("6728c304e48e1123e882f1d2");
        list.add("6728c304e48e1123e882f217");
        list.add("6728c305e48e1123e882f21f");
        list.add("6728c305e48e1123e882f419");
        list.add("6728c306e48e1123e882f4ed");
        list.add("6728c306e48e1123e882f59a");
        list.add("6728c306e48e1123e882f61a");
        list.add("6728c30ae48e1123e882ff44");
        list.add("6728c30be48e1123e88301ab");
        list.add("6728c30be48e1123e8830222");
        list.add("6728c30ce48e1123e8830264");
        list.add("6728c30ce48e1123e8830294");
        list.add("6728c31de48e1123e88302eb");
        list.add("6728c31de48e1123e8830331");
        list.add("6728c31de48e1123e8830341");
        list.add("6728c31ee48e1123e8830695");
        list.add("6728c31fe48e1123e8830733");
        list.add("6728c31fe48e1123e8830785");
        list.add("6728c31fe48e1123e8830833");
        list.add("6728c320e48e1123e8830a74");
        list.add("6728c320e48e1123e8830a8e");
        list.add("6728c321e48e1123e8830c5a");
        list.add("6728c322e48e1123e8830e25");
        list.add("6728c322e48e1123e8830eac");
        list.add("6728c322e48e1123e8830f00");
        list.add("6728c323e48e1123e88311ad");
        list.add("6728c323e48e1123e8831203");
        list.add("6728c324e48e1123e8831388");
        list.add("6728c324e48e1123e8831455");
        list.add("6728c325e48e1123e883154e");
        list.add("6728c325e48e1123e88315a4");
        list.add("6728c326e48e1123e8831868");
        list.add("6728c326e48e1123e883186d");
        list.add("6728c326e48e1123e883189e");
        list.add("6728c326e48e1123e88318ef");
        list.add("6728c326e48e1123e8831934");
        list.add("6728c326e48e1123e8831947");
        list.add("6728c327e48e1123e88319cd");
        list.add("6728c327e48e1123e88319e6");
        list.add("6728c327e48e1123e8831a41");
        list.add("6728c327e48e1123e8831ab1");
        list.add("6728c327e48e1123e8831aed");
        list.add("6728c327e48e1123e8831b06");
        list.add("6728c327e48e1123e8831b8f");
        list.add("6728c328e48e1123e8831bd3");
        list.add("6728c328e48e1123e8831c94");
        list.add("6728c328e48e1123e8831cc6");
        list.add("6728c328e48e1123e8831d1c");
        list.add("6728c329e48e1123e8831fcc");
        list.add("6728c329e48e1123e8832005");
        list.add("6728c32ae48e1123e8832082");
        list.add("6728c32ae48e1123e8832133");
        list.add("6728c32ae48e1123e8832186");
        list.add("6728c32ae48e1123e88321e6");
        list.add("6728c32be48e1123e8832371");
        list.add("6728c32be48e1123e883243a");
        list.add("6728c32be48e1123e8832462");
        list.add("6728c32be48e1123e883246c");
        list.add("6728c32ce48e1123e88324e7");
        list.add("6728c32ce48e1123e8832597");
        list.add("6728c32ce48e1123e883259e");
        list.add("6728c32ce48e1123e8832680");
        list.add("6728c32ce48e1123e88326c8");
        list.add("6728c355e48e1123e8832ad7");
        list.add("6728c356e48e1123e8832bb8");
        list.add("6728c356e48e1123e8832cd6");
        list.add("6728c358e48e1123e8832ff8");
        list.add("6728c358e48e1123e8832ff9");
        list.add("6728c358e48e1123e8833018");
        list.add("6728c358e48e1123e883304a");
        list.add("6728c358e48e1123e8833086");
        list.add("6728c358e48e1123e8833122");
        list.add("6728c358e48e1123e88331da");
        list.add("6728c359e48e1123e883330e");
        list.add("6728c35ae48e1123e88335e5");
        list.add("6728c35ae48e1123e8833695");
        list.add("6728c35be48e1123e88336fb");
        list.add("6728c35be48e1123e883383e");
        list.add("6728c35be48e1123e88338ef");
        list.add("6728c35be48e1123e88338f1");
        list.add("6728c35ce48e1123e8833aec");
        list.add("6728c35de48e1123e8833bb8");
        list.add("6728c35de48e1123e8833c17");
        list.add("6728c35de48e1123e8833d59");
        list.add("6728c35de48e1123e8833d5a");
        list.add("6728c35de48e1123e8833d9c");
        list.add("6728c35de48e1123e8833dc2");
        list.add("6728c35ee48e1123e8833f01");
        list.add("6728c35ee48e1123e8833fae");
        list.add("6728c35fe48e1123e8834038");
        list.add("6728c35fe48e1123e883408e");
        list.add("6728c35fe48e1123e8834206");
        list.add("6728c360e48e1123e88342d7");
        list.add("6728c360e48e1123e8834318");
        list.add("6728c360e48e1123e8834368");
        list.add("6728c360e48e1123e883444a");
        list.add("6728c361e48e1123e88344e8");
        list.add("6728c361e48e1123e88345de");
        list.add("6728c362e48e1123e8834747");
        list.add("6728c362e48e1123e8834791");
        list.add("6728c362e48e1123e8834796");
        list.add("6728c362e48e1123e88348aa");
        list.add("6728c362e48e1123e88348d1");
        list.add("6728c362e48e1123e8834919");
        list.add("6728c362e48e1123e8834960");
        list.add("6728c363e48e1123e88349e6");
        list.add("6728c363e48e1123e8834a79");
        list.add("6728c363e48e1123e8834a87");
        list.add("6728c363e48e1123e8834b26");
        list.add("6728c363e48e1123e8834bab");
        list.add("6728c363e48e1123e8834bc9");
        list.add("6728c364e48e1123e8834d7d");
        list.add("6728c365e48e1123e8834e54");
        list.add("6728c365e48e1123e8834ecf");
        list.add("6728c365e48e1123e8834f7a");
        list.add("6728c365e48e1123e883506d");
        list.add("6728c377e48e1123e88350cc");
        list.add("6728c377e48e1123e8835134");
        list.add("6728c377e48e1123e8835151");
        list.add("6728c378e48e1123e8835338");
        list.add("6728c378e48e1123e88353a5");
        list.add("6728c379e48e1123e88354f8");
        list.add("6728c379e48e1123e88355d2");
        list.add("6728c379e48e1123e883565f");
        list.add("6728c379e48e1123e88356d7");
        list.add("6728c379e48e1123e88356dc");
        list.add("6728c37be48e1123e8835a2a");
        list.add("6728c37be48e1123e8835a2f");
        list.add("6728c37ce48e1123e8835c30");
        list.add("6728c37ce48e1123e8835c54");
        list.add("6728c37ce48e1123e8835c70");
        list.add("6728c37ce48e1123e8835cf8");
        list.add("6728c37ce48e1123e8835d62");
        list.add("6728c37de48e1123e8835e83");
        list.add("6728c37de48e1123e8835e96");
        list.add("6728c37de48e1123e8835edc");
        list.add("6728c37de48e1123e8835f41");
        list.add("6728c37de48e1123e8835fbc");
        list.add("6728c37ee48e1123e88361ff");
        list.add("6728c37fe48e1123e8836367");
        list.add("6728c37fe48e1123e8836394");
        list.add("6728c37fe48e1123e88363bd");
        list.add("6728c380e48e1123e8836534");
        list.add("6728c380e48e1123e88366a9");
        list.add("6728c384e48e1123e8836ed8");
        list.add("6728c384e48e1123e8836f4e");
        list.add("6728c384e48e1123e8837054");
        list.add("6728c385e48e1123e883728b");
        list.add("6728c385e48e1123e883728d");
        list.add("6728c385e48e1123e88372be");
        list.add("6728c386e48e1123e88373d0");
        list.add("6728c386e48e1123e8837408");
        list.add("6728c386e48e1123e883744e");
        list.add("6728c39de48e1123e88377cf");
        list.add("6728c39de48e1123e8837972");
        list.add("6728c39de48e1123e883798f");
        list.add("6728c39de48e1123e88379d8");
        list.add("6728c39de48e1123e8837a0a");
        list.add("6728c39ee48e1123e8837b3c");
        list.add("6728c39ee48e1123e8837c06");
        list.add("6728c39ee48e1123e8837c21");
        list.add("6728c39fe48e1123e8837cb5");
        list.add("6728c39fe48e1123e8837cde");
        list.add("6728c3a0e48e1123e8837eee");
        list.add("6728c3a0e48e1123e8837f09");
        list.add("6728c3a0e48e1123e8837fa1");
        list.add("6728c3a0e48e1123e8837ff3");
        list.add("6728c3a1e48e1123e8838141");
        list.add("6728c3a2e48e1123e883846c");
        list.add("6728c3a2e48e1123e883858e");
        list.add("6728c3a2e48e1123e88385a1");
        list.add("6728c3a3e48e1123e8838642");
        list.add("6728c3a3e48e1123e8838778");
        list.add("6728c3a3e48e1123e883878a");
        list.add("6728c3a4e48e1123e88388a6");
        list.add("6728c3a4e48e1123e88388b3");
        list.add("6728c3a4e48e1123e8838907");
        list.add("6728c3a4e48e1123e883894c");
        list.add("6728c3a4e48e1123e8838995");
        list.add("6728c3a5e48e1123e8838acd");
        list.add("6728c3a5e48e1123e8838af7");
        list.add("6728c3a6e48e1123e8838d26");
        list.add("6728c3a6e48e1123e8838d40");
        list.add("6728c3a6e48e1123e8838d81");
        list.add("6728c3a7e48e1123e8838e5b");
        list.add("672a10102c01e13ecf54cbb3");
        list.add("672a10102c01e13ecf54cd18");
        list.add("672a10642c01e13ecf54cdad");

        String periodId = "6721b05c081a1f04d15334cb";
        String companyId = "670e2f3836dca57de4a1ffd4";
        String projectId = "670e2f3836dca57de4a1ffd4";

        list.forEach(s ->  {
            System.out.println(s);
            Candidate candidate = this.candidateRepository.findById(new ObjectId(s))
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND));
            List<String> idList = new ArrayList<>();
            idList.add(candidate.get_id().toString());
            long count = this.removeBatch(companyId, projectId, periodId, candidate.getSubjectId().toString(),  idList);
            System.out.println(count);
        });

    }


    /**
     * 查找房间中是否存在考生Id List中的重叠人数
      * @param list 房间的考生列表
     * @param idList 操作的考生Id
     * @return 数量
     */
    private int getSameCount(List<Candidate> list, List<String> idList){
        List<String> roomCandidateIdList = list
                .stream()
                .map(Candidate::get_id)
                .map(ObjectId::toString)
                .toList();
        return idList
                .stream()
                .filter(roomCandidateIdList::contains)
                .toList()
                .size();
    }

    /**
     * 修改 periodRoom 的 candidateCount
     * @param periodRoomId 主键
     * @param count 人数
     */
    private void setPeriodRoomCandidateCount(ObjectId periodRoomId, int count){
        Query query = new Query(Criteria.where("_id").is(periodRoomId));
        Update update = new Update();
        update.set("candidateCount", count);
        mongoTemplate.updateFirst(query, update, PeriodRoom.class);
    }

    /**
     * 批量更新考生的Room Id
     * @param list 考生
     * @param roomId 房间 Id
     */
    private void setCandidateRoomId(List<Candidate> list, ObjectId roomId){
        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Candidate.class);
        for(Candidate candidate : list){
            Query query = new Query(Criteria.where("_id").is(candidate.get_id()));
            Update update = new Update()
                    .set("roomId", roomId);
            bulkOperations.updateOne(query, update);
        }
        if (!list.isEmpty()) {
            BulkWriteResult result = bulkOperations.execute();
            log.info(String.format("移出 房间 %s - %d - 人", roomId , result.getModifiedCount()));
        }
    }

    void handleCandidateCountInProjectAndSubject(String projectId, String periodId, String subjectId){

        Project project = this.projectRepository.findById(new ObjectId(projectId)).orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
        Period period = this.periodRepository.findById(new ObjectId(periodId)).orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));

        int p = this.candidateRepository.countByProjectId(new ObjectId(projectId));
        int s = this.candidateRepository.countBySubjectId(new ObjectId(subjectId));

        project.setCandidateCount(p);
        this.projectRepository.save(project);

        for(Subject subject : period.getSubjectList()){
            if(subject.get_id().toString().equals(subjectId)){
                subject.setCandidateCount(s);
            }
        }
        this.periodRepository.save(period);

        cacheService.deletePeriod(periodId);
    }

    void calculatePeriodRoomCandidateCount(String periodId, String roomId){
        int count = this.candidateRepository.countByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId));
        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .addCriteria(Criteria.where("roomId").is(new ObjectId(roomId)));
        Update update = new Update()
                .set("candidateCount", count);
        mongoTemplate.updateFirst(query, update, PeriodRoom.class);
    }

    @Override
    public void transferOut(String roomId, List<String> candidateIdList) {
        List<Candidate> list = this.candidateRepository.findBy_idIn(candidateIdList
                .stream()
                .map(ObjectId::new)
                .toList());
        Candidate transCandidate = list
                .stream()
                .filter(c -> c.getTransRoomId() != null)
                .findFirst()
                .orElse(null);
        if(transCandidate!= null){
            throw new ServiceException(ExceptionEnum.TRANSFER_ONLY_ONCE, String.format(", %s已经转过考场了",transCandidate.getFullName()) );
        }

        for(Candidate candidate : list){
            candidate.setTransRoomId(new ObjectId(roomId));
            this.candidateRepository.save(candidate);
        }
    }

    @Override
    public void transferIn(List<String> candidateIdList) {
        List<Candidate> list = this.candidateRepository.findBy_idIn(candidateIdList
                .stream()
                .map(ObjectId::new)
                .toList());
        ObjectId periodId = new ObjectId(list.get(0).getPeriodId().toString()),
                roomId = new ObjectId(list.get(0).getRoomId().toString()),
                exchangeRoomId = new ObjectId(list.get(0).getTransRoomId().toString());

        for(Candidate candidate : list){
            candidate.setRoomId(candidate.getTransRoomId());
            candidate.setSeatNum(null);
            candidate.setTransRoomId(null);
            this.candidateRepository.save(candidate);
        }

        PeriodRoom outPeriodRoom = this.periodRoomRepository.findByPeriodIdAndRoomId(periodId, roomId)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_ROOM_NOT_FOUND));
        outPeriodRoom.setCandidateCount(outPeriodRoom.getCandidateCount() - candidateIdList.size());
        outPeriodRoom.setCandidateArrivedCount(outPeriodRoom.getCandidateArrivedCount() - candidateIdList.size());

        PeriodRoom inPeriodRoom = this.periodRoomRepository.findByPeriodIdAndRoomId(periodId, exchangeRoomId)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_ROOM_NOT_FOUND));
        inPeriodRoom.setCandidateCount(inPeriodRoom.getCandidateCount() + candidateIdList.size());
        inPeriodRoom.setCandidateArrivedCount(inPeriodRoom.getCandidateArrivedCount() + candidateIdList.size());

        this.periodRoomRepository.save(outPeriodRoom);
        this.periodRoomRepository.save(inPeriodRoom);

        //考生转入后，重新调整座位号
        Project project = this.cacheService.getProject(outPeriodRoom.getProjectId().toString());
        if(project.getFixedPosition()){
            this.setCandidateSeatNum(periodId.toString(), exchangeRoomId.toString());
        }
    }

    @Override
    public void changeSeatNum(String candidateId, Integer seatNum) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(candidateId)));
        Update update = new Update()
                .set("seatNum", seatNum);
        mongoTemplate.updateFirst(query, update, Candidate.class);
    }

    @Override
    public List<Candidate> transferring(String periodId, String roomId) {
        return this.candidateRepository.findByPeriodIdAndTransRoomId(new ObjectId(periodId), new ObjectId(roomId));
    }
}
