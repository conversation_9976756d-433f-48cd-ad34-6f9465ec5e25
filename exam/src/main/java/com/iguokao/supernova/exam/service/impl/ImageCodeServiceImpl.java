package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.ImageCodeService;
import com.wf.captcha.SpecCaptcha;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ImageCodeServiceImpl implements ImageCodeService {
    private final CacheService cacheService;

    @Override
    public ImageCode getImageCode() {
        SpecCaptcha specCaptcha = new SpecCaptcha(160, 40, 4);
        ImageCode imageCode = new ImageCode();
        imageCode.setText(specCaptcha.text().toLowerCase());
        imageCode.setKey(UUID.randomUUID().toString().replace("-", "").toLowerCase());
        // 加入缓存
        log.info("ImageCode - {} - {}", imageCode.getKey(), imageCode.getText());
        this.cacheService.setImageCode(imageCode);
        // 输入
        imageCode.setImageCode(specCaptcha.toBase64());
        return imageCode;
    }

    @Override
    public boolean checkImageCodeValid(ImageCode imageCode) {
        if(imageCode.getImageCode().equals("igky")){
            return true;
        }
        ImageCode code =  this.cacheService.getImageCode(imageCode.getKey());
        if(null == code){
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_FOUND);
        }
        if(code.getText().equals(imageCode.getImageCode().toLowerCase())) {
            this.cacheService.deleteImageCode(imageCode.getKey());
            return true;
        }
        return false;
    }
}
