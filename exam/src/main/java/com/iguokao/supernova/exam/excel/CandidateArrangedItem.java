package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.exam.document.Candidate;
import com.iguokao.supernova.exam.document.PeriodRoom;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.document.Site;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

@Getter
@Setter
public class CandidateArrangedItem {
    private String candidateId;
    private String siteId = "";
    private String roomId = "";
    private String periodId;
    private String fullName;
    private String city;
    private String siteName;
    private String roomNum;
    private String roomName;
    private String roomAddress;
    private String seatNum;
    private Integer periodCount;
    private String idCardNum;
    private Long num;
    private String subjectName;
    private String email;
    private String mobile;
    private String custom1;
    private String custom2;
    private String custom3;

    public static CandidateArrangedItem of(Candidate obj, Site site, Room room, String subjectName, int periodCount, PeriodRoom periodRoom){
        if(obj == null){
            return null;
        }
        CandidateArrangedItem res = new CandidateArrangedItem();
        BeanUtils.copyProperties(obj, res);
        if(site != null){
            res.setSiteName(site.getName());
            res.setSiteId(obj.getSiteId().toString());
        }
        if(room != null){
            res.setRoomName(room.getName());
            res.setRoomAddress(room.getAddress());
            res.setRoomNum(String.format("第%d考场", periodRoom.getRoomIndex() + 1));
            res.setRoomId(obj.getRoomId().toString());
        }

        res.setSeatNum(obj.getSeatNum() != null ? obj.getSeatNum().toString() : null);
        res.setSubjectName(subjectName);
        res.setPeriodCount(periodCount);
        res.setCandidateId(obj.get_id().toString());

        if(obj.getPeriodId() != null){
            res.setPeriodId(obj.getPeriodId().toString());
        }
        return res;
    }
}
