package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.PeriodRoom;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.document.Site;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RoomResponse {

    @Schema(description = "考点Id")
    private String siteId;

    @Schema(description = "考场Id")
    private String roomId;

    @Schema(description = "考场名称")
    private String name;

    @Schema(description = "考场编号")
    private String roomNum;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "容量")
    private Integer capacity;

    @Schema(description = "可用")
    private Integer available;

    @Schema(description = "房间登录码")
    private String loginCode;

    @Schema(description = "云机房")
    private Boolean cloudSystem;

    @Schema(description = "本地监控")
    private Boolean localCamera;

    @Schema(description = "是否有挡板")
    private Boolean blocked;

    @Schema(description = "PC摄像头")
    private Integer pcCameraCount;

    public static RoomResponse of(Room obj, PeriodRoom periodRoom, boolean showLoginKey){
        if(obj==null){
            return null;
        }
        RoomResponse res = new RoomResponse();
        BeanUtils.copyProperties(obj, res);
        res.setSiteId(obj.getSiteId().toString());
        res.setRoomId(obj.get_id().toString());
        if(null != periodRoom){
            res.setRoomNum(String.format("第%d考场", periodRoom.getRoomIndex() + 1));
        }
        if(!showLoginKey){
            res.setLoginCode(null);
        }
        return res;
    }

    public static List<RoomResponse> of(List<Room> list, boolean showLoginKey){
        if(list==null){
            return new ArrayList<>();
        }
        List<RoomResponse> res = new ArrayList<>();
        for(Room obj : list){
            res.add(of(obj,null, showLoginKey));
        }
        return res;
    }
}
