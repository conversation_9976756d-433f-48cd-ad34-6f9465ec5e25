package com.iguokao.supernova.exam.controller;

import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.response.AttachmentResponse;
import com.iguokao.supernova.exam.response.ProctorProjectResponse;
import com.iguokao.supernova.exam.response.ProctorRoomResponse;
import com.iguokao.supernova.exam.response.SiteResponse;
import com.iguokao.supernova.exam.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/proctor")
@RequiredArgsConstructor
public class ProctorController {

    private final ProctorService proctorService;
    private final SiteService siteService;
    private final ProjectService projectService;
    private final PeriodService periodService;
    private final RoomService roomService;
    private final PeriodRoomService periodRoomService;

    @GetMapping("/project/{uuid}")
    @Operation(summary = "根据督考官唯一码获取考试相关信息")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = AttachmentResponse.class))))
    public RestResponse<ProctorProjectResponse> projectInfo(@PathVariable String uuid) {
        Proctor proctor = this.proctorService.getByUuid(uuid);
        Project project = this.projectService.getById(proctor.getProjectId().toString());
        List<Period> periodList = this.periodService.getByProjectId(project.get_id().toString());
        return RestResponse.success(ProctorProjectResponse.of(project,periodList));
    }

    @GetMapping("/site/{uuid}")
    @Operation(summary = "考点详情-根据督考官唯一码")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = SiteResponse.class))))
    public RestResponse<SiteResponse> siteInfo(@PathVariable String uuid) {
        Proctor proctor = this.proctorService.getByUuid(uuid);
        Site site = this.siteService.getById(proctor.getSiteId().toString());
        return RestResponse.success(SiteResponse.of(site));
    }

    @GetMapping("/room/{uuid}/{periodId}")
    @Operation(summary = "督考官巡视考点的考场列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = SiteResponse.class))))
    public RestResponse<List<ProctorRoomResponse>> room(@PathVariable String uuid, @PathVariable String periodId) {
        Proctor proctor = this.proctorService.getByUuid(uuid);
        List<ProctorRoomResponse> proctorRoomResponseList = new ArrayList<>();
        List<PeriodRoom> periodRoomList = this.periodRoomService.getRoomListByPeriodId(periodId,proctor.getSiteId().toString());
        for(PeriodRoom periodRoom : periodRoomList){
            Room room = this.roomService.getById(periodRoom.getRoomId().toString());
            proctorRoomResponseList.add(ProctorRoomResponse.of(periodRoom,room));
        }
        return RestResponse.success(proctorRoomResponseList);
    }
}

