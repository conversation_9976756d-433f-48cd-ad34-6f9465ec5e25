package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.common.document.QuestionGroupOption;
import com.iguokao.supernova.common.document.QuestionOption;
import com.iguokao.supernova.exam.document.Question;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class QuestionResponse {

    @Schema(description = "试题Id")
    private String questionId;

    @Schema(description = "类型")
    private Integer type;

    @Schema(description = "题干")
    private String body;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "题目分数")
    private Double questionScore;

    @Schema(description = "子题分数")
    private List<Double> groupScore = new ArrayList<>();

    @Schema(description = "解析")
    private String analysis;

    @Schema(description = "记分规则 0 严格积分，1是常规积分 2宽松计分")
    private Integer scoreType;

    @Schema(description = "正确答案")
    private List<String> correctValue = new ArrayList<>();

    @Schema(description = "选项")
    private List<QuestionOption> optionList = new ArrayList<>();

    @Schema(description = "复合体小题")
    private List<QuestionGroupOption> groupOptionList = new ArrayList<>();

    @Schema(description = "字数限制")
    private Integer wordLimit;

    public static QuestionResponse of(Question obj){
        if(obj==null){
            return null;
        }
        QuestionResponse res = new QuestionResponse();
        BeanUtils.copyProperties(obj, res);
        res.setQuestionId(obj.get_id().toString());
        return res;
    }

    public static List<QuestionResponse> of(List<Question> list){
        if(list.isEmpty()){
            return new ArrayList<>();
        }
        List<QuestionResponse> res = new ArrayList<>();
        for(Question obj : list){
            res.add(of(obj));
        }
        return res;
    }
}


