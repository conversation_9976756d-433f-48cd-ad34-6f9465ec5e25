package com.iguokao.supernova.exam.response;


import com.iguokao.supernova.exam.document.RtcSig;
import com.iguokao.supernova.exam.document.SendInfo;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.Date;


@Getter
@Setter
public class SendInfoResponse {
    private String batchId;
    private Integer state;
    private Date updatedAt;

    public static SendInfoResponse of(SendInfo obj){
        if(null == obj){
            return null;
        }
        SendInfoResponse vo = new SendInfoResponse();
        BeanUtils.copyProperties(obj, vo);
        return vo;
    }
}