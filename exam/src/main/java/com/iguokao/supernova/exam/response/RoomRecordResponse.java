package com.iguokao.supernova.exam.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.exam.document.PeriodRoom;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.document.Site;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RoomRecordResponse {

    private String siteId;


    private String roomId;

    private String periodId;

    private String roomName; // 主机位推流ID
    private String city; // 主机位推流ID
    private String siteName; // 主机位推流ID

    private String roomNum; // 考场编号

    private Integer streamRoomNumber; // 推流房间号
    private List<String> primaryVideoList; // 主机位推流ID
    private List<String> secondaryVideoList; // 次机位推流ID

    public static RoomRecordResponse of(PeriodRoom obj, Site site, Room room){
        if(obj == null  || site == null || room == null){
            return null;
        }
        RoomRecordResponse res = new RoomRecordResponse();
        BeanUtils.copyProperties(obj, res);
        res.setSiteId(obj.getSiteId().toString());
        res.setRoomId(obj.get_id().toString());
        res.setPeriodId(obj.getPeriodId().toString());

        res.setRoomName(room.getName());
        res.setSiteName(site.getName());
        res.setCity(site.getCity());
        res.setRoomNum(String.format("第%d考场", obj.getRoomIndex() + 1));
        res.setPrimaryVideoList(obj.getPrimaryVideoSet().stream().toList());
        res.setSecondaryVideoList(obj.getSecondaryVideoSet().stream().toList());

        return res;
    }

    public static List<RoomRecordResponse> of(List<PeriodRoom> list, List<Site> siteList, List<Room> roomList){
        if(list.isEmpty()  || siteList.isEmpty() || roomList.isEmpty()){
            return new ArrayList<>();
        }
        List<RoomRecordResponse> res = new ArrayList<>();
        for(PeriodRoom pr : list){
            Site site = siteList
                    .stream()
                    .filter(s -> pr.getSiteId().equals(s.get_id()))
                    .findFirst()
                    .orElse(null);
            Room room = roomList
                    .stream()
                    .filter(r -> pr.getRoomId().equals(r.get_id()))
                    .findFirst()
                    .orElse(null);
            RoomRecordResponse obj = of(pr, site, room);
            if(obj != null) {
                res.add(obj);
            }
        }
        return res;
    }

}
