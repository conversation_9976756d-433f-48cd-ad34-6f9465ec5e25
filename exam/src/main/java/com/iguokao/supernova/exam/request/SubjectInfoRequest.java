package com.iguokao.supernova.exam.request;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class SubjectInfoRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段id")
    @Length(min = 24, max = 24, message = "24位id")
    private String periodId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "科目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String subjectId;

}