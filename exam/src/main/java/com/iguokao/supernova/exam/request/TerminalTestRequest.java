package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class TerminalTestRequest {
    @NotNull(message = "项目id不能为空")
    @Schema(description = "项目id")
    private String projectId;


    @Min(value = 2, message = "类型只能是 2,环境检测 3,摄像头测试 4,试考")
    @Max(value = 4, message = "类型只能是 2,环境检测 3,摄像头测试 4,试考")
    @Schema(description = "类型 2,环境检测 3,摄像头测试 4,试考")
    private Integer type;

    @Schema(description = "测试是否通过")
    private Boolean passed;

    @Schema(description = "错误信息")
    private String error;
}
