package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.exam.document.Agent;
import com.iguokao.supernova.exam.document.Site;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface SiteService {
    String add(Site site);
    void update(Site site);
    void setPriority(String siteId, Integer priority);
    void updateOperatorId(String siteId, String operatorId);

    void remove(String siteId);
    Site getById(String siteId);
    List<Site> getByIdList(List<ObjectId> list);
    Tuple2<List<Site>, Integer> getPage(String city, String siteName, Pageable pageable);

    boolean lock(String siteId);

    List<Site> getAll();

    void addAgent(String siteId, Agent agent);
    void updateAgent(String siteId, Agent agent);
    void removeAgent(String siteId, String loginCode);

}
