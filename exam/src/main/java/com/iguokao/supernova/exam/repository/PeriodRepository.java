package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Period;
import com.iguokao.supernova.exam.document.Room;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface PeriodRepository extends MongoRepository<Period, ObjectId> {
    int countByProjectIdAndName(ObjectId projectId, String name);
    int countByProjectId(ObjectId projectId);

    Optional<Period> findBySubjectList__id(ObjectId subjectId);
    List<Period> findByProjectId(ObjectId projectId);

    List<Period> findByProjectIdIn(List<ObjectId> list);

    List<Period> findBy_idIn(List<ObjectId> list);

}
