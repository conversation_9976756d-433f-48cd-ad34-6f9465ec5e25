package com.iguokao.supernova.exam.controller;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.enums.CredentialCategoryEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.RegistrationRemote;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.response.ImportCandidateResponse;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.CandidateConfirmStateEnum;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.excel.*;
import com.iguokao.supernova.exam.request.*;
import com.iguokao.supernova.exam.response.*;
import com.iguokao.supernova.exam.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xwpf.usermodel.*;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.view.RedirectView;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/v1/candidate")
@RequiredArgsConstructor
public class CandidateController {

    private final CandidateService candidateService;
    private final PeriodService periodService;
    private final ProjectService projectService;
    private final RoomService roomService;
    private final CacheService cacheService;
    private final PeriodRoomService periodRoomService;
    private final RegistrationRemote registrationRemote;

    @Value(value = "${app.admission-url}")
    private String admissionUrl;

    @PostMapping("/add")
    @Operation(summary = "添加考生")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@Validated @RequestBody CandidateAddRequest request) {
        Candidate candidate = new Candidate();
        BeanUtils.copyProperties(request, candidate);
        candidate.setCompanyId(new ObjectId(request.getCompanyId()));
        candidate.setProjectId(new ObjectId(request.getProjectId()));
        candidate.setPeriodId(new ObjectId(request.getPeriodId()));
        candidate.setSubjectId(new ObjectId(request.getSubjectId()));
        this.candidateService.add(candidate);
        return RestResponse.success();
    }

    @PostMapping("/registration/import")
    @Operation(summary = "报名系统导入考生")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<Integer> importCandidate(@Validated @RequestBody ImportCandidateRequest request) {
        com.iguokao.supernova.common.request.ImportCandidateRequest r = new com.iguokao.supernova.common.request.ImportCandidateRequest();
        r.setType(request.getRegistrationImportType());
        r.setSubjectId(request.getRegistrationSubjectId());
        RestResponse<List<ImportCandidateResponse>> res = registrationRemote.importCandidate(r);
        int total = 0;
        if(res.getCode() == 0){
            if(res.getData().isEmpty()){
                return RestResponse.success(total);
            }
            List<Candidate> list = new ArrayList<>();
            res.getData().forEach(c -> {
                Candidate candidate = new Candidate();
                BeanUtils.copyProperties(c, candidate);
                candidate.setCompanyId(new ObjectId(request.getCompanyId()));
                candidate.setProjectId(new ObjectId(request.getProjectId()));
                candidate.setPeriodId(new ObjectId(request.getPeriodId()));
                candidate.setSubjectId(new ObjectId(request.getSubjectId()));
                list.add(candidate);
            });
            total = this.candidateService.addAll(list);
        } else {
            log.error("{} - {}", res.getCode(), res.getMessage());
            throw new ServiceException(BaseExceptionEnum.REMOTE_COMMUNICATION_FAILED);
        }
        return RestResponse.success(total);
    }

    @GetMapping("/info/{candidateId}")
    @Operation(summary = "考生详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<CandidateResponse> info(@PathVariable String candidateId) {
        Candidate candidate = this.candidateService.getById(candidateId);
        return RestResponse.success(CandidateResponse.of(candidate));
    }

    @PostMapping("/edit")
    @Operation(summary = "编辑考生")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> edit(@Validated @RequestBody CandidateUpdateRequest request) {
        Candidate candidate = this.candidateService.getById(request.getCandidateId());
        BeanUtils.copyProperties(request, candidate);
        this.candidateService.update(candidate);
        return RestResponse.success();
    }

    @PostMapping("/remove")
    @Operation(summary = "删除")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@Validated @RequestBody CandidateDeleteRequest request) {
        Long deleteCount = this.candidateService.removeBatch(request.getCompanyId(),request.getProjectId(),
                request.getPeriodId(),request.getSubjectId(),request.getCandidateIdList());
        return RestResponse.success(deleteCount.toString());
    }

    @PostMapping("/page")
    @Operation(summary = "考生列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageCandidateResponse.class)))
    public RestResponse<PageResponse<CandidateResponse>> list(@Validated @RequestBody CandidatePageRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Candidate>, Integer> page = this.candidateService.getPage(request.getCompanyId(),request.getProjectId(),
                request.getSubjectId(),
                request.getFullName(),
                request.getMobile(),
                request.getEmailState(),
                request.getSmsState(),
                request.getConfirmState(),
                request.getState(),
                request.getArranged(),
                request.getAvatarNull(),
                pageable);
        List<CandidateResponse> res = CandidateResponse.of(page.first());
        res.forEach(c -> {

            int startIndex;
            int endIndex;
            int length;

            if(c.getIdCardType().equals(CredentialCategoryEnum.ID_CARD.getCode())){
                startIndex = 6;
                endIndex = 12;
            }
            else {
                startIndex = 3;
                endIndex = c.getIdCardNum().length() - 2;
            }
            length = endIndex - startIndex;
            StringBuilder sb = new StringBuilder(c.getIdCardNum());
            sb.replace(startIndex, endIndex, "X".repeat(length));
            String result = sb.toString();
            c.setIdCardNum(result);
        });
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }

    @GetMapping("/room/{periodId}/{roomId}")
    @Operation(summary = "房间 考生列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = CandidateResponse.class))))
    public RestResponse<List<CandidateResponse>> roomCandidateList(@PathVariable String periodId, @PathVariable String roomId) {
        List<Candidate> list = this.candidateService.getRoomCandidateList(periodId, roomId);
        return RestResponse.success(CandidateResponse.of(list));
    }

    @RequestMapping(value = "/import/{periodId}/{subjectId}", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "批量导入")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExcelErrResponse.class))))
    public RestResponse<List<ExcelErrResponse>>  importExcel(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId,
                                                             @PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String subjectId,
                                                             @RequestPart(value = "file") MultipartFile file) throws IOException {

        String lockKey = String.format("importCandidate_%s", subjectId);
        if(this.cacheService.getLock(lockKey) != null){
            throw new ServiceException(BaseExceptionEnum.IMPORT_PENDING);
        }
        this.cacheService.setLock(lockKey);

        List<ExcelErrResponse> errResponseList = new ArrayList<>();
        Period period = this.periodService.getBySubjectId(periodId, subjectId);
        if(period.getProjectId() == null){
            throw new ServiceException(ExceptionEnum.SUBJECT_UNABLE_IMPORT);
        }
        List<String> existsIdCards = this.candidateService.getExistsIdCardsInSubject(periodId,subjectId);
        List<Long> existsNum = this.candidateService.getExistsNumInSubject(periodId,subjectId);
        Project project = this.projectService.getById(period.getProjectId().toString());
        EasyExcel.read(file.getInputStream(), CandidateItem.class, new CandidateItemListener(project.getCompanyId().toString(),
                project.get_id().toString(), period.get_id().toString(),
                subjectId, existsIdCards, existsNum, errResponseList, candidateService)).sheet().doRead();

        this.cacheService.deleteLock(lockKey);
        return RestResponse.success(errResponseList);
    }

    @GetMapping("/period/site/{projectId}/{periodId}/{siteId}")
    @Operation(summary = "考点中的考生详情")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoomCandidateListResponse.class))))
    public RestResponse<List<RoomCandidateListResponse>> projectRoom(@PathVariable String projectId, @PathVariable String periodId, @PathVariable String siteId) {
        List<Candidate> list = this.candidateService.getPeriodSiteCandidateList(projectId, periodId, siteId);
        List<PeriodRoom> periodRoomList = this.periodRoomService.getRoomListByPeriodId(periodId, siteId);
        Map<ObjectId, List<Candidate>> map = list
                .stream()
                .collect(Collectors.groupingBy(Candidate::getRoomId));
        //List<Room> roomList = this.roomService.getByIdList(new ArrayList<>(map.keySet()));
        List<Room> roomList = this.roomService.getByIdList(periodRoomList.stream().map(PeriodRoom::getRoomId).toList());
        List<RoomCandidateListResponse> res = new ArrayList<>();
        roomList.forEach(room -> {
            List<Candidate> candidateList = list
                    .stream()
                    .filter(c-> c.getRoomId().equals(room.get_id()))
                    .toList();
            PeriodRoom periodRoom = periodRoomList
                    .stream()
                    .filter(pr -> pr.getRoomId().equals(room.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_ROOM_NOT_FOUND));
            RoomCandidateListResponse item = RoomCandidateListResponse.of(room, candidateList);
            if(periodRoom.getRoomIndex() != null){
                item.getRoom().setRoomNum(String.format("第%d考场", periodRoom.getRoomIndex() + 1));
            }
            res.add(item);
        });
        return RestResponse.success(res);
    }

    @PreAuthorize("hasAnyAuthority('ROLE_SITE', 'ROLE_OPERATOR', 'ROLE_SYS_ADMIN')")
    @GetMapping("/room/doc/{periodId}/{roomId}")
    @Operation(summary = "房间考生名单")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void roomCandidate(@PathVariable String periodId, @PathVariable String roomId, HttpServletResponse response) throws IOException {
        Room room = this.roomService.getById(roomId);
        Period period = this.cacheService.getPeriod(periodId);

        Project project = this.projectService.getById(period.getProjectId().toString());
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("考生名单_%s.docx",
                room.getAddress()), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".docx");

        XWPFDocument document = this.candidateService.getRoomCandidateDoc(project, period, room, periodId, roomId);
        OutputStream out = response.getOutputStream();
        document.write(out);
        out.close();
    }

    @GetMapping("/email/confirm/{candidateId}/{confirmState}")
    @Operation(summary = "考生确认")
    public RedirectView confirm(@PathVariable String candidateId, @PathVariable Integer confirmState) {
        this.candidateService.confirm(candidateId, confirmState);
        String redirectUrl = String.format("%s/confirm/result", admissionUrl);
        return new RedirectView(redirectUrl);
    }

    @GetMapping("/notification/{periodId}/{subjectId}")
    @Operation(summary = "考生确认通知统计")
    @ApiResponse(content = @Content(schema = @Schema(implementation = CandidateNotificationResponse.class)))
    public RestResponse<CandidateNotificationResponse> notification(@PathVariable String periodId, @PathVariable String subjectId) {
        CandidateNotificationResponse candidateNotificationResponse = new CandidateNotificationResponse();
        candidateNotificationResponse.setCandidateCount(this.candidateService.countBySubjectId(periodId,subjectId));
        candidateNotificationResponse.setNotifiedCount(this.candidateService.countNotifiedBySubjectId(periodId,subjectId));
        candidateNotificationResponse.setConfirmedCount(this.candidateService.countConfirmed(periodId,subjectId));
        candidateNotificationResponse.setUnconfirmedCount(this.candidateService.countUnconfirmed(periodId,subjectId));
        candidateNotificationResponse.setRefusedCount(this.candidateService.countRefused(periodId,subjectId));
        candidateNotificationResponse.setDownloadedCount(this.candidateService.countStateGreaterBySubjectId(periodId,subjectId, CandidateStateEnum.DOWNLOADED.getCode()));
        return RestResponse.success(candidateNotificationResponse);
    }

    @GetMapping("/confirm/excel/{periodId}")
    @Operation(summary = "下载考生确认状态表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void arrangeData(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId,
                            HttpServletResponse response) throws IOException {
        List<CandidateConfirmItem> list = this.candidateService.getConfirmStateByPeriodId(periodId);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("确认状态表_%s", periodId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        EasyExcel.write(response.getOutputStream())
                .sheet("确认状态")
                .head(CandidateConfirmExporter.head())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(CandidateConfirmExporter.data(list));
    }

    @PostMapping("/avatar/upload")
    @Operation(summary = "头像上传")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<List<String>> avatarUpload(@RequestBody CandidateAvatarUpdateRequest request) {
        List<String> res = this.candidateService.setAvatar(request.getSubjectId(), request.getIdList());
        return RestResponse.success(res);
    }

    @GetMapping("/avatar/count/{subjectId}")
    @Operation(summary = "科目头像统计")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<Integer> avatarCount(@PathVariable String subjectId) {
        int count = this.candidateService.avatarCount(subjectId);
        return RestResponse.success(count);
    }
}
