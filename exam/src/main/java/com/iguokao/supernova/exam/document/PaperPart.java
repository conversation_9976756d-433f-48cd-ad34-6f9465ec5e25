package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class PaperPart {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId partId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private List<ObjectId> questionIdList = new ArrayList<>();

    private List<List<Integer>> randomList = new ArrayList<>();
}
