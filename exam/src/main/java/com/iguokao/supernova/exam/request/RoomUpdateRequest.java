package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RoomUpdateRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考点Id")
    private String siteId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考场id")
    private String roomId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考场名称")
    private String name;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "容量")
    private Integer capacity;

    @Schema(description = "可用")
    private Integer available;

    @Schema(description = "云机房")
    private Boolean cloudSystem;

    @Schema(description = "本地监控")
    private Boolean localCamera;

    @Schema(description = "有挡板")
    private Boolean blocked;

    @Schema(description = "摄像头数量")
    private Integer pcCameraCount;
}
