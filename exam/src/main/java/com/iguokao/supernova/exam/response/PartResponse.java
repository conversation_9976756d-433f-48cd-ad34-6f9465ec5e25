package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class PartResponse {

    @Schema(description = "科目id")
    private String subjectId;

    @Schema(description = "小卷id")
    private String partId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "子卷分值")
    private Double partScore;

    @Schema(description = "子卷题目数量")
    private Integer partQuestionCount;

    @Schema(description = "试题乱序")
    private Boolean questionRandomized;

    @Schema(description = "选项乱序")
    private Boolean optionRandomized;

    @Schema(description = "题目列表")
    private List<QuestionResponse> questionList = new ArrayList<>();

    @Schema(description = "题目乱序列表")
    private List<List<Integer>> randomList = new ArrayList<>();

    public static PartResponse of(Part obj, List<Question> questionList, Paper paper){
        if(obj==null){
            return null;
        }
        PartResponse res = new PartResponse();
        BeanUtils.copyProperties(obj, res);
        res.setSubjectId(obj.getSubjectId().toString());
        res.setPartId(obj.get_id().toString());
        res.setPartQuestionCount(obj.getQuestionList().size());
        if(questionList != null){
            for(PartQuestion partQuestion : obj.getQuestionList()){
                questionList
                        .stream()
                        .filter(q -> q.get_id().equals(partQuestion.getQuestionId()))
                        .findFirst()
                        .ifPresent(q -> {
                            QuestionResponse qRes = QuestionResponse.of(q);
                            if(!partQuestion.getGroupScore().isEmpty()){
                                qRes.setGroupScore(partQuestion.getGroupScore());
                            }
                            qRes.setQuestionScore(partQuestion.getScore());
                            res.getQuestionList().add(qRes);
                        });

            }
        }
        if(paper != null){
            for(PaperPart pp : paper.getPaperPartList()){
                if(pp.getPartId().toString().equals(obj.get_id().toString())){
                    res.setRandomList(pp.getRandomList());
                    break;
                }
            }
        }
        return res;
    }

    public static List<PartResponse> of(List<Part> list, List<Question> questionList, Paper paper){
        if(list==null){
            return new ArrayList<>();
        }
        List<PartResponse> res = new ArrayList<>();
        for(Part part : list){
            res.add(of(part,questionList,paper));
        }
        return res;
    }
}

