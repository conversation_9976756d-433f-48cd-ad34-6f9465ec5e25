package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.InputItem;
import com.iguokao.supernova.exam.document.MonitorSignature;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class InputItemResponse {
    private String clsid;
    private String guid;
    private String name;

    public static InputItemResponse of(InputItem obj){
        if(obj==null){
            return null;
        }
        InputItemResponse res = new InputItemResponse();
        BeanUtils.copyProperties(obj, res);
        return res;
    }

    public static List<InputItemResponse> of(List<InputItem> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<InputItemResponse> res = new ArrayList<>();
        for(InputItem obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
