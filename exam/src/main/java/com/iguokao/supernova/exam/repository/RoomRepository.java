package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.document.Site;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface RoomRepository extends MongoRepository<Room, ObjectId> {
    int countBySiteId(ObjectId siteId);
    int countBySiteIdAndName(ObjectId siteId, String name);

    Optional<Room> findByLoginCode(String loginCode);
    List<Room> findBy_idIn(List<ObjectId> list);
    List<Room> findBySiteId(ObjectId siteId);
}
