package com.iguokao.supernova.exam.request;

import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class QuestionPageRequest extends PageRequest {

    @Schema(description = "公司id")
    @Length(min = 24, max = 24, message = "24位id")
    private String companyId;

    @Schema(description = "题目类型")
    private Integer type;

    @Schema(description = "试题备注")
    private String note;

    @Schema(description = "题干")
    private String body;

    @Schema(description = "小卷id")
    private String partId;

}
