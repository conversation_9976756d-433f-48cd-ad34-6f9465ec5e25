package com.iguokao.supernova.exam.document;

import com.iguokao.supernova.exam.excel.CandidateArrangeManualItem;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

@Getter
@Setter
public class CandidateManualArrangeItem {
    private String idCardNum;
    private ObjectId siteId;
    private ObjectId roomId;
    private Integer seatNum;

    public static CandidateManualArrangeItem of(CandidateArrangeManualItem obj){
        if(obj == null){
            return null;
        }
        CandidateManualArrangeItem res = new CandidateManualArrangeItem();
        BeanUtils.copyProperties(obj, res);
        if(obj.getSiteId() != null){
            res.setSiteId(new ObjectId(obj.getSiteId()));
        }
        if(obj.getRoomId() != null){
            res.setRoomId(new ObjectId(obj.getRoomId()));
        }
        return res;
    }
}
