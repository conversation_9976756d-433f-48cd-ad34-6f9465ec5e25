package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class QuestionUpdateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "题目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String questionId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "题干")
    @NotBlank(message = "题干不能为空")
    private String body;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "试题总分")
    private Double questionScore;

    @Schema(description = "试题备注")
    private String note;

    @Schema(description = "答案解析")
    private String analysis;

    @Schema(description = "积分规则")
    @Min(value = 1, message = "积分规则错误")
    @Max(value = 2, message = "积分规则错误")
    private Integer scoreType;

    @Schema(description = "正确答案")
    private List<String> correctValue;

    @Schema(description = "选择题选项")
    private List<QuestionOptionRequest> optionList;

    @Schema(description = "复合题选项")
    private List<QuestionGroupOptionRequest> groupOptionList;

    @Schema(description = "字数限制")
    private Integer wordLimit;
}
