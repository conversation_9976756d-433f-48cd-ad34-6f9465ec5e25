package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.*;

@Getter
@Setter
public class RoomProjectTestResponse {
    @Schema(description = "房间 ID")
    private String roomId;

    @Schema(description = "项目 ID")
    private String projectId;

    @Schema(description = "项目 名称")
    private String name;

    @Schema(description = "项目 状态 0 创建, 2 考场确认, 3考生编排完成, 4进行中, 5已结束")
    private Integer state;

    @Schema(description = "最大参考人数")
    private Integer candidateCount;

    @Schema(description = "时段列表")
    List<PeriodResponse> periodList;

    @Schema(description = "测试结果")
    List<ActionResponse> testActionList;

    @Schema(description = "客户端版本")
    private String clientVersion;

    @Schema(description = "输入法")
    private List<InputItemResponse> inputList;

    public static RoomProjectTestResponse of(RoomProjectTest obj){
        if(obj == null){
            return null;
        }
        RoomProjectTestResponse res = new RoomProjectTestResponse();
        BeanUtils.copyProperties(obj, res);
        res.setPeriodList(PeriodResponse.of(obj.getPeriodList()));
        res.setTestActionList(ActionResponse.of(obj.getTestActionList()));
        res.setInputList(InputItemResponse.of(obj.getInputList()));
        return res;
    }

    public static List<RoomProjectTestResponse> of(List<RoomProjectTest> list){
        if(list.isEmpty()){
            return new ArrayList<>();
        }
        List<RoomProjectTestResponse> res = new ArrayList<>();
        for(RoomProjectTest obj : list){
            res.add(of(obj));
        }
        return res;
    }

}
