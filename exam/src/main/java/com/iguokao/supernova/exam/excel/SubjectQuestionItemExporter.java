package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.common.util.ExcelUtil;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.exam.document.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class SubjectQuestionItemExporter {

    public static List<List<String>> head(int max) {

        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("科目名称");
        list.add(head0);

        for(int i =0;i<max;i++){
            List<String> head = new ArrayList<>();
            head.add("题目");
            list.add(head);
        }

        return list;
    }

    public static List<List<Object>> data(Period period, List<Part> partList, List<Question> questionList) {
        List<List<Object>> res = new ArrayList<>();
        for(int i =0;i<period.getSubjectList().size();i++){
            System.out.println("进行了" + i + "个科目");
            Subject subject = period.getSubjectList().get(i);
            List<Object> line = new ArrayList<>();
            line.add(subject.getName());
            List<Part> sPart = new ArrayList<>();
            for(Part part : partList){
                if(part.getSubjectId().toString().equals(subject.get_id().toString())){
                    sPart.add(part);
                }
            }

            List<Part> parts = sPart.stream()
                .sorted(Comparator.comparingLong(Part::getSort))
                .toList();

            for(Part part : parts){
                for(PartQuestion partQuestion : part.getQuestionList()){
                    for(Question question : questionList){
                        if(question.get_id().toString().equals(partQuestion.getQuestionId().toString())){
                            if(question.getBody().length() > 1000){
                                line.add(ExcelUtil.formatQuestionBody(question.getBody().substring(0,999)));
                            }
                            else {
                                line.add(ExcelUtil.formatQuestionBody(question.getBody()));
                            }
                        }
                    }
                }
            }
            res.add(line);
        }
        return res;
    }
}
