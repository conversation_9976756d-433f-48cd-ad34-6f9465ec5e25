package com.iguokao.supernova.exam.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.IdNumFormatter;
import com.iguokao.supernova.exam.document.Candidate;
import com.iguokao.supernova.exam.document.Period;
import com.iguokao.supernova.exam.document.Question;
import com.iguokao.supernova.exam.document.SendInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class CandidateResponse {
    @Schema(description = "考生 Id")
    private String candidateId;

    @Schema(description = "项目 Id")
    private String projectId;

    @Schema(description = "企业 Id")
    private String companyId;

    @Schema(description = "科目 Id")
    private String subjectId;

    @Schema(description = "时间段 Id")
    private String periodId;

    @Schema(description = "房间 Id")
    private String roomId;

    @Schema(description = "试卷 Id")
    private String paperId;

    @Schema(description = "试卷 Id")
    private String transRoomId;

    @Schema(description = "试卷 编号 0-x")
    private Integer paperIndex;

    @Schema(description = "考生姓名")
    private String fullName;

    @Schema(description = "准考证号")
    private Long num;

    @Schema(description = "考生登录密码")
    private String loginPassword;

    @Schema(description = "确认状态")
    private Integer confirmState;

    @Schema(description = "状态")
    private Integer state;

    @Schema(description = "证件类型")
    private Integer idCardType;

    @Schema(description = "证件号")
    private String idCardNum;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "电子邮件")
    private String email;

    @Schema(description = "拒绝原因")
    private String refuseMessage;

    @Schema(description = "座位号")
    private Integer seatNum;

    @Schema(description = "分数")
    private Double score;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "头像Url")
    private String avatarUrl;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "科目名称")
    private String subjectName;

    @Schema(description = "最后一次邮件发送状态")
    private SendInfoResponse emailState;

    @Schema(description = "最后一次短信发送状态")
    private SendInfoResponse smsState;

    public static CandidateResponse of(Candidate obj){
        if(obj==null){
            return null;
        }
        CandidateResponse res = new CandidateResponse();
        BeanUtils.copyProperties(obj, res);
        res.setCandidateId(obj.get_id().toString());
        res.setCompanyId(obj.getCompanyId().toString());
        res.setProjectId(obj.getProjectId().toString());
        if(obj.getSubjectId() != null){
            res.setSubjectId(obj.getSubjectId().toString());
        }
        if(obj.getPeriodId() != null){
            res.setPeriodId(obj.getPeriodId().toString());
        }
        if(obj.getRoomId() != null){
            res.setRoomId(obj.getRoomId().toString());
        }
        if(obj.getEmailState() != null){
            res.setEmailState(SendInfoResponse.of(obj.getEmailState()));
        }
        if(obj.getSmsState() != null){
            res.setSmsState(SendInfoResponse.of(obj.getSmsState()));
        }
        if(obj.getTransRoomId() != null){
            res.setTransRoomId(obj.getTransRoomId().toString());
        }
        return res;
    }

    public static List<CandidateResponse> of(List<Candidate> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<CandidateResponse> res = new ArrayList<>();
        for(Candidate obj : list){
            res.add(of(obj));
        }
        return res;
    }

    public static List<CandidateResponse> of(List<Candidate> list, Period period){
        if(list==null){
            return new ArrayList<>();
        }
        List<CandidateResponse> res = new ArrayList<>();
        for(Candidate obj : list){
            CandidateResponse item = of(obj);
            period.getSubjectList()
                    .stream()
                    .filter(subject -> subject.get_id().equals(obj.getSubjectId()))
                    .findFirst()
                    .ifPresent(subject -> item.setSubjectName(subject.getName()));
            res.add(item);
        }
        return res;
    }
}
