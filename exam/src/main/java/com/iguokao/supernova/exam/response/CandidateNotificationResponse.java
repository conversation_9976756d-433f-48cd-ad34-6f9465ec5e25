package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Candidate;
import com.iguokao.supernova.exam.document.Period;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class CandidateNotificationResponse {
    @Schema(description = "考生总数")
    private Long candidateCount;

    @Schema(description = "已通知人数")
    private Long notifiedCount;

    @Schema(description = "确认参加人数")
    private Long confirmedCount;

    @Schema(description = "未确认参加人数")
    private Long unconfirmedCount;

    @Schema(description = "拒绝参加人数")
    private Long refusedCount;

    @Schema(description = "打印准考证人数")
    private Long downloadedCount;
}
