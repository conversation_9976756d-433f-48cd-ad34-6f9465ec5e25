package com.iguokao.supernova.exam.request;

import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RoomListRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "siteId")
    private String siteId;

    @Schema(description = "名称")
    private String name;
}
