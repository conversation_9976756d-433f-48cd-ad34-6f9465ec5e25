package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.exam.document.Candidate;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

@Getter
@Setter
public class CandidateArrangeManualItem {

    private String fullName;
    private String city;
    private String siteName;
    private String roomNum;
    private String roomName;

    private String roomAddress;
    private Integer seatNum;
    private Integer periodCount;
    private String idCardNum;
    private Long num;

    private String subjectName;
    private String mobile;
    private String email;
    private String custom1;
    private String custom2;

    private String custom3;
    private String candidateId;
    private String siteId;
    private String roomId;
    private String periodId;

    public static CandidateArrangeManualItem of(Candidate obj, String siteName, String roomName){
        if(obj == null){
            return null;
        }
        CandidateArrangeManualItem res = new CandidateArrangeManualItem();
        BeanUtils.copyProperties(obj, res);
        res.setSiteName(siteName);
        res.setRoomName(roomName);
        res.setCandidateId(obj.get_id().toString());
        if(obj.getSiteId() != null){
            res.setSiteId(obj.getSiteId().toString());
        }
        if(obj.getRoomId() != null){
            res.setRoomId(obj.getRoomId().toString());
        }
        if(obj.getPeriodId() != null){
            res.setPeriodId(obj.getPeriodId().toString());
        }
        return res;
    }
}
