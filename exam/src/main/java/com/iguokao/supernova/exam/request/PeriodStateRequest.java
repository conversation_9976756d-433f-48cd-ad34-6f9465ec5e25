package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class PeriodStateRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private String periodId;

    @Schema(description = "所在城市")
    private String city;

    @Schema(description = "默认传 null 传0 代表查询考官未签到")
    private Integer operatorSignatureCount;

    @Schema(description = "默认传 null 传false 代表查询未测试")
    private Boolean tested;

    @Schema(description = "默认传 null 传false 代表查询未下载考生数据")
    private Boolean candidateDownloaded;

    @Schema(description = "默认传 null 传false 代表查询未下载试卷")
    private Boolean paperDownloaded;

    @Schema(description = "默认传 null 传false 代表查询未解密试卷")
    private Boolean passwordGet;

    @Schema(description = "默认传 null 传false 代表查询未上传答案")
    private Boolean answerUploaded;

    @Schema(description = "默认传 null 传false 代表查询未上传日志")
    private Boolean actionUploaded;
}
