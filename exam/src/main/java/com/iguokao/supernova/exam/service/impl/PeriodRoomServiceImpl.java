package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.enums.PeriodStateEnum;
import com.iguokao.supernova.exam.enums.RoomStateEnum;
import com.iguokao.supernova.exam.excel.RoomInfoItem;
import com.iguokao.supernova.exam.excel.RoomLoginItem;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.MonitorService;
import com.iguokao.supernova.exam.service.PeriodRoomService;
import com.mongodb.bulk.BulkWriteResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PeriodRoomServiceImpl implements PeriodRoomService {
    private final PeriodRoomRepository periodRoomRepository;
    private final PeriodRepository periodRepository;
    private final MongoTemplate mongoTemplate;
    private final SiteRepository siteRepository;
    private final RoomRepository roomRepository;
    private final CacheService cacheService;
    private final ConfirmRepository confirmRepository;
    private final MonitorService monitorService;

    @Override
    public PeriodRoom getById(String periodId, String roomId) {
        return this.periodRoomRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId))
                .orElse(null);
    }

    @Override
    public void generate(String periodId, List<Confirm> confirmList, List<Room> roomList, boolean countCandidate) {

        Period period = this.periodRepository.findById(new ObjectId(periodId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));

        if(countCandidate){
            this.clear(periodId);

            int confirmCount = confirmList
                    .stream()
                    .mapToInt(Confirm::getConfirmCount)
                    .sum();
            int periodCandidateCount = period.getSubjectList()
                    .stream()
                    .mapToInt(Subject::getCandidateCount)
                    .sum();
            if(confirmCount < periodCandidateCount){
                throw new ServiceException(ExceptionEnum.PROJECT_EXIST,
                        String.format("当前考生%d人，机位数量%d", periodCandidateCount, confirmCount));
            }
        }
        List<ObjectId> roomIdList = confirmList
                .stream()
                .map(Confirm::getRoomIdList)
                .flatMap(Collection::stream)
                .toList();
        List<PeriodRoom> res = new ArrayList<>();

        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, PeriodRoom.class);
        for (ObjectId roomId : roomIdList) {
            ObjectId siteId = confirmList
                    .stream()
                    .filter(confirm -> confirm.getRoomIdList().contains(roomId))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND))
                    .getSiteId();
            Confirm confirm = confirmList
                    .stream()
                    .filter(c -> c.getSiteId().equals(siteId))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));

            PeriodRoom periodRoom = new PeriodRoom();
            periodRoom.setProjectId(period.getProjectId());
            periodRoom.setPeriodId(period.get_id());
            periodRoom.setRoomIndex(confirm.getRoomIdList().indexOf(roomId));
            periodRoom.setSiteId(siteId);
            periodRoom.setRoomId(roomId);
            periodRoom.setRoomState(RoomStateEnum.INIT.getCode());
            periodRoom.setAvailable(roomList
                    .stream()
                    .filter(room -> room.get_id().toString().equals(roomId.toString()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND))
                    .getAvailable());
            res.add(periodRoom);
        }
        bulkOperations.insert(res);

        if (!roomIdList.isEmpty()) {
            BulkWriteResult result = bulkOperations.execute();
            log.info("PeriodRoom {} 关联考场数量 - {}", periodId, result.getModifiedCount());
        }
    }

    @Override
    public void setVideoUrl(String periodId, String roomId, String taskId, String position, String url) {
        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .addCriteria(Criteria.where("roomId").is(new ObjectId(roomId)));
        Update update = new Update()
                .push(position.equals("p") ? "primaryVideoSet" : "secondaryVideoSet", url)
                .set("taskId", taskId);

        mongoTemplate.updateFirst(query, update, PeriodRoom.class);
    }

    @Override
    public void setRoomNumber(String periodId, String roomId, Integer roomNumber) {
        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .addCriteria(Criteria.where("roomId").is(new ObjectId(roomId)));
        Update update = new Update()
                .set("streamRoomNumber", roomNumber);
        mongoTemplate.updateFirst(query, update, PeriodRoom.class);
    }

    @Override
    public void setRoomState(String periodId, String roomId, RoomStateEnum state) {
        // PeriodRoom
        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .addCriteria(Criteria.where("roomId").is(new ObjectId(roomId)));
        Update update = new Update()
                .set("roomState", state.getCode());
        mongoTemplate.updateFirst(query, update, PeriodRoom.class);
        this.cacheService.deletePeriodRoom(roomId);
        // MonitorRoom
        if(state.equals(RoomStateEnum.ANSWER_UPLOADED)){
            this.monitorService.setAnswerUploaded(periodId, roomId);
        } else if(state.equals(RoomStateEnum.ACTION_UPLOADED)){
            this.monitorService.setActionUploaded(periodId, roomId);
        }
    }

    @Override
    public List<Site> getSiteListByPeriodId(String periodId) {
        List<PeriodRoom> list = this.periodRoomRepository.findByPeriodId(new ObjectId(periodId));
        List<ObjectId> idList =  list
                .stream()
                .map(PeriodRoom::getSiteId)
                .distinct()
                .toList();

        Query query = new Query(Criteria.where("_id").in(idList))
                .with(Sort.by(Sort.Direction.ASC, "province","city"));
        return mongoTemplate.find(query, Site.class);
    }

    @Override
    public List<PeriodRoom> getRoomListByPeriodId(String periodId, String siteId) {
        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .addCriteria(Criteria.where("siteId").is(new ObjectId(siteId)))
                .with(Sort.by(Sort.Direction.ASC, "roomIndex"));
        return mongoTemplate.find(query, PeriodRoom.class);
    }

    @Override
    public List<PeriodRoom> getRoomListByProjectIdAndSiteId(String projectId, String siteId) {
        Query query = new Query(Criteria.where("projectId").is(new ObjectId(projectId)))
                .addCriteria(Criteria.where("siteId").is(new ObjectId(siteId)))
                .with(Sort.by(Sort.Direction.ASC, "periodId","roomIndex"));
        return mongoTemplate.find(query, PeriodRoom.class);
    }

    @Override
    public List<PeriodRoom> getRoomListByPeriodId(String periodId) {

        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .with(Sort.by(Sort.Direction.ASC, "roomIndex"));
        return mongoTemplate.find(query, PeriodRoom.class);
    }

    @Override
    public List<PeriodRoom> getRoomListByPeriodIdSortBySiteId(String periodId) {

        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .with(Sort.by(Sort.Direction.ASC, "siteId","roomIndex"));
        return mongoTemplate.find(query, PeriodRoom.class);
    }

    private void clear(String periodId) {
        this.periodRoomRepository.deleteByPeriodId(new ObjectId(periodId));
    }

    @Override
    public void initMonitorRoom(String periodId, boolean ready){
        this.monitorService.deleteMonitorRoom(periodId);
        Period period = this.cacheService.getPeriod(periodId);
        this.periodRoomRepository.findByPeriodId(new ObjectId(periodId));
        boolean monitorRoomLEmpty = this.monitorService.getMonitorRoomList(periodId).isEmpty();

        if(!ready && monitorRoomLEmpty && !period.getState().equals(PeriodStateEnum.ARRANGED.getCode())){
            List<Confirm> confirmList = this.confirmRepository.findByPeriodId(new ObjectId(periodId));
            List<Room> roomList = this.roomRepository.findBy_idIn(confirmList
                    .stream()
                    .map(Confirm::getRoomIdList)
                    .flatMap(Collection::stream)
                    .toList());
            this.generate(periodId, confirmList, roomList, false);
        }

        List<PeriodRoom> list = this.periodRoomRepository.findByPeriodId(new ObjectId(periodId));
        List<ObjectId> roomIdList = list
                .stream()
                .map(PeriodRoom::getRoomId)
                .distinct()
                .toList();

        List<ObjectId> siteIdList = list
                .stream()
                .map(PeriodRoom::getSiteId)
                .distinct()
                .toList();

        List<Site> siteList = this.siteRepository.findBy_idIn(siteIdList);
        List<Room> roomList = this.roomRepository.findBy_idIn(roomIdList);

        for (PeriodRoom periodRoom : list){
            Site site = siteList
                    .stream()
                    .filter(s -> s.get_id().equals(periodRoom.getSiteId()))
                    .findFirst()
                    .orElseThrow(()->new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
            Room room = roomList
                    .stream()
                    .filter(r -> r.get_id().equals(periodRoom.getRoomId()))
                    .findFirst()
                    .orElseThrow(()->new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
            MonitorRoom monitorRoom = MonitorRoom.of(periodRoom, site.getProvince(), site.getCity(), room.getName());
            this.monitorService.add(monitorRoom);
        }
    }

    @Override
    public void updateAttachment(String periodId, String roomId, List<String> list) {
        PeriodRoom periodRoom = this.getById(periodId, roomId);
        if(null == periodRoom){
            throw new ServiceException(ExceptionEnum.PERIOD_ROOM_NOT_FOUND);
        }
        periodRoom.setAttachmentList(list);
        this.periodRoomRepository.save(periodRoom);
        this.cacheService.deletePeriodRoom(roomId);
    }

    @Override
    public List<PeriodRoom> getAllByPeriodId(String periodId) {
        return this.periodRoomRepository.findByPeriodId(new ObjectId(periodId));
    }

    @Override
    public void roomIndexChange(String periodId, String siteId, String roomId, int roomIndex) {
        PeriodRoom periodRoom = this.getById(periodId, roomId);
        Optional<PeriodRoom> pr = this.periodRoomRepository.findByPeriodIdAndSiteIdAndRoomIndex(new ObjectId(periodId),new ObjectId(siteId), roomIndex);
        if(pr.isPresent()){
            throw new ServiceException(ExceptionEnum.PERIOD_ROOM_INDEX_EXIST);
        }
//        if(this.periodRoomRepository.findByPeriodIdAndSiteIdAndRoomIndex(new ObjectId(periodId),new ObjectId(siteId), roomIndex).orElse(null) != null){
//            throw new ServiceException(ExceptionEnum.PERIOD_ROOM_INDEX_EXIST);
//        }
        periodRoom.setRoomIndex(roomIndex);
        this.periodRoomRepository.save(periodRoom);
        this.cacheService.deletePeriodRoom(roomId);
    }

    @Override
    public List<RoomInfoItem> getRoomInfoData(String periodId) {
        List<PeriodRoom> periodRoomList = this.getRoomListByPeriodIdSortBySiteId(periodId);

        List<Site> siteList = this.siteRepository.findBy_idIn(periodRoomList
                .stream()
                .map(PeriodRoom::getSiteId)
                .toList());
        List<Room> roomList = this.roomRepository.findBy_idIn(periodRoomList
                .stream()
                .map(PeriodRoom::getRoomId)
                .toList());

        List<RoomInfoItem> list = new ArrayList<>();

        for(PeriodRoom periodRoom : periodRoomList){
            Site site = siteList
                    .stream()
                    .filter(s -> periodRoom.getSiteId().equals(s.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));

            Room room = roomList
                    .stream()
                    .filter(r -> periodRoom.getRoomId().equals(r.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));

            list.add(handleRoomPoly(periodRoom, site, room));
        }
        return list;
    }

    @Override
    public List<RoomLoginItem> getRoomLoginData(String periodId, String siteId) {
        List<PeriodRoom> periodRoomList = this.getRoomListByPeriodIdSortBySiteId(periodId);

        if(siteId != null){
            periodRoomList = periodRoomList
                    .stream()
                    .filter(pr -> pr.getSiteId().toString().equals(siteId))
                    .toList();
        }
        List<Site> siteList = this.siteRepository.findBy_idIn(periodRoomList
                .stream()
                .map(PeriodRoom::getSiteId)
                .toList());
        List<Room> roomList = this.roomRepository.findBy_idIn(periodRoomList
                .stream()
                .map(PeriodRoom::getRoomId)
                .toList());

        List<RoomLoginItem> list = new ArrayList<>();

        int num = 1;
        for(PeriodRoom periodRoom : periodRoomList){
            Site site = siteList
                    .stream()
                    .filter(s -> periodRoom.getSiteId().equals(s.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));

            Room room = roomList
                    .stream()
                    .filter(r -> periodRoom.getRoomId().equals(r.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));

            list.add(handleRoomInfo(periodRoom, site, room, num));
            num++;
        }
        return list;

    }

    @Override
    public List<RoomLoginItem> getRoomLoginDataNotArranged(String periodId, String siteId) {
        Confirm confirm = this.confirmRepository.findByPeriodIdAndSiteId(new ObjectId(periodId), new ObjectId(siteId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CONFIRM_NOT_FOUND));
        Site site = this.siteRepository.findById(new ObjectId(siteId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
        List<Room> roomList = this.roomRepository.findBy_idIn(confirm.getRoomIdList());
        List<RoomLoginItem> list = new ArrayList<>();
        int num = 1;
        for(Room room : roomList){
            RoomLoginItem item = new RoomLoginItem();
            item.setNum(String.valueOf(num));
            item.setSiteName(site.getName());
            item.setName(room.getName());
            item.setAddress(room.getAddress());
            item.setCapacity(room.getCapacity());
            item.setLoginCode(room.getLoginCode());
            item.setRoomNum("待定");
            list.add(item);
            num++;
        }
        return list;
    }

    @Override
    public List<RoomLoginItem> getRoomLoginDataByPeriodAndSiteId(String periodId, String siteId) {
        List<PeriodRoom> periodRoomList = this.getRoomListByPeriodId(periodId,siteId);
        Site site = this.siteRepository.findById(new ObjectId(siteId)).orElseThrow(() ->new ServiceException(ExceptionEnum.SITE_NOT_FOUND));

        List<Room> roomList = this.roomRepository.findBy_idIn(periodRoomList
                .stream()
                .map(PeriodRoom::getRoomId)
                .toList());

        List<RoomLoginItem> list = new ArrayList<>();

        int num = 1;
        for(PeriodRoom periodRoom : periodRoomList){

            Room room = roomList
                    .stream()
                    .filter(r -> periodRoom.getRoomId().equals(r.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));

            list.add(handleRoomInfo(periodRoom, site, room, num));
            num++;
        }
        return list;

    }

    private RoomLoginItem handleRoomInfo(PeriodRoom pr, Site site, Room room, int num){

        RoomLoginItem res = new RoomLoginItem();
        res.setNum(String.valueOf(num));
        res.setRoomNum(String.format("第%d考场", pr.getRoomIndex() + 1));
        res.setSiteName(site.getName());
        res.setName(room.getName());
        res.setAddress(room.getAddress());
        res.setCapacity(room.getCapacity());
        res.setAvailable(pr.getAvailable());
        res.setCandidateCount(pr.getCandidateCount());
        res.setLoginCode(room.getLoginCode());
        return res;
    }

    private RoomInfoItem handleRoomPoly(PeriodRoom pr, Site site, Room room){

        RoomInfoItem res = new RoomInfoItem();
        res.setProvince(site.getProvince());
        res.setCity(site.getCity());
        res.setSiteName(site.getName());

        res.setName(room.getName());
        res.setAddress(room.getAddress());
        res.setRoomIndexName(String.format("第%d考场", pr.getRoomIndex() + 1));

        res.setCapacity(room.getCapacity());
        res.setAvailable(pr.getAvailable());
        res.setCandidateCount(pr.getCandidateCount());

        res.setPcCamera(room.getPcCameraCount() > 0 ? room.getPcCameraCount() + "个" : "无");
        res.setCloudSystem(room.getCloudSystem() ? "是" : "否");
        res.setBlocked(room.getBlocked() ? "有" : "无");
        res.setNote(room.getNote());

        return res;
    }

}
