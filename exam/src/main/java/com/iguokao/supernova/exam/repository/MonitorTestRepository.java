package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.MonitorRoom;
import com.iguokao.supernova.exam.document.MonitorTest;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.CountQuery;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface MonitorTestRepository extends MongoRepository<MonitorTest, ObjectId> {
    int countByProjectId(ObjectId projectId);
    int countByProjectIdAndRoomId(ObjectId projectId, ObjectId roomId);
    Optional<MonitorTest> findByProjectIdAndRoomId(ObjectId projectId, ObjectId roomId);
    List<MonitorTest> findByProjectId(ObjectId projectId);
    List<MonitorTest> findByProjectIdAndRoomIdIn(ObjectId projectId, List<ObjectId> roomIdList);
}
