package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.document.AdmissionCard;

import java.util.List;

public interface AdmissionCardService {
    List<AdmissionCard> getCandidateAdmissionCard(ImageCode imageCode, String projectId, String idCardNum);

    void preheat(String projectId, String lockKey);
    void preheat(String projectId, List<String> idCardNumList);
    void clear(String projectId);


//    void preheatOne(String projectId, String candidateId);
}
