package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class Subject extends BaseDocument{
    @JsonSerialize(using = ObjectIdSerializer.class)
    @Indexed(name = "periodId_index")
    private ObjectId periodId; //  时段ID

    private String name; // 名称
    private Integer duration = 0; // 时长
    private Integer candidateCount =0 ; // 报名人数
    private Double score = 0D; // 总分
    private Integer questionCount = 0; // 总试题数量
    private Boolean paperGenerated = false;
    private String note; // 备注
    private Boolean calculatorEnabled = true;
    private Boolean showScore = true;
    private Integer submitSecond = 0; // 提前交卷时间
}