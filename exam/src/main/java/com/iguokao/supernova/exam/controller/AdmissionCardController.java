package com.iguokao.supernova.exam.controller;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.request.AdmissionCardGenRequest;
import com.iguokao.supernova.exam.request.IdCheckRequest;
import com.iguokao.supernova.exam.response.AdmissionCardResponse;
import com.iguokao.supernova.exam.response.CompanyResponse;
import com.iguokao.supernova.exam.response.ProjectAdmissionCardResponse;
import com.iguokao.supernova.exam.service.AdmissionCardService;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.CandidateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;


@RestController
@RequestMapping("/api/v1/admission")
@RequiredArgsConstructor
public class AdmissionCardController {

    private final AdmissionCardService admissionCardService;
    private final CacheService cacheService;
    private final CandidateService candidateService;
    private final OssService ossService;

    @Value(value = "${app.ali.oss-bucket-exam}")
    private String ossBucketExam;

    @PostMapping("/card")
    @Operation(summary = "准考证查询")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ProjectAdmissionCardResponse.class)))
    public RestResponse<ProjectAdmissionCardResponse> card(@Validated @RequestBody IdCheckRequest request) {
        Project project = this.cacheService.getProject(request.getProjectId());
        if(project.getEndAt().getTime() <= new Date().getTime()){
            throw new ServiceException(ExceptionEnum.PROJECT_EXPIRED);
        }
        ImageCode imageCode = new ImageCode();
        BeanUtils.copyProperties(request, imageCode);
        List<AdmissionCard> list = this.admissionCardService.getCandidateAdmissionCard(imageCode, request.getProjectId(), request.getIdCardNum());
        if(list.isEmpty() || !list.get(0).getFullName().equals(request.getFullName())){
            throw new ServiceException(ExceptionEnum.ADMISSION_CARD_LOGIN_FAILED);
        }
        ProjectAdmissionCardResponse res = ProjectAdmissionCardResponse.of(project, list);
        for(AdmissionCardResponse card : res.getAdmissionCardList()){
            if(card.getAvatar() != null){
                int duration = (int)((project.getEndAt().getTime() - new Date().getTime()) / 1000);
                card.setAvatarUrl(this.ossService.generateSignedUrl(ossBucketExam, card.getAvatar(), duration).toString());
            }
        }
        return RestResponse.success(res);
    }

    @GetMapping("/preheat/{projectId}")
    @Operation(summary = "准考证 生成 预热")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> genAdmissionCard(@PathVariable String projectId) {
        String lockKey = String.format("cardGen_%s", projectId);
        if(this.cacheService.getLock(lockKey) != null){
            throw new ServiceException(BaseExceptionEnum.ADMISSION_CARD_GENERATING);
        }
        this.cacheService.setLock(lockKey);
        this.admissionCardService.preheat(projectId, lockKey);
        return RestResponse.success();
    }

    @PostMapping("/preheat/candidate")
    @Operation(summary = "准考证 生成 预热")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> genAdmissionByIdNum(@RequestBody AdmissionCardGenRequest request) {
        this.admissionCardService.preheat(request.getProjectId(), request.getIdCardNumList());
        return RestResponse.success();
    }

    @GetMapping("/clear/{projectId}")
    @Operation(summary = "准考证清除")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> clearAdmissionCard(@PathVariable String projectId) {
        this.admissionCardService.clear(projectId);
        return RestResponse.success();
    }

//    @GetMapping("/check/{periodId}")
//    @Operation(summary = "准考证 当前数量")
//    @ApiResponse(content = @Content(schema = @Schema(implementation = Integer.class)))
//    public RestResponse<Integer> check(@PathVariable String periodId) {
//        Period period = this.cacheService.getPeriod(periodId);
//        return RestResponse.success(period.getAdmissionCardCount());
//    }

    @GetMapping("/download/{candidateId}")
    @Operation(summary = "准考证已下载")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> download(@PathVariable String candidateId) {
        this.candidateService.setState(candidateId, CandidateStateEnum.DOWNLOADED);
        return RestResponse.success();
    }

    @GetMapping("/company/{companyId}")
    @Operation(summary = "企业信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<CompanyResponse> company(@PathVariable String companyId) {
        CompanyRemoteResponse remoteResponse = this.cacheService.getCompany(companyId);
        CompanyResponse res = CompanyResponse.of(remoteResponse);
        if(res.getLogo() != null && !res.getLogo().isEmpty()) {
            res.setLogoUrl(this.ossService.generateSignedUrl(ossBucketExam, res.getLogo(), 180));
        }
        if(res.getTheme() != null && !res.getTheme().isEmpty()){
            res.setThemeUrl(this.ossService.generateSignedUrl(ossBucketExam, res.getTheme(), 180));
        }
        return RestResponse.success(res);
    }
}
