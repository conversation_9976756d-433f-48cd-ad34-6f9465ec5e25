package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SitePriorityRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考点Id")
    private String siteId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "优先级 默认 5 最小 0 最大100")
    private Integer priority;
}
