package com.iguokao.supernova.exam.excel;

import java.util.ArrayList;
import java.util.List;

public class CandidateArrangedItemExporter {

    public static List<List<String>> head() {
        List<List<String>> list = new ArrayList<>();

        List<String> head0 = new ArrayList<>();
        head0.add("姓名");
        List<String> head1 = new ArrayList<>();
        head1.add("城市");
        List<String> head2 = new ArrayList<>();
        head2.add("考点名称");
        List<String> head3 = new ArrayList<>();
        head3.add("考场号");
        List<String> head4 = new ArrayList<>();
        head4.add("考场名称");
        List<String> head5 = new ArrayList<>();
        head5.add("考场地址");
        List<String> head6 = new ArrayList<>();
        head6.add("座位号");
        List<String> head7 = new ArrayList<>();
        head7.add("参考次数");
        List<String> head8 = new ArrayList<>();
        head8.add("身份证号");
        List<String> head9 = new ArrayList<>();
        head9.add("准考证号");
        List<String> head10 = new ArrayList<>();
        head10.add("科目名称");

        List<String> head11 = new ArrayList<>();
        head11.add("手机号");
        List<String> head12 = new ArrayList<>();
        head12.add("Email");
        List<String> head13 = new ArrayList<>();
        head13.add("自定义1");
        List<String> head14 = new ArrayList<>();
        head14.add("自定义2");
        List<String> head15 = new ArrayList<>();
        head15.add("自定义3");


        List<String> head16 = new ArrayList<>();
        head16.add("考生ID");
        List<String> head17 = new ArrayList<>();
        head17.add("考点ID");
        List<String> head18 = new ArrayList<>();
        head18.add("考场ID");
        List<String> head19 = new ArrayList<>();
        head19.add("时段ID");


        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);

        list.add(head5);
        list.add(head6);
        list.add(head7);
        list.add(head8);
        list.add(head9);

        list.add(head10);
        list.add(head11);
        list.add(head12);
        list.add(head13);
        list.add(head14);

        list.add(head15);
        list.add(head16);
        list.add(head17);
        list.add(head18);
        list.add(head19);
        return list;
    }

    public static List<List<Object>> data(List<CandidateArrangedItem> list) {
        List<List<Object>> res = new ArrayList<>();
        for(CandidateArrangedItem item : list){
            List<Object> line = new ArrayList<>();
            line.add(item.getFullName());
            line.add(item.getCity());
            line.add(item.getSiteName());
            line.add(item.getRoomNum());
            line.add(item.getRoomName());
            line.add(item.getRoomAddress());
            line.add(item.getSeatNum());
            line.add(item.getPeriodCount());

            line.add(item.getIdCardNum());
            line.add(item.getNum());
            line.add(item.getSubjectName());
            line.add(item.getMobile());
            line.add(item.getEmail());
            line.add(item.getCustom1());

            line.add(item.getCustom2());
            line.add(item.getCustom3());

            line.add(item.getCandidateId());
            line.add(item.getSiteId());
            line.add(item.getRoomId());
            line.add(item.getPeriodId());

            res.add(line);
        }
        return res;
    }
}
