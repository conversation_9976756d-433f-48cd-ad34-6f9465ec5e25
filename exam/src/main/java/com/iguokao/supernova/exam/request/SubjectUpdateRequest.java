package com.iguokao.supernova.exam.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class SubjectUpdateRequest {

    @Schema(description = "时段id")
    private String periodId;

    @Schema(description = "科目id")
    private String subjectId;

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "时长")
    @Min(value = 1, message = "时长错误")
    @Max(value = 100000, message = "时长错误")
    private Integer duration;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "使用计算器")
    private Boolean calculatorEnabled;

    @Schema(description = "考生可以查看题分")
    private Boolean showScore;

    @Schema(description = "允许交卷的秒数 超出 0不限")
    private Integer submitSecond;
}