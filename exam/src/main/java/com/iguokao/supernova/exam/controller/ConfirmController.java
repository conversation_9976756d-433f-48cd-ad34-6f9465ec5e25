package com.iguokao.supernova.exam.controller;

import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.request.ConfirmAddRequest;
import com.iguokao.supernova.exam.request.ConfirmSubmitRequest;
import com.iguokao.supernova.exam.request.ConfirmUpdateRequest;
import com.iguokao.supernova.exam.response.ConfirmResponse;
import com.iguokao.supernova.exam.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/confirm")
@RequiredArgsConstructor
public class ConfirmController {
    private final ConfirmService confirmService;
    private final SiteService siteService;
    private final JwtService jwtService;
    private final ProjectService projectService;
    private final PeriodService periodService;
    private final PeriodRoomService periodRoomService;

    @PostMapping("/add")
    @Operation(summary = "添加")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@RequestBody ConfirmAddRequest request) {
        Confirm confirm = new Confirm();
        confirm.setProjectId(new ObjectId(request.getProjectId()));
        confirm.setPeriodId(new ObjectId(request.getPeriodId()));
        confirm.setSiteId(new ObjectId(request.getSiteId()));
        confirm.setCandidateCount(request.getCandidateCount());
        confirmService.add(confirm);
        return RestResponse.success();
    }

    @PostMapping("/update")
    @Operation(summary = "修改")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody ConfirmUpdateRequest request) {
        confirmService.updateCandidateCount(request.getConfirmId(), request.getCandidateCount());
        return RestResponse.success();
    }

    @GetMapping("/remove/{confirmId}")
    @Operation(summary = "取消一个确认")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@PathVariable String confirmId) {
        this.confirmService.remove(confirmId);
        return RestResponse.success();
    }

    @GetMapping("/project/list")
    @Operation(summary = "项目确认列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ConfirmResponse.class))))
    public RestResponse<List<ConfirmResponse>> projectList() {
        String siteId =  this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        List<Confirm> list = confirmService.getListBySiteId(siteId);
        List<ObjectId> idList = list
                .stream()
                .map(Confirm::getProjectId)
                .toList();
        List<Project> projectList = projectService.getByIdList(idList);
        List<Period> periodList = periodService.getByProjectIdList(idList);
        return RestResponse.success(ConfirmResponse.fillList(ConfirmResponse.of(list), projectList, periodList));
    }

    @GetMapping("/site/selected/{periodId}")
    @Operation(summary = "从某个时段查询选择的考点")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ConfirmResponse.class))))
    public RestResponse<List<ConfirmResponse>> selected(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        List<Confirm> list = confirmService.getListByPeriodId(periodId);
        List<PeriodRoom> periodRoomList = periodRoomService.getRoomListByPeriodId(periodId);
        List<Site> siteList = this.siteService.getByIdList(list
                .stream()
                .map(Confirm::getSiteId)
                .toList());
        List<ConfirmResponse> res = ConfirmResponse.fillSiteList(ConfirmResponse.of(list), siteList);
        if(!periodRoomList.isEmpty()){
            ConfirmResponse.fillArrangeCount(res, periodRoomList);
        }
        return RestResponse.success(res);
    }

    @PreAuthorize("hasAuthority('ROLE_SITE')")
    @PostMapping("/manager/submit")
    @Operation(summary = "站点 提交确认")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<Integer> managerSubmit(@RequestBody ConfirmSubmitRequest request) {
        int count = confirmService.managerSubmit(request.getConfirmId(), jwtService.currentOperatorId(), request.getRoomIdList());
        return RestResponse.success(count);
    }

    @PostMapping("/operator/submit")
    @Operation(summary = "管理员 提交确认")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Integer.class)))
    public RestResponse<Integer> operatorSubmit(@RequestBody ConfirmSubmitRequest request) {
        int count = confirmService.operatorSubmit(request.getConfirmId(), jwtService.currentOperatorId(), request.getRoomIdList());
        return RestResponse.success(count);
    }

    @GetMapping("/site/room/{periodId}")
    @Operation(summary = "从某个时段查询选择的考点")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ConfirmResponse.class))))
    public RestResponse<List<ConfirmResponse>> room(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        List<Confirm> list = confirmService.getListByPeriodId(periodId);
        List<Site> siteList = this.siteService.getByIdList(list
                .stream()
                .map(Confirm::getSiteId)
                .toList());
        return RestResponse.success(ConfirmResponse.fillSiteList(ConfirmResponse.of(list), siteList));
    }

}
