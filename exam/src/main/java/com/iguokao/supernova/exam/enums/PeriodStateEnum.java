package com.iguokao.supernova.exam.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum PeriodStateEnum implements BaseEnum {

    INIT(0, "初始"),
    ARRANGING(1, "编排中"),
    ARRANGED(2, "编排"),
    FINISHED(7, "已结束"),
    DATA_UPLOADED(10, "数据已上传"),
    DONE(20, "报告完成"),
    ;

    PeriodStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
