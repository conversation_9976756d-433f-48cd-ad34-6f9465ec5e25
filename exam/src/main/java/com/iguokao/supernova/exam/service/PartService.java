package com.iguokao.supernova.exam.service;
import com.iguokao.supernova.exam.document.Part;
import com.iguokao.supernova.exam.document.PartQuestion;
import com.iguokao.supernova.exam.document.Question;

import java.util.List;
import java.util.Set;

public interface PartService {
    String add(Part part);

    void update(Part part);

    List<Question> questionList(String partId);

    void questionFill(List<PartQuestion> partQuestionList, String partId, Set<String> questionIds);

    List<Part> getBySubjectId(String subjectId);

    void sort(String periodId,String subjectId, List<String> partList);

    void deletePart(String partId, String periodId, String subjectId);

    Part getById(String partId);

    List<Part> getByPeriodId(String periodId);
}
