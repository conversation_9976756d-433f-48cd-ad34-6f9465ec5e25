package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Attachment;
import com.iguokao.supernova.exam.document.Project;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AttachmentResponse {

    @Schema(description = "附件名称")
    private String name;

    @Schema(description = "附件描述")
    private String info;

    @Schema(description = "附件路径")
    private String path;

    @Schema(description = "类型")
    private Integer type;

    public static AttachmentResponse of(Attachment obj){
        if(obj==null){
            return null;
        }
        AttachmentResponse res = new AttachmentResponse();
        BeanUtils.copyProperties(obj, res);
        return res;
    }

    public static List<AttachmentResponse> of(List<Attachment> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<AttachmentResponse> res = new ArrayList<>();
        for(Attachment obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
