package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class QuestionGroupOptionRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "小题类型")
    private Integer type;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "小题分值")
    private Double questionScore;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "小题题干")
    private String body;

    @Schema(description = "是否显示答题框")
    private Boolean isShowAnswerBox;

    @Schema(description = "正确答案")
    private List<String> correctValue;

    @Schema(description = "解析")
    private String analysis;

    @Schema(description = "选项")
    private List<QuestionOptionRequest> optionList;

}


