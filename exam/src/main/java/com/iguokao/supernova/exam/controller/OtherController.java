package com.iguokao.supernova.exam.controller;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.OssSign;
import com.iguokao.supernova.common.response.ImageCodeResponse;
import com.iguokao.supernova.common.response.OssSignResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.exam.document.Project;
import com.iguokao.supernova.exam.request.SignUrlRequest;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.ImageCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/v1/other")
@RequiredArgsConstructor
public class OtherController {
    private final CacheService cacheService;
    private final OssService ossService;
    private final ImageCodeService imageCodeService;

    @Value(value = "${app.ali.oss-bucket-exam}")
    private String ossBucketExam;
    @Operation(security = @SecurityRequirement(name = "ignore"))

    @GetMapping("/validate/code")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ImageCodeResponse.class)))
    public RestResponse<ImageCodeResponse> imageCode() {
        ImageCode imageCode = this.imageCodeService.getImageCode();
        return RestResponse.success(ImageCodeResponse.of(imageCode));
    }

    @GetMapping("/client/change/{version}")
    @Operation(summary = "客户端 版本 更新")
    public RestResponse<String> clientVersionChange(@PathVariable String version) {
        this.cacheService.setClientVersion(version);
        return RestResponse.success(this.cacheService.getClientVersion());
    }

    @GetMapping("/client/version")
    @Operation(summary = "客户端 版本")
    public RestResponse<String> clientVersion() {
        return RestResponse.success(this.cacheService.getClientVersion());
    }


    @GetMapping("/oss/sign/{projectId}")
    @Operation(summary = "获取Oss Sign")
    @ApiResponse(content = @Content(schema = @Schema(implementation = OssSignResponse.class)))
    public RestResponse<OssSignResponse> ossSign(@PathVariable String projectId) {
        Project project = this.cacheService.getProject(projectId);
        int duration = (int)((project.getEndAt().getTime() - new Date().getTime()) / 1000);
        OssSign sign = this.ossService.getOssSign(ossBucketExam, duration);
        return RestResponse.success(OssSignResponse.of(sign));
    }

    @GetMapping("/oss/sign/short")
    @Operation(summary = "获取Oss Sign 180秒")
    @ApiResponse(content = @Content(schema = @Schema(implementation = OssSignResponse.class)))
    public RestResponse<OssSignResponse> ossSign() {
        OssSign sign = this.ossService.getOssSign(ossBucketExam, 180);
        return RestResponse.success(OssSignResponse.of(sign));
    }

    @PostMapping("/oss/sign/url")
    @Operation(summary = "获取Oss Sign URL 180秒")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<List<String>> ossSignUrl(@RequestBody SignUrlRequest request) {
        int duration = 180;
        if(request.getProjectId() != null){
            Project project = this.cacheService.getProject(request.getProjectId());
            duration = (int)((project.getEndAt().getTime() - new Date().getTime()) / 1000);//修改为
        }
        List<String> res = new ArrayList<>();
        for(String path : request.getPathList()){
            String url = this.ossService.generateSignedUrl(this.ossBucketExam, path, duration);
            res.add(url);
        }
        return RestResponse.success(res);
    }
}
