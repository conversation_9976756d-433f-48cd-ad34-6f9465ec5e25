package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.service.RedisService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public abstract class RedisServiceImpl implements RedisService {
    private final RedisTemplate<String, ImageCode> imageCodeRedisTemplate;
    private final RedisTemplate<String, AdmissionCard> admissionCardRedisTemplate;
    private final RedisTemplate<String, OperatorRemoteResponse> operatorRedisTemplate;
    private final RedisTemplate<String, Site> siteRedisTemplate;
    private final RedisTemplate<String, Room> roomRedisTemplate;
    private final RedisTemplate<String, PeriodRoom> periodRoomRedisTemplate;
    private final RedisTemplate<String, Candidate> candidateRedisTemplate;
    private final RedisTemplate<String, MonitorSignature> monitorSignatureRedisTemplate;
    private final RedisTemplate<String, MonitorRoom> monitorRoomRedisTemplate;
    private final RedisTemplate<String, Paper> paperRedisTemplate;
    private final RedisTemplate<String, Project> projectRedisTemplate;
    private final RedisTemplate<String, Period> periodRedisTemplate;
    private final RedisTemplate<String, RoomProjectTest> roomProjectTestRedisTemplate;
    private final RedisTemplate<String, CompanyRemoteResponse> companyRedisTemplate;
    private final RedisTemplate<String, MonitorTest> testRedisTemplate;
    private final StringRedisTemplate redisTemplate;

    public static Integer DURATION_MINUTE = 60;
    public static Integer DURATION_FIVE_MIN = 60 * 5;
    public static Integer DURATION_FOUR_HOUR = 3600 * 4;
    public static Integer DURATION_HALF_DAY = 3600 * 12;
    public static Integer DURATION_ONE_DAY = 3600 * 23;
    public static Integer DURATION_TWO_WEEK = 3600 * 24 * 16;
    public static Integer DURATION_THREE_WEEK = 3600 * 24 * 23;

    public static String PREFIX_IMAGE_CODE = "ic";
    public static String PREFIX_OPERATOR = "op";
    public static String PREFIX_SITE = "s";
    public static String PREFIX_ROOM = "r";
    public static String PREFIX_PERIOD_ROOM = "pr";
    public static String PREFIX_ROOM_CANDIDATE = "rc";
    public static String PREFIX_MONITOR_SIGNATURE = "ms";
    public static String PREFIX_MONITOR_ROOM = "mr";
    public static String PREFIX_MONITOR_ROOM_TEST = "mrt";
    public static String PREFIX_PAPER = "pp";

    public static String PREFIX_PROJECT = "pj";
    public static String PREFIX_PERIOD = "pe";
    public static String PREFIX_ROOM_TEST = "rt";
    public static String KEY_CLIENT_VER = "cv";
    public static String PREFIX_ADMISSION_CARD = "ac";

    public static String PREFIX_COMPANY = "cp";
    public static String PREFIX_TOKEN = "tk";
    public static String PREFIX_LOCK = "lk";

    public static String KEY_TENCENT_MAX_ROOM_NUM = "tencent:room";

    @Override
    public ImageCode getImageCode(String key) {
        String k = String.format("%s:%s", PREFIX_IMAGE_CODE, key);
        return imageCodeRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setImageCode(ImageCode imageCode) {
        String k = String.format("%s:%s", PREFIX_IMAGE_CODE, imageCode.getKey());
        imageCodeRedisTemplate.opsForValue().set(k, imageCode, DURATION_MINUTE, TimeUnit.SECONDS);
    }

    @Override
    public void deleteImageCode(String key) {
        String k = String.format("%s:%s", PREFIX_IMAGE_CODE, key);
        imageCodeRedisTemplate.delete(k);
    }

    @Override
    public Integer getNextRoomNumber() {
        String maxRoomNumber = redisTemplate.opsForValue().get(KEY_TENCENT_MAX_ROOM_NUM);
        if(null == maxRoomNumber){
            maxRoomNumber = String.valueOf(new Date().getTime() / 3600);
            redisTemplate.opsForValue().set(KEY_TENCENT_MAX_ROOM_NUM, maxRoomNumber);
        } else {
            maxRoomNumber = String.valueOf(redisTemplate.opsForValue().increment(KEY_TENCENT_MAX_ROOM_NUM));
        }
        return Integer.parseInt(maxRoomNumber);
    }

    @Override
    public Site getSite(String siteId) {
        String k = String.format("%s:%s", PREFIX_SITE, siteId);
        return this.siteRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setSite(Site site) {
        String k = String.format("%s:%s", PREFIX_SITE, site.get_id().toString());
        this.siteRedisTemplate.opsForValue().set(k, site, DURATION_ONE_DAY, TimeUnit.SECONDS);
    }

    @Override
    public Room getRoom(String roomId) {
        String k = String.format("%s:%s", PREFIX_ROOM, roomId);
        return this.roomRedisTemplate.opsForValue().get(k);
    }

    @Override
    public List<Room> getRoom(List<String> list) {
        List<String> ks = list
                .stream()
                .map(s -> String.format("%s:%s", PREFIX_ROOM, s))
                .toList();
        return this.roomRedisTemplate.opsForValue().multiGet(ks);
    }

    @Override
    public void setRoom(Room room) {
        String k = String.format("%s:%s", PREFIX_ROOM, room.get_id().toString());
        this.roomRedisTemplate.opsForValue().set(k, room, DURATION_ONE_DAY, TimeUnit.SECONDS);
    }

    @Override
    public OperatorRemoteResponse getOperator(String operatorId) {
        String k = String.format("%s:%s", PREFIX_OPERATOR, operatorId);
        return this.operatorRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setOperator(OperatorRemoteResponse operator) {
        String k = String.format("%s:%s", PREFIX_OPERATOR, operator.getOperatorId());
        this.operatorRedisTemplate.opsForValue().set(k, operator, DURATION_ONE_DAY, TimeUnit.SECONDS);
    }

    @Override
    public List<PeriodRoom> getPeriodRoomList(String roomId) {
        String k = String.format("%s:%s", PREFIX_PERIOD_ROOM, roomId);
        return this.periodRoomRedisTemplate.opsForList().range(k, 0, -1);
    }

    @Override
    public void setPeriodRoomList(List<PeriodRoom> list) {
        if(list.isEmpty()){
            return;
        }
        String roomId = list.get(0).getRoomId().toString();
        String k = String.format("%s:%s", PREFIX_PERIOD_ROOM, roomId);
        this.periodRoomRedisTemplate.opsForList().leftPushAll(k, list);
        this.periodRoomRedisTemplate.expire(k, DURATION_FIVE_MIN, TimeUnit.SECONDS);
    }

    @Override
    public void deletePeriodRoom(String roomId) {
        String k = String.format("%s:%s", PREFIX_PERIOD_ROOM, roomId);
        this.periodRoomRedisTemplate.delete(k);
    }

//    @Override
//    public List<Candidate> getRoomCandidateList(String periodId, String roomId) {
//        String k = String.format("%s:%s:%s", PREFIX_ROOM_CANDIDATE, roomId, periodId);
//        return this.candidateRedisTemplate.opsForList().range(k, 0, -1);
//    }
//
//    @Override
//    public void setRoomCandidateList(List<Candidate> list) {
//        String k = String.format("%s:%s:%s", PREFIX_ROOM_CANDIDATE, list.get(0).getPeriodId(), list.get(0).getRoomId());
//        this.candidateRedisTemplate.opsForList().leftPushAll(k, list);
//        this.candidateRedisTemplate.expire(k, DURATION_FOUR_HOUR, TimeUnit.SECONDS);
//    }
//
//    @Override
//    public void deleteRoomCandidateList(String periodId, String roomId) {
//        String k = String.format("%s:%s:%s", PREFIX_ROOM_CANDIDATE, periodId, roomId);
//        System.out.println("删除 - " + k);
//        this.candidateRedisTemplate.delete(k);
//    }
//
//    @Override
//    public void deleteRoomCandidateList(String periodId, List<String> roomIdList) {
//        List<String> keys = new ArrayList<>();
//        for(String roomId : roomIdList){
//            String k = String.format("%s:%s:%s", PREFIX_ROOM_CANDIDATE, periodId, roomId);
//            keys.add(k);
//        }
//        this.candidateRedisTemplate.delete(keys);
//    }

    @Override
    public List<MonitorSignature> getMonitorSignatureList(String periodId, String roomId) {
        String k = String.format("%s:%s:%s", PREFIX_MONITOR_SIGNATURE, periodId, roomId);
        return this.monitorSignatureRedisTemplate.opsForList().range(k, 0, -1);
    }

    @Override
    public void setMonitorSignature(MonitorSignature monitorSignature) {
        String k = String.format("%s:%s:%s", PREFIX_MONITOR_SIGNATURE,
                monitorSignature.getPeriodId(),
                monitorSignature.getRoomId());
        this.monitorSignatureRedisTemplate.opsForList().leftPush(k, monitorSignature);
    }

    @Override
    public void deleteMonitorSignature(MonitorSignature monitorSignature) {
        String k = String.format("%s:%s:%s", PREFIX_MONITOR_SIGNATURE,
                monitorSignature.getPeriodId(),
                monitorSignature.getRoomId());
        List<MonitorSignature> list = this.monitorSignatureRedisTemplate.opsForList().range(k, 0, -1);
        if(list != null){
            list.removeIf(ms -> ms.getPeriodId().equals(monitorSignature.getPeriodId()) &&
                    ms.getRoomId().equals(monitorSignature.getRoomId()) &&
                    (ms.getCandidateId() != null && ms.getCandidateId().equals(monitorSignature.getCandidateId())));
            this.monitorSignatureRedisTemplate.delete(k);
            if(!list.isEmpty()){
                this.monitorSignatureRedisTemplate.opsForList().leftPushAll(k, list);
            }
        }
    }

//    @Override
//    public MonitorRoom getMonitorRoom(String periodId, String roomId) {
//        String k = String.format("%s:%s", PREFIX_MONITOR_ROOM, periodId);
//        Object res = this.monitorRoomRedisTemplate.opsForHash().get(k, roomId);
//        return (MonitorRoom)res;
//    }
//
//    @Override
//    public List<MonitorRoom> getMonitorRoomList(String periodId) {
//        String k = String.format("%s:%s", PREFIX_MONITOR_ROOM, periodId);
//        Map<Object, Object> map = this.monitorRoomRedisTemplate.opsForHash().entries(k);
//        return map.values()
//                .stream()
//                .map(o -> (MonitorRoom)o)
//                .toList();
//    }
//
//    @Override
//    public void  setMonitorRoom(MonitorRoom monitorRoom) {
//        String k = String.format("%s:%s", PREFIX_MONITOR_ROOM, monitorRoom.getPeriodId());
//        this.monitorRoomRedisTemplate.opsForHash().put(k, monitorRoom.getRoomId(), monitorRoom);
//        this.monitorRoomRedisTemplate.expire(k, DURATION_THREE_WEEK, TimeUnit.SECONDS);
//    }
//
//    @Override
//    public void deleteMonitorRoom(String periodId) {
//        String k = String.format("%s:%s", PREFIX_MONITOR_ROOM, periodId);
//        this.monitorRoomRedisTemplate.delete(k);
//    }

//    @Override
//    public List<MonitorTest>  getRoomTest(String projectId) {
//        String k = String.format("%s:%s", PREFIX_MONITOR_ROOM_TEST, projectId);
//        Map<Object, Object> map = this.testRedisTemplate.opsForHash().entries(k);
//        List<MonitorTest> res = new ArrayList<>();
//        map.keySet().forEach(o -> res.add((MonitorTest)map.get(o)));
//        return res;
//    }
//
//    @Override
//    public MonitorTest getRoomTest(String projectId, String roomId) {
//        String k = String.format("%s:%s", PREFIX_MONITOR_ROOM_TEST, projectId);
//        var v = this.testRedisTemplate.opsForHash().get(k, roomId);
//        if(v != null){
//            return (MonitorTest)v;
//        }
//        return null;
//    }
//
//    @Override
//    public void setRoomTest(MonitorTest test) {
//        String k = String.format("%s:%s", PREFIX_MONITOR_ROOM_TEST, test.getProjectId());
//        this.testRedisTemplate.opsForHash().put(k, test.getRoomId(), test);
//        this.testRedisTemplate.expire(k, DURATION_THREE_WEEK, TimeUnit.SECONDS);
//    }

    @Override
    public List<Paper> getPaperList(String periodId) {
        String k = String.format("%s:%s", PREFIX_PAPER, periodId);
        Set<Paper> set = this.paperRedisTemplate.opsForSet().members(k);
        if(set == null || set.isEmpty()){
            return new ArrayList<>();
        }
        return set
                .stream()
                .toList();
    }

    @Override
    public void setPaperList(String periodId, List<Paper> list) {
        String k = String.format("%s:%s", PREFIX_PAPER, periodId);
        Paper[] array = new Paper[list.size()];
        list.toArray(array);
        this.paperRedisTemplate.opsForSet().add(k, array);
    }

    @Override
    public Project getProject(String projectId) {
        String k = String.format("%s:%s", PREFIX_PROJECT, projectId);
        return this.projectRedisTemplate.opsForValue().get(k);
    }

    @Override
    public List<Project> getProject(List<String> list) {
        List<String> keyList = list
                .stream()
                .map(s -> String.format("%s:%s", PREFIX_PROJECT, s))
                .toList();
        return this.projectRedisTemplate.opsForValue().multiGet(keyList);
    }

    @Override
    public void setProject(Project project) {
        String k = String.format("%s:%s", PREFIX_PROJECT, project.get_id().toString());
        this.projectRedisTemplate.opsForValue().set(k, project);
    }

    @Override
    public void deleteProject(String projectId) {
        String k = String.format("%s:%s", PREFIX_PROJECT, projectId);
        this.projectRedisTemplate.delete(k);
    }

    @Override
    public Period getPeriod(String periodId) {
        String k = String.format("%s:%s", PREFIX_PERIOD, periodId);
        return this.periodRedisTemplate.opsForValue().get(k);
    }

    @Override
    public List<Period> getPeriod(List<String> list) {
        List<String> keyList = list
                .stream()
                .map(s -> String.format("%s:%s", PREFIX_PERIOD, s))
                .toList();
        return this.periodRedisTemplate.opsForValue().multiGet(keyList);
    }

    @Override
    public void setPeriod(Period period) {
        String k = String.format("%s:%s", PREFIX_PERIOD, period.get_id().toString());
        this.periodRedisTemplate.opsForValue().set(k, period);
    }

    @Override
    public void deletePeriod(String periodId) {
        String k = String.format("%s:%s", PREFIX_PERIOD, periodId);
        this.periodRedisTemplate.delete(k);
    }

    @Override
    public List<RoomProjectTest> getRoomProjectTest(String roomId) {
        String k = String.format("%s:%s", PREFIX_ROOM_TEST, roomId);
        return this.roomProjectTestRedisTemplate.opsForList().range(k, 0, -1);
    }

    @Override
    public void setRoomProjectTest(List<RoomProjectTest> list) {
        if(list.isEmpty()){
            return;
        }
        String k = String.format("%s:%s", PREFIX_ROOM_TEST, list.get(0).getRoomId());
        this.deleteRoomProjectTest(list.get(0).getRoomId());
        this.roomProjectTestRedisTemplate.opsForList().leftPushAll(k, list);
        this.roomProjectTestRedisTemplate.expire(k, DURATION_FIVE_MIN, TimeUnit.SECONDS);
    }

    @Override
    public void deleteRoomProjectTest(String roomId) {
        String k = String.format("%s:%s", PREFIX_ROOM_TEST, roomId);
        this.roomProjectTestRedisTemplate.delete(k);
    }

    @Override
    public List<AdmissionCard> getAdmissionCardByIdCardNum(String projectId, String idCardNum) {
        String k = String.format("%s:%s:%s", PREFIX_ADMISSION_CARD, projectId, idCardNum);
        return this.admissionCardRedisTemplate.opsForList().range(k, 0, -1);
    }

    @Override
    public void deleteAdmissionCardByIdCardNum(String projectId, String idCardNum) {
        String k = String.format("%s:%s:%s", PREFIX_ADMISSION_CARD, projectId, idCardNum);
        this.admissionCardRedisTemplate.delete(k);
    }

    @Override
    public void setAdmissionCard(List<AdmissionCard> list) {
        String k = String.format("%s:%s:%s", PREFIX_ADMISSION_CARD, list.get(0).getProjectId(), list.get(0).getIdCardNum());
        this.admissionCardRedisTemplate.delete(k);
        this.admissionCardRedisTemplate.opsForList().leftPushAll(k, list);
        this.admissionCardRedisTemplate.expire(k, DURATION_TWO_WEEK, TimeUnit.SECONDS);
    }

    @Override
    public void setClientVersion(String ver) {
        this.redisTemplate.opsForValue().set(KEY_CLIENT_VER, String.valueOf(ver));
    }

    @Override
    public String getClientVersion() {
        String v = this.redisTemplate.opsForValue().get(KEY_CLIENT_VER);
        if(null == v){
            v = "1.0.1";
            this.setClientVersion(v);
            return v;
        }
        return v;
    }

    @Override
    public CompanyRemoteResponse getCompany(String companyId) {
        String k = String.format("%s:%s", PREFIX_COMPANY, companyId);
        return this.companyRedisTemplate.opsForValue().get(k);
    }

    @Override
    public void setCompany(CompanyRemoteResponse company) {
        String k = String.format("%s:%s", PREFIX_COMPANY, company.getCompanyId());
        this.companyRedisTemplate.opsForValue().set(k, company, DURATION_FIVE_MIN, TimeUnit.SECONDS);
    }

    @Override
    public String getToken(String key) {
        String k = String.format("%s:%s", PREFIX_TOKEN, key);
        return this.redisTemplate.opsForValue().get(k);
    }

    @Override
    public void setToken(String key, String token) {
        String k = String.format("%s:%s", PREFIX_TOKEN, key);
        this.redisTemplate.opsForValue().set(k, token, DURATION_FIVE_MIN, TimeUnit.SECONDS);
    }

    @Override
    public String getLock(String key) {
        String k = String.format("%s:%s", PREFIX_LOCK, key);
        return this.redisTemplate.opsForValue().get(k);
    }

    @Override
    public void setLock(String key) {
        String k = String.format("%s:%s", PREFIX_LOCK, key);
        this.redisTemplate.opsForValue().set(k, "lock");
    }

    @Override
    public void deleteLock(String key) {
        String k = String.format("%s:%s", PREFIX_LOCK, key);
        this.redisTemplate.delete(k);
    }
}
