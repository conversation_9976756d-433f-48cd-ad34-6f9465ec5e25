package com.iguokao.supernova.exam.controller;

import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.exam.document.Email;
import com.iguokao.supernova.exam.document.NotificationBatch;
import com.iguokao.supernova.exam.request.*;
import com.iguokao.supernova.exam.response.EmailResponse;
import com.iguokao.supernova.exam.service.EmailService;
import com.iguokao.supernova.exam.service.NotificationBatchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/notification")
@RequiredArgsConstructor
public class NotificationController {

    private final EmailService emailService;
    private final NotificationBatchService notificationBatchService;

    @PostMapping("/email/add")
    @Operation(summary = "添加邮件模板")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> emailAdd(@RequestBody EmailAddRequest request) {
        this.emailService.add(request.getCompanyId(),request.getName(),request.getTitle(),request.getContent());
        return RestResponse.success();
    }

    @GetMapping("/email/remove/{emailId}")
    @Operation(summary = "删除邮件模板")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> emailRemove(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String emailId) {
        this.emailService.remove(emailId);
        return RestResponse.success();
    }

    @PostMapping("/email/edit")
    @Operation(summary = "编辑邮件删除")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> emailEdit(@RequestBody EmailEditRequest request) {
        this.emailService.edit(request.getEmailId(),request.getName(),request.getTitle(),request.getContent());
        return RestResponse.success();
    }

    @GetMapping("/email/list/{companyId}")
    @Operation(summary = "编辑邮件列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<EmailResponse>> emailList(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String companyId) {
        List<Email> emailList = this.emailService.list(companyId);
        return RestResponse.success(EmailResponse.of(emailList));
    }

    @PostMapping("/email/send/group")
    @Operation(summary = "批量发送邮件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendGroup(@RequestBody SendGroupRequest request) {
        this.emailService.sendGroup(request.getPeriodId(),
                request.getSubjectId(),
                request.getTempId(),
                request.getCandidateIdList(),
                request.getTestEmailAddress());
        return RestResponse.success();
    }

    @PostMapping("/email/send/subject")
    @Operation(summary = "批量发送邮件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendAll(@RequestBody SendAllRequest request) {
        this.emailService.sendAll(request.getPeriodId(),request.getSubjectId(),request.getTempId(),request.getConfirmState(),request.getEmailState(),request.getState());
        return RestResponse.success();
    }

    @PostMapping("/sms/send/group")
    @Operation(summary = "批量发送短信")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendSmsGroup(@RequestBody SendGroupRequest request) {
        this.emailService.sendSmsGroup(request.getPeriodId(),request.getSubjectId(),request.getTempId(),request.getCandidateIdList());
        return RestResponse.success();
    }

    @PostMapping("/sms/send/subject")
    @Operation(summary = "批量发送邮件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> sendSmsAll(@RequestBody SendAllRequest request) {
        this.emailService.sendSmsAll(request.getPeriodId(),request.getSubjectId(),request.getTempId(),request.getConfirmState(),request.getSmsState(),request.getState());
        return RestResponse.success();
    }

    @GetMapping("/batch/list/{periodId}/{subjectId}")
    @Operation(summary = "时段-科目最新发送批次列表（5条）")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<NotificationBatch>> batchList(@PathVariable String periodId, @PathVariable String subjectId) {
        List<NotificationBatch> batchList = this.notificationBatchService.latestList(periodId, subjectId, 5);
        return RestResponse.success(batchList);
    }
}
