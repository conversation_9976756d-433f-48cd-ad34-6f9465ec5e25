package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class SendGroupRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private String periodId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "科目 Id")
    @Length(min = 24, max = 24, message = "subjectId 错误")
    private String subjectId;

    @Schema(description = "测试邮件地址")
    private String testEmailAddress;

    @Schema(description = "模板id")
    @Length(min = 6, max = 24, message = "tempId 错误")
    private String tempId;

    @Schema(description = "此组考生")
    @NotEmpty(message = "考生id列表不能为空")
    private List<String> candidateIdList;
}
