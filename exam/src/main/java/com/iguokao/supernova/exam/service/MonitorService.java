package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.exam.document.*;

import java.util.List;

public interface MonitorService {
//    List<Candidate> getRoomCandidateList(String periodId, String roomId);
//    void setRoomCandidateList(List<Candidate> list);
//    void deleteRoomCandidateList(String periodId, String roomId);
//    void deleteRoomCandidateList(String periodId, List<String> roomIdList);
//
//    List<MonitorSignature> getMonitorSignatureList(String periodId, String roomId);
//    void setMonitorSignature(MonitorSignature monitorSignature);
//    void deleteMonitorSignature(MonitorSignature monitorSignature);
    void add(MonitorRoom monitorRoom);
    void update(MonitorRoom monitorRoom);

    List<MonitorRoom> getMonitorRoomList(String periodId);
    MonitorRoom getMonitorRoom(String periodId, String roomId);
    void deleteMonitorRoom(String periodId);
    void setPaperDownloaded(String periodId, String roomId);
    void setPasswordGet(String periodId, String roomId);
    void setCandidateGet(String periodId, String roomId);
    void setAnswerUploaded(String periodId, String roomId);
    void setActionUploaded(String periodId, String roomId);
    void incOperatorSignatureCount(String periodId, String roomId);

    Tuple2<Integer, Integer> finishedCount(String periodId);

    List<MonitorTest> getRoomTest(String projectId);
    MonitorTest getRoomTest(String projectId, String roomId);
    void setRoomTest(MonitorTest test);
    void setTestPassed(String projectId, List<String> roomIdList);

//    List<MonitorTest> getRoomTest(String projectId);
//    MonitorTest getRoomTest(String projectId, String roomId);
//    void setRoomTest(MonitorTest test);
//
//    List<RoomProjectTest> getRoomProjectTest(String roomId);
//    void setRoomProjectTest(List<RoomProjectTest> list);
//    void deleteRoomProjectTest(String roomId);
}
