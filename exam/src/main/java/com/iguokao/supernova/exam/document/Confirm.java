package com.iguokao.supernova.exam.document;

import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class Confirm extends BaseDocument {
    @Indexed(name = "siteId_index")
    private ObjectId siteId;

    @Indexed(name = "projectId_index")
    private ObjectId projectId;

    @Indexed(name = "periodId_index")
    private ObjectId periodId;

    private ObjectId operatorId;
    private Integer candidateCount; // 待分配的考生数
    private Integer confirmCount;//  可分配的机器数量
    private Integer state = 0;
    private List<ObjectId> roomIdList = new ArrayList<>();
}
