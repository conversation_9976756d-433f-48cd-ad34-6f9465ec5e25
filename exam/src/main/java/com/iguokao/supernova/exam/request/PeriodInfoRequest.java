package com.iguokao.supernova.exam.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Getter
@Setter
public class PeriodInfoRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目 Id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String projectId; // 项目ID

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private String periodId;

}
