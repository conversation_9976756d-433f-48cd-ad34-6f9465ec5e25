package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Part;
import com.iguokao.supernova.exam.document.Subject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class SubjectResponse {

    @Schema(description = "科目id")
    private String subjectId;

    @Schema(description = "时段id")
    private String periodId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = " 时长")
    private Integer duration;

    @Schema(description = "报名人数")
    private Integer candidateCount;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "总分")
    private Double score;

    @Schema(description = "总试题数量")
    private Integer questionCount;

    @Schema(description = "生成试卷状态")
    private Boolean paperGenerated;

    @Schema(description = "小卷列表")
    private List<PartResponse> partList = new ArrayList<>();

    @Schema(description = "使用计算器")
    private Boolean calculatorEnabled;

    @Schema(description = "考生可以查看题分")
    private Boolean showScore;

    @Schema(description = "允许交卷的秒数 超出 0不限")
    private Integer submitSecond;

    public static SubjectResponse of(Subject obj, List<Part> partList){
        if(obj==null){
            return null;
        }
        SubjectResponse res = new SubjectResponse();
        BeanUtils.copyProperties(obj, res);
        res.setSubjectId(obj.get_id().toString());
        res.setPeriodId(null == obj.getPeriodId() ? null : obj.getPeriodId().toString());
        if(null != partList){
            res.setPartList(PartResponse.of(partList,null,null));
        }
        return res;
    }

    public static List<SubjectResponse> of(List<Subject> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<SubjectResponse> res = new ArrayList<>();
        for(Subject obj : list){
            res.add(of(obj,null));
        }
        return res;
    }
}

