package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Getter
@Setter
@Document("period_room")
public class PeriodRoom extends BaseDocument {

    @Indexed(name = "projectId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId operatorId;

    @Indexed(name = "siteId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId siteId;

    @Indexed(name = "roomId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId roomId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    @Indexed(name = "periodId_index")
    private ObjectId periodId;

    private Integer roomIndex; // 考场编号


    private String taskId; // 推流Id
    private Integer streamRoomNumber; // 推流房间号
    private Set<String> primaryVideoSet = new HashSet<>(); // 主机位推流ID
    private Set<String> secondaryVideoSet = new HashSet<>(); // 次机位推流ID
    private Integer roomState;
    private Boolean testPassed = false; // 试考通过
    private Integer available = 0; // 可用
    private Integer candidateCount = 0; // 当前考生数量
    private Integer candidateArrivedCount = 0; // 到场人数
    private List<String> attachmentList = new ArrayList<>();
}