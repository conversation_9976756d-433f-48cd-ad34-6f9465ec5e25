package com.iguokao.supernova.exam.util;

import com.iguokao.supernova.exam.document.Candidate;
import com.iguokao.supernova.exam.repository.CandidateRepository;
import org.bson.types.ObjectId;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Stream;

public class CandidateUtil {
    public static void renameAvatarName(String periodId, String path, CandidateRepository repository){
        List<Candidate> list = repository.findByPeriodId(new ObjectId(periodId));
        try (
                Stream<Path> paths = Files.walk(Paths.get(path))) {
            paths
                    .filter(Files::isRegularFile)
                    .forEach(f -> {
                        String id = f.getFileName().toString().replace(".jpg", "");
                        Candidate candidate = list
                                .stream()
                                .filter(c -> c.get_id().toString().equals(id))
                                .findFirst()
                                .orElse(null);
                        if(candidate != null){
                            String file = String.format("%s/%s_%s.jpg", path, candidate.getFullName(), candidate.getIdCardNum());
                            f.toFile().renameTo(new File(file));
                            System.out.println(file);
                        }
                    });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
