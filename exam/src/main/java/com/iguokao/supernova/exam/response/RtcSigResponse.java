package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.RtcSig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RtcSigResponse {
    private String roomId;
    private Long appId;
    private String sig;
    private Integer roomNumber;
    private String userId;

    public static RtcSigResponse of(RtcSig sig){
        if(null == sig){
            return null;
        }
        RtcSigResponse vo = new RtcSigResponse();
        BeanUtils.copyProperties(sig, vo);
        return vo;
    }

    public static List<RtcSigResponse> of(List<RtcSig> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<RtcSigResponse> voList = new ArrayList<>();
        for(RtcSig obj : list){
            voList.add(of(obj));
        }
        return voList;
    }
}
