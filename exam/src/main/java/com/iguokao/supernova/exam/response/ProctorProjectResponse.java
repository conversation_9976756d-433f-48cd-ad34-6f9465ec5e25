package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Period;

import com.iguokao.supernova.exam.document.Project;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ProctorProjectResponse {

    private String name;
    private List<ProctorPeriodResponse> periodList = new ArrayList<>();

    public static ProctorProjectResponse of(Project project, List<Period> list){

        ProctorProjectResponse res = new ProctorProjectResponse();
        res.setName(project.getName());
        for(Period period : list){
            ProctorPeriodResponse ppr = new ProctorPeriodResponse();
            ppr.setName(period.getName());
            ppr.setStartAt(period.getStartAt());
            ppr.setDuration(period.getDuration());
            ppr.setPeriodId(period.get_id().toString());
            res.periodList.add(ppr);
        }
        return res;
    }
}

