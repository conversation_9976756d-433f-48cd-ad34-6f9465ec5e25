package com.iguokao.supernova.exam.config;

import com.iguokao.supernova.common.component.ApiLogAspect;
import com.iguokao.supernova.common.exception.SecurityAccessDeniedHandler;
import com.iguokao.supernova.common.exception.SecurityAuthenticationEntryPoint;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.common.service.impl.JwtServiceImpl;
import com.iguokao.supernova.common.service.impl.OssServiceImpl;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.iai.v20200303.IaiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.filter.ForwardedHeaderFilter;

@Configuration
public class BeanConfig {

    @Profile({"test", "dev"})
    @Bean
    ApiLogAspect apiLogAspect(){
        return new ApiLogAspect();
    }

    @Bean
    OssService ossService(Environment environment){
        return new OssServiceImpl(environment);
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityAccessDeniedHandler securityAccessDeniedHandler(){
        return new SecurityAccessDeniedHandler();
    }

    @Bean
    public SecurityAuthenticationEntryPoint securityAuthenticationEntryPoint(){
        return new SecurityAuthenticationEntryPoint();
    }

    @Bean
    ForwardedHeaderFilter forwardedHeaderFilter() {
        return new ForwardedHeaderFilter();
    }

    @Bean
    JwtService jwtService(){
        return new JwtServiceImpl();
    }

    // 腾讯
    @Bean
    Credential credential(@Value("${app.tencent.secret-id}") String secretId,
                          @Value("${app.tencent.secret-key}") String secretKey){
        return new Credential(secretId, secretKey);
    }

    @Bean
    IaiClient client(Credential cred){
        return new IaiClient(cred, "ap-beijing");
    }

    @Bean
    FaceidClient getFaceIdClient(Credential credential){
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("faceid.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        return new FaceidClient(credential, "", clientProfile);
    }

    
}
