package com.iguokao.supernova.exam.request;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.Project;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class PartAddRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段id")
    @Length(min = 24, max = 24, message = "24位id")
    private String periodId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "大卷id")
    @Length(min = 24, max = 24, message = "24位id")
    private String subjectId;

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;


    @Schema(description = "时长")
    private Integer duration;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "试题乱序")
    private Boolean questionRandomized;

    @Schema(description = "选项乱序")
    private Boolean optionRandomized;

}