package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.service.AdmissionCardService;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.ImageCodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdmissionCardServiceImpl implements AdmissionCardService {

    private final PeriodRoomRepository periodRoomRepository;
    private final PeriodRepository periodRepository;
    private final RoomRepository roomRepository;
    private final SiteRepository siteRepository;
    private final CandidateRepository candidateRepository;
    private final ProjectRepository projectRepository;

    private final CacheService cacheService;

    private final MongoTemplate mongoTemplate;
    private final ImageCodeService imageCodeService;

    @Override
    public List<AdmissionCard> getCandidateAdmissionCard(ImageCode imageCode, String projectId, String idCardNum) {
        if(this.imageCodeService.checkImageCodeValid(imageCode)){
            return this.cacheService.getAdmissionCardByIdCardNum(projectId, idCardNum);
        } else {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_VALID);
        }
    }

    @Async
    @Override
    public void preheat(String projectId, String lockKey) {
        try {
            Project project = this.cacheService.getProject(projectId);
            if(!project.getOnline()){
                throw new ServiceException(ExceptionEnum.PROJECT_NOT_ONLINE);
            }
            List<Candidate> candidateList = this.candidateRepository.findByProjectId(project.get_id());
            List<Period> periodList = this.periodRepository.findByProjectId(project.get_id());
            this.genAdmissionCard(project, periodList, candidateList);
            periodList.forEach(period -> {
                int periodCandidateCount = (int)candidateList.stream().filter(c -> c.getPeriodId().equals(period.get_id())).count();
                period.setAdmissionCardCount(periodCandidateCount);
                this.periodRepository.save(period);
                this.cacheService.deletePeriod(period.get_id().toString());
            });
        } finally {
            this.cacheService.deleteLock(lockKey);
        }
    }

    @Override
    public void preheat(String projectId, List<String> idCardNumList) {
        Project project = this.cacheService.getProject(projectId);
        List<Period> periodList = this.periodRepository.findByProjectId(new ObjectId(projectId));

        idCardNumList.forEach(s -> {
            List<AdmissionCard> list = this.cacheService.getAdmissionCardByIdCardNum(projectId, s);
            if(!list.isEmpty()){
                throw new ServiceException(ExceptionEnum.ADMISSION_CARD_EXIST, s);
            }
        });

        if(!project.getOnline()){
            throw new ServiceException(ExceptionEnum.PROJECT_NOT_ONLINE);
        }
        List<Candidate> candidateList = this.candidateRepository.findByProjectIdAndIdCardNumIn(project.get_id(), idCardNumList);
        periodList.forEach(period -> {
            int periodCandidateCount = (int)candidateList.stream().filter(c -> c.getPeriodId().equals(period.get_id())).count();
            period.setAdmissionCardCount(period.getAdmissionCardCount() + periodCandidateCount);
            this.periodRepository.save(period);
            this.cacheService.deletePeriod(period.get_id().toString());
        });
        this.genAdmissionCard(project, periodList, candidateList);
    }

    private void genAdmissionCard(Project project, List<Period> periodList, List<Candidate> candidateList){
        if(!candidateList
                .stream()
                .filter(c -> c.getRoomId() == null)
                .toList()
                .isEmpty()){
            throw new ServiceException(ExceptionEnum.CANDIDATE_NOT_IN_SEAT);
        }

        List<ObjectId> siteIdList = candidateList
                .stream()
                .map(Candidate::getSiteId)
                .distinct()
                .toList();
        List<ObjectId> roomIdList = candidateList
                .stream()
                .map(Candidate::getRoomId)
                .distinct()
                .toList();

        List<PeriodRoom> periodRoomList = this.periodRoomRepository.findByProjectId(project.get_id());
        List<Site> siteList = this.siteRepository.findBy_idIn(siteIdList);
        List<Room> roomList = this.roomRepository.findBy_idIn(roomIdList);

        Map<String, List<Candidate>> map = candidateList
                .stream()
                .collect(Collectors.groupingBy(Candidate::getIdCardNum));
        int i = 0;
        for(String idCardNum : map.keySet()){
            List<Candidate> list = map.get(idCardNum);
            List<AdmissionCard> res = new ArrayList<>();
            for(Candidate candidate : list){
                Site site = siteList
                        .stream()
                        .filter(s -> candidate.getSiteId().equals(s.get_id()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
                Room room = roomList
                        .stream()
                        .filter(r -> candidate.getRoomId().equals(r.get_id()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
                PeriodRoom periodRoom = periodRoomList
                        .stream()
                        .filter(pr -> pr.getRoomId().equals(room.get_id()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_ROOM_NOT_FOUND));
                Period period = periodList
                        .stream()
                        .filter(p -> p.get_id().equals(candidate.getPeriodId()))
                        .findFirst()
                        .orElseThrow(()->new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
                AdmissionCard card = this.getAdmissionCard(candidate, project, period, site, room, periodRoom);
                res.add(card);
            }
            i++;
            log.info("考生准考证生成: {}", i);
            this.cacheService.setAdmissionCard(res);

            LocalDateTime dateTime = LocalDateTime.now().plusDays(14);
            ZoneId zoneId = ZoneId.systemDefault();
            Date expiredAt = Date.from(dateTime.atZone(zoneId).toInstant());
            project.setAdmissionCardExpiredAt(expiredAt);
            this.projectRepository.save(project);
            this.cacheService.deleteProject(project.get_id().toString());
        }
    }

    public AdmissionCard getAdmissionCard(Candidate candidate, Project project, Period period, Site site, Room room, PeriodRoom periodRoom){
        if(candidate == null || project == null || period == null || site == null || room == null){
            return null;
        }
        AdmissionCard admissionCard = new AdmissionCard();
        BeanUtils.copyProperties(candidate, admissionCard);

        admissionCard.setCandidateId(candidate.get_id().toString());
        admissionCard.setProjectId(candidate.getProjectId().toString());
        admissionCard.setPeriodId(candidate.getPeriodId().toString());
        admissionCard.setSubjectId(candidate.getSubjectId().toString());
        admissionCard.setSiteId(candidate.getSiteId().toString());
        admissionCard.setRoomId(candidate.getRoomId().toString());

        admissionCard.setRoomName(room.getName());
        admissionCard.setRoomAddress(room.getAddress());

        admissionCard.setSiteName(site.getName());
        admissionCard.setSiteAddress(site.getAddress());
        admissionCard.setDistrict(site.getDistrict());

        admissionCard.setStartAt(period.getStartAt());
        admissionCard.setRoomNum(String.format("第%d考场", periodRoom.getRoomIndex() + 1));

        period.getSubjectList()
                .stream()
                .filter(s -> s.get_id().equals(candidate.getSubjectId()))
                .findFirst()
                .ifPresent(subject -> {
                    admissionCard.setDuration(subject.getDuration());
                });

        period.getSubjectList()
                .stream()
                .filter(s -> s.get_id().equals(candidate.getSubjectId()))
                .findFirst()
                .ifPresent(s -> admissionCard.setSubjectName(s.getName()));
        return admissionCard;
    }

    @Async
    @Override
    public void clear(String projectId) {
        List<Period> periodList = this.periodRepository.findByProjectId(new ObjectId(projectId));

        List<Candidate> candidateList = this.candidateRepository.findByProjectId(new ObjectId(projectId));
        for(Candidate candidate : candidateList){
            this.cacheService.deleteAdmissionCardByIdCardNum(projectId, candidate.getIdCardNum());
        }

        for(Period period : periodList){
            Query query = new Query(Criteria.where("_id").is(period.get_id()));
            Update update = new Update()
                    .set("admissionCardCount", 0);
            this.mongoTemplate.updateFirst(query, update, Period.class);
            this.cacheService.deletePeriod(period.get_id().toString());
        }
    }
}
