package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Project;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface ProjectRepository  extends MongoRepository<Project, ObjectId> {
    int countByName(String name);

    List<Project> findBy_idIn(List<ObjectId> list);
}
