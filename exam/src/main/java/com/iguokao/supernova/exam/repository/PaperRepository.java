package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Paper;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface PaperRepository  extends MongoRepository<Paper, ObjectId> {
    Optional<Paper> findBySubjectId(ObjectId subjectId);
    List<Paper> findByPeriodId(ObjectId periodId);
    void deleteBySubjectId(ObjectId subjectId);
}
