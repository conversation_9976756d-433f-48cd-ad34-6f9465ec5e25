package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.exam.enums.CandidateConfirmStateEnum;
import com.iguokao.supernova.exam.enums.ConfirmStateEnum;

import java.util.ArrayList;
import java.util.List;

public class CandidateConfirmExporter {

    public static List<List<String>> head() {
        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("考生姓名");

        List<String> head1 = new ArrayList<>();
        head1.add("手机号");

        List<String> head2 = new ArrayList<>();
        head2.add("身份证号");

        List<String> head3 = new ArrayList<>();
        head3.add("准考证号");

        List<String> head4 = new ArrayList<>();
        head4.add("考试城市");

        List<String> head5 = new ArrayList<>();
        head5.add("通知确认状态");

        List<String> head6 = new ArrayList<>();
        head6.add("打印准考证状态");


        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        list.add(head6);

        return list;
    }

    public static List<List<Object>> data(List<CandidateConfirmItem> list) {
        List<List<Object>> res = new ArrayList<>();
        for(CandidateConfirmItem item : list){
            List<Object> line = new ArrayList<>();
            line.add(item.getFullName());
            line.add(item.getMobile());
            line.add(item.getIdCardNum());
            line.add(item.getNum().toString());
            line.add(item.getCity());

            if(item.getConfirmState().equals(CandidateConfirmStateEnum.AGREE.getCode())){
                line.add("已确认");
            }
            else if(item.getConfirmState().equals(CandidateConfirmStateEnum.REFUSE.getCode())){
                line.add("已放弃");
            }
            else {
                line.add("未确认");
            }

            if(item.getState().equals(CandidateStateEnum.DOWNLOADED.getCode())){
                line.add("已打印");
            }else {
                line.add("未打印");
            }


            res.add(line);
        }
        return res;
    }
}
