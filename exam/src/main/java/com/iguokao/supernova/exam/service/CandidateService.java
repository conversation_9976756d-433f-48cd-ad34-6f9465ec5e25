package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.excel.CandidateConfirmItem;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface CandidateService {
    void add(Candidate candidate);
    int addAll(List<Candidate> list);
    void remove(String candidateId);
    void setState(String candidateId, CandidateStateEnum state);
    void setStateByIdCardNum(String projectId, String idCardNum, CandidateStateEnum state);
    Tuple2<List<Candidate>, Integer> getPage(String companyId , String projectId, String subjectId, String fullName, String mobile, Integer emailState, Integer smsState, Integer confirm, Integer state, <PERSON>olean arranged, Boolean avatarNull, Pageable pageable);
    List<Candidate> getRoomCandidateList(String periodId, String roomId);
    XWPFDocument getRoomCandidateDoc(Project project, Period period, Room room, String periodId, String roomId);
    Integer roomIn(String periodId, String roomId, List<String> idList);
    Integer roomOut(String periodId, String roomId, List<String> idList);
    Long removeBatch(String companyId, String projectId, String periodId, String subjectId, List<String> candidateIdList);
    void update(Candidate candidate);
    Candidate getById(String candidateId);
    List<Candidate> getPeriodSiteCandidateList(String projectId ,String periodId, String siteId);
    List<Candidate> getTestCandidateList(Project project, Period period, int parseInt, String testPaperId, String siteId, String roomId);
    Tuple2<Integer, Integer> countPeriodCandidate(String periodId);

    List<Candidate> getNotArrangedCandidateList(String periodId);
    void setCandidateListSiteAndRoom (List<String> idList, String periodId, String siteId, String roomId);
    void cancelCandidateListSiteAndRoom (List<String> idList, String periodId, String roomId);

    List<Candidate> getMinitorRoomCandidateList(String periodId, String roomId);

    List<Candidate> getByPeriodId(String periodId);

    List<CandidateConfirmItem> getConfirmStateByPeriodId(String periodId);

    void exchange(String candidateId, String roomId, String siteId);

    List<String> getExistsIdCardsInSubject(String periodId, String subjectId);

    List<Long> getExistsNumInSubject(String periodId, String subjectId);

    List<String> setAvatar(String subjectId, List<String> idList);

    void confirm(String candidateId, Integer confirmState);

    Long countBySubjectId(String periodId, String subjectId);

    Long countNotifiedBySubjectId(String periodId, String subjectId);

    Long countStateGreaterBySubjectId(String periodId, String subjectId, Integer state);

    void transferOut(String roomId, List<String> candidateIdList);
    void transferIn(List<String> candidateIdList);
    void changeSeatNum(String candidateId, Integer seatNum);

    List<Candidate> transferring(String periodId, String roomId);

    void setCandidateSeatNum(String periodId, String roomId);

    int avatarCount(String subjectId);

    Long countRefused(String periodId, String subjectId);

    Long countConfirmed(String periodId, String subjectId);

    Long countUnconfirmed(String periodId, String subjectId);

    void test();
}
