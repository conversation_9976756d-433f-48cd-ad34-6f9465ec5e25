package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class AdmissionCardGenRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;


    @Schema(description = "证件号")
    private List<String> idCardNumList;
}
