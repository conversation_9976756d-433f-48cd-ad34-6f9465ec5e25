package com.iguokao.supernova.exam.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.exam.document.Candidate;
import com.iguokao.supernova.exam.document.MonitorSignature;
import com.iguokao.supernova.exam.document.Period;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class CandidateSignResponse {
    @Schema(description = "考生 Id")
    private String candidateId;

    @Schema(description = "项目 Id")
    private String projectId;

    @Schema(description = "企业 Id")
    private String companyId;

    @Schema(description = "科目 Id")
    private String subjectId;

    @Schema(description = "时间段 Id")
    private String periodId;

    @Schema(description = "房间 Id")
    private String roomId;

    @Schema(description = "考生姓名")
    private String fullName;

    @Schema(description = "准考证号")
    private Long num;

    @Schema(description = "状态")
    private Integer state;

    @Schema(description = "座位号")
    private Integer seatNum;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "头像Url")
    private String avatarUrl;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "签字时间")
    private Date signedAt;

    public static CandidateSignResponse of(Candidate obj, MonitorSignature ms){
        if(obj==null){
            return null;
        }
        CandidateSignResponse res = new CandidateSignResponse();
        BeanUtils.copyProperties(obj, res);
        res.setCandidateId(obj.get_id().toString());
        res.setCompanyId(obj.getCompanyId().toString());
        res.setProjectId(obj.getProjectId().toString());
        if(obj.getSubjectId() != null){
            res.setSubjectId(obj.getSubjectId().toString());
        }
        if(obj.getPeriodId() != null){
            res.setPeriodId(obj.getPeriodId().toString());
        }
        if(obj.getRoomId() != null){
            res.setRoomId(obj.getRoomId().toString());
        }
        if(ms !=null){
            res.setSignedAt(ms.getCreatedAt());
        }
        return res;
    }

    public static List<CandidateSignResponse> of(List<Candidate> list, List<MonitorSignature> monitorSignatureList){
        if(list==null){
            return new ArrayList<>();
        }
        List<CandidateSignResponse> res = new ArrayList<>();
        for(Candidate obj : list){
            MonitorSignature monitorSignature = monitorSignatureList
                    .stream()
                    .filter(ms -> ms.getCandidateId() != null && ms.getCandidateId().equals(obj.get_id().toString()))
                    .findFirst()
                    .orElse(null);
            res.add(of(obj, monitorSignature));
        }
        return res;
    }


}
