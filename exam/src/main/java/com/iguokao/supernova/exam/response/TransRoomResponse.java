package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.PeriodRoom;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class TransRoomResponse {

    @Schema(description = "考点Id")
    private String siteId;

    @Schema(description = "考场Id")
    private String roomId;

    @Schema(description = "考场名称")
    private String name;

    @Schema(description = "可用")
    private Integer available;

    @Schema(description = "考生数量")
    private Integer candidateCount;

    @Schema(description = "房间编号")
    private Integer roomNum;

    public static TransRoomResponse of(Room obj, PeriodRoom periodRoom){
        if(obj==null){
            return null;
        }
        TransRoomResponse res = new TransRoomResponse();
        BeanUtils.copyProperties(obj, res);
        res.setSiteId(obj.getSiteId().toString());
        res.setRoomId(obj.get_id().toString());
        res.setAvailable(res.getAvailable());
        res.setCandidateCount(periodRoom.getCandidateCount());
        res.setRoomNum(periodRoom.getRoomIndex() + 1);
        return res;
    }

    public static List<TransRoomResponse> of(List<Room> list, List<PeriodRoom> periodRoomList){
        if(list==null){
            return new ArrayList<>();
        }
        List<TransRoomResponse> res = new ArrayList<>();
        for(Room obj : list){
            PeriodRoom periodRoom = periodRoomList
                    .stream()
                    .filter(pr -> pr.getRoomId().equals(obj.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_ROOM_NOT_FOUND));
            res.add(of(obj, periodRoom));
        }
        return res;
    }
}
