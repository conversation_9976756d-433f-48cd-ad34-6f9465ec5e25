package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.constant.RoleConstant;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.common.enums.ActionTypeEnum;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.ImageCodeService;
import com.iguokao.supernova.exam.service.RoomService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class RoomServiceImpl implements RoomService {
    private final MongoTemplate mongoTemplate;
    private final RoomRepository roomRepository;
    private final ConfirmRepository confirmRepository;
    private final ProjectRepository projectRepository;

    private final SiteRepository siteRepository;
    private final JwtService jwtService;
    private final ActionRepository actionRepository;
    private final CacheService cacheService;
    private final ImageCodeService imageCodeService;

    @Override
    public void add(Room room) {
        if(this.getSiteLocked(room.getSiteId())){
            throw new ServiceException(ExceptionEnum.SITE_LOCKED);
        }
        int count = this.roomRepository.countBySiteIdAndName(room.getSiteId(), room.getName());
        //一个考点中考场名字不能重复
        if(count > 0){
            throw new ServiceException(ExceptionEnum.SITE_EXIST);
        }
        room.setLoginCode(StringUtil.genUpperCode(20));

        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "sort"));
        query.addCriteria(Criteria.where("siteId").is(room.getSiteId()));
        Room roomLast = this.mongoTemplate.findOne(query, Room.class);
        if(null != roomLast){
            room.setSort(roomLast.getSort() + 1);
        }
        this.roomRepository.insert(room);
        this.updateAvailable(room.getSiteId().toString());
    }

    private void updateAvailable(String siteId){
        List<Room> list = this.roomRepository.findBySiteId(new ObjectId(siteId));
        Site site = this.siteRepository.findById(new ObjectId(siteId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
        site.setAvailable(list
                .stream()
                .mapToInt(Room::getAvailable)
                .sum());
        site.setRoomCount(list.size());
        site.setCapacity(list
                .stream()
                .mapToInt(Room::getCapacity)
                .sum());
        this.siteRepository.save(site);
    }

    @Override
    public void addAll(List<Room> list) {
        list.forEach(this::add);
    }

    @Override
    public void update(Room room) {
        if(this.getSiteLocked(room.getSiteId())){
            throw new ServiceException(ExceptionEnum.SITE_LOCKED);
        }
        Room exist = this.getById(room.get_id().toString());
        BeanUtils.copyProperties(room, exist, "siteId", "loginCode", "sort");
        this.roomRepository.save(exist);

        this.updateAvailable(room.getSiteId().toString());
    }

    @Override
    public void remove(String roomId) {
        Room room = this.getById(roomId);
        if(this.getSiteLocked(room.getSiteId())){
            throw new ServiceException(ExceptionEnum.SITE_LOCKED);
        }
        this.roomRepository.deleteById(new ObjectId(roomId));

        this.updateAvailable(room.getSiteId().toString());
    }

    @Override
    public Room getById(String roomId) {
        return this.roomRepository.findById(new ObjectId(roomId))
                .orElseThrow(()->new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
    }

    @Override
    public List<Room> getByIdList(List<ObjectId> list) {
        return this.roomRepository.findBy_idIn(list);
    }

    @Override
    public  List<Room> getList(String siteId, String name){
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "_id","createdAt"));
        query.addCriteria(Criteria.where("siteId").is(new ObjectId(siteId)));
        if(null != name){
            query = query.addCriteria(Criteria.where("name").is(name));
        }
        return this.mongoTemplate.find(query, Room.class);
    }

    @Override
    public  List<Room> getBySiteId(String siteId){
        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "sort"));
        query.addCriteria(Criteria.where("siteId").is(new ObjectId(siteId)));

        return this.mongoTemplate.find(query, Room.class);
    }

    @Override
    public boolean usingRoom(String roomId) {
        List<Confirm> list = this.confirmRepository.findByRoomId(new ObjectId(roomId));
        List<ObjectId> projectIdList =  list
                .stream()
                .map(Confirm::getProjectId)
                .toList();
        List<Project> periodList = this.projectRepository.findBy_idIn(projectIdList);
        long count = periodList
                .stream()
                .filter(p -> p.getEndAt().getTime() > new Date().getTime())
                .count();
        return count > 0;
    }

    @Override
    public String roomLogin(ImageCode imageCode, String loginCode, String version, String ip) {
        if(this.imageCodeService.checkImageCodeValid(imageCode)){
            Room room = this.roomRepository.findByLoginCode(loginCode)
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
            Site site = this.cacheService.getSite(room.getSiteId().toString());
            OperatorRemoteResponse operator = this.cacheService.getOperator(site.getOperatorId().toString());
            List<String> list = new ArrayList<>();
            list.add(String.format("%s_%s", IdConstant.SITE_ID_PREFIX, site.get_id()));
            list.add(String.format("%s_%s", IdConstant.ROOM_ID_PREFIX, room.get_id()));
            operator.getRoleList().clear();
            operator.getRoleList().add(RoleConstant.ROOM);

            // 登录事件
            Action action = new Action();
            action.setSiteId(room.getSiteId());
            action.setRoomId(room.get_id());
            action.setType(ActionTypeEnum.LOGIN.getCode());
            action.setText(String.format("%s_%s", ip, version));
            this.actionRepository.insert(action);

            return this.jwtService.generateToken(operator.getRoleList()
                    .stream()
                    .map(SimpleGrantedAuthority::new)
                    .toList(), operator, list);
        } else {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_VALID);
        }
    }

    @Override
    public Tuple2<Site, String> agentLogin(ImageCode imageCode, String loginCode, String version, String ip) {
        if(this.imageCodeService.checkImageCodeValid(imageCode)){
            Site site = this.siteRepository.findByAgentList_LoginCode(loginCode)
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
            OperatorRemoteResponse operator = this.cacheService.getOperator(site.getOperatorId().toString());
            List<String> list = new ArrayList<>();
            list.add(String.format("%s_%s", IdConstant.SITE_ID_PREFIX, site.get_id()));
            operator.getRoleList().clear();
            operator.getRoleList().add(RoleConstant.ROOM);

            // 登录事件
            Action action = new Action();
            action.setSiteId(site.get_id());
            action.setType(ActionTypeEnum.LOGIN.getCode());
            action.setText(String.format("%s_%s", ip, version));
            this.actionRepository.insert(action);
            return new Tuple2<>(site, this.jwtService.generateToken(operator.getRoleList()
                    .stream()
                    .map(SimpleGrantedAuthority::new)
                    .toList(), operator, list));
        } else {
            throw new ServiceException(BaseExceptionEnum.IMAGE_CODE_NOT_VALID);
        }
    }

    private boolean getSiteLocked(ObjectId siteId){
        Site site = siteRepository.findById(siteId)
                .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
        return site.getLocked();
    }
}
