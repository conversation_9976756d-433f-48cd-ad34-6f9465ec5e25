package com.iguokao.supernova.exam.request;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class PartQuestionRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "试题id")
    @Length(min = 24, max = 24, message = "24位id")
    private String questionId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "试题分值")
    @Min(value = 0, message = "试题分值错误")
    @Max(value = 100, message = "试题分值错误")
    private Double score;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "复合题 分数")
    private List<Double> groupScore;

}