package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class CandidateAvatarUpdateRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "科目 id")
    @Length(min = 24, max = 24, message = "24位id")
    private String subjectId;

    private List<String> idList;
}
