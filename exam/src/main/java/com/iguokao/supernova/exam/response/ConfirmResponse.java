package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.*;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class ConfirmResponse {
    private String confirmId;
    private String projectId;
    private String siteId;
    private String periodId;
    private Integer candidateCount;
    private Integer confirmCount;
    private Integer arrangeCount;
    private Integer state;
    private Integer oversize;
    private List<String> roomIdList = new ArrayList<>();
    private SiteResponse site;
    private ProjectResponse project;
    private PeriodResponse period;

    public static ConfirmResponse of(Confirm obj){
        if(obj==null){
            return null;
        }
        ConfirmResponse res = new ConfirmResponse();
        BeanUtils.copyProperties(obj, res);
        res.setConfirmId(obj.get_id().toString());
        res.setSiteId(obj.getSiteId().toString());
        res.setPeriodId(obj.getPeriodId().toString());
        res.setProjectId(obj.getProjectId().toString());
        res.setRoomIdList(obj.getRoomIdList()
                .stream()
                .map(ObjectId::toString)
                .collect(Collectors.toList()));
        return res;
    }

    public static List<ConfirmResponse> of(List<Confirm> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<ConfirmResponse> res = new ArrayList<>();
        for(Confirm obj : list){
            res.add(of(obj));
        }
        return res;
    }

    public static List<ConfirmResponse> fillSiteList(List<ConfirmResponse> list, List<Site> siteList){
        if(list == null || siteList == null){
            return new ArrayList<>();
        }
        list.forEach(confirm -> {
            siteList
                    .stream()
                    .filter(s -> s.get_id().toString().equals(confirm.getSiteId()))
                    .findFirst()
                    .ifPresent(s -> confirm.setSite(SiteResponse.of(s)));
        });
        return list;
    }

    public static void fillArrangeCount(List<ConfirmResponse> list, List<PeriodRoom> periodRoomList){
        if(list == null || periodRoomList == null){
            return;
        }
        list.forEach(confirm -> {
            confirm.setArrangeCount(periodRoomList
                    .stream()
                    .filter(periodRoom -> periodRoom.getSiteId().toString().equals(confirm.getSiteId()))
                    .mapToInt(PeriodRoom::getCandidateCount)
                    .sum());
        });
    }

    public static List<ConfirmResponse> fillList(List<ConfirmResponse> list, List<Project> projectList, List<Period> periodList){
        if(list == null || periodList == null){
            return new ArrayList<>();
        }
        list.forEach(confirm -> {
            periodList
                    .stream()
                    .filter(p -> p.get_id().toString().equals(confirm.getPeriodId()))
                    .findFirst()
                    .ifPresent(p -> confirm.setPeriod(PeriodResponse.of(p)));
            projectList
                    .stream()
                    .filter(p -> p.get_id().toString().equals(confirm.getProjectId()))
                    .findFirst()
                    .ifPresent(p -> confirm.setProject(ProjectResponse.of(p,null)));
        });
        return  list
                .stream()
                .sorted(Comparator.comparingLong(p -> p.getPeriod().getStartAt().getTime()))
                .toList();
    }

}
