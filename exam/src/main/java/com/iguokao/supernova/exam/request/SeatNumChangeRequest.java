package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class SeatNumChangeRequest {
    @Schema(description = "考生 Id")
    @Length(min = 24, max = 24, message = "candidateId 错误")
    private String candidateId;

    @Schema(description = "考生 座位号")
    @Length(min = 24, max = 24, message = "candidateId 错误")
    private Integer seatNum;
}
