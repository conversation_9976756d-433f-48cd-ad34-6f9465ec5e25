package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.Candidate;
import com.iguokao.supernova.exam.document.MonitorRoom;
import com.iguokao.supernova.exam.document.MonitorTest;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.repository.MonitorRoomRepository;
import com.iguokao.supernova.exam.repository.MonitorTestRepository;
import com.iguokao.supernova.exam.service.MonitorService;
import com.mongodb.client.result.UpdateResult;
import com.mongodb.internal.bulk.UpdateRequest;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@RequiredArgsConstructor
@Service
public class MonitorServiceImpl implements MonitorService {
    private final MonitorRoomRepository monitorRoomRepository;
    private final MongoTemplate mongoTemplate;
    private final MonitorTestRepository monitorTestRepository;

    @Override
    public void add(MonitorRoom monitorRoom) {
        if(this.monitorRoomRepository.countByPeriodIdAndRoomId(monitorRoom.getPeriodId(), monitorRoom.getRoomId()) > 0){
            throw new ServiceException(ExceptionEnum.MONITOR_ROOM_EXIST);
        }
        this.monitorRoomRepository.save(monitorRoom);
    }

    @Override
    public void update(MonitorRoom monitorRoom) {

        Query query = new Query(Criteria.where("periodId").is(monitorRoom.getPeriodId()))
                .addCriteria(Criteria.where("roomId").is(monitorRoom.getRoomId()));
        Update update = new Update()
                .set("roomState", monitorRoom.getRoomState())
                .set("candidateArrivedCount", monitorRoom.getCandidateArrivedCount())
                .set("candidateFinishedCount", monitorRoom.getCandidateFinishedCount())
                .set("candidateAnsweringCount", monitorRoom.getCandidateAnsweringCount())
                .set("candidateLoginCount", monitorRoom.getCandidateLoginCount())
                .set("cheatCount", monitorRoom.getCheatCount())
                .set("forceFinishedCount", monitorRoom.getForceFinishedCount())
                .set("lateCount", monitorRoom.getLateCount())
                .set("delayCount", monitorRoom.getDelayCount())
                .set("updatedAt", new Date())
        ;
        UpdateResult res =  this.mongoTemplate.updateFirst(query, update, MonitorRoom.class);
        System.out.println(res);
    }

    @Override
    public List<MonitorRoom> getMonitorRoomList(String periodId) {
        return this.monitorRoomRepository.findByPeriodId(new ObjectId(periodId));
    }

    @Override
    public MonitorRoom getMonitorRoom(String periodId, String roomId) {
        return this.monitorRoomRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.MONITOR_ROOM_NOT_FOUND));
    }

    @Override
    public void deleteMonitorRoom(String periodId) {
        this.monitorRoomRepository.deleteByPeriodId(new ObjectId(periodId));
    }

    @Override
    public void setPaperDownloaded(String periodId, String roomId) {
        this.setKeyTrue(periodId, roomId, "paperDownloaded");
    }

    @Override
    public void setPasswordGet(String periodId, String roomId) {
        this.setKeyTrue(periodId, roomId, "passwordGet");
    }

    @Override
    public void setCandidateGet(String periodId, String roomId) {
        this.setKeyTrue(periodId, roomId, "candidateDownloaded");
    }

    @Override
    public void setAnswerUploaded(String periodId, String roomId) {
        this.setKeyTrue(periodId, roomId, "answerUploaded");
    }

    @Override
    public void setActionUploaded(String periodId, String roomId) {
        this.setKeyTrue(periodId, roomId, "actionUploaded");
    }

    @Override
    public void incOperatorSignatureCount(String periodId, String roomId) {
        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .addCriteria(Criteria.where("roomId").is(new ObjectId(roomId)));
        Update update = new Update()
                .inc("operatorSignatureCount", 1);
        this.mongoTemplate.updateFirst(query, update, MonitorRoom.class);
    }

    @Override
    public Tuple2<Integer, Integer> finishedCount(String periodId) {
        int total = this.monitorRoomRepository.countByPeriodId(new ObjectId(periodId));
        int finished = this.monitorRoomRepository.countByPeriodFinished(new ObjectId(periodId));
        return new Tuple2<>(finished, total);
    }

    @Override
    public List<MonitorTest> getRoomTest(String projectId) {
        return this.monitorTestRepository.findByProjectId(new ObjectId(projectId));
    }

    @Override
    public MonitorTest getRoomTest(String projectId, String roomId) {
        return this.monitorTestRepository.findByProjectIdAndRoomId(new ObjectId(projectId), new ObjectId(roomId))
                .orElse(null);
    }

    @Override
    public void setRoomTest(MonitorTest test) {
        this.monitorTestRepository.save(test);
    }

    @Override
    public void setTestPassed(String projectId, List<String> roomIdList) {
        List<ObjectId> idList = roomIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        Query query = new Query(Criteria.where("projectId").is(new ObjectId(projectId)))
                .addCriteria(Criteria.where("roomId").in(idList));
        Update update = new Update()
                .set("envTested", true);
        this.mongoTemplate.updateFirst(query, update, MonitorTest.class);
    }

    public void setKeyTrue(String periodId, String roomId, String key) {
        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .addCriteria(Criteria.where("roomId").is(new ObjectId(roomId)));
        Update update = new Update()
                .set(key, true);
        this.mongoTemplate.updateFirst(query, update, MonitorRoom.class);
    }
}
