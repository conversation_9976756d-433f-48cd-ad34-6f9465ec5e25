package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class SendAllRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private String periodId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "科目 Id")
    @Length(min = 24, max = 24, message = "subjectId 错误")
    private String subjectId;

    @Schema(description = "模板id")
    @Length(min = 6, max = 24, message = "tempId 错误")
    private String tempId;


    @Schema(description = "确认状态")
    private Integer confirmState;

    @Schema(description = "状态")
    private Integer state;

    @Schema(description = "短信状态")
    private Integer smsState;

    @Schema(description = "邮件状态")
    private Integer emailState;

}
