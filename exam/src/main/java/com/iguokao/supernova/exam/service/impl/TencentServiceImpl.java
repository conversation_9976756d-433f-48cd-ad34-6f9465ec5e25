package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.TencentService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.*;
import com.tencentyun.TLSSigAPIv2;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Service
@RequiredArgsConstructor
public class TencentServiceImpl implements TencentService {

    private final CacheService cacheService;
    private final FaceidClient faceidClient;

    @Value("${app.tencent.rtc-app-supernova-id}")
    private Long appId;

    @Value("${app.tencent.rtc-app-supernova-key}")
    private String appKey;

    @Value("${app.tencent.merchant-id}")
    private String merchantId;

    @Override
    public RtcSig getRtcSig(String userId, Integer roomNumber){
        TLSSigAPIv2 api;
        RtcSig res = new RtcSig();
        String sig;
        api = new TLSSigAPIv2(appId, appKey);
        res.setAppId(appId);
        sig = api.genUserSig(userId, 3*60*60);
        res.setSig(sig);
        res.setUserId(userId);
        res.setRoomNumber(roomNumber);
        return res;
    }

    @Override
    public String getEidToken(String candidateId, String fullName, String idNum) {
        GetEidTokenRequest req = new GetEidTokenRequest();
        GetEidTokenConfig config = new GetEidTokenConfig();
        config.setInputType("4");
        req.setConfig(config);
        req.setExtra(candidateId);
        req.setMerchantId(merchantId);
        req.setName(fullName);
        req.setIdCard(idNum);

        // 返回的resp是一个GetEidTokenResponse的实例，与请求对象对应
        try {
            GetEidTokenResponse resp = faceidClient.GetEidToken(req);
            return resp.getEidToken();
        } catch (TencentCloudSDKException e){
            throw new ServiceException(ExceptionEnum.TENCENT_EID_TOKEN_GET_FAILED);
        }
    }

    @Override
    public GetEidResultResponse getEidResult(String token) {
        GetEidResultRequest req = new GetEidResultRequest();
        req.setEidToken(token);
        req.setBestFramesCount(1L);

        try {
            return faceidClient.GetEidResult(req);
        } catch (TencentCloudSDKException e){
            throw new ServiceException(ExceptionEnum.TENCENT_EID_RESULT_GET_FAILED);
        }
    }

}
