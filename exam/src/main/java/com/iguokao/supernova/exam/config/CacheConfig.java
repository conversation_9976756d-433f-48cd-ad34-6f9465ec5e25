package com.iguokao.supernova.exam.config;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import com.iguokao.supernova.exam.document.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * <AUTHOR>
 * 缓存配置类
 */
@Configuration
@Slf4j
@ConfigurationProperties("app.redis")
@Getter
@Setter
class CacheConfig {

    private String host;
    private Integer port;
    private String password;
    private Integer database;
    private Integer maxActive;
    private Integer maxIdle;
    private Integer minIdle;

    @Bean
    LettuceConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration redisConfiguration = new RedisStandaloneConfiguration();
        redisConfiguration.setHostName(host);
        redisConfiguration.setPort(port);
        redisConfiguration.setPassword(password);
        redisConfiguration.setDatabase(database);

        GenericObjectPoolConfig<?> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(maxActive);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);

        LettuceClientConfiguration clientConfiguration = LettucePoolingClientConfiguration.
                builder()
                .poolConfig(poolConfig)
                .build();
        return new LettuceConnectionFactory(redisConfiguration, clientConfiguration);
    }

    @Bean
    public StringRedisTemplate stringRedisTemplate(LettuceConnectionFactory factory) {
        final StringRedisTemplate redisTemplate = new StringRedisTemplate();
        redisTemplate.setConnectionFactory(factory);
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, OperatorRemoteResponse> operatorRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, OperatorRemoteResponse> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(OperatorRemoteResponse.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, Site> siteRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, Site> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Site.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, Room> roomRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, Room> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Room.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, Project> projectRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, Project> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Project.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, Period> periodRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, Period> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Period.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, Candidate> candidateRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, Candidate> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Candidate.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, PeriodRoom> periodRoomRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, PeriodRoom> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(PeriodRoom.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, MonitorSignature> monitorSignatureRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, MonitorSignature> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(MonitorSignature.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, MonitorRoom> monitorRoomRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, MonitorRoom> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new Jackson2JsonRedisSerializer<>(MonitorRoom.class));
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(MonitorRoom.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, MonitorTest> monitorTestRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, MonitorTest> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new Jackson2JsonRedisSerializer<>(MonitorTest.class));
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(MonitorTest.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, Paper> paperRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, Paper> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Paper.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, RoomProjectTest> roomProjectTestRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, RoomProjectTest> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(RoomProjectTest.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, ImageCode> imageCodeRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, ImageCode> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(ImageCode.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, AdmissionCard> admissionCardRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, AdmissionCard> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(AdmissionCard.class));
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, CompanyRemoteResponse> companyRedisTemplate(LettuceConnectionFactory factory) {
        RedisTemplate<String, CompanyRemoteResponse> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(CompanyRemoteResponse.class));
        return redisTemplate;
    }
}

