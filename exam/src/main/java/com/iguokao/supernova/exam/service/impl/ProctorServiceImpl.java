package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.document.QuestionOption;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.QuestionScoreTypeEnum;
import com.iguokao.supernova.common.enums.QuestionTypeEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.repository.PartRepository;
import com.iguokao.supernova.exam.repository.ProctorRepository;
import com.iguokao.supernova.exam.repository.QuestionRepository;
import com.iguokao.supernova.exam.service.ProctorService;
import com.iguokao.supernova.exam.service.QuestionService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class ProctorServiceImpl implements ProctorService {
    private final ProctorRepository proctorRepository;

    @Override
    public void add(Proctor proctor) {
        this.proctorRepository.insert(proctor);
    }

    @Override
    public Proctor getByUuid(String uuid) {
        Proctor proctor = this.proctorRepository.findProctorByUuid(uuid);
        if(null == proctor){
            throw new ServiceException(ExceptionEnum.PROCTOR_NOT_EXIST);
        }
        return proctor;
    }

    @Override
    public void edit(Proctor proctor) {
        this.proctorRepository.save(proctor);
    }

    @Override
    public void select(List<Proctor> proctorList,String projectId) {

        for(int i = proctorList.size() -1 ; i >= 0 ; i--){
            Proctor proctor = proctorList.get(i);
            Proctor checkP = this.proctorRepository.findByProjectIdAndMobile(new ObjectId(projectId), proctor.getMobile());
            if(null == checkP){
                this.add(proctor);
            }
            else {
                proctor.setUuid(checkP.getUuid());
                if(!checkP.getSiteId().toString().equals(proctor.getSiteId().toString())){
                    checkP.setName(proctor.getName());
                    checkP.setSiteId(proctor.getSiteId());
                    this.edit(checkP);
                }
            }
        }
    }
}
