package com.iguokao.supernova.exam.response;
import com.iguokao.supernova.exam.document.Paper;
import com.iguokao.supernova.exam.document.Part;
import com.iguokao.supernova.exam.document.Question;
import com.iguokao.supernova.exam.document.Subject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class TestResponse {

    private ProjectResponse project;
    private PeriodResponse period;
    private List<CandidateResponse> candidateList;
    private String paperId;
    private String paperUrl;
    private String paperPassword;
}

