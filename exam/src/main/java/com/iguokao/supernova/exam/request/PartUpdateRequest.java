package com.iguokao.supernova.exam.request;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class PartUpdateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "小卷id")
    @Length(min = 24, max = 24, message = "24位id")
    private String partId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "时长")
    private Integer duration;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "试题乱序")
    private Boolean questionRandomized;

    @Schema(description = "选项乱序")
    private Boolean optionRandomized;

}