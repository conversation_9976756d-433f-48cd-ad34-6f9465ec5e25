package com.iguokao.supernova.exam.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.exam.document.Subject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class PeriodAddRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目 Id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String projectId; // 项目ID

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @NotNull(message = "开始时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startAt;

    @Schema(description = "时长")
    @Min(value = 60, message = "时长错误")
    @Max(value = 21600, message = "时长错误")
    private Integer duration;
}
