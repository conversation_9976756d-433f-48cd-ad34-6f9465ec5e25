package com.iguokao.supernova.exam.response;
import com.iguokao.supernova.exam.document.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class PaperResponse {

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "时段id")
    private String periodId;

    @Schema(description = "科目")
    private List<PaperSubjectResponse> subjectList = new ArrayList<>();

    private String hash;

    public static PaperResponse of(Period period, List<PaperGroup> paperGroupList){
        PaperResponse res = new PaperResponse();
        res.setProjectId(period.getProjectId().toString());
        res.setPeriodId(period.get_id().toString());
        for(PaperGroup pg : paperGroupList){
            res.getSubjectList().add(PaperSubjectResponse.of(period,pg));
        }
        return res;
    }

}

