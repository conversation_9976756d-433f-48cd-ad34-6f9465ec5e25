package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Document("monitor_room")
@CompoundIndexes({
        @CompoundIndex(def = "{'periodId': -1, 'roomId': -1}", name = "periodId_roomId_index")
})
public class MonitorRoom extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId projectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId roomId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId siteId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId periodId;

    private String province;
    private String city;
    private String roomName;

    private Integer candidateCount = 0;

    private Integer roomState = 0;
    private Integer candidateArrivedCount = 0;
    private Integer candidateFinishedCount = 0;
    private Integer candidateAnsweringCount = 0;
    private Integer candidateLoginCount = 0;

    private Integer cheatCount = 0;
    private Integer forceFinishedCount = 0;
    private Integer lateCount = 0;
    private Integer delayCount = 0;
    private Integer operatorSignatureCount = 0;
    private Integer roomIndex; // 考场编号

    private Boolean candidateDownloaded = false;
    private Boolean paperDownloaded = false;
    private Boolean passwordGet = false;
    private Boolean answerUploaded = false;
    private Boolean actionUploaded = false;

    public static MonitorRoom of(PeriodRoom periodRoom, String province, String city, String roomName){
        MonitorRoom monitorRoom = new MonitorRoom();
        monitorRoom.setProjectId(periodRoom.getProjectId());
        monitorRoom.setPeriodId(periodRoom.getPeriodId());
        monitorRoom.setSiteId(periodRoom.getSiteId());
        monitorRoom.setRoomId(periodRoom.getRoomId());
        monitorRoom.setCandidateCount(periodRoom.getCandidateCount());
        monitorRoom.setRoomIndex(periodRoom.getRoomIndex());
        monitorRoom.setProvince(province);
        monitorRoom.setCity(city);
        monitorRoom.setRoomName(roomName);

        return monitorRoom;
    }

}
