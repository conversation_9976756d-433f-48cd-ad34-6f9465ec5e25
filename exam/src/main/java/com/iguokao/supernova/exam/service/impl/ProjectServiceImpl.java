package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ShortUrlRequest;
import com.iguokao.supernova.common.remote.StoneExamRemote;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.util.DateUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ConfirmStateEnum;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.enums.PeriodStateEnum;
import com.iguokao.supernova.exam.enums.ProjectTypeEnum;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.PeriodRoomService;
import com.iguokao.supernova.exam.service.ProjectService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ProjectServiceImpl implements ProjectService {
    private final ProjectRepository projectRepository;
    private final MongoTemplate mongoTemplate;
    private final PeriodRepository periodRepository;
    private final CacheService cacheService;
    private final ConfirmRepository confirmRepository;
    private final PeriodRoomService periodRoomService;
    private final StoneExamRemote stoneExamRemote;
    private final PeriodRoomRepository periodRoomRepository;
    private final CandidateRepository candidateRepository;

    @Value(value = "${app.admission-url}")
    private String admissionUrl;

    @Override
    public String add(Project project) {
        if(this.projectRepository.countByName(project.getName()) > 0){
            throw new ServiceException(ExceptionEnum.PROJECT_EXIST);
        }
        Pageable pageable = PageRequest.of(
                0,
                1,
                Sort.by("_id").descending());
        Page<Project> page = this.projectRepository.findAll(pageable);
        if(!page.getContent().isEmpty() && page.getContent().get(0).getNum() != null){
            project.setNum(page.getContent().get(0).getNum() + 1);
        } else {
            project.setNum(1000);
        }
        project.setClientVersion(this.cacheService.getClientVersion());
        project = this.projectRepository.insert(project);
        return project.get_id().toString();
    }

    @Override
    public void update(Project project) {
        Project exist = this.getById(project.get_id().toString());
        List<Period> list = this.periodRepository.findByProjectId(exist.get_id());
        if(project.getFixedPosition() != exist.getFixedPosition()){
            if(list.stream().anyMatch(p -> p.getState().equals(PeriodStateEnum.ARRANGED.getCode()))){
                throw new ServiceException(ExceptionEnum.PERIOD_ARRANGED);
            }
        }
        //判断其中的时段是否存在已经生成完准考证的
        if(list.stream().anyMatch(p -> p.getAdmissionCardCount() > 0)){
            throw new ServiceException(ExceptionEnum.PERIOD_ADMISSION_CARD_EXISTS);
        }

        BeanUtils.copyProperties(project, exist, "attachmentList");
        this.projectRepository.save(exist);
        this.cacheService.deleteProject(project.get_id().toString());
    }

    @Override
    public void updateClientVersion(String projectId, String version) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(projectId)));
        Update update = new Update();
        update.set("clientVersion", version);
        mongoTemplate.updateFirst(query, update, Project.class);
        this.cacheService.deleteProject(projectId);
    }


    @Override
    public void remove(String projectId) {
        Project project = this.getById(projectId);
        if(project.getOnline()){
            throw new ServiceException(ExceptionEnum.PROJECT_ONLINE);
        }
        if(this.periodRepository.countByProjectId(new ObjectId(projectId)) > 0){
            throw new ServiceException(ExceptionEnum.PERIOD_EXIST);
        }
        if(this.periodRoomRepository.countByProjectId(new ObjectId(projectId)) > 0){
            throw new ServiceException(ExceptionEnum.PERIOD_ROOM_EXIST);
        }
        if(this.candidateRepository.countByProjectId(new ObjectId(projectId)) > 0){
            throw new ServiceException(ExceptionEnum.CANDIDATE_EXIST);
        }

        this.projectRepository.deleteById(new ObjectId(projectId));
        this.cacheService.deleteProject(projectId);
    }

    @Override
    public Project getById(String projectId) {
        return this.projectRepository.findById(new ObjectId(projectId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
    }

    @Override
    public List<Project> getByIdList(List<ObjectId> idList) {
        return this.projectRepository.findBy_idIn(idList);
    }

    @Override
    public Tuple2<List<Project>, Integer> getPage(String companyId, String name, Pageable pageable) {
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "_id", "createdAt"));
        if(null != companyId){
            query = query.addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));
        }
        if(null != name){
            query = query.addCriteria(Criteria.where("name").is(name));
        }
        // 计算总数
        long count = this.mongoTemplate.count(query, Project.class);
        // 分页信息
        query = query.with(pageable);
        List<Project> list = this.mongoTemplate.find(query, Project.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public boolean online(String projectId) {
        Project project = this.getById(projectId);
        if(project.getOnline()){
            throw new ServiceException(ExceptionEnum.PROJECT_ONLINE);
        }
        List<Period> list = this.periodRepository.findByProjectId(new ObjectId(projectId));
        if(list
                .stream()
                .map(Period::getSubjectList)
                .mapToLong(Collection::size)
                .sum() == 0){
            throw new ServiceException(ExceptionEnum.SUBJECT_EMPTY);
        }
        if(this.confirmRepository.countByProjectId(new ObjectId(projectId)) == 0){
            throw new ServiceException(ExceptionEnum.PROJECT_NO_CONFIRM);
        }
        if(this.confirmRepository.countByProjectIdAndState(new ObjectId(projectId), ConfirmStateEnum.NOT_CONFIRMED.getCode()) > 0){
            throw new ServiceException(ExceptionEnum.PROJECT_HAS_NOT_CONFIRM);
        }
        //处理短链接
        String loginUrl = admissionUrl + "/login?projectId=AAA&companyId=BBB&projectName=CCC";
        loginUrl = loginUrl.replaceAll("AAA",project.get_id().toString());
        loginUrl = loginUrl.replaceAll("BBB",project.getCompanyId().toString());
        String encodeProjectName;
        try {
            encodeProjectName = URLEncoder.encode(project.getName(), StandardCharsets.UTF_8);
        }
        catch (Exception e){
            throw new ServiceException(ExceptionEnum.PROJECT_NO_CONFIRM);
        }
        loginUrl = loginUrl.replaceAll("CCC",encodeProjectName);

        ShortUrlRequest shortUrlRequest = new ShortUrlRequest();
        shortUrlRequest.setUrl(loginUrl);
        RestResponse<String> res = stoneExamRemote.genShortUrl(shortUrlRequest);

        Query query = new Query(Criteria.where("_id").is(new ObjectId(projectId)));
        Update update = new Update();
        update.set("online", !project.getOnline());
        update.set("shortCode", res.getData());


        mongoTemplate.updateFirst(query, update, Project.class);
        this.cacheService.deleteProject(projectId);
        list.forEach(period -> {
            this.periodRoomService.initMonitorRoom(period.get_id().toString(), false);
        });
        return !project.getOnline();
    }

    @Override
    public void addAttachment(String projectId, Attachment attachment) {
        Project project = this.getById(projectId);
        for(Attachment att : project.getAttachmentList()){
            if(att.getName().equals(attachment.getName())){
                throw new ServiceException(ExceptionEnum.PROJECT_ATTACHMENT_EXIST);
            }
        }
        project.getAttachmentList().add(attachment);
        this.projectRepository.save(project);
        this.cacheService.deleteProject(projectId);
    }

    @Override
    public void deleteAttachment(String projectId, String attachmentName) {
        Project project = this.getById(projectId);
        project.getAttachmentList().removeIf(attachment -> attachment.getName().equals(attachmentName));
        this.projectRepository.save(project);
        this.cacheService.deleteProject(projectId);
    }

    @Override
    public Project getTestProject(Integer candidateCount) {
        Project project = new Project();
        project.set_id(new ObjectId());
        project.setName(String.format("试考项目%s", DateUtil.dateToStr(new Date())));
        project.setShortName("测试");
        project.setCandidateCount(candidateCount);
        project.setCompanyId(new ObjectId());
        project.setOnline(true);

        LocalDateTime now = LocalDateTime.now();
        project.setStartAt(Date.from(now.with(LocalTime.MIN).atZone(ZoneId.of("GMT+8")).toInstant()));
        project.setEndAt(Date.from(now.with(LocalTime.MAX).atZone(ZoneId.of("GMT+8")).toInstant()));
        return project;
    }

    @Override
    public Tuple2<List<Project>, List<PeriodRoom>> getAgentProjectList(String siteId) {
        List<PeriodRoom> list = this.periodRoomRepository.findBySiteId(new ObjectId(siteId));
        List<String> projectIdList = list
                .stream()
                .map(PeriodRoom::getProjectId)
                .map(ObjectId::toString)
                .distinct()
                .toList();
        List<Project> projectList = this.cacheService.getProject(projectIdList);
        final List<Project> agentProjectList = projectList
                .stream()
                .filter(p -> p.getType() != null && p.getType().equals(ProjectTypeEnum.THREE_LEVEL_AGENT.getCode()))
                .toList();

        List<PeriodRoom> agentPeriodRoomList = list
                .stream()
                .filter(pr -> agentProjectList.stream().anyMatch(p -> p.get_id().equals(pr.getProjectId())))
                .toList();

        return new Tuple2<>(agentProjectList, agentPeriodRoomList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(pr -> pr.getSiteId().toString() + "_" + pr.getPeriodId().toString())
                        )),
                        ArrayList::new
                )));
    }
}
