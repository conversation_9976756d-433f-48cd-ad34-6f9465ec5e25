package com.iguokao.supernova.exam.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.OssSign;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import com.iguokao.supernova.common.response.OssSignResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.common.util.Md5Util;
import com.iguokao.supernova.common.util.ProfileUtil;
import com.iguokao.supernova.common.util.RequestUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.common.enums.ActionTypeEnum;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.enums.PeriodStateEnum;
import com.iguokao.supernova.exam.enums.RoomStateEnum;
import com.iguokao.supernova.exam.request.*;
import com.iguokao.supernova.exam.response.*;
import com.iguokao.supernova.exam.service.*;
import com.tencentcloudapi.faceid.v20180301.models.GetEidResultResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v1/client")
@RequiredArgsConstructor
public class ClientController {
    private final RoomService roomService;
    private final JwtService jwtService;
    private final CacheService cacheService;
    private final ActionService actionService;
    private final ProjectService projectService;
    private final PeriodService periodService;
    private final MonitorService monitorService;
    private final PeriodRoomService periodRoomService;
    private final CandidateService candidateService;
    private final OssService ossService;
    private final TencentService tencentService;
    private final Environment environment;

    @Value(value = "${app.test-paper-id}")
    private String testPaperId;

    @Value(value = "${app.test-period-id}")
    private String testPeriodId;

    @Value(value = "${app.ali.oss-bucket-exam}")
    private String ossBucketExam;

    @Value(value = "${app.test-paper-password}")
    private String testPaperPassword;

    @PostMapping("/login")
    @Operation(summary = "房间登录码登录", security = @SecurityRequirement(name = "ignore"))
    @ApiResponse(content = @Content(schema = @Schema(implementation = RoomLoginRequest.class)))
    public RestResponse<String> login(@RequestBody RoomLoginRequest request, HttpServletRequest req) {
        ImageCode imageCode = new ImageCode();
        BeanUtils.copyProperties(request, imageCode);
        String token = this.roomService.roomLogin(imageCode, request.getLoginCode(), request.getVersion(), RequestUtil.getClientIp(req));
        return RestResponse.success(token);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/site/info")
    @Operation(summary = "考点信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = SiteResponse.class)))
    public RestResponse<SiteResponse> siteInfo() {
        String siteId =  this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        Site site = this.cacheService.getSite(siteId);
        return RestResponse.success(SiteResponse.of(site));
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/room/info")
    @Operation(summary = "考场信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = RoomResponse.class)))
    public RestResponse<RoomResponse> roomInfo() {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        Room room = this.cacheService.getRoom(roomId);
        return RestResponse.success(RoomResponse.of(room,null, false));
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/project/test/list")
    @Operation(summary = "查看所有项目")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoomProjectTestResponse.class))))
    public RestResponse<List<RoomProjectTestResponse>> projectTestList() {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        List<RoomProjectTest> list = this.cacheService.getRoomProjectTest(roomId);
        List<RoomProjectTestResponse> res = RoomProjectTestResponse.of(list);
        return RestResponse.success(res
                .stream()
                .sorted(Comparator.comparing(rpt -> rpt.getTestActionList().size()))
                .toList());
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/period/list")
    @Operation(summary = "查看所有项目时段")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = PeriodRoomResponse.class))))
    public RestResponse<List<PeriodRoomResponse>> periodList() {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        return RestResponse.success(this.getPeriodRoomRes(roomId));
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/candidate/list/{periodId}")
    @Operation(summary = "房间内的考生")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ProjectResponse.class))))
    public RestResponse<List<CandidateResponse>> candidateList(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        List<Candidate> list = this.candidateService.getRoomCandidateList(periodId, roomId);
        Period period = this.cacheService.getPeriod(periodId);
        List<CandidateResponse> res = CandidateResponse.of(list, period);
        Project project = this.cacheService.getProject(period.getProjectId().toString());
        int duration = (int)((project.getEndAt().getTime() - new Date().getTime()) / 1000) + 3600*24*7;
        res.forEach(cr -> {
            if(cr.getAvatar() != null){
                cr.setAvatarUrl(this.ossService.generateSignedUrl(ossBucketExam, cr.getAvatar(), duration));
            }
        });
        this.monitorService.setCandidateGet(periodId, roomId);
        return RestResponse.success(res);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/project/test")
    @Operation(summary = "测试结果提交")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> test(@Validated @RequestBody TerminalTestRequest request) throws JsonProcessingException {
        String siteId =  this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        Action action = new Action();
        action.setProjectId(new ObjectId(request.getProjectId()));
        action.setRoomId(new ObjectId(roomId));
        action.setSiteId(new ObjectId(siteId));
        action.setType(request.getType());
        action.setValue(request.getPassed()? 1 : 0);
        action.setText(new ObjectMapper().writeValueAsString(request));

        this.actionService.add(action);
        this.cacheService.deleteRoomProjectTest(roomId);

        MonitorTest test = this.monitorService.getRoomTest(request.getProjectId(), roomId);
        if(test == null){
            test = new MonitorTest();
            test.setProjectId(new ObjectId(request.getProjectId()));
            test.setSiteId(new ObjectId(siteId));
            test.setRoomId(new ObjectId(roomId));
        }
        if(request.getType().equals(ActionTypeEnum.EXAM_TEST.getCode())){
            test.setExamTested(request.getPassed());
            test.setExamError(request.getError());
        } else if(request.getType().equals(ActionTypeEnum.ENV_CHECK.getCode())){
            test.setEnvTested(request.getPassed());
            test.setEnvError(request.getError());
        } else if(request.getType().equals(ActionTypeEnum.CAMERA_TEST.getCode())){
            test.setCameraTested(request.getPassed());
        }
        this.monitorService.setRoomTest(test);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/paper/{periodId}")
    @Operation(summary = "获取试卷")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ClientPaperResponse.class))))
    public RestResponse<ClientPaperResponse> paper(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        Period period = this.cacheService.getPeriod(periodId);
        if(!period.getReady()){
            throw new ServiceException(ExceptionEnum.PERIOD_NOT_READY);
        }
        if(period.getState().equals(PeriodStateEnum.FINISHED.getCode())){
            throw new ServiceException(ExceptionEnum.PERIOD_FINISHED);
        }
        int duration = (int)((period.getStartAt().getTime() - new Date().getTime()) / 1000 / 60);
        if(!ProfileUtil.isTestOrDev(environment) && duration > 240){
            throw new ServiceException(ExceptionEnum.PAPER_PASSWORD_NOT_ON_TIME);
        }
        String siteId =  this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);

        Action action = new Action();
        action.setSiteId(new ObjectId(siteId));
        action.setRoomId(new ObjectId(roomId));
        action.setPeriodId(new ObjectId(periodId));
        action.setType(ActionTypeEnum.DATA_PAPER.getCode());
        this.actionService.add(action);
        this.monitorService.setPaperDownloaded(periodId, roomId);

        return RestResponse.success(ClientPaperResponse.of(period, ossService, ossBucketExam));
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/paper/password/{periodId}")
    @Operation(summary = "获取试卷密码")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PaperPasswordResponse.class)))
    public RestResponse<PaperPasswordResponse> paperPassword(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        Period period = this.cacheService.getPeriod(periodId);
        String siteId =  this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        if(!period.getReady()){
            throw new ServiceException(ExceptionEnum.PERIOD_NOT_READY);
        }
        if(period.getState().equals(PeriodStateEnum.FINISHED.getCode())){
            throw new ServiceException(ExceptionEnum.PERIOD_FINISHED);
        }
        int duration = (int)((period.getStartAt().getTime() - new Date().getTime()) / 1000 / 60);
        if(!ProfileUtil.isTestOrDev(environment) && duration > 70){
            throw new ServiceException(ExceptionEnum.PAPER_PASSWORD_NOT_ON_TIME);
        }

        //更新状态
        Action action = new Action();
        action.setSiteId(new ObjectId(siteId));
        action.setRoomId(new ObjectId(roomId));
        action.setPeriodId(new ObjectId(periodId));
        action.setType(ActionTypeEnum.PASSWORD_GET.getCode());
        this.actionService.add(action);
        this.monitorService.setPasswordGet(periodId, roomId);

        return RestResponse.success(PaperPasswordResponse.of(period));
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/candidate/signature")
    @Operation(summary = "考生签到")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> signature(@Validated @RequestBody CandidateSignatureRequest request) {
        String siteId =  this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        MonitorSignature signature = new MonitorSignature();
        signature.setFullName(request.getFullName());
        signature.setAvatar(request.getAvatar());
        signature.setCandidateId(request.getCandidateId());
        signature.setRoomId(roomId);
        signature.setPeriodId(request.getPeriodId());
        signature.setSiteId(siteId);
        if(request.getCanceled() != null && request.getCanceled()){
            this.cacheService.deleteMonitorSignature(signature);
            return RestResponse.success();
        }
        this.cacheService.setMonitorSignature(signature);
        String url = null;
        if(request.getAvatar() != null && !request.getAvatar().isEmpty()){
            Project project = this.cacheService.getProject(request.getProjectId());
            int duration = (int)((project.getEndAt().getTime() - new Date().getTime()) / 1000);
            url = this.ossService.generateSignedUrl(ossBucketExam, request.getAvatar(), duration);
        }
        return RestResponse.success(url);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/operator/signature")
    @Operation(summary = "监考官签到")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> operatorSignature(@Validated @RequestBody OperatorSignatureRequest request) {
        String siteId =  this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        MonitorSignature signature = new MonitorSignature();
        signature.setFullName(request.getFullName());
        signature.setAvatar(request.getAvatar());
        signature.setRoomId(roomId);
        signature.setPeriodId(request.getPeriodId());
        signature.setSiteId(siteId);
        signature.setNote(request.getMobile());
        this.cacheService.setMonitorSignature(signature);
        this.monitorService.incOperatorSignatureCount(request.getPeriodId(), roomId);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/operator/signature/list/{periodId}")
    @Operation(summary = "监考官签到 记录")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<MonitorSignatureResponse>> operatorSignatureList(@PathVariable String periodId) {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        List<MonitorSignature> list = this.cacheService.getMonitorSignatureList(periodId, roomId);
        List<MonitorSignatureResponse> monitorSignatureResponseList = MonitorSignatureResponse.of(list
                .stream()
                .filter(monitorSignature -> monitorSignature.getCandidateId() == null)
                .toList());
        return RestResponse.success(monitorSignatureResponseList);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/monitor/room/tick")
    @Operation(summary = "房间状态 定时更新")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> roomState(@Validated @RequestBody RoomStateRequest request) {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        MonitorRoom monitorRoom = new MonitorRoom();
        monitorRoom.setPeriodId(new ObjectId(request.getPeriodId()));
        monitorRoom.setRoomId(new ObjectId(roomId));

        monitorRoom.setRoomState(request.getRoomState());
        monitorRoom.setCandidateArrivedCount(request.getCandidateArrivedCount());
        monitorRoom.setCandidateFinishedCount(request.getCandidateFinishedCount());
        monitorRoom.setCheatCount(request.getCheatCount());
        monitorRoom.setForceFinishedCount(request.getForceFinishedCount());
        monitorRoom.setLateCount(request.getLateCount());
        monitorRoom.setDelayCount(request.getDelayCount());
        monitorRoom.setCandidateLoginCount(request.getCandidateLoginCount());
        monitorRoom.setCandidateAnsweringCount(request.getCandidateAnsweringCount());

        this.monitorService.update(monitorRoom);
        return RestResponse.success();
    }

    //@PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/test/data/{candidateCount}")
    @Operation(summary = "试考数据")
    @ApiResponse(content = @Content(schema = @Schema(implementation = TestResponse.class)))
    public RestResponse<TestResponse> testProject(@PathVariable String candidateCount) {
        TestResponse testResponse = new TestResponse();
        String siteId = this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        String roomId = this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        Project project = this.projectService.getTestProject(Integer.parseInt(candidateCount));
        Period period = this.periodService.getTestPeriod(project,this.testPaperId);
        List<Candidate> candidateList = this.candidateService.getTestCandidateList(project,
                period,
                Integer.parseInt(candidateCount),
                this.testPaperId,
                siteId,
                roomId);

        testResponse.setProject(ProjectResponse.of(project,null));
        testResponse.setCandidateList(CandidateResponse.of(candidateList));
        testResponse.setPeriod(PeriodResponse.of(period));
        testResponse.getPeriod().getSubjectList().clear();
        testResponse.setPaperId(this.testPaperId);
        testResponse.setPaperPassword(testPaperPassword);
        String key = String.format("paper/%s.ikp", testPeriodId);
        testResponse.setPaperUrl(ossService.generateSignedUrl(ossBucketExam, key, 180));
        //this.periodService.readyV2(period);
        return RestResponse.success(testResponse);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/oss/sign")
    @Operation(summary = "获取Oss Sign")
    @ApiResponse(content = @Content(schema = @Schema(implementation = OssSignResponse.class)))
    public RestResponse<OssSignResponse> ossSign() {
        OssSign sign = this.ossService.getOssSign(ossBucketExam, 1800);
        return RestResponse.success(OssSignResponse.of(sign));
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/oss/sign/{projectId}")
    @Operation(summary = "获取Oss Project Sign")
    @ApiResponse(content = @Content(schema = @Schema(implementation = OssSignResponse.class)))
    public RestResponse<OssSignResponse> ossProjectSign(@PathVariable String projectId) {
        Project project = this.cacheService.getProject(projectId);
        int duration = (int)((project.getEndAt().getTime() - new Date().getTime()) / 1000);
        OssSign sign = this.ossService.getOssSign(ossBucketExam, duration);
        return RestResponse.success(OssSignResponse.of(sign));
    }

    @GetMapping("/time")
    @Operation(summary = "获取服务器时间")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Long.class)))
    public RestResponse<Long> time() {
        return RestResponse.success(new Date().getTime() / 1000);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/rtc/sig/{periodId}")
    @Operation(summary = "推流 sig")
    @ApiResponse(content = @Content(schema = @Schema(implementation = RoomRtcSigResponse.class)))
    public RestResponse<RoomRtcSigResponse> rtcSig(@PathVariable String periodId) {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        PeriodRoom periodRoom = this.periodRoomService.getById(periodId, roomId);
        Integer roomNumber = periodRoom.getStreamRoomNumber();
        if(roomNumber == null){
            roomNumber = this.cacheService.getNextRoomNumber();
        }
        String userIdPrimary = String.format("%s_%s_p", periodId, roomId);
        String userIdSecondary = String.format("%s_%s_s", periodId, roomId);
        RtcSig rtcSigPrimary = this.tencentService.getRtcSig(userIdPrimary, roomNumber);
        RtcSig rtcSigSecondary = this.tencentService.getRtcSig(userIdSecondary, roomNumber);
        RoomRtcSigResponse res = new RoomRtcSigResponse();
        res.setPrimary(RtcSigResponse.of(rtcSigPrimary));
        res.setSecondary(RtcSigResponse.of(rtcSigSecondary));
        if(periodRoom.getStreamRoomNumber() == null){
            this.periodRoomService.setRoomNumber(periodId, roomId, roomNumber);
        }
        return RestResponse.success(res);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/rtc/period/list")
    @Operation(summary = "推流场次")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = PeriodRoomResponse.class))))
    public RestResponse<List<PeriodRoomResponse>> rtcPeriodList() {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        List<PeriodRoomResponse> res = this.getPeriodRoomRes(roomId);
        res.removeIf(pr ->  new Date().getTime() < pr.getProject().getEndAt().getTime());
        return RestResponse.success();
    }

    private List<PeriodRoomResponse> getPeriodRoomRes(String roomId){
        List<PeriodRoom> list = this.cacheService.getPeriodRoomList(roomId);
        List<String> projectIdList = list
                .stream()
                .map(PeriodRoom::getProjectId)
                .map(ObjectId::toString)
                .distinct()
                .toList();
        List<String> periodIdList = list
                .stream()
                .map(PeriodRoom::getPeriodId)
                .map(ObjectId::toString)
                .distinct()
                .toList();
        List<Project> projectList = this.cacheService.getProject(projectIdList);
        List<Period> periodList = this.cacheService.getPeriod(periodIdList);
        return PeriodRoomResponse.of(list, projectList, periodList);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/action/add")
    @Operation(summary = "添加房间事件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> addAction(@RequestBody ActionAddRequest request) {

        Action action = new Action();
        action.setProjectId(new ObjectId(request.getProjectId()));
        action.setPeriodId(new ObjectId(request.getPeriodId()));
        action.setSiteId(new ObjectId(request.getSiteId()));
        action.setRoomId(new ObjectId(request.getRoomId()));
        if(request.getCandidateId() != null){
            action.setCandidateId(new ObjectId(request.getCandidateId()));
        }
        BeanUtils.copyProperties(request, action);
        this.actionService.add(action);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/company/{companyId}")
    @Operation(summary = "企业信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<CompanyResponse> company(@PathVariable String companyId) {
        CompanyRemoteResponse remoteResponse = this.cacheService.getCompany(companyId);
        CompanyResponse res = CompanyResponse.of(remoteResponse);
        res.setLogoUrl(this.ossService.generateSignedUrl(ossBucketExam, res.getLogo(), 180));
        res.setThemeUrl(this.ossService.generateSignedUrl(ossBucketExam, res.getTheme(), 180));
        return RestResponse.success(res);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/exchange")
    @Operation(summary = "换房间")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<CompanyRemoteResponse> exchange(@RequestBody CandidateRoomExchangeRequest request) {
        String siteId = this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        this.candidateService.exchange(request.getCandidateId(), request.getRoomId(), siteId);
       return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/room/list")
    @Operation(summary = "房间列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoomResponse.class))))
    public RestResponse<List<RoomResponse>> roomList() {
        String siteId = this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        List<Room> list = this.roomService.getList(siteId, null);
        return RestResponse.success(RoomResponse.of(list, false));
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/room/action/{periodId}/{roomId}")
    @Operation(summary = "上传房间日志")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> actionUpload(@PathVariable String periodId, @PathVariable String roomId) {
        PeriodRoom periodRoom = this.periodRoomService.getById(periodId, roomId);
        if(periodRoom.getRoomState().equals(RoomStateEnum.ANSWER_UPLOADED.getCode())){
            this.periodRoomService.setRoomState(periodId, roomId,RoomStateEnum.ACTION_UPLOADED);
        } else {
            throw new ServiceException(ExceptionEnum.PERIOD_ROOM_STATE_NOT_ANSWER_UPLOADED);
        }
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/transfer/out")
    @Operation(summary = "房间转出")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> exchangeOut(@RequestBody TransRoomRequest request) {
        this.candidateService.transferOut(request.getTransRoomId(), request.getCandidateIdList());
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/transfer/in")
    @Operation(summary = "转出房间选择")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> transferIn(@RequestBody TransRoomRequest request) {
        this.candidateService.transferIn(request.getCandidateIdList());
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/transfer/candidate/{periodId}")
    @Operation(summary = "可以转入的人")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = CandidateResponse.class))))
    public RestResponse<List<CandidateResponse>> transferCandidate(@PathVariable String periodId) {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        List<Candidate> list = this.candidateService.transferring(periodId, roomId);
        return RestResponse.success(CandidateResponse.of(list));
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/transfer/room/{periodId}")
    @Operation(summary = "可以转入的人")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = TransRoomResponse.class))))
    public RestResponse<List<TransRoomResponse>> transferRoom(@PathVariable String periodId) {
        String siteId = this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        List<PeriodRoom> list =  this.periodRoomService.getRoomListByPeriodId(periodId, siteId);
        List<Room> roomList =  this.roomService.getByIdList(list
                .stream()
                .map(PeriodRoom::getRoomId)
                .toList());
        return RestResponse.success(TransRoomResponse.of(roomList, list));
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/change/seat")
    @Operation(summary = "考生座位")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> changeSeatNum(@RequestBody SeatNumChangeRequest request) {
        this.candidateService.changeSeatNum(request.getCandidateId(), request.getSeatNum());
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/attachment/upload/{periodId}")
    @Operation(summary = "上传房间附件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> attachmentUpload(@PathVariable String periodId, @RequestBody List<String> list) {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        this.periodRoomService.updateAttachment(periodId, roomId, list);
        return RestResponse.success();
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/room/num/{periodId}")
    @Operation(summary = "考场编号")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<Integer> room(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        String roomId =  this.jwtService.getId(IdConstant.ROOM_ID_PREFIX);
        List<PeriodRoom> list = this.cacheService.getPeriodRoomList(roomId);
        PeriodRoom periodRoom = list
                .stream()
                .filter(pr -> pr.getPeriodId().toString().equals(periodId))
                .findFirst()
                .orElse(null);
        return RestResponse.success(periodRoom == null ? 0 : periodRoom.getRoomIndex() +1);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/token/s/{periodId}/{roomId}")
    @Operation(summary = "短 token")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> tokenShort(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId,
                                           @PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String roomId,
                                           HttpServletRequest request) {
        String key = Md5Util.genSign(String.format("%s%s", periodId, roomId));
        this.cacheService.setToken(key, request.getHeader("Authorization"));
        return RestResponse.success(key);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @GetMapping("/version")
    @Operation(summary = "客户端 版本")
    public RestResponse<String> clientVersion() {
        return RestResponse.success(this.cacheService.getClientVersion());
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/eid/token")
    @Operation(summary = "eid token")
    public RestResponse<String> eidToken(@RequestBody EidTokenRequest request) {
        String token = tencentService.getEidToken(request.getCandidateId(), request.getFullName(), request.getIdNum());
        return RestResponse.success(token);
    }

    @PreAuthorize("hasAuthority('ROLE_ROOM')")
    @PostMapping("/eid/result")
    @Operation(summary = "eid token")
    public RestResponse<GetEidResultResponse> eidResult(@RequestBody EidResultRequest request) {
        GetEidResultResponse res = tencentService.getEidResult(request.getToken());
        return RestResponse.success(res);
    }
}
