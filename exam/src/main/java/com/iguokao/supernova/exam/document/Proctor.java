package com.iguokao.supernova.exam.document;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import java.util.UUID;

@Getter
@Setter
public class Proctor extends BaseDocument {
    @Indexed(name = "projectId_index")
    private ObjectId projectId;
    private String name;
    private String mobile;
    private ObjectId siteId;
    @Indexed(name = "uuid_index")
    private String uuid = UUID.randomUUID().toString().replace("-", "");
}
