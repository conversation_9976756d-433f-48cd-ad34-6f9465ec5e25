package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Site;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class SiteResponse {

    @Schema(description = "考点Id")
    private String siteId;

    @Schema(description = "考点名称")
    private String name;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "可用")
    private Integer available;

    @Schema(description = "总机位数")
    private Integer capacity;

    @Schema(description = "房间数量")
    private Integer roomCount;

    @Schema(description = "街道")
    private String district;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "交通方式")
    private String traffic;

    @Schema(description = "考点负责人")
    private String manager; // 考点负责人

    @Schema(description = "考点负责人手机号")
    private String managerMobile; // 考点负责人手机号

    @Schema(description = "考点技术老师")
    private String itContract;

    @Schema(description = "考点技术老师手机号")
    private String itMobile;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "是否锁定")
    private Boolean locked;

    @Schema(description = "到邮寄地址")
    private String expressAddress;

    @Schema(description = "电子屏幕")
    private Boolean screen;

    private List<AgentResponse> agentList;

    public static SiteResponse of(Site obj){
        if(obj==null){
            return null;
        }
        SiteResponse res = new SiteResponse();
        BeanUtils.copyProperties(obj, res);
        res.setSiteId(obj.get_id().toString());

        res.setAgentList(obj.getAgentList()
                .stream()
                .map(agent -> {
                    AgentResponse agentResponse = new AgentResponse();
                    BeanUtils.copyProperties(agent, agentResponse);
                    return agentResponse;
                })
                .toList());
        return res;
    }

    public static List<SiteResponse> of(List<Site> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<SiteResponse> res = new ArrayList<>();
        for(Site obj : list){
            res.add(of(obj));
        }
        return res;
    }

    @Getter
    @Setter
    public static class AgentResponse{
        private String name;
        private String ip;
        private String loginCode;
        private String remark;
    }
}
