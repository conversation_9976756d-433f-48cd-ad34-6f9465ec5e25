package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import com.iguokao.supernova.common.response.ActionRemoteResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
@NoArgsConstructor
public class Action extends BaseDocument implements Serializable {

    @JsonSerialize(using = ObjectIdSerializer.class)
    @Indexed(name = "candidateId_index")
    private ObjectId candidateId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    @Indexed(name = "projectId_index")
    private ObjectId projectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    @Indexed(name = "periodId_index")
    private ObjectId periodId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId subjectId;

    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId siteId;

    @Indexed(name = "roomId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId roomId;

    private Integer type;
    private Integer value;
    private String text;


    public static ActionRemoteResponse toRemoteResponse(Action obj){
        if(obj==null){
            return null;
        }
        ActionRemoteResponse res = new ActionRemoteResponse();
        BeanUtils.copyProperties(obj, res);
        if(obj.get_id() != null){
            res.setActionId(obj.get_id().toString());
        }
        if(obj.getCandidateId() != null){
            res.setCandidateId(obj.getCandidateId().toString());
        }
        if(obj.getProjectId() != null){
            res.setProjectId(obj.getProjectId().toString());
        }
        if(obj.getPeriodId() != null){
            res.setPeriodId(obj.getPeriodId().toString());
        }
        if(obj.getSubjectId() != null){
            res.setSubjectId(obj.getSubjectId().toString());
        }
        if(obj.getSiteId() != null){
            res.setSiteId(obj.getSiteId().toString());
        }
        if(obj.getRoomId() != null){
            res.setRoomId(obj.getRoomId().toString());
        }
        return res;
    }

    public static List<ActionRemoteResponse> toRemoteResponse(List<Action> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<ActionRemoteResponse> res = new ArrayList<>();
        for(Action obj : list){
            res.add(toRemoteResponse(obj));
        }
        return res;
    }
}
