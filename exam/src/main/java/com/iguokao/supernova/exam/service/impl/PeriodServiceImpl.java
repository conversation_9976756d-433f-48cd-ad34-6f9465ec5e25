package com.iguokao.supernova.exam.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iguokao.supernova.common.enums.BaseExceptionEnum;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.common.util.Md5Util;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.common.util.TeaUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.enums.PeriodStateEnum;
import com.iguokao.supernova.exam.excel.CandidateArrangedItem;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.response.PaperResponse;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.PeriodRoomService;
import com.iguokao.supernova.exam.service.PeriodService;
import com.mongodb.bulk.BulkWriteResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;


@Service
@Slf4j
@RequiredArgsConstructor
public class PeriodServiceImpl implements PeriodService {
    private final PeriodRepository periodRepository;
    private final ProjectRepository projectRepository;
    private final CandidateRepository candidateRepository;
    private final MongoTemplate mongoTemplate;
    private final ConfirmRepository confirmRepository;
    private final SiteRepository siteRepository;
    private final PartRepository partRepository;
    private final RoomRepository roomRepository;
    private final PaperRepository paperRepository;
    private final QuestionRepository questionRepository;
    private final OssService ossService;
    private final PeriodRoomService periodRoomService;
    private final CacheService cacheService;

    @Value("${app.tmp-path}")
    private String tmpPath;

    @Value(value = "${app.test-period-id}")
    private String testPeriodId;

    @Value(value = "${app.test-subject-id}")
    private String testSubjectId;

    @Value("${app.ali.oss-bucket-exam}")
    private String ossBucketExam;

    @Value(value = "${app.test-paper-password}")
    private String testPaperPassword;

    //上传测试练习题到oss
    @Value(value = "${app.test-paper-id}")
    private String testPaperId;

    @Override
    public void add(Period period) {
        if (this.periodRepository.countByProjectIdAndName(period.getProjectId(), period.getName()) > 0) {
            throw new ServiceException(ExceptionEnum.PROJECT_EXIST);
        }
        period.setPaperPassword(StringUtil.genPassword(8));
        this.periodRepository.insert(period);
    }

    @Override
    public void update(Period period, Boolean isOffline) {
        Period exist = this.getById(period.get_id().toString());
        Project project = this.projectRepository.findById(exist.getProjectId())
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));

        //如果是 普通subject才进行判断，如果是离线录题的subject，此条件放开
        if(null == isOffline || !isOffline){
            if(this.candidateRepository.countByPeriodId(period.get_id()) > 0){
                throw  new ServiceException(ExceptionEnum.SUBJECT_CONTAINS_CANDIDATE);
            }
            //非封闭录题时段，上线以后不能更新
            if(project.getOnline()){
                throw  new ServiceException(ExceptionEnum.PROJECT_ONLINE);
            }
        }
        else {
            exist.setSubjectList(period.getSubjectList());
        }

        exist.setName(period.getName());
        exist.setDuration(period.getDuration());
        exist.setStartAt(period.getStartAt());
        this.periodRepository.save(exist);
        this.cacheService.deletePeriod(period.get_id().toString());
    }

    @Override
    public Period getById(String periodId) {
        return this.periodRepository.findById(new ObjectId(periodId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
    }

    @Override
    public List<Period> getByProjectIdList(List<ObjectId> idList) {
        return this.periodRepository.findByProjectIdIn(idList);
    }

    @Override
    public List<Period> getByProjectList(List<Project> projectList) {
        return getByProjectIdList(projectList
                .stream()
                .map(Project::get_id)
                .toList());
    }

    @Override
    public void autoArrange(String periodId, boolean random) {
        Period period = this.getById(periodId);
        if (period.getState().equals(PeriodStateEnum.ARRANGED.getCode())) {
            throw new ServiceException(ExceptionEnum.PERIOD_ARRANGED);
        }
        Project project = this.projectRepository.findById(period.getProjectId())
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));

        List<Candidate> candidateList = this.candidateRepository.findByPeriodId(new ObjectId(periodId));
        if(candidateList.isEmpty()){
            throw new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND);
        }
        List<CandidateArranged> priorityList = this.getMultiPeriodCandidateList(project, periodId);
        List<Candidate> sortedCandidateList = sortedCandidateList(candidateList, priorityList);
        List<Confirm> confirmList = this.confirmRepository.findByPeriodId(new ObjectId(periodId));
        List<Room> roomList = this.roomRepository.findBy_idIn(confirmList
                .stream()
                .map(Confirm::getRoomIdList)
                .flatMap(Collection::stream)
                .toList());
        List<Site> siteList = this.siteRepository.findBy_idIn(confirmList
                        .stream()
                        .map(Confirm::getSiteId)
                        .toList())
                .stream()
                .sorted(Comparator.comparing(Site::getPriority))
                .toList();
        int count = confirmList
                .stream()
                .mapToInt(Confirm::getConfirmCount)
                .sum();
        if (sortedCandidateList.size() > count) {
            throw new ServiceException(ExceptionEnum.CANDIDATE_BEYOND_SEAT);
        }

        // 生成
        this.periodRoomService.generate(periodId, confirmList, roomList, true);

        //一个时段在某个考点的 房间与人的集合
        for (Confirm confirm : confirmList) {
            Site site = siteList
                    .stream()
                    .filter(s -> confirm.getSiteId().equals(s.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));

            int roomCandidateCount = 0;
            int roomIndex = 0;
            ObjectId roomId = confirm.getRoomIdList().get(0);
            Room room = null;
            int blank = (int) Math.floor((confirm.getConfirmCount() - (double)confirm.getCandidateCount()) / confirm.getRoomIdList().size());
            ObjectId lastSubjectId = null;

            for (int i = 0; i < confirm.getCandidateCount(); i++) {
                if (roomCandidateCount == 0) {
                    ObjectId finalRoomId = roomId;
                    room = roomList
                            .stream()
                            .filter(r -> r.get_id().equals(finalRoomId))
                            .findFirst()
                            .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
                }
                Candidate candidate;
                if(random){
                    candidate = this.getNextNotArrangedCandidate(candidateList, site.getCity(), lastSubjectId);
                    if(candidate != null){
                        lastSubjectId = candidate.getSubjectId();
                    }
                } else {
                    candidate = this.getNextNotArrangedCandidate(candidateList, site.getCity());
                }
                if(candidate != null) {
                    candidate.setSiteId(confirm.getSiteId());
                    candidate.setRoomId(roomId);
                    roomCandidateCount++;
                    if (project.getFixedPosition()) {
                        // 固定座位 处理考生编号
                        candidate.setSeatNum(roomCandidateCount);
                    }
                }

                if(candidate == null || i == confirm.getCandidateCount() -1){
                    setPeriodRoomCandidateCount(periodId, room.get_id().toString(), roomCandidateCount);
                    break;
                }

                if (roomCandidateCount == room.getAvailable() - blank) {
                    // 进入下一个房间
                    setPeriodRoomCandidateCount(periodId, room.get_id().toString(), roomCandidateCount);
                    roomCandidateCount = 0;
                    roomIndex++;
                    if(roomIndex < confirm.getRoomIdList().size()){
                        roomId = confirm.getRoomIdList().get(roomIndex);
                    }
                }
            }
        }

        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Candidate.class);
        for (Candidate candidate : sortedCandidateList) {
            Query query = new Query(Criteria.where("_id").is(candidate.get_id()));
            Update update = new Update()
                    .set("seatNum", candidate.getSeatNum())
                    .set("roomId", candidate.getRoomId())
                    .set("siteId", candidate.getSiteId());
            int periodCount = priorityList
                    .stream()
                    .filter(ca -> ca.getIdCardNum().equals(candidate.getIdCardNum()))
                    .map(CandidateArranged::getPeriodCount)
                    .findFirst()
                    .orElse(1);
            if(periodCount > 1){
                update = update.set("periodCount", periodCount);
            }
            bulkOperations.updateOne(query, update);
        }
        if (!sortedCandidateList.isEmpty()) {
            BulkWriteResult result = bulkOperations.execute();
            log.info("{} - 更新编排 - {} - 人", periodId, result.getModifiedCount());
        }

        this.setPeriodArranged(periodId, true);
    }

    @Override
    public void siteArrange(String periodId, boolean random, List<String> siteIdList) {
        Period period = this.getById(periodId);
        if (period.getState().equals(PeriodStateEnum.ARRANGED.getCode())) {
            throw new ServiceException(ExceptionEnum.PERIOD_ARRANGED);
        }
        Project project = this.projectRepository.findById(period.getProjectId())
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
        // 找到未分配的考生
        List<Candidate> candidateList = this.candidateRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), null);
        if(candidateList.isEmpty()){
            throw new ServiceException(ExceptionEnum.CANDIDATE_NOT_FOUND);
        }
        List<CandidateArranged> priorityList = this.getMultiPeriodCandidateList(project, periodId);
        List<Candidate> sortedCandidateList = sortedCandidateList(candidateList, priorityList);

        List<ObjectId> siteObjIdList = siteIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        List<Confirm> confirmList = this.confirmRepository.findByPeriodIdAndSiteIdIn(new ObjectId(periodId), siteObjIdList);
        List<ObjectId> roomIdList = confirmList
                .stream()
                .map(Confirm::getRoomIdList)
                .flatMap(Collection::stream)
                .toList();

        List<Room> roomList = this.roomRepository.findBy_idIn(roomIdList);
        List<Site> siteList = this.siteRepository.findBy_idIn(siteObjIdList);
        siteList = siteList
                .stream()
                .sorted(Comparator.comparing(Site::getPriority))
                .toList();

//        int count = confirmList
//                .stream()
//                .mapToInt(Confirm::getConfirmCount)
//                .sum();
//        if (sortedCandidateList.size() > count) {
//            throw new ServiceException(ExceptionEnum.CANDIDATE_BEYOND_SEAT);
//        }

        // 生成
        this.periodRoomService.generate(periodId, confirmList, roomList, false);

        //一个时段在某个考点的 房间与人的集合
        for (Confirm confirm : confirmList) {
            Site site = siteList
                    .stream()
                    .filter(s -> confirm.getSiteId().equals(s.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));

            int roomCandidateCount = 0;
            int roomIndex = 0;
            ObjectId roomId = confirm.getRoomIdList().get(0);
            Room room = null;
            int blank = (int) Math.floor((confirm.getConfirmCount() - (double)confirm.getCandidateCount()) / confirm.getRoomIdList().size());
            ObjectId lastSubjectId = null;

            for (int i = 0; i < confirm.getCandidateCount(); i++) {
                if (roomCandidateCount == 0) {
                    ObjectId finalRoomId = roomId;
                    room = roomList
                            .stream()
                            .filter(r -> r.get_id().equals(finalRoomId))
                            .findFirst()
                            .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
                }
                Candidate candidate;
                if(random){
                    candidate = this.getNextNotArrangedCandidate(candidateList, site.getCity(), lastSubjectId);
                    if(candidate != null){
                        lastSubjectId = candidate.getSubjectId();
                    }
                } else {
                    candidate = this.getNextNotArrangedCandidate(candidateList, site.getCity());
                }
                if(candidate != null) {
                    candidate.setSiteId(confirm.getSiteId());
                    candidate.setRoomId(roomId);
                    roomCandidateCount++;
                    if (project.getFixedPosition()) {
                        // 固定座位 处理考生编号
                        candidate.setSeatNum(roomCandidateCount);
                    }
                }

                if(candidate == null || i == confirm.getCandidateCount() -1){
                    setPeriodRoomCandidateCount(periodId, room.get_id().toString(), roomCandidateCount);
                    break;
                }

                if (roomCandidateCount == room.getAvailable() - blank) {
                    // 进入下一个房间
                    setPeriodRoomCandidateCount(periodId, room.get_id().toString(), roomCandidateCount);
                    roomCandidateCount = 0;
                    roomIndex++;
                    if(roomIndex < confirm.getRoomIdList().size()){
                        roomId = confirm.getRoomIdList().get(roomIndex);
                    }
                }
            }
        }

        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Candidate.class);
        for (Candidate candidate : sortedCandidateList) {
            if(candidate.getRoomId() != null){
                Query query = new Query(Criteria.where("_id").is(candidate.get_id()));
                Update update = new Update()
                        .set("seatNum", candidate.getSeatNum())
                        .set("roomId", candidate.getRoomId())
                        .set("siteId", candidate.getSiteId());
                int periodCount = priorityList
                        .stream()
                        .filter(c -> c.getIdCardNum().equals(candidate.getIdCardNum()))
                        .map(CandidateArranged::getPeriodCount)
                        .findFirst()
                        .orElse(1);
                if(periodCount > 1){
                    update = update.set("periodCount", periodCount);
                }
                bulkOperations.updateOne(query, update);
            }
        }
        if (!sortedCandidateList.isEmpty()) {
            BulkWriteResult result = bulkOperations.execute();
            log.info("{} - 更新编排 - {} - 人", periodId, result.getModifiedCount());
        }

        this.setPeriodArranged(periodId, null);
    }

    @Override
    public List<CandidateArrangedItem> arrangeData(String periodId) {
        Period period = this.cacheService.getPeriod(periodId);
        Project project = this.cacheService.getProject(period.getProjectId().toString());

        List<Candidate> candidateList = this.candidateRepository.findByPeriodId(new ObjectId(periodId));

        List<Room> roomList = this.roomRepository.findBy_idIn(candidateList
                .stream()
                .map(Candidate::getRoomId)
                .distinct()
                .toList());
        List<Site> siteList = this.siteRepository.findBy_idIn(candidateList
                .stream()
                .map(Candidate::getSiteId)
                .distinct()
                .toList());

        List<PeriodRoom> periodRoomList = this.periodRoomService.getAllByPeriodId(periodId);

        return this.exportArrangeData(project, candidateList, siteList, roomList, period.getSubjectList(), periodRoomList);
    }

    private void setPeriodRoomCandidateCount(String periodId, String roomId, int count){
        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)));
        query.addCriteria(Criteria.where("roomId").is(new ObjectId(roomId)));
        Update update = new Update()
                .set("candidateCount", count);
        mongoTemplate.updateFirst(query, update, PeriodRoom.class);

    }

    private void setPeriodArranged(String periodId, Boolean arranged){
        int periodState = PeriodStateEnum.ARRANGED.getCode();

        if(arranged == null){
            periodState = PeriodStateEnum.ARRANGING.getCode();
            int notArrangedCount = this.candidateRepository.countNotArranged(new ObjectId(periodId));
            if(notArrangedCount == 0){
                periodState = PeriodStateEnum.ARRANGED.getCode();
            }
        } else if(!arranged) {
            periodState = PeriodStateEnum.INIT.getCode();
        }
        Query query = new Query(Criteria.where("_id").is(new ObjectId(periodId)));
        Update update = new Update()
                .set("state", periodState);
        mongoTemplate.updateFirst(query, update, Period.class);
        cacheService.deletePeriod(periodId);
        //取消编排
        if(periodState == PeriodStateEnum.INIT.getCode()){
            Query queryPeriodRoom = new Query(Criteria.where("periodId").is(new ObjectId(periodId)));
            mongoTemplate.remove(queryPeriodRoom, PeriodRoom.class);
        }
    }

    private List<CandidateArrangedItem> exportArrangeData(Project project,
                                                          List<Candidate> candidateList,
                                                          List<Site> siteList,
                                                          List<Room> roomList,
                                                          List<Subject> subjectList,
                                                          List<PeriodRoom> periodRoomList){
        List<CandidateArrangedItem> res = new ArrayList<>();
        List<CandidateArranged> priorityList = this.getMultiPeriodCandidateList(project, null);

        for(Candidate candidate : candidateList){
            Site site = null;
            if(candidate.getSiteId() != null){
                site = siteList
                        .stream()
                        .filter(s -> s.get_id().equals(candidate.getSiteId()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
            }

            Room room = null;
            if(candidate.getRoomId() != null){
                room = roomList
                        .stream()
                        .filter(r -> r.get_id().equals(candidate.getRoomId()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
            }

            Subject subject = subjectList
                    .stream()
                    .filter(s -> s.get_id().equals(candidate.getSubjectId()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.SUBJECT_NOT_FOUND));
            int round = priorityList
                    .stream()
                    .filter(ca -> ca.getIdCardNum().equals(candidate.getIdCardNum()))
                    .map(CandidateArranged::getPeriodCount)
                    .findFirst()
                    .orElse(1);
            PeriodRoom periodRoom = null;
            if(room != null){
                periodRoom = periodRoomList
                        .stream()
                        .filter(s -> s.getRoomId().equals(candidate.getRoomId()) && s.getPeriodId().equals(candidate.getPeriodId()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_ROOM_NOT_FOUND));
            }

            CandidateArrangedItem item = CandidateArrangedItem.of(candidate, site, room,subject.getName(), round, periodRoom);
            res.add(item);
        }
        return res;
    }

    @Override
    public int arrangeCancel(String periodId) {
        Period period = this.getById(periodId);
        if (period.getState().equals(PeriodStateEnum.INIT.getCode())) {
            return 0;
        }
        long existsDownloaded = candidateRepository.countByPeriodIdAndStateGreaterThanEqual(new ObjectId(periodId), CandidateStateEnum.DOWNLOADED.getCode());
        if(existsDownloaded > 0){
            throw new ServiceException(ExceptionEnum.PERIOD_CANCEL_ARRANGE_ERR);
        }
        List<Candidate> list = this.candidateRepository.findByPeriodId(new ObjectId(periodId));
        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Candidate.class);
        for (Candidate candidate : list) {
            Query query = new Query(Criteria.where("_id").is(candidate.get_id()));
            Update update = new Update()
                    .set("seatNum", null)
                    .set("roomId", null)
                    .set("siteId", null);
            bulkOperations.updateOne(query, update);
        }
        if (!list.isEmpty()) {
            BulkWriteResult result = bulkOperations.execute();
            log.info("{} - 取消编排 - {} - 人", periodId, result.getModifiedCount());
        }

        this.setPeriodArranged(periodId, false);
        return list.size();
    }

    /**
     * 返回 多个时段都存在的考生
     *
     * @param project 项目
     * @return List<CandidateArranged> 根据 PeriodCount 进行倒排序
     */
    private List<CandidateArranged> getMultiPeriodCandidateList(Project project, String excludePeriodId) {
        Query query = new Query()
                .addCriteria(Criteria.where("projectId").is(project.get_id()));
        if(excludePeriodId != null){
            query.addCriteria(Criteria.where("periodId").ne(new ObjectId(excludePeriodId)));
        }
        query.fields()
                .include("_id")
                .include("siteId")
                .include("IdCardNum");

        List<Candidate> list = this.mongoTemplate.find(query, Candidate.class);
        Map<String, Long> map = list
                .stream()
                .collect(groupingBy(Candidate::getIdCardNum, counting()));
        List<CandidateArranged> res = new ArrayList<>();
        for (String id : map.keySet()) {
            if (map.get(id) > 1) {
                List<Candidate> candidateList = list
                        .stream()
                        .filter(c -> c.getIdCardNum().equals(id))
                        .toList();

                CandidateArranged item = new CandidateArranged();
                item.setIdCardNum(candidateList.get(0).getIdCardNum());
                item.setPeriodCount(candidateList.size());
                candidateList
                        .stream().filter(c -> c.getSiteId() != null)
                        .findFirst()
                        .ifPresent(candidate -> item.setSiteId(candidate.getSiteId().toString()));
                res.add(item);
            }
        }
        return res
                .stream()
                .sorted(Comparator.comparingInt(CandidateArranged::getPeriodCount).reversed())
                .toList();
    }

    /**
     * 根据场次内的多时段考生数据 对当前时段考生进行排序 时段越多的考生排序越靠前
     *
     * @param periodCandidateList 当前时段考生
     * @param priorityList        多时段考生 k 身份证 v 重复数量
     * @return 排序后的 periodCandidateList
     */
    private List<Candidate> sortedCandidateList(List<Candidate> periodCandidateList, List<CandidateArranged> priorityList) {
        int i = 0;
        for (CandidateArranged ca : priorityList) {
            Candidate candidate = periodCandidateList
                    .stream()
                    .filter(c -> c.getIdCardNum().equals(ca.getIdCardNum()))
                    .findFirst()
                    .orElse(null);
            if (candidate == null) {
                continue;
            }
            int index = periodCandidateList.indexOf(candidate);
            Collections.swap(periodCandidateList, i, index);
            i++;
        }
        return periodCandidateList;
    }

    /**
     * 获取下一个需要安排到房间的考生
     *
     * @param list 当前时段考生
     * @param city 城市名称
     * @return 没有房间信息的考生
     */
    private Candidate getNextNotArrangedCandidate(List<Candidate> list, String city) {
        return list
                .stream()
                .filter(candidate -> candidate.getRoomId() == null && candidate.getCity().equals(city))
                .findFirst()
                .orElse(null);
    }

    private Candidate getNextNotArrangedCandidate(List<Candidate> list, String city, ObjectId subjectId) {
        if(subjectId == null){
            return getNextNotArrangedCandidate(list, city);
        }
        Candidate candidate =  list
                .stream()
                .filter(c -> c.getRoomId() == null &&
                        c.getCity().equals(city) &&
                        !c.getSubjectId().equals(subjectId))
                .findFirst()
                .orElse(null);
        if(candidate == null) {
            candidate = getNextNotArrangedCandidate(list, city);
        }
        return candidate;
    }

    @Override
    public List<String> getPeriodIds(String projectId) {
        List<String> ids = new ArrayList<>();
        List<Period> periodList = this.periodRepository.findByProjectId(new ObjectId(projectId));
        for (Period period : periodList) {
            ids.add(period.get_id().toString());
        }
        return ids;
    }

    @Override
    public List<Period> getByProjectId(String projectId) {
        return this.periodRepository.findByProjectId(new ObjectId(projectId));
    }

    @Override
    public void remove(String projectId, String periodId) {
        Project project = this.projectRepository.findById(new ObjectId(projectId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
        Period period = this.getById(periodId);
        if(project.getOnline()){
            throw new ServiceException(ExceptionEnum.PROJECT_ONLINE);
        }
        if(period.getReady()){
            throw new ServiceException(ExceptionEnum.PERIOD_READY);
        }
        int count = period.getSubjectList()
                .stream()
                .mapToInt(Subject::getCandidateCount)
                .sum();
        if (count > 0) {
            throw new ServiceException(ExceptionEnum.SUBJECT_CONTAINS_CANDIDATE);
        }
        for(Subject subject : period.getSubjectList()){
            this.partRepository.deleteBySubjectId(subject.get_id());
        }
        //删除
        this.periodRepository.deleteById(new ObjectId(periodId));
        this.confirmRepository.deleteByPeriodId(new ObjectId(periodId));
        this.cacheService.deletePeriod(periodId);
    }

    @Override
    public boolean ready(String periodId) {
        Period period = this.getById(periodId);

        // 考生是否全部就位
        long notInSeatCount = this.mongoTemplate.count(new Query()
                .addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)))
                .addCriteria(Criteria.where("roomId").is(null)), Candidate.class);
        if(notInSeatCount > 0){
            throw new ServiceException(ExceptionEnum.CANDIDATE_NOT_IN_SEAT);
        }

        // 检查是否有考生没有准考证
        int candidateCount = period.getSubjectList()
                .stream()
                .mapToInt(Subject::getCandidateCount)
                .sum();
        if(!period.getAdmissionCardCount().equals(candidateCount)){
            throw new ServiceException(ExceptionEnum.CANDIDATE_COUNT_NOT_EQUAL_ADMISSION_CARD,
                    String.format("%d/%d", period.getAdmissionCardCount(), candidateCount));
        }

        // 检查试卷生成
        if(period.getSubjectList()
                .stream()
                .filter(Subject::getPaperGenerated)
                .toList()
                .size() < period.getSubjectList().size()){
            throw new ServiceException(ExceptionEnum.PAPER_NOT_GENERATED);
        }

        Query query = new Query(Criteria.where("_id").is(new ObjectId(periodId)));
        Update update = new Update();
        update.set("ready", !period.getReady());
        mongoTemplate.updateFirst(query, update, Period.class);
        this.cacheService.deletePeriod(periodId);

        if(!period.getReady()){
            this.uploadPaper(period);
            this.periodRoomService.initMonitorRoom(periodId, true);
        }
        return !period.getReady();
    }

    @Override
    public void readyV2(Period period) {
        this.uploadPaperV2(period);
    }

    @Override
    public void addSubject(String periodId, Subject subject) {
        Period period = this.getById(periodId);
        if(period.getDuration() < subject.getDuration()){
            throw new ServiceException(ExceptionEnum.SUBJECT_DURATION_FAIL);
        }
        period.getSubjectList().add(subject);
        this.periodRepository.save(period);
        this.cacheService.deletePeriod(periodId);
    }

    @Override
    public void updateSubject(String periodId, Subject subject) {
        Period period = this.getById(periodId);
        if(period.getDuration() < subject.getDuration()){
            throw new ServiceException(ExceptionEnum.SUBJECT_DURATION_FAIL);
        }
        for(Subject s : period.getSubjectList()){
            if(s.get_id().equals(subject.get_id())){
                s.setName(subject.getName());
                s.setDuration(subject.getDuration());
                s.setNote(subject.getNote());
                s.setCalculatorEnabled(subject.getCalculatorEnabled());
                s.setShowScore(subject.getShowScore());
                s.setSubmitSecond(subject.getSubmitSecond());
                break;
            }
        }
        this.periodRepository.save(period);
        this.cacheService.deletePeriod(periodId);
    }

    @Override
    public void removeSubject(String subjectId, String periodId) {
        Period period = this.getById(periodId);
        Project project = this.cacheService.getProject(period.getProjectId().toString());
        Subject subject = period.getSubjectList()
                .stream()
                .filter(s -> s.get_id().toString().equals(subjectId))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ExceptionEnum.SUBJECT_NOT_FOUND));
        if(project.getOnline()){
            throw new ServiceException(ExceptionEnum.PROJECT_ONLINE);
        }
        if(period.getReady()){
            throw new ServiceException(ExceptionEnum.PERIOD_READY);
        }
        if(subject.getPaperGenerated()){
            throw new ServiceException(ExceptionEnum.PAPER_GENERATED);
        }
        if(this.candidateRepository.countBySubjectId(new ObjectId(subjectId)) > 0){
            throw new ServiceException(ExceptionEnum.SUBJECT_CONTAINS_CANDIDATE);
        }

        List<Part> parts = this.partRepository.findBySubjectId(new ObjectId(subjectId));
        for(Part part : parts){
            this.partRepository.delete(part);
        }
        period.getSubjectList().remove(subject);
        this.periodRepository.save(period);
        this.cacheService.deletePeriod(periodId);
    }

    @Override
    public Subject getSubject(String subjectId, String periodId) {
        Period period = this.getById(periodId);
        for(Subject subject : period.getSubjectList()){
            if(subject.get_id().toString().equals(subjectId)){
                return subject;
            }
        }
        return null;
    }

    @Override
    public Period getBySubjectId(String periodId, String subjectId) {
        Query query = new Query();
        query = query.addCriteria(Criteria.where("_id").is(new ObjectId(periodId)));
        query = query.addCriteria(Criteria.where("subjectList._id").is(new ObjectId(subjectId)));
        return this.mongoTemplate.findOne(query, Period.class);
    }

    @Override
    public Period getTestPeriod(Project project, String testPaperId) {
        Period period = new Period();
        period.set_id(new ObjectId(testPeriodId));
        period.setProjectId(project.get_id());
        period.setName("测试-时间段");
        period.setStartAt(project.getStartAt());
        period.setDuration(((int) (project.getEndAt().getTime() - period.getStartAt().getTime())));
        period.setState(PeriodStateEnum.ARRANGED.getCode());
        period.setReady(true);
        period.setPaperPassword(testPaperPassword);

        Subject subject = new Subject();
        subject.set_id(new ObjectId(testSubjectId));
        subject.setPeriodId(period.get_id());
        subject.setName("测试科目");
        subject.setNote("测试使用");
        subject.setDuration(period.getDuration());
        subject.setPaperGenerated(true);

        Paper paper = this.paperRepository.findById(new ObjectId(testPaperId)).
                orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));

        int questionCount = 0;
        Double score = 0D;
        for(PaperPart paperPart : paper.getPaperPartList()){
            questionCount += paperPart.getQuestionIdList().size();
            Part part = this.partRepository.findById(paperPart.getPartId()).orElseThrow(() -> new ServiceException(ExceptionEnum.PART_NOT_FOUND));
            score += part.getPartScore();
        }
        subject.setScore(score);
        subject.setQuestionCount(questionCount);
        period.getSubjectList().add(subject);
        return period;
    }

    @Override
    public void done(String periodId) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(periodId)));
        Update update = new Update();
        update.set("state", PeriodStateEnum.DONE.getCode());
        mongoTemplate.updateFirst(query, update, Period.class);

        this.cacheService.deletePeriod(periodId);
    }

    @Override
    public List<Period> reviewPeriodList(int day) {
        Query query = new Query(Criteria.where("state").is(PeriodStateEnum.DONE.getCode()));
        long time = (long) day* 86400 *1000;
        Date before = new Date(System.currentTimeMillis() - time);
        query = query.addCriteria(Criteria.where("startAt").gte(before));
        return mongoTemplate.find(query, Period.class);
    }

    @Override
    public void arrangeManual(String periodId, List<CandidateManualArrangeItem> list) {
        Period period = this.getById(periodId);
        if (period.getState().equals(PeriodStateEnum.ARRANGED.getCode())) {
            throw new ServiceException(ExceptionEnum.PERIOD_ARRANGED);
        }
        List<Confirm> confirmList = this.confirmRepository.findByPeriodId(new ObjectId(periodId));
        List<Room> roomList = this.roomRepository.findBy_idIn(confirmList
                .stream()
                .map(Confirm::getRoomIdList)
                .flatMap(Collection::stream)
                .toList());
        for(CandidateManualArrangeItem item : list){
            Candidate candidate = this.candidateRepository.findByPeriodIdAndIdCardNum(new ObjectId(periodId), item.getIdCardNum())
                    .orElse(null);
            if(candidate != null){
                int roomCandidateCount = this.candidateRepository.countByPeriodIdAndRoomId(new ObjectId(periodId), item.getRoomId());
                Room room = roomList
                        .stream()
                        .filter(r -> r.get_id().equals(item.getRoomId()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
                if(room.getCapacity() > roomCandidateCount){
                    candidate.setSiteId(item.getSiteId());
                    candidate.setRoomId(item.getRoomId());
                    candidate.setSeatNum(item.getSeatNum());
                    this.candidateRepository.save(candidate);
                } else {
                    this.setPeriodArranged(periodId, false);
                    throw new ServiceException(ExceptionEnum.PERIOD_CLONE_ARRANGE_ERR);
                }
            }
        }
        this.periodRoomService.generate(periodId, confirmList, roomList, true);
        for(Room room : roomList){
            int count = this.candidateRepository.countByPeriodIdAndRoomId(new ObjectId(periodId), room.get_id());
            this.setPeriodRoomCandidateCount(periodId, room.get_id().toString(), count);
        }
        this.setPeriodArranged(periodId, true);
        this.cacheService.deletePeriod(periodId);
    }

    @Override
    public List<Period> recent(String companyId) {
        Query dis = new Query(Criteria.where("companyId").is(new ObjectId(companyId)));
        List<ObjectId> projectIdList = mongoTemplate.findDistinct(dis, "_id", Project.class, ObjectId.class);

        long time = (long) 31 * 86400 *1000;
        Date before = new Date(System.currentTimeMillis() - time);
        Query query = new Query()
                .with(Sort.by(Sort.Direction.ASC, "startAt", "_id"))
                .addCriteria(Criteria.where("projectId").in(projectIdList))
                .addCriteria(Criteria.where("state").is(PeriodStateEnum.DONE.getCode()))
                .addCriteria(Criteria.where("startAt").gte(before));

        return this.mongoTemplate.find(query, Period.class);
    }

    private void uploadPaper(Period period){
        ObjectMapper mapper = new ObjectMapper();
        List<Paper> paperList = this.paperRepository.findByPeriodId(period.get_id());
        List<PaperGroup> paperGroupList = new ArrayList<>();
        paperList.forEach(paper -> {
            PaperGroup paperGroup = new PaperGroup();
            paperGroup.setPaper(paper);
            List<Part> partList = this.partRepository.findBy_idInOrderBySortAsc(paper.getPaperPartList()
                    .stream()
                    .map(PaperPart::getPartId)
                    .toList());
            List<Question> questionList = this.questionRepository.findBy_idIn(paper.getPaperPartList()
                    .stream()
                    .map(PaperPart::getQuestionIdList)
                    .flatMap(Collection::stream)
                    .toList());
            paperGroup.setPartList(partList);
            paperGroup.setQuestionList(questionList);
            paperGroupList.add(paperGroup);
        });

        PaperResponse res = PaperResponse.of(period,paperGroupList);
        try {
            String json = mapper.writeValueAsString(res.getSubjectList());
            String sign = Md5Util.genSign(json);
            PaperJson paperJson = new PaperJson();
            paperJson.setHash(sign);
            paperJson.setPeriodId(period.get_id().toString());
            paperJson.setJson(json);
            this.updateHash(paperJson);
            this.uploadPaper(period, paperJson);
        }catch (JsonProcessingException e){
            throw new ServiceException(BaseExceptionEnum.JSON_OBJECT_CONVERT_EXCEPTION);
        }
    }

    private void updateHash(PaperJson paperJson){
        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Period.class);

        Query query = new Query(Criteria.where("_id").is(new ObjectId(paperJson.getPeriodId())));
        Update update = new Update()
                .set("hash", paperJson.getHash());
        bulkOperations.updateOne(query, update);

        BulkWriteResult result = bulkOperations.execute();
        log.info(String.format("Paper 更新 - sign %d - 人", result.getModifiedCount()));

    }

    private void uploadPaper(Period period, PaperJson paperJson){

        String jsonFolder = String.format("%s/%s", tmpPath, period.get_id().toString());

        String jsonPath = String.format("%s.ikp", jsonFolder);
        Path path = Paths.get(jsonPath);
        File file = new File(jsonPath);
        try {
            Files.write(path, TeaUtil.encrypt(paperJson.getJson().getBytes(),period.getPaperPassword()));
        } catch (IOException e){
            throw new ServiceException(BaseExceptionEnum.IO_EXCEPTION);
        }

        String paperPath = String.format("paper/%s.ikp", period.get_id().toString());
        ossService.uploadFile(ossBucketExam, paperPath, file);
    }

    private void uploadPaperV2(Period period){
        Paper paper = this.paperRepository.findById(new ObjectId(this.testPaperId)).orElseThrow();
        List<Part> partList = new ArrayList<>();
        List<Question> questionList = new ArrayList<>();
        for(PaperPart pp : paper.getPaperPartList()){
            Part part = this.partRepository.findById(pp.getPartId()).orElseThrow();
            partList.add(part);
            for(PartQuestion pq : part.getQuestionList()){
                Question question = this.questionRepository.findById(pq.getQuestionId()).orElseThrow();
                questionList.add(question);
            }
        }

        ObjectMapper mapper = new ObjectMapper();
        PaperGroup paperGroup = new PaperGroup();
        paperGroup.setPaper(paper);
        paperGroup.setPartList(partList);
        paperGroup.setQuestionList(questionList);
        List<PaperGroup> paperGroupList = new ArrayList<>();
        paperGroupList.add(paperGroup);
        PaperResponse res = PaperResponse.of(period,paperGroupList);
        try {
            String json = mapper.writeValueAsString(res.getSubjectList());
            String sign = Md5Util.genSign(json);
            PaperJson paperJson = new PaperJson();
            paperJson.setHash(sign);
            paperJson.setPeriodId(paper.getPeriodId().toString());
            paperJson.setJson(json);
            this.updateHash(paperJson);
            this.uploadPaper(period, paperJson);
        }catch (JsonProcessingException e){
            throw new ServiceException(BaseExceptionEnum.JSON_OBJECT_CONVERT_EXCEPTION);
        }
    }
}
