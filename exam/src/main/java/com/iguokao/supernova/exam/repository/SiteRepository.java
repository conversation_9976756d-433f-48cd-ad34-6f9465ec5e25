package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Project;
import com.iguokao.supernova.exam.document.Site;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface SiteRepository extends MongoRepository<Site, ObjectId> {
    int countByName(String name);
    List<Site> findBy_idIn(List<ObjectId> list);

    Optional<Site> findByAgentList_LoginCode(String loginCode);
}
