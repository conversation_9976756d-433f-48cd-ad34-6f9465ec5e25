package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Confirm;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.CountQuery;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface ConfirmRepository extends MongoRepository<Confirm, ObjectId> {
    int countBySiteIdAndPeriodId(ObjectId siteId, ObjectId periodId);
    int countByProjectId(ObjectId projectId);
    int countByProjectIdAndState(ObjectId projectId, Integer state);

    List<Confirm> findByPeriodId(ObjectId periodId);
    List<Confirm> findBySiteId(ObjectId siteId);
    Optional<Confirm> findByPeriodIdAndSiteId(ObjectId periodId, ObjectId siteId);
    List<Confirm> findByPeriodIdAndSiteIdIn(ObjectId periodId, List<ObjectId> siteIdList);

    void deleteByPeriodId(ObjectId periodId);


    @Query("{ 'roomIdList' : { $elemMatch : {$eq: ?0 } } }")
    List<Confirm>  findByRoomId(ObjectId roomId);
}