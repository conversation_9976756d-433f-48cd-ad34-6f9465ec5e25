package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.request.EmailTaskAddRequest;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.enums.NotificationSendStateEnum;
import com.iguokao.supernova.exam.enums.NotificationTypeEnum;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.service.EmailService;
import com.iguokao.supernova.exam.service.NotificationBatchService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
@RequiredArgsConstructor
public class NotificationBatchServiceImpl implements NotificationBatchService {
    private final NotificationBatchRepository notificationBatchRepository;
    private final MongoTemplate mongoTemplate;

    @Override
    public List<NotificationBatch> latestList(String periodId, String subjectId ,Integer count) {
        Query query = new Query(Criteria.where("periodId").is(new ObjectId(periodId)))
                .addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)))
                .with(Sort.by(Sort.Direction.DESC, "createdAt"))
                .limit(count);

        return mongoTemplate.find(query, NotificationBatch.class);
    }

    @Override
    public void pdfResult(String subjectId, Boolean isFailed) {
        Query query = new Query(Criteria.where("subjectId").is(new ObjectId(subjectId)))
                .with(Sort.by(Sort.Direction.DESC, "createdAt"))
                .limit(1);

        NotificationBatch notificationBatch = mongoTemplate.findOne(query, NotificationBatch.class);
        if(notificationBatch != null){
            notificationBatch.setCompleted(1);
            if(isFailed != null && isFailed){
                notificationBatch.setSuccess(0);
            }
            else {
                notificationBatch.setSuccess(1);
            }
            this.notificationBatchRepository.save(notificationBatch);
        }
    }

    @Override
    public NotificationBatch add(String periodId, String subjectId, Integer type, Integer count) {
        NotificationBatch notificationBatch = new NotificationBatch();
        notificationBatch.setSubjectId(new ObjectId(subjectId));
        notificationBatch.setPeriodId(new ObjectId(periodId));
        notificationBatch.setType(NotificationTypeEnum.PDF.getCode());
        notificationBatch.setTotal(1);
        notificationBatch.setCreatedAt(new Date());
        return notificationBatchRepository.save(notificationBatch);
    }

    @Override
    public NotificationBatch findById(String batchId) {
        return notificationBatchRepository.findById(new ObjectId(batchId)).orElse(null);
    }
}
