package com.iguokao.supernova.exam.request;

import com.iguokao.supernova.exam.document.InputItem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class InputUpdateRequest {
    @NotNull(message = "项目Id 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目Id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    private List<InputItemRequest> inputList;

    public List<InputItem> getInputList(){
        List<InputItem> res = new ArrayList<>();
        this.inputList.forEach(i -> {
            InputItem ii = new InputItem();
            BeanUtils.copyProperties(i, ii);
            res.add(ii);
        });
        return res;
    }
}
