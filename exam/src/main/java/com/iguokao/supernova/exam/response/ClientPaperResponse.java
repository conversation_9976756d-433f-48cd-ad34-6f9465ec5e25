package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.exam.document.Period;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ClientPaperResponse {
    private String periodId;
    private String url;

    public static ClientPaperResponse of(Period obj, OssService ossService, String bucketName){
        if(obj == null){
            return null;
        }
        ClientPaperResponse res = new ClientPaperResponse();
        res.setPeriodId(obj.get_id().toString());
        String url = ossService.generateSignedUrl(bucketName, String.format("paper/%s.ikp", obj.get_id()), 180);
        res.setUrl(url);
        return res;
    }

//    public static List<ClientPaperResponse> of(List<Paper> list, OssService ossService, String bucketName){
//        if(list==null){
//            return new ArrayList<>();
//        }
//        List<ClientPaperResponse> res = new ArrayList<>();
//        for(Paper obj : list){
//            res.add(of(obj, ossService, bucketName));
//        }
//        return res;
//    }
}
