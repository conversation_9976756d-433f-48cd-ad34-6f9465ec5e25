package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.service.CandidateService;

import java.util.ArrayList;
import java.util.List;

public class CandidateStatisticsItemExporter {

    public static List<List<String>> head() {

        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("城市");

        List<String> head1 = new ArrayList<>();
        head1.add("考点");

        List<String> head2 = new ArrayList<>();
        head2.add("考场号");

        List<String> head3 = new ArrayList<>();
        head3.add("考场名");

        List<String> head4 = new ArrayList<>();
        head4.add("应考人数");

        List<String> head5 = new ArrayList<>();
        head5.add("签到人数");

        List<String> head6 = new ArrayList<>();
        head6.add("登陆人数");

        List<String> head7 = new ArrayList<>();
        head7.add("缺考人数");

        List<String> head8 = new ArrayList<>();
        head8.add("交卷人数");

        List<String> head9 = new ArrayList<>();
        head9.add("实考率");


        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        list.add(head6);
        list.add(head7);
        list.add(head8);
        list.add(head9);

        return list;
    }

    public static List<List<Object>> data( List<MonitorRoom> monitorRoomList,List<PeriodRoom> periodRoomList,List<Site> siteList,List<Room> roomList) {
        List<List<Object>> res = new ArrayList<>();

        for(PeriodRoom periodRoom : periodRoomList) {
            List<Object> line = new ArrayList<>();
            Site site = null;
            Room room = null;
            MonitorRoom mr = null;
            for(Site s : siteList){
                if(periodRoom.getSiteId().toString().equals(s.get_id().toString())){
                    site = s;
                }
            }
            for(Room r : roomList){
                if(periodRoom.getRoomId().toString().equals(r.get_id().toString())){
                    room = r;
                }
            }
            if(site == null || room == null){
                continue;
            }

            for(MonitorRoom m : monitorRoomList){
                if(m.getRoomId().equals(room.get_id()) && m.getSiteId().equals(site.get_id())){
                    mr = m;
                }
            }

            if(mr == null){
                continue;
            }

            line.add(site.getCity());
            line.add(site.getName());
            line.add("考场" + (periodRoom.getRoomIndex() + 1));
            line.add(room.getName());

            line.add(mr.getCandidateCount());
            line.add(mr.getCandidateArrivedCount());
            line.add(mr.getCandidateLoginCount());
            line.add(mr.getCandidateCount() - mr.getCandidateArrivedCount());
            line.add(mr.getCandidateFinishedCount());
            line.add(String.format("%.1f",((double)mr.getCandidateArrivedCount() / (double)mr.getCandidateCount() )* 100) + "%");

            res.add(line);
        }
        return res;
    }
}
