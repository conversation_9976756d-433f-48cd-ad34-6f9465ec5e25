package com.iguokao.supernova.exam.excel;

import java.util.ArrayList;
import java.util.List;

public class RoomLoginItemExporter {

    public static List<List<String>> head() {

        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("序号");

        List<String> head01 = new ArrayList<>();
        head01.add("考点名称");

        List<String> head1 = new ArrayList<>();
        head1.add("考场号");

        List<String> head2 = new ArrayList<>();
        head2.add("考场名称");

        List<String> head3 = new ArrayList<>();
        head3.add("考场地址");

        List<String> head4 = new ArrayList<>();
        head4.add("机位总数");

        List<String> head5 = new ArrayList<>();
        head5.add("可用机位");

        List<String> head6 = new ArrayList<>();
        head6.add("编排考生");

        List<String> head7 = new ArrayList<>();
        head7.add("房间登录码 ");


        list.add(head0);
        list.add(head01);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        list.add(head6);
        list.add(head7);
        return list;
    }

    public static List<List<Object>> data(List<RoomLoginItem> list, String projectName) {
        List<List<Object>> res = new ArrayList<>();
        for(RoomLoginItem item : list){
            List<Object> line = new ArrayList<>();
            line.add(item.getNum());
            line.add(item.getSiteName());
            line.add(item.getRoomNum());
            line.add(item.getName());
            line.add(item.getAddress());

            line.add(item.getCapacity());
            line.add(item.getAvailable());
            line.add(item.getCandidateCount());

            line.add(item.getLoginCode());
            res.add(line);
        }

        List<Object> line0 = new ArrayList<>();
        line0.add("");
        line0.add("");

        List<Object> line1 = new ArrayList<>();
        line1.add("");
        line1.add("");
        line1.add("");
        line1.add("");
        line1.add("");
        line1.add("");
        line1.add("");
        line1.add("视频监控:");
        line1.add("https://supernova-monitor.iguokao.com/login");

        List<Object> line2 = new ArrayList<>();
        line2.add("");
        line2.add("");
        line2.add("");
        line2.add("");
        line2.add("");
        line2.add("");
        line2.add("");
        line2.add("考试项目");
        line2.add(projectName);

        res.add(line0);
        res.add(line1);
        res.add(line2);

        return res;
    }
}
