package com.iguokao.supernova.exam.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.exam.document.Period;
import com.iguokao.supernova.exam.document.Project;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ProjectResponse {
    @Schema(description = "项目 Id")
    private String projectId;

    @Schema(description = "企业 Id")
    private String companyId;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "项目简称")
    private String shortName;

    @Schema(description = "项目类型 1常规 2三级隔离")
    private Integer type;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "开始时间")
    private Date startAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    @Schema(description = "结束时间")
    private Date endAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date admissionCardExpiredAt;

    @Schema(description = "允许迟到的秒数 超出 0不限")
    private Integer lateSecond;

    @Schema(description = "最早交卷时间")
    private Integer submitSecond;

    @Schema(description = "状态")
    private Integer state;

    @Schema(description = "上线状态")
    private Boolean online;

    @Schema(description = "离线模式")
    private Boolean offlineMode;

    @Schema(description = "状态")
    private Boolean ready;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "唯一码")
    private String shortCode;

    @Schema(description = "考试须知")
    private String requirement;

    @Schema(description = "准考证考试须知")
    private String admissionCardRequirement;

    @Schema(description = "准考证数量")
    private Integer admissionCardCount;

    @Schema(description = "考生数量")
    private Integer candidateCount;

    @Schema(description = "固定座位")
    private Boolean fixedPosition;

    @Schema(description = "时段列表")
    private List<String> periodIdList = new ArrayList<>();

    @Schema(description = "时段数量")
    private Integer periodCount;

    @Schema(description = "科目数量")
    private Integer subjectCount;

    @Schema(description = "客户端版本")
    private String clientVersion;

    @Schema(description = "配置")
    private List<InputItemResponse> inputList;

    @Schema(description = "人脸对比")
    private Integer faceDiff;

    @Schema(description = "身份证登陆")
    private Boolean idNumLoginOnly;


    public static ProjectResponse of(Project obj, List<Period> periodList){
        if(obj == null){
            return null;
        }
        ProjectResponse res = new ProjectResponse();
        BeanUtils.copyProperties(obj, res);
        res.setProjectId(obj.get_id().toString());
        res.setCompanyId(obj.getCompanyId().toString());
        res.setInputList(InputItemResponse.of(obj.getInputList()));
        if(periodList != null){
            int p = 0;
            int s = 0;
            for(Period period : periodList){
                if(period.getProjectId().equals(obj.get_id())){
                    p++;
                    s+=period.getSubjectList().size();
                }
            }
            res.setPeriodCount(p);
            res.setSubjectCount(s);
        }
        return res;
    }

    public static List<ProjectResponse> of(List<Project> list, List<Period> periodList) {
        if(list == null){
            return new ArrayList<>();
        }
        List<ProjectResponse> res = new ArrayList<>();
        for(Project obj : list){
            res.add(of(obj,periodList));
        }
        return res;
    }
}
