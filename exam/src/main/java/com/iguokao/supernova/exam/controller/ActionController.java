package com.iguokao.supernova.exam.controller;

import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.exam.document.Action;
import com.iguokao.supernova.exam.request.RoomActionListRequest;
import com.iguokao.supernova.exam.response.ActionResponse;
import com.iguokao.supernova.exam.service.ActionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/action")
@RequiredArgsConstructor
public class ActionController {
    private final ActionService actionService;

    @PostMapping("/room")
    @Operation(summary = "房间日志")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<ActionResponse>> actionAdd(@RequestBody RoomActionListRequest request) {
        List<Action> list;
        if(request.getPeriodId() != null){
            list = this.actionService.getListByPeriodIdAndRoomId(request.getPeriodId(), request.getRoomId());
        } else {
            list = this.actionService.getListByProjectIdAndRoomId(request.getProjectId(), request.getRoomId());
        }
        return RestResponse.success(ActionResponse.of(list));
    }


}
