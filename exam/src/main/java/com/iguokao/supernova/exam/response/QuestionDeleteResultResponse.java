package com.iguokao.supernova.exam.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class QuestionDeleteResultResponse {

    @Schema(description = "成功删除题目数量")
    private Integer success;

    @Schema(description = "结果详情")
    private List<String> resultList = new ArrayList<>();
}


