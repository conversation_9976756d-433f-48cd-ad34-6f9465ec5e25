package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.Confirm;
import com.iguokao.supernova.exam.document.Period;
import com.iguokao.supernova.exam.document.Project;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.enums.ConfirmStateEnum;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.service.ConfirmService;
import com.iguokao.supernova.exam.service.PeriodService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ConfirmServiceImpl implements ConfirmService {
    private final ConfirmRepository confirmRepository;
    private final RoomRepository roomRepository;
    private final ProjectRepository projectRepository;
    private final MongoTemplate mongoTemplate;
    private final PeriodRepository periodRepository;

    @Override
    public void add(Confirm confirm) {
        int count = this.confirmRepository.countBySiteIdAndPeriodId(confirm.getSiteId(), confirm.getPeriodId());
        if(count > 0){
            throw new ServiceException(ExceptionEnum.CONFIRM_EXIST);
        }
        this.confirmRepository.insert(confirm);
    }

    @Override
    public void updateCandidateCount(String confirmId, Integer candidateCount) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(confirmId)));
        Update update = new Update();
        update.set("candidateCount", candidateCount);
        mongoTemplate.updateFirst(query, update, Confirm.class);
    }

    @Override
    public void remove(String confirmId) {
        Confirm confirm = this.getById(confirmId);
        if(confirm.getState().equals(ConfirmStateEnum.SITE_MANAGER_CONFIRMED.getCode())
                || confirm.getState().equals(ConfirmStateEnum.OPERATOR_CONFIRMED.getCode())){
            throw new ServiceException(ExceptionEnum.CONFIRM_NOT_FOUND);
        }
        this.confirmRepository.deleteById(new ObjectId(confirmId));
    }

    @Override
    public Confirm getById(String periodId) {
        return this.confirmRepository.findById(new ObjectId(periodId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.CONFIRM_NOT_FOUND));
    }

    @Override
    public List<Confirm> getListByPeriodId(String periodId) {
        return this.confirmRepository.findByPeriodId(new ObjectId(periodId));
    }

    @Override
    public List<Confirm> getListBySiteId(String siteId) {
        return this.confirmRepository.findBySiteId(new ObjectId(siteId));
    }

    @Override
    public int managerSubmit(String confirmId, String operatorId, List<String> roomIdList) {
        Confirm confirm = this.getById(confirmId);
        if(confirm.getState().equals(ConfirmStateEnum.SITE_MANAGER_CONFIRMED.getCode())){
            throw new ServiceException(ExceptionEnum.CONFIRM_EXIST);
        }
        confirm.setState(ConfirmStateEnum.SITE_MANAGER_CONFIRMED.getCode());

        Project project = this.projectRepository.findById(confirm.getProjectId())
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
        if(project.getOnline()){
            throw new ServiceException(ExceptionEnum.PROJECT_ONLINE);
        }
        return this.submit(confirm, roomIdList);
    }

    @Override
    public int operatorSubmit(String confirmId, String operatorId, List<String> roomIdList) {
        Confirm confirm = this.getById(confirmId);
        Period period = this.periodRepository.findById(confirm.getPeriodId())
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
        if(period.getReady()){
            throw new ServiceException(ExceptionEnum.PERIOD_READY);
        }
        confirm.setState(ConfirmStateEnum.OPERATOR_CONFIRMED.getCode());
        confirm.setOperatorId(new ObjectId(operatorId));
        return this.submit(confirm, roomIdList);
    }

    private int submit(Confirm confirm, List<String> roomIdList){
        List<Room> roomList = this.roomRepository.findBy_idIn(roomIdList.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList()));
        int count = roomList
                .stream()
                .mapToInt(Room::getAvailable)
                .sum();
        if(count < confirm.getCandidateCount()){
            throw new ServiceException(ExceptionEnum.CONFIRM_NOT_ENOUGH);
        }

        confirm.setConfirmCount(count);
        confirm.setRoomIdList(roomList
                .stream()
                .map(Room::get_id)
                .collect(Collectors.toList()));

        int blank = (int) Math.floor((confirm.getConfirmCount() - (double)confirm.getCandidateCount()) / confirm.getRoomIdList().size());
        Room room = roomList
                .stream()
                .filter(r -> r.getAvailable() <= blank)
                .findFirst()
                .orElse(null);
        if(room != null){
            throw new ServiceException(ExceptionEnum.CONFIRM_ROOM_SIZE_LESS_AVG);
        }

        this.confirmRepository.save(confirm);
        return count;
    }
}
