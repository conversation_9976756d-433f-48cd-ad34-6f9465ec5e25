package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class NotificationResultRequest {

    private String candidateId;
    private String batchName;
    private Integer sendResult;
    private Integer type;
    private String err;
}
