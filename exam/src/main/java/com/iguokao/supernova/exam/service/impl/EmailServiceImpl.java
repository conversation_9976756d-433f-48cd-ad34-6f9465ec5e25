package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.TaskRemote;
import com.iguokao.supernova.common.request.SmsTaskAddRequest;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.CandidateConfirmStateEnum;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.enums.NotificationSendStateEnum;
import com.iguokao.supernova.exam.enums.NotificationTypeEnum;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.common.request.EmailTaskAddRequest;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.EmailService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {
    private final EmailRepository emailRepository;
    private final CandidateRepository candidateRepository;
    private final PeriodRepository periodRepository;
    private final ProjectRepository projectRepository;
    private final NotificationBatchRepository notificationBatchRepository;
    private final TaskRemote taskRemote;
    private final CacheService cacheService;

    private final MongoTemplate mongoTemplate;

    @Value(value = "${app.service.task-callback}")
    private String notificationPrefix;

    @Value(value = "${app.short-url-prefix}")
    private String shortUrl;

    @Value(value = "${app.candidate-confirm-url}")
    private String confirmUrl;

    @Override
    public void add(String companyId, String name, String title, String content) {
        Email email = new Email();
        email.setCompanyId(new ObjectId(companyId));
        email.setName(name);
        email.setTitle(title);
        email.setContent(content);
        emailRepository.insert(email);
    }

    @Override
    public void remove(String emailId) {
        emailRepository.deleteById(new ObjectId(emailId));
    }

    @Override
    public void edit(String emailId, String name, String title, String content) {
        Email email = this.emailRepository.findById(new ObjectId(emailId)).orElseThrow(
                () -> new ServiceException(ExceptionEnum.EMAIL_NOT_EXIST));

        email.setName(name);
        email.setTitle(title);
        email.setContent(content);
        emailRepository.save(email);
    }

    @Override
    public List<Email> list(String companyId) {
        return emailRepository.findByCompanyId(new ObjectId(companyId));
    }

    @Override
    public void sendGroup(String periodId, String subjectId, String emailTempId, List<String> candidateIdList, String testEmailAddress) {
        List<ObjectId> idList = candidateIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        List<Candidate> candidateList = this.candidateRepository.findBy_idIn(idList);
        //发送测试邮件逻辑
        if(null != testEmailAddress && candidateList.size() == 1){
            candidateList.get(0).setEmail(testEmailAddress);
        }
        handleMail(periodId,subjectId,emailTempId,candidateList);
    }

    @Async
    @Override
    public void sendAll(String periodId, String subjectId, String emailTempId, Integer confirmState, Integer emailState, Integer state) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "createdAt", "_id"))
                .addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)))
                .addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));

        if (null != confirmState) {
            query = query.addCriteria(Criteria.where("confirmState").is(confirmState));
        }
        if (null != emailState) {
            query = query.addCriteria(Criteria.where("emailState.state").is(emailState));
        }
        if (null != state) {
            query = query.addCriteria(Criteria.where("state").is(state));
        }

        List<Candidate> candidateList = this.mongoTemplate.find(query, Candidate.class);
        handleMail(periodId,subjectId,emailTempId,candidateList);
    }

    @Override
    public void sendSmsGroup(String periodId, String subjectId, String tempId, List<String> candidateIdList) {
        List<ObjectId> idList = candidateIdList
                .stream()
                .map(ObjectId::new)
                .toList();
        List<Candidate> candidateList = this.candidateRepository.findBy_idIn(idList);
        handleSms(periodId,subjectId,tempId,candidateList);
    }

    @Async
    @Override
    public void sendSmsAll(String periodId, String subjectId, String tempId, Integer confirmState, Integer smsState, Integer state) {

        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "_id", "mobile"))
                .addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)))
                .addCriteria(Criteria.where("periodId").is(new ObjectId(periodId)));

        if (null != confirmState) {
            query = query.addCriteria(Criteria.where("confirmState").is(confirmState));
        }
        if (null != smsState) {
            query = query.addCriteria(Criteria.where("smsState.state").is(smsState));
        }
        if (null != state) {
            query = query.addCriteria(Criteria.where("state").is(state));
        }
        List<Candidate> candidateList = this.mongoTemplate.find(query, Candidate.class);
        handleSms(periodId,subjectId,tempId,candidateList);
    }



    @Override
    public void sendResult(String candidateId, String batchId, Integer sendType, Integer sendResult, String err) {

        Query query = new Query(Criteria.where("_id").is(new ObjectId(batchId)));
        Update update = new Update()
                .inc("completed", 1);
        //发送成功
        if(sendResult.equals(NotificationSendStateEnum.SEND_STATE_SUCCESS.getCode())){
            update.inc("success", 1);
        }
        mongoTemplate.updateFirst(query, update, NotificationBatch.class);


        //更新考生字段
        Query query2 = new Query(Criteria.where("_id").is(new ObjectId(candidateId)));
        SendInfo sendInfo = new SendInfo();
        sendInfo.setBatchId(batchId);
        sendInfo.setUpdatedAt(new Date());
        sendInfo.setState(sendResult);

        String property = "emailState";
        // 短信
        if(sendType.equals(NotificationTypeEnum.SMS.getCode())){
           property = "smsState";
        }

        Update update2 = new Update().set(property, sendInfo);
        mongoTemplate.updateFirst(query2, update2, Candidate.class);

        // 发送失败 单独记录
        if(sendResult.equals(NotificationSendStateEnum.SEND_STATE_FAILED.getCode())){
            NotificationFailDetail detail = new NotificationFailDetail();
            detail.setCandidateId(candidateId);
            detail.setErrInfo(err);
            detail.setState(sendResult);
            Query query3 = new Query(Criteria.where("_id").is(new ObjectId(batchId)));
            Update update3 = new Update()
                    .addToSet("failList", detail);
            mongoTemplate.updateFirst(query3, update3, NotificationBatch.class);
        }

    }

    void handleSms(String periodId, String subjectId, String tempId, List<Candidate> candidateList){
        //        1919036
        //        机考正式通知
        //        【国考云消息】{1}准考证已开放打印，请登录如下网址打印准考证。{2}
        //
        //
        //        1919038
        //        机考通知（催确认）
        //        【国考云消息】您还未打印{1}准考证，请登录如下网址打印准考证。{2}

        Period period = this.periodRepository.findById(new ObjectId(periodId)).orElseThrow(
                () -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
        Project project = this.projectRepository.findById(period.getProjectId()).orElseThrow(
                () -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));

        String admissionUrl = getAdmissionUrl(project);

        List<Candidate> smsSuccessCandidateList = new ArrayList<>();
        NotificationBatch batch = handleBatch(periodId,subjectId,candidateList,smsSuccessCandidateList,NotificationTypeEnum.SMS.getCode());

        //筛选完毕，开始发送
        for(Candidate candidate : smsSuccessCandidateList){

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("to",candidate.getMobile());
            jsonObject.put("appId","8a216da8790d6de5017941af72ee0da3");
            jsonObject.put("templateId",tempId);
            jsonObject.put("datas", Arrays.asList(project.getName(), admissionUrl));

            SmsTaskAddRequest addRequest = new SmsTaskAddRequest();
            addRequest.setContent(jsonObject.toString());
            addRequest.setCandidateId(candidate.get_id().toString());
            addRequest.setBatchName(batch.get_id().toString());
            addRequest.setUrl(notificationPrefix + "/api/v1/remote/notification/result");
            taskRemote.addSmsTask(addRequest);
        }
    }

    void handleMail(String periodId, String subjectId, String emailTempId, List<Candidate> candidateList){
        Email email = this.emailRepository.findById(new ObjectId(emailTempId)).orElseThrow(
                () -> new ServiceException(ExceptionEnum.EMAIL_NOT_EXIST));
        Period period = this.periodRepository.findById(new ObjectId(periodId)).orElseThrow(
                () -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
        Project project = this.projectRepository.findById(period.getProjectId()).orElseThrow(
                () -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));

        email.setContent(email.getContent().replaceAll("&lt;","<"));
        email.setContent(email.getContent().replaceAll("&gt;",">"));

        List<Candidate> emailSuccessCandidateList = new ArrayList<>();
        NotificationBatch batch = handleBatch(periodId,subjectId,candidateList,emailSuccessCandidateList,NotificationTypeEnum.EMAIL.getCode());

        //筛选完毕，开始发送
        for(Candidate candidate : emailSuccessCandidateList){
            sendMail(candidate,email,period,project,batch);
        }
    }

    void sendMail(Candidate candidate, Email email, Period period, Project project, NotificationBatch batch){

        String refuse = confirmUrl + "/" + candidate.get_id().toString()  + "/" + CandidateConfirmStateEnum.REFUSE.getCode();
        String agree =  confirmUrl + "/" + candidate.get_id().toString()  + "/" + CandidateConfirmStateEnum.AGREE.getCode();

        String aUrl = getAdmissionUrl(project);

        String newContent = email.getContent().replaceAll("<--姓名-->",candidate.getFullName());
        newContent = newContent.replaceAll("<--准考证地址-->",aUrl);
        newContent = newContent.replaceAll("<--放弃参加-->",refuse);
        newContent = newContent.replaceAll("<--确认参加-->",agree);

        String totalMailContent = "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">"
                + "<html xmlns=\"http://www.w3.org/1999/xhtml\">"
                + "\r\n<head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=gb2312\" />\r\n<title></title>\r\n"
                + "<style>\r\n"
                + "body,pre,div {font-family: \"Microsoft YaHei\", Arial; padding:0px 0px; margin:0px 0px;}\r\n"
                + ".NoticeTool {background-color:orange;color:white;font-weight:bold;font-family:\"Microsoft YaHei\", \"Arial Black\", Verdana, 黑体, Arial;font-size:12.5pt;text-decoration:none;}\r\n"
                + ".NoticeTool a, .NoticeTool a:link, .NoticeTool a:hover, .NoticeTool a:visited, .NoticeTool a:active {display:block;width:100%;height:100%;text-decoration:none;}\r\n"
                + "</style>\r\n"
                + "</head>\r\n<body>\r\n" + newContent
                + "</body>"
                + "</html>";

        EmailTaskAddRequest addRequest = new EmailTaskAddRequest();
        addRequest.setFrom("国考云" + "<<EMAIL>>");
        addRequest.setEmail(candidate.getEmail());
        addRequest.setContent(totalMailContent);
        addRequest.setSubject(email.getTitle());
        addRequest.setCandidateId(candidate.get_id().toString());
        addRequest.setBatchName(batch.get_id().toString());
        addRequest.setUrl(notificationPrefix + "/api/v1/remote/notification/result");

        taskRemote.addTask(addRequest);

    }

    private NotificationBatch handleBatch(String periodId, String subjectId, List<Candidate> candidateList, List<Candidate> successList, Integer type){
        NotificationBatch batch = new NotificationBatch();
        batch.setPeriodId(new ObjectId(periodId));
        batch.setSubjectId(new ObjectId(subjectId));
        batch.setType(type);
        batch.setTotal(candidateList.size());

        for (Candidate candidate : candidateList) {
            //没有准考证无法发送
            List<AdmissionCard> cardList = this.cacheService.getAdmissionCardByIdCardNum(candidate.getProjectId().toString(), candidate.getIdCardNum());
            if(null == cardList || cardList.size() == 0){
                NotificationFailDetail detail = new NotificationFailDetail();
                detail.setCandidateId(candidate.get_id().toString());
                detail.setErrInfo("未编排考生无法发送通知！");
                batch.getFailList().add(detail);
            }
            else {
                //发送邮件
                if(type.equals(NotificationTypeEnum.EMAIL.getCode())){
                    //邮箱地址非法
                    if (!StringUtil.validEmail(candidate.getEmail())) {
                        NotificationFailDetail detail = new NotificationFailDetail();
                        detail.setCandidateId(candidate.get_id().toString());
                        detail.setErrInfo("邮箱地址错误！");
                        batch.getFailList().add(detail);
                    } else {
                        successList.add(candidate);
                    }
                }
                else {
                    // 发送短信
                    if (!StringUtil.validMobile(candidate.getMobile())) {
                        NotificationFailDetail detail = new NotificationFailDetail();
                        detail.setCandidateId(candidate.get_id().toString());
                        detail.setErrInfo("手机号格式错误！");
                        batch.getFailList().add(detail);
                    } else {
                        successList.add(candidate);
                    }
                }
            }
        }

        return this.notificationBatchRepository.save(batch);
    }

    private String getAdmissionUrl(Project project){
//        String loginUrl = admissionUrl + "/login?projectId=AAA&companyId=BBB&projectName=CCC";
//        loginUrl = loginUrl.replaceAll("AAA",project.get_id().toString());
//        loginUrl = loginUrl.replaceAll("BBB",project.getCompanyId().toString());
//        loginUrl = loginUrl.replaceAll("CCC",project.getName());
//        return loginUrl;
        return shortUrl + project.getShortCode();
    }

}
