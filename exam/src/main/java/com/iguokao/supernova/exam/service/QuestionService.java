package com.iguokao.supernova.exam.service;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.exam.document.Paper;
import com.iguokao.supernova.exam.document.Part;
import com.iguokao.supernova.exam.document.Question;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

public interface QuestionService {
    String addQuestion(Question question);

    Boolean preAddQuestion(Question question);

    void updateQuestion(Question site);

    Question getById(String questionId);
    List<Question> getByIds(Set<String> questionIds);

    List<String> deleteQuestions(List<String> questionIdList, String companyId);

    Tuple2<List<Question>, Integer> getPageData(String companyId, Integer type, String note, String body, String partId, Pageable pageable);

    List<Question> getByPaper(Paper paper);

    List<Question> getReviewQuestionByParts(List<Part> partList);
}
