package com.iguokao.supernova.exam.document;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Document
public class Period extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    @Indexed(name = "projectId_index")
    private ObjectId projectId; // 项目ID

    private String name;
    private Integer state = 0; // 状态

    private Date startAt; // 开始时间
    private Integer duration = 0; // 时长
    private Boolean ready = false; // 完成
    private String paperPassword;
    private String hash;
    private Integer admissionCardCount = 0;
    private List<Subject> subjectList = new ArrayList<>(); // 科目列表
}
