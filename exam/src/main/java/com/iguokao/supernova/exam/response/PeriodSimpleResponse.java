package com.iguokao.supernova.exam.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iguokao.supernova.exam.document.Period;
import com.iguokao.supernova.exam.document.Subject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class PeriodSimpleResponse {
    @Schema(description = "period Id")
    private String periodId;

    @Schema(description = "project Id")
    private String projectId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "状态")
    private Integer state;

    @Schema(description = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startAt;

    @Schema(description = "时长")
    private Integer duration;

    @Schema(description = "科目列表")
    private List<SubjectSimpleResponse> subjectList = new ArrayList<>();

    public static PeriodSimpleResponse of(Period obj){
        if(obj == null){
            return null;
        }
        PeriodSimpleResponse res = new PeriodSimpleResponse();
        BeanUtils.copyProperties(obj, res);
        res.setProjectId(obj.getProjectId().toString());
        res.setPeriodId(obj.get_id().toString());
        for(Subject subject : obj.getSubjectList()){
            res.getSubjectList().add(SubjectSimpleResponse.of(subject));
        }
        return res;
    }

    public static List<PeriodSimpleResponse> of(List<Period> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<PeriodSimpleResponse> res = new ArrayList<>();
        for(Period obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
