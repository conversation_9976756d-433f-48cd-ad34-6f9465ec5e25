package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.exam.document.Project;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ProjectAdmissionCardResponse {
    @Schema(description = "项目 Id")
    private String projectId;

    @Schema(description = "企业 Id")
    private String companyId;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "须知")
    private String admissionCardRequirement;

    @Schema(description = "Sign")
    private String sign;

    List<AdmissionCardResponse> admissionCardList = new ArrayList<>();

    public static ProjectAdmissionCardResponse of(Project project, List<AdmissionCard> list){
        if(project ==null || list==null){
            return null;
        }
        ProjectAdmissionCardResponse res = new ProjectAdmissionCardResponse();
        res.setProjectId(project.get_id().toString());
        res.setCompanyId(project.getCompanyId().toString());
        res.setName(project.getName());
        res.setAdmissionCardRequirement(project.getAdmissionCardRequirement());
        res.setAdmissionCardList(AdmissionCardResponse.of(list));
        return res;
    }
}
