package com.iguokao.supernova.exam.document;

import com.iguokao.supernova.common.enums.ActionTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
public class RoomProjectTest {
    private String roomId;
    private String projectId;
    private String name;
    private Integer state;
    private Integer candidateCount;

    private String clientVersion;
    private List<InputItem> inputList;

    List<Period> periodList;
    List<Action> testActionList;

    public static List<RoomProjectTest> of(List<Project> list, String roomId){
        if(list.isEmpty()) {
            return new ArrayList<>();
        }
        List<RoomProjectTest> res = new ArrayList<>();
        list.forEach(project -> {
            if(project.getOnline()){
                RoomProjectTest test = new RoomProjectTest();
                BeanUtils.copyProperties(project, test);
                test.setProjectId(project.get_id().toString());
                test.setRoomId(roomId);
                res.add(test);
            }
        });
        return res;
    }

    public static List<RoomProjectTest> fillPeriodList(List<RoomProjectTest> list, List<Period> periodList){
        if(list.isEmpty()) {
            return new ArrayList<>();
        }
        list.forEach(test -> {
            List<Period> projectPeriodList = periodList
                    .stream()
                    .filter(period -> test.getProjectId().equals(period.getProjectId().toString()))
                    .toList();
            test.setPeriodList(projectPeriodList);
        });
        return list;
    }

    public static List<RoomProjectTest> fillActionList(List<RoomProjectTest> list, List<Action> actionList){
        if(list.isEmpty()) {
            return new ArrayList<>();
        }
        list.forEach(test -> {
            List<Action> projectActionList = actionList
                    .stream()
                    .filter(action -> action.getRoomId().toString().equals(test.getRoomId()) && action.getProjectId().toString().equals(test.getProjectId()))
                    .toList();
            List<Action> testctionList = getLastTestResult(projectActionList);
            test.setTestActionList(testctionList);
        });
        return list;
    }

    private static List<Action> getLastTestResult(List<Action> list){
        if(list.isEmpty()){
            return new ArrayList<>();
        }
        Map<Integer, List<Action>> map = list
                .stream()
                .collect(Collectors.groupingBy(Action::getType));
        List<Action> res = new ArrayList<>();
        if(map.get(ActionTypeEnum.ENV_CHECK.getCode()) != null){
            map.get(ActionTypeEnum.ENV_CHECK.getCode()).sort(Comparator.comparing(Action::getCreatedAt).reversed());
            if(!map.get(ActionTypeEnum.ENV_CHECK.getCode()).isEmpty()){
                res.add(map.get(ActionTypeEnum.ENV_CHECK.getCode()).get(0));
            }
        }
        if(map.get(ActionTypeEnum.CAMERA_TEST.getCode()) != null) {
            map.get(ActionTypeEnum.CAMERA_TEST.getCode()).sort(Comparator.comparing(Action::getCreatedAt).reversed());
            if (!map.get(ActionTypeEnum.CAMERA_TEST.getCode()).isEmpty()) {
                res.add(map.get(ActionTypeEnum.CAMERA_TEST.getCode()).get(0));
            }
        }
        if(map.get(ActionTypeEnum.EXAM_TEST.getCode()) != null) {
            map.get(ActionTypeEnum.EXAM_TEST.getCode()).sort(Comparator.comparing(Action::getCreatedAt).reversed());
            if (!map.get(ActionTypeEnum.EXAM_TEST.getCode()).isEmpty()) {
                res.add(map.get(ActionTypeEnum.EXAM_TEST.getCode()).get(0));
            }
        }
        return res;
    }

    public static List<RoomProjectTest> fillCandidateCount(List<RoomProjectTest> list, Map<String, Optional<PeriodRoom>> map){
        if(list.isEmpty()) {
            return new ArrayList<>();
        }
        list.forEach(test -> {
            map.get(test.getProjectId())
                    .ifPresent(periodRoom -> test.setCandidateCount(periodRoom.getCandidateCount()));
        });
        return list;
    }
}
