package com.iguokao.supernova.exam.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum FaceDiffEnum implements BaseEnum {

    NO_PHOTO_NO_CAPTURE(1, "无照片，无需拍照"),
    NO_PHOTO_CAPTURE(2, "无照片，需拍照"),
    PHOTO_NO_CAPTURE(3, "有照片，无需拍照"),
    PHOTO_CAPTURE(4, "有照片，需拍照"),
    ;

    FaceDiffEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
