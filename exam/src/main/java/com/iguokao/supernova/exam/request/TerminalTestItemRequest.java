package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TerminalTestItemRequest {
    @Schema(description = "终端ip")
    private String ip;

    @Schema(description = "机位号")
    private Integer num;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Override
    public String toString() {
        return String.format("机位:%d IP:%s 错误信息:%s", this.num, this.ip, this.errorMessage);
    }
}
