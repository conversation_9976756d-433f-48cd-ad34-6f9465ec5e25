package com.iguokao.supernova.exam.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Getter
@Setter
public class ProjectUpdateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "小卷id")
    @Length(min = 24, max = 24, message = "24位id")
    private String companyId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String projectId;

    @Schema(description = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String name;

    @Schema(description = "项目简称")
    private String shortName;

    @Schema(description = "项目类型 1常规 2三级隔离")
    private Integer type;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date startAt;

    @Schema(description = "结束时间")
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date endAt;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "考试须知")
    private String requirement;

    @Schema(description = "准考证考试须知")
    private String admissionCardRequirement;

    @Schema(description = "固定座位")
    private Boolean fixedPosition;

    @Schema(description = "迟到登录时间")
    private Integer lateSecond;

    @Schema(description = "人脸对比")
    private Integer faceDiff;

    @Schema(description = "身份证登陆")
    private Boolean idNumLoginOnly;

    @Schema(description = "离线模式")
    private Boolean offlineMode;
}
