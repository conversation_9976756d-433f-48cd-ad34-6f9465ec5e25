package com.iguokao.supernova.exam.controller;

import cn.idev.excel.EasyExcel;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.remote.TaskRemote;
import com.iguokao.supernova.common.request.SmsTaskAddRequest;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.excel.ProctorItem;
import com.iguokao.supernova.exam.excel.ProctorItemListener;
import com.iguokao.supernova.exam.request.*;
import com.iguokao.supernova.exam.response.AttachmentResponse;
import com.iguokao.supernova.exam.response.PageProjectResponse;
import com.iguokao.supernova.exam.response.ProjectResponse;
import com.iguokao.supernova.exam.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/v1/project")
@RequiredArgsConstructor
public class ProjectController {

    private final ProjectService projectService;
    private final PeriodService periodService;
    private final ProctorService proctorService;
    private final SiteService siteService;
    private final CacheService cacheService;
    private final TaskRemote taskRemote;

    @Value(value = "${app.service.task-callback}")
    private String notificationPrefix;

    @PostMapping("/page")
    @Operation(summary = "查看所有项目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageProjectResponse.class)))
    public RestResponse<PageResponse<ProjectResponse>> list(@RequestBody ProjectPageRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"createdAt");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Project>, Integer> page = this.projectService.getPage(request.getCompanyId(), request.getName(), pageable);
        List<Period> periodList = this.periodService.getByProjectList(page.first());
        List<ProjectResponse> res = ProjectResponse.of(page.first(),periodList);
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }

    @PostMapping("/add")
    @Operation(summary = "添加项目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@Validated @RequestBody ProjectAddRequest request) {
        Project project = new Project();
        BeanUtils.copyProperties(request, project);
        project.setCompanyId(new ObjectId(request.getCompanyId()));
        String projectIdStr = this.projectService.add(project);
        return RestResponse.success(projectIdStr);
    }

    @PostMapping("/update")
    @Operation(summary = "更新项目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@Validated @RequestBody ProjectUpdateRequest request) {
        Project project = this.projectService.getById(request.getProjectId());
        BeanUtils.copyProperties(request, project);
        this.projectService.update(project);
        return RestResponse.success();
    }

    @PostMapping("/client/version/update")
    @Operation(summary = "更新项目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> updateClientVersion(@Validated @RequestBody ProjectClientVersionUpdateRequest request) {
        this.projectService.updateClientVersion(request.getProjectId(), request.getClientVersion());
        return RestResponse.success();
    }

    @GetMapping("/client/version")
    @Operation(summary = "获取当前版本")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> getClientVersion() {
        String ver = this.cacheService.getClientVersion();
        return RestResponse.success(ver);
    }

    @GetMapping("/info/{projectId}")
    @Operation(summary = "项目信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = ProjectResponse.class)))
    public RestResponse<ProjectResponse> info(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String projectId) {
        Project res = this.projectService.getById(projectId);
        ProjectResponse response = ProjectResponse.of(res,null);
        response.setPeriodIdList(this.periodService.getPeriodIds(projectId));
        return RestResponse.success(response);
    }

    @PostMapping("/remove/{projectId}")
    @Operation(summary = "删除项目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> remove(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String projectId) {
        this.projectService.remove(projectId);
        return RestResponse.success();
    }

    @GetMapping("/online/{projectId}")
    @Operation(summary = "项目上线")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Boolean.class)))
    public RestResponse<Boolean> online(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String projectId) {
        boolean res = this.projectService.online(projectId);
        return RestResponse.success(res);
    }


    @PostMapping("/attachment/add")
    @Operation(summary = "添加附件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> attachmentAdd(@Validated @RequestBody AttachmentAddRequest request) {
        Attachment attachment = new Attachment();
        BeanUtils.copyProperties(request, attachment);
        this.projectService.addAttachment(request.getProjectId(), attachment);
        return RestResponse.success();
    }

    @PostMapping("/attachment/delete")
    @Operation(summary = "删除附件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> attachmentDelete(@Validated @RequestBody AttachmentDeleteRequest request) {
        this.projectService.deleteAttachment(request.getProjectId(), request.getName());
        return RestResponse.success();
    }

    @GetMapping("/attachment/list/{projectId}")
    @Operation(summary = "附件列表")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = AttachmentResponse.class))))
    public RestResponse<List<AttachmentResponse>> attachmentAdd(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String projectId) {
        Project project = this.projectService.getById(projectId);
        return RestResponse.success(AttachmentResponse.of(project.getAttachmentList()));
    }

    @PostMapping("/input/update")
    @Operation(summary = "输入法列表 更新")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = AttachmentResponse.class))))
    public RestResponse<List<AttachmentResponse>> inputUpdate(@RequestBody @Validated InputUpdateRequest request) {
        Project project = this.projectService.getById(request.getProjectId());
        project.setInputList(request.getInputList());
        this.projectService.update(project);
        return RestResponse.success(AttachmentResponse.of(project.getAttachmentList()));
    }

    @RequestMapping(value = "/excel/proctor/{projectId}", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "批量导入督考官")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExcelErrResponse.class))))
    public RestResponse<List<ExcelErrResponse>>  excel(@PathVariable String projectId, @RequestPart(value = "file") MultipartFile file) throws IOException {
        List<ExcelErrResponse> errResponseList = new ArrayList<>();
        Project project = this.projectService.getById(projectId);
        String time = "";
        int count = 1;
        DateFormat format = new SimpleDateFormat("MM月dd日 HH:mm");
        DateFormat format2 = new SimpleDateFormat("HH:mm");
        List<Period> periodList = this.periodService.getByProjectId(project.get_id().toString());
        for(Period period : periodList){
            Date periodEndAt = new Date(period.getStartAt().getTime() + period.getDuration() * 1000);
            time += format.format(period.getStartAt()) + "-" + format2.format(periodEndAt);
            if(count != periodList.size() ){
                count++;
                time += ", ";
            }
        }
        
        List<Proctor> proctorList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), ProctorItem.class, new ProctorItemListener(projectId, errResponseList, proctorList, proctorService, siteService)).sheet().doRead();

        System.out.println("为督考官发送短信通知");

        //督考list 去发催促短信
        for(int i = proctorList.size() -1 ; i >= 0 ; i--){

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("to",proctorList.get(i).getMobile());
            jsonObject.put("appId","8a216da8790d6de5017941af72ee0da3");
            jsonObject.put("templateId","2735769");
            jsonObject.put("datas", Arrays.asList(project.getName(), time,"https://supernova-sys.iguokao.com/#/inspection?uuid=" + proctorList.get(i).getUuid()));

            SmsTaskAddRequest addRequest = new SmsTaskAddRequest();
            addRequest.setContent(jsonObject.toString());
            addRequest.setCandidateId("proctor");
            addRequest.setBatchName("proctor_batch");
            addRequest.setUrl(notificationPrefix + "/api/v1/remote/notification/result");
            taskRemote.addSmsTask(addRequest);
        }

        return RestResponse.success(errResponseList);
    }
}
