package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Subject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;


@Getter
@Setter
public class SubjectSimpleResponse {

    @Schema(description = "科目id")
    private String subjectId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = " 时长")
    private Integer duration;


    @Schema(description = "备注")
    private String note;

    @Schema(description = "总分")
    private Double score;

    public static SubjectSimpleResponse of(Subject obj){
        if(obj==null){
            return null;
        }
        SubjectSimpleResponse res = new SubjectSimpleResponse();
        BeanUtils.copyProperties(obj, res);
        res.setSubjectId(obj.get_id().toString());

        return res;
    }
}

