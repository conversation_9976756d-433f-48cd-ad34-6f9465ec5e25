package com.iguokao.supernova.exam.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum ProjectTypeEnum implements BaseEnum {
    STANDARD(1, "标准"),
    THREE_LEVEL_AGENT(2, "3层代理"),
    ;

    ProjectTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
