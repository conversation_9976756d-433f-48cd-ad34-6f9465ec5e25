package com.iguokao.supernova.exam.request;

import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SubjectPageRequest extends PageRequest {

    @Schema(description = "公司id")
    private String companyId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "是否加入到时段中")
    private Boolean isPeriod;
}
