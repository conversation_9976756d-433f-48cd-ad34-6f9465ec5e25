package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Getter
@Setter
public class CompanyResponse {
    private String companyId;
    private String name;
    private String shortName;
    private String logo;
    private String theme;

    private String logoUrl;
    private String themeUrl;

    public static CompanyResponse of(CompanyRemoteResponse obj){
        if(obj == null) return null;
        CompanyResponse res = new CompanyResponse();
        BeanUtils.copyProperties(obj, res);
        return res;
    }
}
