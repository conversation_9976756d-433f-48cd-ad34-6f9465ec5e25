package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.document.Site;
import org.bson.types.ObjectId;

import java.util.List;

public interface RoomService {
    void add(Room room);
    void addAll(List<Room> list);
    void update(Room room);
    void remove(String roomId);
    Room getById(String roomId);
    List<Room> getByIdList(List<ObjectId> list);
    List<Room> getList(String siteId, String name);
    List<Room> getBySiteId(String siteId);
    boolean usingRoom(String roomId);

    String roomLogin(ImageCode imageCode, String loginCode, String version, String ip);
    Tuple2<Site, String> agentLogin(ImageCode imageCode, String loginCode, String version, String ip);
}
