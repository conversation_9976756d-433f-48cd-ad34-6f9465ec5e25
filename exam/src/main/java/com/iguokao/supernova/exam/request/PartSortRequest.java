package com.iguokao.supernova.exam.request;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class PartSortRequest {

    @Schema(description = "时段")
    @Length(min = 24, max = 24, message = "24位id")
    private String periodId;

    @Schema(description = "科目id")
    @Length(min = 24, max = 24, message = "24位id")
    private String subjectId;

    @Schema(description = "小卷数据")
    @NotEmpty(message = "数组不能为空")
    private List<String> partList;
}