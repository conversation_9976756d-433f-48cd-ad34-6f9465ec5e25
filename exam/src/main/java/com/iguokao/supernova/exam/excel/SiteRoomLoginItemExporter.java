package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.exam.document.Room;

import java.util.ArrayList;
import java.util.List;

public class SiteRoomLoginItemExporter {

    public static List<List<String>> head() {

        List<List<String>> list = new ArrayList<>();

        List<String> head0 = new ArrayList<>();
        head0.add("序号");

        List<String> head1 = new ArrayList<>();
        head1.add("考场名称");

        List<String> head2 = new ArrayList<>();
        head2.add("考场地址");

        List<String> head3 = new ArrayList<>();
        head3.add("机位总数");

        List<String> head4 = new ArrayList<>();
        head4.add("可用机位");

        List<String> head5 = new ArrayList<>();
        head5.add("房间登录码 ");


        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        return list;
    }

    public static List<List<Object>> data(List<Room> list) {
        List<List<Object>> res = new ArrayList<>();
        int num = 1;
        for(Room item : list){
            List<Object> line = new ArrayList<>();
            line.add(num);
            line.add(item.getName());
            line.add(item.getAddress());
            line.add(item.getCapacity());
            line.add(item.getAvailable());
            line.add(item.getLoginCode());
            num ++;
            res.add(line);
        }

        return res;
    }
}
