package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.RoomStateEnum;
import com.iguokao.supernova.exam.excel.RoomInfoItem;
import com.iguokao.supernova.exam.excel.RoomLoginItem;

import java.util.List;

public interface PeriodRoomService {

    PeriodRoom getById(String periodId, String roomId);
    void generate(String periodId, List<Confirm> confirmList, List<Room> roomList, boolean countCandidate);
    void setVideoUrl(String periodId, String roomId, String taskId, String position, String url);
    void setRoomNumber(String periodId, String roomId, Integer roomNumber);
    void setRoomState(String periodId, String roomId, RoomStateEnum state);
    List<Site> getSiteListByPeriodId(String periodId);
    List<PeriodRoom> getRoomListByPeriodId(String periodId, String siteId);
    List<PeriodRoom> getRoomListByProjectIdAndSiteId(String projectId, String siteId);
    List<PeriodRoom> getRoomListByPeriodId(String periodId);
    List<PeriodRoom> getRoomListByPeriodIdSortBySiteId(String periodId);
    void initMonitorRoom(String periodId, boolean ready);
    void updateAttachment(String periodId, String roomId, List<String> list);

    List<PeriodRoom> getAllByPeriodId(String periodId);

    void roomIndexChange(String periodId, String siteId, String roomId, int roomIndex);

    List<RoomInfoItem> getRoomInfoData(String periodId);

    List<RoomLoginItem> getRoomLoginData(String periodId, String siteId);
    List<RoomLoginItem> getRoomLoginDataNotArranged(String periodId, String siteId);

    List<RoomLoginItem> getRoomLoginDataByPeriodAndSiteId(String periodId, String siteId);
}
