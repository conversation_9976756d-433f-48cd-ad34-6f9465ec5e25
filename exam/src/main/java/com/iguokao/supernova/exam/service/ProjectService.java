package com.iguokao.supernova.exam.service;

import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.exam.document.Attachment;
import com.iguokao.supernova.exam.document.PeriodRoom;
import com.iguokao.supernova.exam.document.Project;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ProjectService {
    String add(Project project);
    void update(Project project);

    void updateClientVersion(String projectId, String version);

    void remove(String projectId);
    Project getById(String projectId);
    List<Project> getByIdList(List<ObjectId> idList);
    Tuple2<List<Project>, Integer> getPage(String companyId, String name, Pageable pageable);
    boolean online(String projectId);
    void addAttachment(String projectId, Attachment attachment);
    void deleteAttachment(String projectId, String attachmentName);
    Project getTestProject(Integer candidateCount);

    Tuple2<List<Project>, List<PeriodRoom>> getAgentProjectList(String siteId);
}
