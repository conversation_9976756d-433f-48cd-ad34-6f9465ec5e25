package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class CandidateUpdateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生id")
    @Length(min = 24, max = 24, message = "24位id")
    private String candidateId;

    @Schema(description = "考生姓名")
    @NotBlank(message = "名称不能为空")
    private String fullName;

    @Schema(description = "证件号")
    @NotBlank(message = "证件号不能为空")
    private String idCardNum;

    @Schema(description = "证件类型")
    @Min(value = 1, message = "证件类型错误")
    @Max(value = 5, message = "证件类型错误")
    private Integer idCardType;

    @Schema(description = "城市")
    @NotBlank(message = "城市不能为空")
    private String city;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "电子邮件")
    private String email;

    @Schema(description = "座位号")
    private String seatNum;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "头像")
    private String avatar;
}
