package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class AutoArrangeRequest {

    @NotNull(message = "random 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "座位 乱序")
    private Boolean random;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private String periodId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private List<String> siteIdList;
}
