package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Proctor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface ProctorRepository extends MongoRepository<Proctor, ObjectId> {

    Proctor findByProjectIdAndMobile(ObjectId projectId, String mobile);
    Proctor findProctorByUuid(String uuid);
}
