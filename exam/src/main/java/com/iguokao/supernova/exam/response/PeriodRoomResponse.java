package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class PeriodRoomResponse {

    @Schema(description = "periodRoom Id")
    private String periodRoomId;

    @Schema(description = "project Id")
    private String projectId;

    @Schema(description = "operator Id")
    private String operatorId;

    @Schema(description = "room Id")
    private String roomId;

    @Schema(description = "period Id")
    private String periodId;

    @Schema(description = "主机位推流ID")
    private String primaryStreamId;

    @Schema(description = "主机位推流房间号")
    private String primaryRoomNumber;

    @Schema(description = "次机位推流ID")
    private String secondaryStreamId;

    @Schema(description = "次机位推流房间号")
    private String secondaryRoomNumber;

    @Schema(description = "房间状态")
    private Integer roomState;

    @Schema(description = "是否测试通过")
    private Boolean testPassed = false;

    @Schema(description = "可用机位数量")
    private Integer available;

    @Schema(description = "考生数量")
    private Integer candidateCount;

    @Schema(description = "考生到场数量")
    private Integer candidateArrivedCount;

    @Schema(description = "项目")
    private ProjectResponse project;

    @Schema(description = "时段")
    private PeriodResponse period;

    private RoomResponse room;

    public static PeriodRoomResponse of(PeriodRoom obj){
        if(obj==null){
            return null;
        }
        PeriodRoomResponse res = new PeriodRoomResponse();
        BeanUtils.copyProperties(obj, res);
        res.setPeriodRoomId(obj.get_id().toString());
        res.setProjectId(obj.getProjectId().toString());
        res.setPeriodId(obj.getPeriodId().toString());
        res.setRoomId(obj.getRoomId().toString());
        return res;
    }

    public static PeriodRoomResponse of(PeriodRoom obj, Project project, Period period){
        if(obj==null){
            return null;
        }
        PeriodRoomResponse res = new PeriodRoomResponse();
        BeanUtils.copyProperties(obj, res);
        res.setPeriodRoomId(obj.get_id().toString());
        res.setProjectId(obj.getProjectId().toString());
        res.setPeriodId(obj.getPeriodId().toString());
        res.setRoomId(obj.getRoomId().toString());
        if(project != null){
            res.setProject(ProjectResponse.of(project,null));
        }
        if(period != null){
            res.setPeriod(PeriodResponse.of(period));
        }
        return res;
    }

    public static List<PeriodRoomResponse> of(List<PeriodRoom> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<PeriodRoomResponse> res = new ArrayList<>();
        for(PeriodRoom obj : list){
            res.add(of(obj));
        }
        return res;
    }

    public static List<PeriodRoomResponse> of(List<PeriodRoom> list, List<Project> projectList, List<Period> periodList){
        if(list==null){
            return new ArrayList<>();
        }
        List<PeriodRoomResponse> res = new ArrayList<>();
        for(PeriodRoom obj : list){
            Project pr = projectList
                    .stream()
                    .filter(f -> obj.getProjectId().equals(f.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
            Period pe = periodList
                    .stream()
                    .filter(f -> obj.getPeriodId().equals(f.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
            if(pr.getOnline()){
                res.add(of(obj, pr, pe));
            }
        }
        return res;
    }
}
