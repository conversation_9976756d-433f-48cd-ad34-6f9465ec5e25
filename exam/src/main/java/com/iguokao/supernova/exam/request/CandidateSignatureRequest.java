package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CandidateSignatureRequest {
    @Schema(description = "考生Id Id")
    private String candidateId;

    @Schema(description = "时段 Id")
    private String periodId;

    @Schema(description = "项目 Id")
    private String projectId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "考生姓名")
    private String fullName;

    @Schema(description = "取消签到")
    private Boolean canceled;
}
