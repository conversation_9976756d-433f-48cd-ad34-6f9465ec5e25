package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.document.ImageCode;
import com.iguokao.supernova.common.response.CompanyRemoteResponse;
import com.iguokao.supernova.common.response.OperatorRemoteResponse;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.remote.ManagementRemote;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.common.enums.ActionTypeEnum;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.repository.*;
import com.iguokao.supernova.exam.service.CacheService;
import org.bson.types.ObjectId;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CacheServiceImpl  extends RedisServiceImpl implements CacheService {
    private final ProjectRepository projectRepository;
    private final PeriodRoomRepository periodRoomRepository;
    private final PeriodRepository periodRepository;
    private final SiteRepository siteRepository;
    private final RoomRepository roomRepository;
    private final CandidateRepository candidateRepository;
    private final PaperRepository paperRepository;
    private final ActionRepository actionRepository;
    private final ManagementRemote managementRemote;

    public CacheServiceImpl(
            RedisTemplate<String, ImageCode> imageCodeRedisTemplate,
            RedisTemplate<String, AdmissionCard> admissionCardRedisTemplate,
            RedisTemplate<String, OperatorRemoteResponse> operatorRedisTemplate,
            RedisTemplate<String, Site> siteRedisTemplate,
            RedisTemplate<String, Room> roomRedisTemplate,
            RedisTemplate<String, PeriodRoom> periodRoomRedisTemplate,
            RedisTemplate<String, Candidate> candidateRedisTemplate,
            RedisTemplate<String, MonitorSignature> monitorSignatureRedisTemplate,
            RedisTemplate<String, MonitorRoom> monitorRoomRedisTemplate,
            RedisTemplate<String, Paper> paperRedisTemplate,
            RedisTemplate<String, Project> projectRedisTemplate,
            RedisTemplate<String, Period> periodRedisTemplate,
            RedisTemplate<String, RoomProjectTest> roomProjectTestRedisTemplate,
            RedisTemplate<String, CompanyRemoteResponse> companyRedisTemplate,
            RedisTemplate<String, MonitorTest> testRedisTemplate,
            StringRedisTemplate redisTemplate,
            ProjectRepository projectRepository,
            PeriodRoomRepository periodRoomRepository,
            PeriodRepository periodRepository,
            SiteRepository siteRepository,
            RoomRepository roomRepository,
            CandidateRepository candidateRepository,
            PaperRepository paperRepository,
            ActionRepository actionRepository, ManagementRemote managementRemote) {
        super(imageCodeRedisTemplate, admissionCardRedisTemplate, operatorRedisTemplate, siteRedisTemplate, roomRedisTemplate, periodRoomRedisTemplate, candidateRedisTemplate, monitorSignatureRedisTemplate, monitorRoomRedisTemplate, paperRedisTemplate, projectRedisTemplate, periodRedisTemplate, roomProjectTestRedisTemplate, companyRedisTemplate, testRedisTemplate, redisTemplate);
        this.projectRepository = projectRepository;
        this.periodRoomRepository = periodRoomRepository;
        this.periodRepository = periodRepository;
        this.siteRepository = siteRepository;
        this.roomRepository = roomRepository;
        this.candidateRepository = candidateRepository;
        this.paperRepository = paperRepository;
        this.actionRepository = actionRepository;
        this.managementRemote = managementRemote;
    }

    @Override
    public Site getSite(String siteId) {
        Site site = super.getSite(siteId);
        if(site == null){
            site = this.siteRepository.findById(new ObjectId(siteId))
                    .orElseThrow(()->new ServiceException(ExceptionEnum.SITE_NOT_FOUND));
            super.setSite(site);
        }
        return site;
    }

    @Override
    public Room getRoom(String roomId) {
        Room room = super.getRoom(roomId);
        if(room == null){
            room = this.roomRepository.findById(new ObjectId(roomId))
                    .orElseThrow(()->new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
            super.setRoom(room);
        }
        return room;
    }

    @Override
    public List<Room> getRoom(List<String> list) {
        List<Room> res = super.getRoom(list);
        int index = 0;
        for(Room room : res){
            if(room == null){
                room = this.roomRepository.findById(new ObjectId(list.get(index)))
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.ROOM_NOT_FOUND));
                res.add(room);
                super.setRoom(room);
            }
            index++;
        }
        return res;
    }

    @Override
    public OperatorRemoteResponse getOperator(String operatorId) {
        OperatorRemoteResponse operator = super.getOperator(operatorId);
        if(operator == null){
            RestResponse<OperatorRemoteResponse> res = this.managementRemote.getOperatorById(operatorId);
            if(res.getCode() != 0){
                throw ServiceException.remote(res);
            }
            operator = res.getData();
            super.setOperator(operator);
        }
        return operator;
    }

    @Override
    public List<PeriodRoom> getPeriodRoomList(String roomId) {
        List<PeriodRoom> list = super.getPeriodRoomList(roomId);
        if(list.isEmpty()){
            list = this.periodRoomRepository.findByRoomId(new ObjectId(roomId));
            if(!list.isEmpty()){
                long monthSecond = 3600 * 24 * 31L;
                list = list
                        .stream()
                        .filter(periodRoom -> (new Date().getTime() - periodRoom.getCreatedAt().getTime()) / 1000 < monthSecond)
                        .toList();
            }
            super.setPeriodRoomList(list);
        }
        return list;
    }

//    @Override
//    public List<Candidate> getRoomCandidateList(String periodId, String roomId) {
//        List<Candidate> list = super.getRoomCandidateList(periodId, roomId);
//        if(list.isEmpty()){
//            list = this.candidateRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId));
//            if(!list.isEmpty()){
//                super.setRoomCandidateList(list);
//            }
//        }
//        return list;
//    }

    @Override
    public List<Paper> getPaperList(String periodId) {
        List<Paper> list = super.getPaperList(periodId);
        if(list.isEmpty()){
            list = this.paperRepository.findByPeriodId(new ObjectId(periodId));
            if(list.isEmpty()){
                throw new ServiceException(ExceptionEnum.PAPER_NOT_FOUND);
            }
            super.setPaperList(periodId, list);
        }
        return list;
    }

    @Override
    public Project getProject(String projectId) {
        Project project = super.getProject(projectId);
        if(project == null){
            project = this.projectRepository.findById(new ObjectId(projectId))
                    .orElseThrow(()->new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
            super.setProject(project);
        }
        return project;
    }

    @Override
    public List<Project> getProject(List<String> list) {
        List<Project> res = super.getProject(list);
        for(int i=0; i<res.size(); i++){
            Project project = res.get(i);
            if(project == null){
                project = this.projectRepository.findById(new ObjectId(list.get(i)))
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.PROJECT_NOT_FOUND));
                res.set(i, project);
                super.setProject(project);
            }
        }
        return res;
    }

    @Override
    public Period getPeriod(String periodId) {
        Period period = super.getPeriod(periodId);
        if(period == null){
            period = this.periodRepository.findById(new ObjectId(periodId))
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
            super.setPeriod(period);
        }
        return period;
    }

    @Override
    public List<Period> getPeriod(List<String> list) {
        List<Period> res = super.getPeriod(list);
        for(int i=0; i<list.size(); i++){
            Period period = res.get(i);
            if(period == null){
                period = this.periodRepository.findById(new ObjectId(list.get(i)))
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.PERIOD_NOT_FOUND));
                res.set(i, period);
                super.setPeriod(period);
            }
        }
        return res;
    }

    @Override
    public List<RoomProjectTest> getRoomProjectTest(String roomId) {
        List<RoomProjectTest> list = super.getRoomProjectTest(roomId);
        if (list.isEmpty()) {
            List<PeriodRoom> periodRoomList = periodRoomRepository.findByRoomId(new ObjectId(roomId));
            if(periodRoomList.isEmpty()){
                return new ArrayList<>();
            }
            List<ObjectId> projectIdList = periodRoomList
                    .stream()
                    .map(PeriodRoom::getProjectId)
                    .distinct()
                    .toList();
            List<ObjectId> periodIdList = periodRoomList
                    .stream()
                    .map(PeriodRoom::getPeriodId)
                    .distinct()
                    .toList();
            Map<String, Optional<PeriodRoom>> map = periodRoomList
                    .stream()
                    .collect(Collectors.groupingBy(periodRoom -> periodRoom.getProjectId().toString(),
                            Collectors.maxBy(Comparator.comparing(PeriodRoom::getCandidateCount))
                    ));

            List<Project> projectList = this.projectRepository.findBy_idIn(projectIdList);
            list = RoomProjectTest.of(projectList, roomId);
            List<Period> periodList = this.periodRepository.findByProjectIdIn(projectIdList);
            list = RoomProjectTest.fillPeriodList(list, periodList);
            List<Integer> typeList = new ArrayList<>();
            typeList.add(ActionTypeEnum.ENV_CHECK.getCode());
            typeList.add(ActionTypeEnum.CAMERA_TEST.getCode());
            typeList.add(ActionTypeEnum.EXAM_TEST.getCode());

            List<Action> actionList = this.actionRepository.findByRoomIdAndProjectIdInAndTypeIn(new ObjectId(roomId), projectIdList, typeList);
            list = RoomProjectTest.fillActionList(list, actionList);
            list = RoomProjectTest.fillCandidateCount(list, map);
            super.setRoomProjectTest(list);
        }
        return list;
    }

    @Override
    public CompanyRemoteResponse getCompany(String companyId) {
        CompanyRemoteResponse company = super.getCompany(companyId);
        if(company == null){
            RestResponse<CompanyRemoteResponse> res = this.managementRemote.getCompany(companyId);
            if(res.getCode() != 0){
                throw ServiceException.remote(res);
            }
            company = res.getData();
            super.setCompany(company);
        }
        return company;
    }
}
