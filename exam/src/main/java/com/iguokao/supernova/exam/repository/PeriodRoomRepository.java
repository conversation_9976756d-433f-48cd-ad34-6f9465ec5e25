package com.iguokao.supernova.exam.repository;

import com.iguokao.supernova.exam.document.Period;
import com.iguokao.supernova.exam.document.PeriodRoom;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface PeriodRoomRepository extends MongoRepository<PeriodRoom, ObjectId> {
    int countByPeriodId(ObjectId periodId);
    int countByProjectId(ObjectId projectId);

    Optional<PeriodRoom> findByPeriodIdAndRoomId(ObjectId periodId, ObjectId roomId);
    Optional<PeriodRoom> findByPeriodIdAndSiteIdAndRoomIndex(ObjectId periodId, ObjectId siteId, Integer index);

    List<PeriodRoom> findByRoomId(ObjectId roomId);
    List<PeriodRoom> findByPeriodId(ObjectId periodId);
    List<PeriodRoom> findByProjectId(ObjectId projectId);
    List<PeriodRoom> findBySiteId(ObjectId siteId);

    List<PeriodRoom> findByPeriodIdAndSiteId(ObjectId periodId, ObjectId siteId);

    void deleteByPeriodId(ObjectId periodId);


}
