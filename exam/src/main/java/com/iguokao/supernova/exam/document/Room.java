package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter
@Setter
@Document
public class Room extends BaseDocument {
    @Indexed(name = "siteId_index")
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId siteId;

    private String name;
    private Integer sort = 100; // 排序
    private String address; // 详细地址
    private String note; // 备注
    private Integer capacity = 0; // 容量
    private Integer available = 0; // 可用

    @Indexed(name = "loginCode_index")
    private String loginCode; // 房间登录码
    private Boolean cloudSystem = false; // 云机房
    private Boolean localCamera = false; // 本地监控
    private Boolean blocked = false; // 挡板
    private Integer pcCameraCount = 0; // 电脑摄像头个数
}
