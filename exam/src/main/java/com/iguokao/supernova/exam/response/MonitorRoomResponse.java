package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Action;
import com.iguokao.supernova.exam.document.MonitorRoom;
import com.iguokao.supernova.exam.document.MonitorTest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class MonitorRoomResponse {
    private String projectId;
    private String roomId;
    private String siteId;
    private String periodId;

    private String province;
    private String city;
    private String roomName;
    private Integer roomIndex; // 考场编号

    private Integer roomState;
    private Integer candidateCount;
    private Integer candidateArrivedCount;
    private Integer candidateFinishedCount;
    private Integer candidateAnsweringCount;
    private Integer candidateLoginCount;
    private Integer cheatCount;
    private Integer forceFinishedCount;
    private Integer lateCount;
    private Integer delayCount;
    private Integer operatorSignatureCount = 0;

    private Boolean candidateDownloaded;
    private Boolean paperDownloaded;
    private Boolean passwordGet;
    private Boolean answerUploaded;
    private Boolean actionUploaded;

    private Boolean examTested;
    private Boolean cameraTested;
    private Boolean envTested;
    private String envError;
    private String examError;
    private Date updatedAt;

    public static MonitorRoomResponse of(MonitorRoom obj, MonitorTest test){
        if(obj==null){
            return null;
        }
        MonitorRoomResponse res = new MonitorRoomResponse();
        BeanUtils.copyProperties(obj, res);
        if(test != null){
            BeanUtils.copyProperties(test, res,"updatedAt");
        }
        res.setProjectId(obj.getProjectId().toString());
        res.setPeriodId(obj.getPeriodId().toString());
        res.setRoomId(obj.getRoomId().toString());
        res.setSiteId(obj.getSiteId().toString());
        return res;
    }

    public static List<MonitorRoomResponse> of(List<MonitorRoom> list, List<MonitorTest> roomTestList){
        if(list.isEmpty()){
            return new ArrayList<>();
        }
        List<MonitorRoomResponse> res = new ArrayList<>();
        for(MonitorRoom obj : list){
            MonitorTest test = roomTestList
                    .stream()
                    .filter(t -> t.getRoomId().equals(obj.getRoomId()))
                    .findFirst()
                    .orElse(null);
            res.add(of(obj, test));
        }
        return res;
    }
}
