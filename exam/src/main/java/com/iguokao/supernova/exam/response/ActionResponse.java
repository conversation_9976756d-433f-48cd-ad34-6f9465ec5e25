package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Action;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ActionResponse {
    private String actionId;
    private String candidateId;
    private String projectId;
    private String periodId;
    private String subjectId;
    private String siteId;
    private String roomId;

    private Integer type;
    private Integer value;
    private String text;

    public static ActionResponse of(Action obj){
        if(obj==null){
            return null;
        }
        ActionResponse res = new ActionResponse();
        BeanUtils.copyProperties(obj, res);
        if(obj.get_id() != null){
            res.setActionId(obj.get_id().toString());
        }
        if(obj.getProjectId() != null){
            res.setProjectId(obj.getProjectId().toString());
        }
        if(obj.getPeriodId() != null){
            res.setPeriodId(obj.getPeriodId().toString());
        }
        if(obj.getSubjectId() != null){
            res.setSubjectId(obj.getSubjectId().toString());
        }
        if(obj.getSiteId() != null){
            res.setSiteId(obj.getSiteId().toString());
        }
        if(obj.getRoomId() != null){
            res.setRoomId(obj.getRoomId().toString());
        }
        return res;
    }

    public static List<ActionResponse> of(List<Action> list){
        if(list==null){
            return new ArrayList<>();
        }
        List<ActionResponse> res = new ArrayList<>();
        for(Action obj : list){
            res.add(of(obj));
        }
        return res;
    }
}
