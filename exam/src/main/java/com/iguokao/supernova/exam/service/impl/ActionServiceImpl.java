package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.exam.document.Action;
import com.iguokao.supernova.exam.repository.ActionRepository;
import com.iguokao.supernova.exam.service.ActionService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ActionServiceImpl implements ActionService {
    private final ActionRepository actionRepository;

    @Override
    public void add(Action action) {
        this.actionRepository.insert(action);
    }

    @Override
    public List<Action> getListByCandidate(String candidateId) {
        return this.actionRepository.findByCandidateId(new ObjectId(candidateId));
    }

    @Override
    public List<Action> getListByPeriodIdAndRoomId(String periodId, String roomId) {
        return this.actionRepository.findByPeriodIdAndRoomId(new ObjectId(periodId), new ObjectId(roomId));
    }

    @Override
    public List<Action> getListByPeriodAndType(String periodId, int type) {
        return this.actionRepository.findByPeriodIdAndType(new ObjectId(periodId), type);
    }

    @Override
    public List<Action> getListByProjectIdAndRoomId(String projectId, String roomId) {
        return this.actionRepository.findByProjectIdAndRoomId(new ObjectId(projectId), new ObjectId(roomId));
    }

}
