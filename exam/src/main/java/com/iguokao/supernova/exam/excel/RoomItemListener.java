package com.iguokao.supernova.exam.excel;


import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.service.RoomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@RequiredArgsConstructor
public class RoomItemListener extends AnalysisEventListener<RoomItem> {

    private final String siteId;
    private final RoomService roomService;
    private final List<ExcelErrResponse> errList;

    private final List<Room> list = new ArrayList<>();

    @Override
    public void invoke(RoomItem item, AnalysisContext analysisContext) {
        Integer currentRow = analysisContext.readRowHolder().getRowIndex();

        if((item.getName() != null && item.getName().contains("考场名称")) ||
                (item.getAddress() != null && item.getAddress().contains("考场地址"))){
            return;
        }

        try {
            if(item.getName() == null ||
                    item.getAddress() == null ||
                    item.getAvailable() == null ||
                    item.getCapacity() == null){
                if(currentRow > 0){
                    ExcelErrResponse err = new ExcelErrResponse();
                    err.setRow(currentRow + 1);
                    err.setError("考场名称、地址、机位数量、可用数量不能为空");
                    errList.add(err);
                    return;
                }
            }

            Room room = new Room();
            room.setSiteId(new ObjectId(siteId));
            room.setName(item.getName());
            room.setNote(item.getNote());
            room.setAddress(item.getAddress());
            room.setCapacity(item.getCapacity());
            room.setAvailable(item.getAvailable());

            //云机房
            if(item.getCloudSystem().equals("是")){
                room.setCloudSystem(true);
            }
            //本地摄像头
            if(item.getLocalCamera().equals("有")){
                room.setLocalCamera(true);
            }
            // 挡板
            if(item.getBlocked().equals("有")){
                room.setBlocked(true);
            }
            // 电脑摄像头个数
            if(null != item.getPcCameraCount() && !item.getPcCameraCount().equals("无")){
                switch (item.getPcCameraCount()) {
                    case "1", "1个" -> room.setPcCameraCount(1);
                    case "2", "2个" -> room.setPcCameraCount(2);
                    case "3", "3个" -> room.setPcCameraCount(3);
                }
            }
            room.setSort(currentRow);
            list.add(room);
        }
        catch (Exception e){
            System.out.println(e.getMessage());
            System.out.println(currentRow + 1 + "行房间，数据错误");
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        this.roomService.addAll(list);
        log.info("房间导入完成");
    }
}