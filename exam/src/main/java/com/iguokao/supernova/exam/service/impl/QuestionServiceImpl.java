package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.document.QuestionOption;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.QuestionScoreTypeEnum;
import com.iguokao.supernova.common.enums.QuestionTypeEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.enums.PeriodStateEnum;
import com.iguokao.supernova.exam.repository.PartRepository;
import com.iguokao.supernova.exam.repository.QuestionRepository;
import com.iguokao.supernova.exam.service.QuestionService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class QuestionServiceImpl implements QuestionService {
    private final QuestionRepository questionRepository;
    private final PartRepository partRepository;
    private final MongoTemplate mongoTemplate;

    @Override
    public String addQuestion(Question question) {
        Boolean result = handleQuestion(question);
        //处理成功
        if(result){
            question = this.questionRepository.insert(question);
            return question.get_id().toString();
        }
        //试题非法
        else {
            return null;
        }
    }

    @Override
    public Boolean preAddQuestion(Question question) {
        return handleQuestion(question);
    }

    @Override
    public void updateQuestion(Question question) {
        //处理xss攻击等
        Boolean result = handleQuestion(question);
        // 单独处理复合题，编程题等
        Boolean result2 = checkGroupQuestion(question);
        //处理成功
        if(result && result2){
           this.questionRepository.save(question);
        }
        //抛出异常
        else {
            throw new ServiceException(ExceptionEnum.QUESTION_NOT_CORRECT);
        }
    }

    @Override
    public Question getById(String questionId) {
        return this.questionRepository.findById(new ObjectId(questionId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.QUESTION_NOT_FOUND));
    }

    @Override
    public List<Question> getByIds(Set<String> questionIds) {
        List<ObjectId> ids = new ArrayList<>();
        for(String s : questionIds){
            ids.add(new ObjectId(s));
        }
        return this.questionRepository.findBy_idIn(ids);
    }

    @Override
    public List<String> deleteQuestions(List<String> questionIdList, String companyId) {
        List<String> failList = new ArrayList<>();
        for(String questionId : questionIdList){
            //判断一下这个试题是否已经加入到小卷中，如果没有可以删除，如果加入了则不可以删除
            Query query = new Query();
            //query.addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));
            query = query.addCriteria(Criteria.where("questionList.questionId").is(new ObjectId(questionId)));
            long count = this.mongoTemplate.count(query, Part.class);
            if(count > 0){
                failList.add(questionId + ":已经加入到试卷中，无法删除！");
            }
            else {
                this.questionRepository.deleteById(new ObjectId(questionId));
            }
        }

        return failList;
    }

    @Override
    public Tuple2<List<Question>, Integer> getPageData(String companyId, Integer type, String note, String body, String partId, Pageable pageable) {

        // 搜索
        Query query = new Query()
                .with(Sort.by(Sort.Direction.DESC, "_id","createdAt"));

        query.addCriteria(Criteria.where("companyId").is(new ObjectId(companyId)));

        if(null != type){
            query = query.addCriteria(Criteria.where("type").is(type));
        }
        if(null != note){
            query = query.addCriteria(Criteria.where("note").is(note));
        }
        if(null != body){
            query = query.addCriteria(Criteria.where("body").regex(body));
        }
        if(null != partId){
            Part part = this.partRepository.findById(new ObjectId(partId)).orElseThrow(() -> new ServiceException(ExceptionEnum.PART_NOT_FOUND));
            if(!part.getQuestionList().isEmpty()){
                List<ObjectId> questionIds = part.getQuestionList()
                        .stream()
                        .map(PartQuestion::getQuestionId)
                        .toList();

                query = query.addCriteria(Criteria.where("_id").nin(questionIds));
            }
        }

        // 计算总数
        long count = this.mongoTemplate.count(query, Question.class);
        // 分页信息
        query = query.with(pageable);
        List<Question> list = this.mongoTemplate.find(query, Question.class);
        return new Tuple2<>(list, (int)count);
    }

    @Override
    public List<Question> getByPaper(Paper paper) {
        List<ObjectId> questionIdList = new ArrayList<>();
        for(PaperPart part : paper.getPaperPartList()){
            questionIdList.addAll(part.getQuestionIdList());
        }
        return this.questionRepository.findBy_idIn(questionIdList);
    }

    @Override
    public List<Question> getReviewQuestionByParts(List<Part> partList) {
        List<ObjectId> questionIds = new ArrayList<>();
        for(Part part : partList){
            for(PartQuestion partQuestion : part.getQuestionList()){
                questionIds.add(partQuestion.getQuestionId());
            }
        }
        //现在机考只能阅 问答题
        Query query = new Query(Criteria.where("_id").in(questionIds));
        query = query.addCriteria(Criteria.where("type").is(QuestionTypeEnum.QA.getCode()));
        return mongoTemplate.find(query, Question.class);
    }


    public Boolean checkGroupQuestion(Question question) {
        if(question.getType().equals(QuestionTypeEnum.GROUP_SELECTION.getCode())){
            Query query = new Query();
            query.addCriteria(Criteria.where("questionList.questionId").is(question.get_id()));
            long count = this.mongoTemplate.count(query, Part.class);
            return count <= 0;
        }
        return true;
    }

    public Boolean handleQuestion(Question question) {
        //xss处理(比如option的title)
        question.setBody(cleanXSS(question.getBody()));
        if(null != question.getAnalysis()){
            question.setAnalysis(cleanXSS(question.getAnalysis()));
        }
        if(null != question.getNote()){
            question.setNote(cleanXSS(question.getNote()));
        }
        for(QuestionOption questionOption : question.getOptionList()){
            questionOption.setTitle(cleanXSS(questionOption.getTitle()));
        }

        //严格计分处理
        if(question.getType().equals(QuestionTypeEnum.SINGLE_SELECTION.getCode()) || question.getType().equals(QuestionTypeEnum.YES_NO.getCode()) ||
            question.getType().equals(QuestionTypeEnum.QA.getCode())){
            question.setScoreType(QuestionScoreTypeEnum.STRICT.getCode());
        }

        //敏感词处理
        String questionStr = question.toString();
        List<String> sensitiveWordList = Arrays.asList("法轮功","法轮大法","89年北京戒严","共x党","推翻中国共产党","香港独立","血腥镇压", "拱铲","新疆分裂", "胡温暴政",
                "六四大屠杀","发动恐怖袭击","中國當局","法lun功","症腐","8964屠杀纪念", "平反六四","89民运", "自焚抗议", "打倒中共",
                "捌玖陆肆","八九学潮","六四大屠殺","六四镇压","西藏独立","破灭中共"," 推翻共产党", "共产党垮台", "中共壓迫","独裁统治",
                "纪念六四","法輪功","香港獨立","六四学潮","六四动乱","废除中共","八九暴乱","屠杀藏人","64屠杀","活摘器官", "天安门大屠杀",
                "支持达赖","天安门屠杀","天安门虐杀","共产党的暴政","打倒共产党","法輪大法");
        for(String s : sensitiveWordList){
            if(questionStr.contains(s)){
                return false;
            }
        }
        return true;
    }

    public String cleanXSS(String value) {
        value = value.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
        value = value.replaceAll("%3C", "&lt;").replaceAll("%3E", "&gt;");
        value = value.replaceAll("%28", "&#40;").replaceAll("%29", "&#41;");
        value = value.replaceAll("\r","<br>");
        value = value.replaceAll("\n","<br>");
        value = value.replaceAll("&#.*?;","<br>");
        value = value.replaceAll("eval\\((.*)\\)", "");
        value = value.replaceAll("[\\\"\\\'][\\s]*javascript:(.*)[\\\"\\\']", "\"\"");
        return value;
    }


}
