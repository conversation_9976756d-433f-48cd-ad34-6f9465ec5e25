package com.iguokao.supernova.exam.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum RoomStateEnum implements BaseEnum {

    INIT(0, "初始"),
    CANDIDATE_DOWNLOADED(1, "考生数据下载"),
    PAPER_DOWNLOADED(2, "试卷下载"),
    PAPER_DECRYPTED(3, "试卷解密"),
    CANDIDATE_ENTERED(10, "考试进场"),
    STARTED(11, "考试开始"),
    FINISHED(12, "考试结束"),
    ANSWER_UPLOADED(20, "答案上传"),
    ACTION_UPLOADED(21, "日志上传"),
    DONE(22, "考试结束"),
    ;

    RoomStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
