package com.iguokao.supernova.exam.controller;

import com.iguokao.supernova.common.document.AdmissionCard;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.enums.CandidateStateEnum;
import com.iguokao.supernova.common.response.ActionRemoteResponse;
import com.iguokao.supernova.common.response.CandidateRemoteResponse;
import com.iguokao.supernova.common.response.PeriodFinishedResponse;
import com.iguokao.supernova.common.response.QuestionJudgeRemoteResponse;
import com.iguokao.supernova.common.enums.ActionTypeEnum;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.enums.RoomStateEnum;
import com.iguokao.supernova.exam.request.DownloadPdfResultRequest;
import com.iguokao.supernova.exam.request.NotificationResultRequest;
import com.iguokao.supernova.exam.request.PaperDownloadRequest;
import com.iguokao.supernova.exam.response.*;
import com.iguokao.supernova.exam.service.*;
import feign.Param;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/api/v1/remote")
@RequiredArgsConstructor
public class RemoteController {
    private final PaperService paperService;
    private final QuestionService questionService;
    private final PartService partService;
    private final ActionService actionService;
    private final CandidateService candidateService;
    private final PeriodRoomService periodRoomService;
    private final CacheService cacheService;
    private final PeriodService periodService;
    private final EmailService emailService;
    private final NotificationBatchService notificationBatchService;
    private final MonitorService monitorService;

    private final OssService ossService;

    @Value(value = "${app.ali.oss-bucket-exam}")
    private String ossBucketExam;

    @GetMapping("/question/{periodId}")
    @Operation(summary = "时段试题")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = QuestionJudgeRemoteResponse.class))))
    public RestResponse<List<QuestionJudgeRemoteResponse>> question(@PathVariable String periodId) {
        List<Paper> paperList = this.paperService.getPeriodPaperList(periodId);
        List<Part> partList = this.partService.getByPeriodId(periodId);
        List<Question> list = new ArrayList<>();
        for(Paper paper : paperList){
            List<String> partIdList = paper.getPaperPartList()
                    .stream()
                    .map(PaperPart::getPartId)
                    .map(ObjectId::toString)
                    .toList();
            List<PartQuestion> partQuestionList = partList
                    .stream()
                    .filter(p -> partIdList.contains(p.get_id().toString()))
                    .map(Part::getQuestionList)
                    .flatMap(Collection::stream)
                    .toList();
            List<Question> questionList = this.questionService.getByPaper(paper);
            questionList.forEach(question -> {
                PartQuestion partQuestion = partQuestionList
                        .stream()
                        .filter(pq -> pq.getQuestionId().equals(question.get_id()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(ExceptionEnum.QUESTION_NOT_FOUND));
                question.setQuestionScore(partQuestion.getScore());
            });
            list.addAll(questionList);
        }
        return RestResponse.success(Question.toRemoteResponse(list));
    }

    @GetMapping("/subject/question/{subjectId}")
    @Operation(summary = "科目试题")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = QuestionJudgeRemoteResponse.class))))
    public RestResponse<List<QuestionJudgeRemoteResponse>> paperQuestion(@PathVariable String subjectId) {
        Paper paper = this.paperService.getSubjectPaper(subjectId);
        List<Part> partList = this.partService.getBySubjectId(subjectId);

        List<String> partIdList = paper.getPaperPartList()
                    .stream()
                    .map(PaperPart::getPartId)
                    .map(ObjectId::toString)
                    .toList();
        List<PartQuestion> partQuestionList = partList
                .stream()
                .filter(p -> partIdList.contains(p.get_id().toString()))
                .map(Part::getQuestionList)
                .flatMap(Collection::stream)
                .toList();
        List<Question> questionList = this.questionService.getByPaper(paper);
        questionList.forEach(question -> {
            PartQuestion partQuestion = partQuestionList
                    .stream()
                    .filter(pq -> pq.getQuestionId().equals(question.get_id()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException(ExceptionEnum.QUESTION_NOT_FOUND));
            question.setQuestionScore(partQuestion.getScore());
        });
        List<Question> list = new ArrayList<>(questionList);
        return RestResponse.success(Question.toRemoteResponse(list));
    }

    @GetMapping("/action/{periodId}")
    @Operation(summary = "违纪事件")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoomCandidateListResponse.class))))
    public RestResponse<List<ActionRemoteResponse>> illegal(@PathVariable String periodId) {
        List<Action> list = this.actionService.getListByPeriodAndType(periodId, ActionTypeEnum.ILLEGAL.getCode());
        return RestResponse.success(Action.toRemoteResponse(list));
    }

    @GetMapping("/candidate/{periodId}")
    @Operation(summary = "时段 考生数据")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoomCandidateListResponse.class))))
    public RestResponse<List<CandidateRemoteResponse>> candidateList(@PathVariable String periodId) {
        List<Candidate> list = this.candidateService.getByPeriodId(periodId);
        List<CandidateRemoteResponse> res = new ArrayList<>();
        list.forEach(c -> {
            res.add(c.toRemoteResponse());
        });
        return RestResponse.success(res);
    }

    @GetMapping("/candidate/one/{candidateId}")
    @Operation(summary = "时段 考生数据")
    RestResponse<CandidateRemoteResponse> candidateInfo(@PathVariable String candidateId){
        Candidate candidate = this.candidateService.getById(candidateId);
        return RestResponse.success(candidate.toRemoteResponse());
    }


    @GetMapping("/room/answer/{periodId}/{roomId}")
    @Operation(summary = "时段 房间 答案")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> roomAnswer(@PathVariable String periodId,
                                           @PathVariable String roomId) {
        this.periodRoomService.setRoomState(periodId, roomId, RoomStateEnum.ANSWER_UPLOADED);
        return RestResponse.success();
    }

    @GetMapping("/room/action/{periodId}/{roomId}")
    @Operation(summary = "时段 房间 事件")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> roomAction(@PathVariable String periodId,
                                           @PathVariable String roomId) {
        this.periodRoomService.setRoomState(periodId, roomId, RoomStateEnum.ACTION_UPLOADED);
        return RestResponse.success();
    }

    @GetMapping("/period/check/finished/{periodId}")
    @Operation(summary = "时段 是否完成")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<PeriodFinishedResponse> checkFinished(@PathVariable String periodId) {
        Tuple2<Integer, Integer> state = this.monitorService.finishedCount(periodId);
        PeriodFinishedResponse res = PeriodFinishedResponse.builder()
                .finishCount(state.first())
                .roomCount(state.second())
                .periodId(periodId)
                .build();
        return RestResponse.success(res);
    }

    @GetMapping("/period/done/{periodId}")
    @Operation(summary = "时段 完成")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<PeriodFinishedResponse> done(@PathVariable String periodId) {
        this.periodService.done(periodId);
        return RestResponse.success();
    }

    @GetMapping("/review/period/list")
    @Operation(summary = "过去30天已经完成时段列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<PeriodResponse>> donePeriodList() {
        List<Period> periodList = this.periodService.reviewPeriodList(30); //30天
        List<PeriodResponse> periodResponses = new ArrayList<>();
        for(Period period : periodList){
            PeriodResponse periodResponse = PeriodResponse.of(period);
            List<SubjectResponse> subjectResponseList = new ArrayList<>();
            for(Subject subject : period.getSubjectList()){
                SubjectResponse subjectResponse = SubjectResponse.of(subject,null);
                subjectResponseList.add(subjectResponse);
            }
            periodResponse.setSubjectList(subjectResponseList);
            periodResponses.add(periodResponse);
        }

        return RestResponse.success(periodResponses);
    }

    @GetMapping("/review/question/{subjectId}")
    @Operation(summary = "阅卷题目")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<QuestionResponse>> questionList(@PathVariable String subjectId) {
        List<Part> partList = this.partService.getBySubjectId(subjectId);
        List<Question> questionList = this.questionService.getReviewQuestionByParts(partList);
        return RestResponse.success(QuestionResponse.of(questionList));
    }

    @GetMapping("/review/subject/{periodId}/{subjectId}")
    @Operation(summary = "科目信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = SubjectResponse.class)))
    public RestResponse<SubjectResponse> subjectInfo(@PathVariable String periodId, @PathVariable String subjectId) {
        Period period = this.periodService.getById(periodId);
        Subject subject = period.getSubjectList()
                .stream()
                .filter(s -> s.get_id().toString().equals(subjectId))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ExceptionEnum.SUBJECT_NOT_FOUND));
        return RestResponse.success(SubjectResponse.of(subject,null));
    }


    @PostMapping("/notification/result")
    @Operation(summary = "发送通知结果")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> noticeResult(@RequestBody NotificationResultRequest request) {
        if(!request.getCandidateId().equals("proctor")){
            this.emailService.sendResult(request.getCandidateId(),request.getBatchName(),request.getType(),request.getSendResult(),request.getErr());
        }
        return RestResponse.success();
    }

    @PostMapping("/paper/pdf/result")
    @Operation(summary = "科目-下载pdf试卷完成通知")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> finishPaper(@RequestBody DownloadPdfResultRequest request) {
        this.notificationBatchService.pdfResult(request.getJobId(),request.getIsFailed());
        return RestResponse.success();
    }

    @GetMapping("/admission/{projectId}/{idCardNum}")
    @Operation(summary = "准考证")
    @ApiResponse(content =  @Content(array = @ArraySchema(schema = @Schema(implementation = AdmissionCardResponse.class))))
    public RestResponse<com.iguokao.supernova.common.response.AdmissionCardResponse> admission(@PathVariable String projectId, @PathVariable String idCardNum) {
        Project project = this.cacheService.getProject(projectId);
        List<AdmissionCard> list = this.cacheService.getAdmissionCardByIdCardNum(projectId, idCardNum);
        com.iguokao.supernova.common.response.AdmissionCardResponse res = new com.iguokao.supernova.common.response.AdmissionCardResponse();
        res.setName(project.getName());
        res.setAdmissionCardRequirement(project.getAdmissionCardRequirement());
        res.setAdmissionCardList(list);

        for(AdmissionCard card : res.getAdmissionCardList()){
            if(card.getAvatar() != null){
                int duration = (int)((project.getEndAt().getTime() - new Date().getTime()) / 1000);
                card.setAvatar(this.ossService.generateSignedUrl(ossBucketExam, card.getAvatar(), duration).toString());
            }
        }
        return RestResponse.success(res);
    }

    @GetMapping("/admission/download/{projectId}/{idCardNum}")
    @Operation(summary = "准考证已下载")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> download(@PathVariable String projectId, @PathVariable String idCardNum) {
        this.candidateService.setStateByIdCardNum(projectId, idCardNum, CandidateStateEnum.DOWNLOADED);
        return RestResponse.success();
    }
}