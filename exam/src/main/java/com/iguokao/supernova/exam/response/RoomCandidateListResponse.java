package com.iguokao.supernova.exam.response;

import com.iguokao.supernova.exam.document.Candidate;
import com.iguokao.supernova.exam.document.Room;
import com.iguokao.supernova.exam.repository.CandidateRepository;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class RoomCandidateListResponse {
    private RoomResponse room;
    private List<CandidateResponse> candidateList;

    public static RoomCandidateListResponse of(Room room, List<Candidate> list){
        if(room == null || list == null){
            return null;
        }
        RoomCandidateListResponse res = new RoomCandidateListResponse();
        res.setCandidateList(CandidateResponse.of(list));
        res.setRoom(RoomResponse.of(room,null, false));
        return res;
    }
}
