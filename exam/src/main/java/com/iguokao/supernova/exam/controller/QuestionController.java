package com.iguokao.supernova.exam.controller;

import cn.idev.excel.EasyExcel;
import com.iguokao.supernova.common.document.QuestionGroupOption;
import com.iguokao.supernova.common.document.QuestionOption;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.response.PageResponse;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.util.ScoreUtil;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.excel.QuestionItem;
import com.iguokao.supernova.exam.excel.QuestionItemListener;

import com.iguokao.supernova.exam.request.*;
import com.iguokao.supernova.exam.response.*;
import com.iguokao.supernova.exam.service.QuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/question")
@RequiredArgsConstructor
public class QuestionController {

    private final QuestionService questionService;

    @PostMapping("/add")
    @Operation(summary = "添加试题")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> add(@Validated @RequestBody QuestionAddRequest request) {
        Question question = new Question();
        BeanUtils.copyProperties(request, question);
        question.setCompanyId(new ObjectId(request.getCompanyId()));
        handleOption(question,request.getOptionList(),request.getGroupOptionList());
        String questionId = questionService.addQuestion(question);
        return RestResponse.success(questionId);
    }

    @PostMapping("/update")
    @Operation(summary = "更新试题")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody QuestionUpdateRequest request) {
        Question question = this.questionService.getById(request.getQuestionId());
        BeanUtils.copyProperties(request, question);
        handleOption(question,request.getOptionList(),request.getGroupOptionList());
        questionService.updateQuestion(question);
        return RestResponse.success();
    }

    @GetMapping("/info/{questionId}")
    @Operation(summary = "试题详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<QuestionResponse> find(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String questionId) {
        Question question = this.questionService.getById(questionId);
        return RestResponse.success(QuestionResponse.of(question));
    }

    @PostMapping("/delete")
    @Operation(summary = "试题删除")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<QuestionDeleteResultResponse> delete(@RequestBody QuestionDeleteRequest request) {
        List<String> resultList = this.questionService.deleteQuestions(request.getQuestionIdList(), request.getCompanyId());
        QuestionDeleteResultResponse result = new QuestionDeleteResultResponse();
        result.setSuccess(request.getQuestionIdList().size() - resultList.size());
        result.setResultList(resultList);
        return RestResponse.success(result);
    }

    @PostMapping("/page")
    @Operation(summary = "试题列表带搜索")
    @ApiResponse(content = @Content(schema = @Schema(implementation = PageQuestionResponse.class)))
    public RestResponse<PageResponse<QuestionResponse>> page(@RequestBody QuestionPageRequest request) {
        Sort sort = Sort.by(Sort.Direction.DESC,"_id");
        Pageable pageable = PageRequest.of(request.getPage(), request.getPageSize(), sort);
        Tuple2<List<Question>, Integer> page = this.questionService.getPageData(request.getCompanyId(),
                request.getType(),
                request.getNote(),
                request.getBody(),
                request.getPartId(),
                pageable);
        List<QuestionResponse> res = QuestionResponse.of(page.first());
        return RestResponse.success(new PageResponse<>(res, page.second(), pageable));
    }

    @RequestMapping(value = "/excel/{companyId}", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "批量导入试题")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExcelErrResponse.class))))
    public RestResponse<List<ExcelErrResponse>>  excel(@PathVariable String companyId, @RequestPart(value = "file") MultipartFile file) throws IOException {
        List<ExcelErrResponse> errResponseList = new ArrayList<>();
        List<Question> questionList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), QuestionItem.class, new QuestionItemListener(companyId, errResponseList, questionList, questionService)).sheet().doRead();
        return RestResponse.success(errResponseList);
    }

    private void handleOption(Question question, List<QuestionOptionRequest> optionList,List<QuestionGroupOptionRequest> groupOptionList) {
        question.setQuestionScore(ScoreUtil.validQuestionScore(question.getQuestionScore().toString()));
        if(question.getQuestionScore() < 0.1){
            throw new ServiceException(ExceptionEnum.QUESTION_NOT_CORRECT);
        }
        question.getOptionList().clear();
        question.getGroupOptionList().clear();
        if(null != optionList){
            for(QuestionOptionRequest q : optionList){
                QuestionOption option = new QuestionOption();
                option.setTitle(q.getTitle());
                option.setValue(q.getValue());
                question.getOptionList().add(option);
            }
        }

        if(null != groupOptionList){
            for(QuestionGroupOptionRequest q : groupOptionList){
                QuestionGroupOption option = new QuestionGroupOption();
                BeanUtils.copyProperties(q, option);
                if(null != q.getOptionList()){
                    for(QuestionOptionRequest one : q.getOptionList()){
                        QuestionOption questionOption = new QuestionOption();
                        questionOption.setTitle(one.getTitle());
                        questionOption.setValue(one.getValue());
                        option.getOptionList().add(questionOption);
                    }
                }
                question.getGroupOptionList().add(option);
            }
        }
    }
}
