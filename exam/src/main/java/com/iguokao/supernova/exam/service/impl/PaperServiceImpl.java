package com.iguokao.supernova.exam.service.impl;

import com.iguokao.supernova.common.exception.ServiceException;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.ExceptionEnum;
import com.iguokao.supernova.exam.repository.PaperRepository;
import com.iguokao.supernova.exam.repository.PartRepository;
import com.iguokao.supernova.exam.service.CacheService;
import com.iguokao.supernova.exam.service.PaperService;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;


@Service
@RequiredArgsConstructor
public class PaperServiceImpl implements PaperService {

    private final PaperRepository paperRepository;
    private final  MongoTemplate mongoTemplate;
    private final CacheService cacheService;

    @Override
    public void generatePaper(String periodId, String projectId, String subjectId) {
        try {
            //清除过往试卷
            this.paperRepository.deleteBySubjectId(new ObjectId(subjectId));

            //查找subject和part数据
            Query query = new Query()
                    .with(Sort.by(Sort.Direction.ASC, "sort"))
                    .with(Sort.by(Sort.Direction.ASC, "createdAt"));

            query = query.addCriteria(Criteria.where("subjectId").is(new ObjectId(subjectId)));
            List<Part> partList = this.mongoTemplate.find(query, Part.class);

            //检查是否为空卷
            if (!partList.isEmpty()) {

                int maxGenPaperCount = 1;

                //step 1
                for (Part part : partList) {
                    //如果开启了试题乱序
                    if (part.getQuestionRandomized()) {
                        maxGenPaperCount = 8;//最大生成的试卷套数
                        break;
                    }
                    //小卷异常
                    if(part.getQuestionList().isEmpty()){
                        throw new ServiceException(ExceptionEnum.PAPER_GEN_EMPTY);
                    }
                }

                //step 2
                Paper paper = new Paper();
                paper.setProjectId(new ObjectId(projectId));
                paper.setSubjectId(new ObjectId(subjectId));
                paper.setPeriodId(new ObjectId(periodId));

                for (Part p : partList) {
                    PaperPart item = new PaperPart();
                    item.setPartId(p.get_id());
                    for(PartQuestion partQuestion : p.getQuestionList()){
                        item.getQuestionIdList().add(partQuestion.getQuestionId());
                    }
                    if(p.getQuestionRandomized()){
                        for(int m =0;m<maxGenPaperCount;m++){
                            item.getRandomList().add(handlePaperQuestion(p,item.getQuestionIdList()));
                        }
                    }
                    else {
                        item.getRandomList().add(handlePaperQuestion(p,item.getQuestionIdList()));
                    }

                    paper.getPaperPartList().add(item);
                }
                this.paperRepository.save(paper);
                this.handlePaperGenerated(periodId,subjectId,true);
            }
            else {
                throw new ServiceException(ExceptionEnum.PAPER_GEN_EMPTY);
            }
        }
        catch (Exception e){
            throw new ServiceException(ExceptionEnum.PAPER_GEN_ERR);
        }
    }

    @Override
    public void clearPaper(String periodId, String projectId, String subjectId) {
        //清除过往试卷
        this.paperRepository.deleteBySubjectId(new ObjectId(subjectId));

        this.handlePaperGenerated(periodId,subjectId,false);
    }

    @Override
    public Paper previewPaper(String periodId, String projectId, String subjectId) {
        return this.paperRepository.findBySubjectId(new ObjectId(subjectId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.SUBJECT_NOT_FOUND));
    }

    @Override
    public List<Paper> getPeriodPaperList(String periodId) {
        return this.paperRepository.findByPeriodId(new ObjectId(periodId));
    }

    @Override
    public Paper getSubjectPaper(String subjectId) {
        return this.paperRepository.findBySubjectId(new ObjectId(subjectId))
                .orElseThrow(() -> new ServiceException(ExceptionEnum.PAPER_NOT_FOUND));
    }

    private List<Integer> handlePaperQuestion(Part part, List<ObjectId> questionIds){

        List<Integer> list = new ArrayList<>();
        for(int i =0;i<questionIds.size();i++){
            list.add(i);
        }
        //试题乱序
        if (part.getQuestionRandomized()) {
            Collections.shuffle(list);
        }

        return list;


//        List<ObjectId> resultList = new ArrayList<>();
//        List<String> questionIdStrList = new ArrayList<>();
//        for(PartQuestion partQuestion : part.getQuestionList()){
//            questionIdStrList.add(partQuestion.getQuestionId().toString());
//        }
//
//        //试题乱序
//        if (part.getQuestionRandomized()) {
//            //Comparator<Integer> cmp = (a,b) -> Math.random() < 0.5 ? 1 : -1;
//            Collections.shuffle(questionIdStrList);
//        }
//
//        for(String questionId : questionIdStrList){
//            resultList.add(new ObjectId(questionId));
//        }
//        return resultList;
    }

    private void handlePaperGenerated(String periodId, String subjectId, Boolean state){
        Query query = new Query(Criteria.where("_id").is(new ObjectId(periodId)));
        query = query.addCriteria(Criteria.where("subjectList._id").is(new ObjectId(subjectId)));
        Update update = new Update();
        update.set("subjectList.$.paperGenerated", state);
        mongoTemplate.updateFirst(query, update, Period.class);
        cacheService.deletePeriod(periodId);
    }
}
