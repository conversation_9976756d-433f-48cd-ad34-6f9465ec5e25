package com.iguokao.supernova.exam.excel;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.iguokao.supernova.common.response.ExcelErrResponse;
import com.iguokao.supernova.common.util.StringUtil;
import com.iguokao.supernova.exam.document.Proctor;
import com.iguokao.supernova.exam.service.ProctorService;
import com.iguokao.supernova.exam.service.SiteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;


import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class ProctorItemListener extends AnalysisEventListener<ProctorItem> {

    private final String projectId;
    private final List<ExcelErrResponse> errList;
    private final List<Proctor> proctorList;
    private final ProctorService proctorService;
    private final SiteService siteService;

    @Override
    public void invoke(ProctorItem item, AnalysisContext analysisContext) {
        Integer currentRow = analysisContext.readRowHolder().getRowIndex();

        //可能是第一行 表头
        if((item.getSiteId() != null && item.getSiteId().contains("考点id"))){
            return;
        }

        try {
            Proctor proctor = new Proctor();
            proctor.setProjectId(new ObjectId(projectId));

            //处理姓名
            if(null == item.getName() || item.getName().length() > 50){
                handleErr(currentRow, "姓名，不能为空也不能超过 50个字符");
                return;
            }
            proctor.setName(item.getName());

            //处理城市
            if(null == item.getCity() || item.getCity().length() > 50 || !StringUtil.checkCity(item.getCity())){
                handleErr(currentRow, "城市名称错误" + item.getCity());
                return;
            }

            //处理手机号
            if(null != item.getMobile()){
                proctor.setMobile(handleInput(item.getMobile()));
                if(!StringUtil.validMobile(proctor.getMobile())){
                    handleErr(currentRow, "手机号格式错误");
                    return;
                }
            }
            //手机号不能为空
            else {
                handleErr(currentRow, "手机号格式错误");
                return;
            }

            //处理siteId
            if(siteService.getById(item.getSiteId()) != null){
                proctor.setSiteId(new ObjectId(item.getSiteId()));
            }
            else {
                handleErr(currentRow, "siteId错误" + item.getSiteId());
                return;
            }


            //最后一步设置 项目id
            proctor.setProjectId(new ObjectId(projectId));
            proctorList.add(proctor);

        }
        catch (Exception e){
            handleErr(currentRow,"导入非法"+ e.getMessage());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        //新增的去添加，更改的编辑
        this.proctorService.select(proctorList, projectId);

        log.info("督考excel处理完成");
    }

    private void handleErr(Integer i, String info){
        ExcelErrResponse errBody = new ExcelErrResponse();
        errBody.setRow(i + 1);
        errBody.setError("请检查：第" + (i + 1) + "行数据," + info);
        errList.add(errBody);
    }

    private String handleInput(String s){
        if(null == s){
            return null;
        }
        s = s.trim();//去掉前后空格
        s = s.replaceAll(" ","");//去除中间空格
        return s;
    }
}