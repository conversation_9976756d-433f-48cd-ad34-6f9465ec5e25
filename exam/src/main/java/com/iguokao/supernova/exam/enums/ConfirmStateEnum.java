package com.iguokao.supernova.exam.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum ConfirmStateEnum implements BaseEnum {

    NOT_CONFIRMED(0, "未确认"),
    SITE_MANAGER_CONFIRMED(1, "考点确认"),
    OPERATOR_CONFIRMED(2, "管理员确认"),

            ;

    ConfirmStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
