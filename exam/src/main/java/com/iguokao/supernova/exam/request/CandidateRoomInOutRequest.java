package com.iguokao.supernova.exam.request;

import com.iguokao.supernova.common.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CandidateRoomInOutRequest{
    @Schema(description = "房间 Id")
    private String roomId;

    @Schema(description = "时段 Id")
    private String periodId;

    @Schema(description = "考生Id 列表")
    private List<String> candidateIdList;
}
