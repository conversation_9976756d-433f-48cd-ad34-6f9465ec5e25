package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class ActionAddRequest {

    @NotNull(message = "projectId 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目 Id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.NOT_REQUIRED, description = "时段 Id")
    @Length(min = 24, max = 24, message = "periodId 错误")
    private String periodId;

    @NotNull(message = "siteId 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考点 Id")
    @Length(min = 24, max = 24, message = "siteId 错误")
    private String siteId;

    @NotNull(message = "roomId 不能为空")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "房间 Id")
    @Length(min = 24, max = 24, message = "roomId 错误")
    private String roomId;

    @Schema(description = "考生 Id")
    @Length(min = 24, max = 24, message = "candidateId 错误")
    private String candidateId;

    private Integer type;
    private Integer value;
    private String text;
}
