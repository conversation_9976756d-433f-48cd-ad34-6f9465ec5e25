package com.iguokao.supernova.exam.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;

@Getter
public enum CandidateConfirmStateEnum implements BaseEnum {

    AGREE(20, "同意"),
    REFUSE(30, "拒绝"),
            ;

    CandidateConfirmStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
