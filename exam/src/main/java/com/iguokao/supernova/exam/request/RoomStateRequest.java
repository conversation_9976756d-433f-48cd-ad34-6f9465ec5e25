package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RoomStateRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目Id")
    private String projectId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "时段Id")
    private String periodId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "省")
    private String province;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "市")
    private String city;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "房间状态 1 考生数据下载 2 试卷下载 3 试卷解密 10 考试进场 11 考试开始 12 考试结束 20 答案上传 21 日志上传")
    private Integer roomState;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生数量")
    private Integer candidateCount;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生到场数量")
    private Integer candidateArrivedCount;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "考生完成数量")
    private Integer candidateFinishedCount;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "作弊数量")
    private Integer cheatCount;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "强制交卷数量")
    private Integer forceFinishedCount;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "答题中人数")
    private Integer candidateAnsweringCount;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "登陆考生数")
    private Integer candidateLoginCount;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "迟到数量")
    private Integer lateCount;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "延时数量")
    private Integer delayCount;
}
