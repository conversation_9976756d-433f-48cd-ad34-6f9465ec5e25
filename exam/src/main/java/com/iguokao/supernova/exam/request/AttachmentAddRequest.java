package com.iguokao.supernova.exam.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
public class AttachmentAddRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "项目 Id")
    @Length(min = 24, max = 24, message = "projectId 错误")
    private String projectId;

    @Schema(description = "附件名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "附件描述")
    private String info;

    @Schema(description = "附件路径")
    private String path;

    @Schema(description = "类型")
    @Min(value = 1, message = "类型错误")
    @Max(value = 10, message = "类型错误")
    private Integer type;
}
