package com.iguokao.supernova.exam.response;
import com.iguokao.supernova.exam.document.PeriodRoom;
import com.iguokao.supernova.exam.document.Room;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class ProctorRoomResponse {

    private Integer roomIndex;
    private Integer available;
    private Integer candidateCount;
    private String address;

    public static ProctorRoomResponse of(PeriodRoom periodRoom, Room room){

        ProctorRoomResponse res = new ProctorRoomResponse();
        res.setRoomIndex(periodRoom.getRoomIndex());
        res.setAvailable(periodRoom.getAvailable());
        res.setCandidateCount(periodRoom.getCandidateCount());
        res.setAddress(room.getAddress());
        return res;
    }
}

