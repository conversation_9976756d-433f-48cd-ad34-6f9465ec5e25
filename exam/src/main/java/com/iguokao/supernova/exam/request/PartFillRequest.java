package com.iguokao.supernova.exam.request;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Getter
@Setter
public class PartFillRequest {

    @Schema(description = "小卷id")
    @Length(min = 24, max = 24, message = "24位id")
    private String partId;

    @Schema(description = "试题数组")
    private List<PartQuestionRequest> questionList;


}