package com.iguokao.supernova.exam.excel;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.iguokao.supernova.exam.document.CandidateManualArrangeItem;
import lombok.RequiredArgsConstructor;

import java.util.List;

@RequiredArgsConstructor
public class CandidateArrangeManualItemListener extends AnalysisEventListener<CandidateArrangeManualItem> {
    private final List<CandidateManualArrangeItem> list;

    @Override
    public void invoke(CandidateArrangeManualItem item, AnalysisContext analysisContext) {
        //可能是第一行 表头
        if(null != item.getFullName() && (item.getFullName().contains("姓名") || item.getFullName().contains("红色为必填项"))){
            return;
        }
        CandidateManualArrangeItem candidate  = CandidateManualArrangeItem.of(item);
        list.add(candidate);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}