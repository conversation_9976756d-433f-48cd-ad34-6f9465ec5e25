package com.iguokao.supernova.exam.enums;

import com.iguokao.supernova.common.enums.BaseEnum;
import lombok.Getter;


@Getter
public enum ProjectStateEnum implements BaseEnum {
    INIT(0, "创建"),
    DONE(10, "已结束"),
    ARCHIVED(99, "归档"),
    ;

    ProjectStateEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    private final Integer code;
    private final String text;
}
