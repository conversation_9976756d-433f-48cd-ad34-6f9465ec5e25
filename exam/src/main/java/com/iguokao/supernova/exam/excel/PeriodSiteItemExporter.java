package com.iguokao.supernova.exam.excel;

import com.iguokao.supernova.exam.document.Site;

import java.util.ArrayList;
import java.util.List;

public class PeriodSiteItemExporter {

    public static List<List<String>> head() {

        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("省份");

        List<String> head1 = new ArrayList<>();
        head1.add("城市");

        List<String> head2 = new ArrayList<>();
        head2.add("考点名称");

        List<String> head3 = new ArrayList<>();
        head3.add("考点地址");

        List<String> head4 = new ArrayList<>();
        head4.add("考点负责人");

        List<String> head5 = new ArrayList<>();
        head5.add("联系人电话");

        List<String> head6 = new ArrayList<>();
        head6.add("技术老师");

        List<String> head7 = new ArrayList<>();
        head7.add("技术老师电话");

        List<String> head8 = new ArrayList<>();
        head8.add("机位总数");

        List<String> head9 = new ArrayList<>();
        head9.add("可用机位数");

        List<String> head10 = new ArrayList<>();
        head10.add("考场数");

        List<String> head11 = new ArrayList<>();
        head11.add("电子屏");

        List<String> head12 = new ArrayList<>();
        head12.add("锁定状态");

        List<String> head13 = new ArrayList<>();
        head13.add("快递地址");

        List<String> head14 = new ArrayList<>();
        head14.add("备注");


        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        list.add(head5);
        list.add(head6);
        list.add(head7);
        list.add(head8);
        list.add(head9);
        list.add(head10);
        list.add(head11);
        list.add(head12);
        list.add(head13);
        list.add(head14);

        return list;
    }

    public static List<List<Object>> data( List<Site> siteList) {
        List<List<Object>> res = new ArrayList<>();

        for(Site site : siteList) {
            List<Object> line = new ArrayList<>();

            line.add(site.getProvince());
            line.add(site.getCity());
            line.add(site.getName());
            line.add(site.getCity() + site.getDistrict() + site.getAddress());
            line.add(site.getManager());
            line.add(site.getManagerMobile());
            line.add(site.getItContract());
            line.add(site.getItMobile());
            line.add(site.getCapacity());
            line.add(site.getAvailable());
            line.add(site.getRoomCount());
            line.add(site.getScreen() ? "有" : "无");

            line.add(site.getLocked() ? "锁定" : "未锁定");
            line.add(site.getExpressAddress());
            line.add(site.getNote());

            res.add(line);
        }
        return res;
    }
}
