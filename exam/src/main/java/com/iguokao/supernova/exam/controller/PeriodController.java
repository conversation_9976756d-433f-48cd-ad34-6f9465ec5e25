package com.iguokao.supernova.exam.controller;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.write.metadata.style.WriteCellStyle;
import cn.idev.excel.write.metadata.style.WriteFont;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.iguokao.supernova.common.constant.IdConstant;
import com.iguokao.supernova.common.entity.Tuple2;
import com.iguokao.supernova.common.response.RestResponse;
import com.iguokao.supernova.common.service.JwtService;
import com.iguokao.supernova.common.service.OssService;
import com.iguokao.supernova.exam.document.*;
import com.iguokao.supernova.exam.enums.PeriodStateEnum;
import com.iguokao.supernova.exam.excel.*;
import com.iguokao.supernova.exam.request.*;
import com.iguokao.supernova.exam.response.*;
import com.iguokao.supernova.exam.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.bson.types.ObjectId;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

@RestController
@RequestMapping("/api/v1/period")
@RequiredArgsConstructor
public class PeriodController {

    private final PeriodService periodService;
    private final PartService partService;
    private final CandidateService candidateService;
    private final ActionService actionService;
    private final CacheService cacheService;
    private final PeriodRoomService periodRoomService;
    private final RoomService roomService;
    private final SiteService siteService;
    private final QuestionService questionService;
    private final ProjectService projectService;
    private final OssService ossService;
    private final JwtService jwtService;
    private final MonitorService monitorService;

    @Value("${app.ali.oss-bucket-exam}")
    private String ossBucketExam;

    @PostMapping("/add")
    @Operation(summary = "添加 时段")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> periodAdd(@RequestBody PeriodAddRequest request) {
        Period period = new Period();
        BeanUtils.copyProperties(request, period);
        period.setProjectId(new ObjectId(request.getProjectId()));
        this.periodService.add(period);
        return RestResponse.success();
    }

    @PostMapping("/update")
    @Operation(summary = "修改 时段")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> update(@RequestBody PeriodUpdateRequest request) {
        Period period = new Period();
        BeanUtils.copyProperties(request, period);
        period.set_id(new ObjectId(request.getPeriodId()));
        this.periodService.update(period, null);
        return RestResponse.success();
    }

    @PostMapping("/info")
    @Operation(summary = "时间周期详情")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<PeriodResponse> periodInfo(@RequestBody PeriodInfoRequest request) {
        Period period = this.cacheService.getPeriod(request.getPeriodId());
        PeriodResponse periodResponse = PeriodResponse.of(period);
        List<SubjectResponse> subjectResponseList = new ArrayList<>();
        for (Subject subject : period.getSubjectList()) {
            List<Part> partList = this.partService.getBySubjectId(subject.get_id().toString());
            SubjectResponse subjectResponse = SubjectResponse.of(subject, partList);
            subjectResponseList.add(subjectResponse);
        }
        periodResponse.setSubjectList(subjectResponseList);
        return RestResponse.success(periodResponse);
    }

    @PostMapping("/remove")
    @Operation(summary = "删除时间周期")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> periodDelete(@RequestBody PeriodInfoRequest request) {
        this.periodService.remove(request.getProjectId(), request.getPeriodId());
        return RestResponse.success();
    }

    @PostMapping("/arrange/auto")
    @Operation(summary = "自动安排座位 只能用于第一次")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> arrangeSeat(@RequestBody AutoArrangeRequest request) throws IOException {
        this.periodService.autoArrange(request.getPeriodId(), request.getRandom());
        return RestResponse.success();
    }

    @PostMapping("/arrange/site")
    @Operation(summary = "自动安排座位 用于一个或者若干考点")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> arrangeSite(@RequestBody AutoArrangeRequest request) throws IOException {
        this.periodService.siteArrange(request.getPeriodId(), request.getRandom(), request.getSiteIdList());
        return RestResponse.success();
    }

    @RequestMapping(value = "/arrange/manual/{periodId}", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "手动编排")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> arrangeClone(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId,
                                             @RequestPart(value = "file") MultipartFile file) throws IOException {
        List<CandidateManualArrangeItem> list = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), CandidateArrangeManualItem.class, new CandidateArrangeManualItemListener(list)).sheet().doRead();
        this.periodService.arrangeManual(periodId, list);
        return RestResponse.success();
    }

    @GetMapping("/arrange/excel/{periodId}")
    @Operation(summary = "下载编排数据")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void arrangeData(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId,
                            HttpServletResponse response) throws IOException {
        List<CandidateArrangedItem> list = this.periodService.arrangeData(periodId);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("座位表_%s", periodId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        EasyExcel.write(response.getOutputStream())
                .sheet("成绩单")
                .head(CandidateArrangedItemExporter.head())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(CandidateArrangedItemExporter.data(list));
    }

    @GetMapping("/room/excel/{periodId}")
    @Operation(summary = "下载时段中的所有房间登录信息")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void excelRoomInfo(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("房间登录信息_%s", periodId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        String siteId = this.jwtService.getId(IdConstant.SITE_ID_PREFIX);
        Period period = this.periodService.getById(periodId);
        List<RoomLoginItem> list;
        if(period.getState() >= PeriodStateEnum.ARRANGED.getCode()){
            list = this.periodRoomService.getRoomLoginData(periodId, siteId);
        } else {
            list = this.periodRoomService.getRoomLoginDataNotArranged(periodId, siteId);
        }
        Project project = this.projectService.getById(period.getProjectId().toString());

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteFont font = new WriteFont();
        font.setFontHeightInPoints((short) 15); // 设置字体大小为15
        contentStyle.setWriteFont(font);

        EasyExcel.write(response.getOutputStream())
                .sheet("房间登录信息")
                .head(RoomLoginItemExporter.head())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(RoomLoginItemExporter.data(list, project.getName()));
    }

    @GetMapping("/arrange/cancel/{periodId}")
    @Operation(summary = "重制自动编排的状态")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Integer.class)))
    public RestResponse<Integer> arrangeCancel(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        int count = this.periodService.arrangeCancel(periodId);
        return RestResponse.success(count);
    }

    @GetMapping("/ready/{periodId}")
    @Operation(summary = "项目就绪")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Boolean.class)))
    public RestResponse<Boolean> ready(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        boolean res = this.periodService.ready(periodId);
        return RestResponse.success(res);
    }

    @GetMapping("/candidate/count/{periodId}")
    @Operation(summary = "考生安排统计")
    @ApiResponse(content = @Content(schema = @Schema(implementation = CandidateArrangedCountResponse.class)))
    public RestResponse<CandidateArrangedCountResponse> candidateCount(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        Tuple2<Integer, Integer> count = this.candidateService.countPeriodCandidate(periodId);
        CandidateArrangedCountResponse res = new CandidateArrangedCountResponse();
        res.setPeriodId(periodId);
        res.setCandidateArrangedCount(count.first());
        res.setCandidateCount(count.second());
        return RestResponse.success(res);
    }

    @GetMapping("/candidate/not-arranged/{periodId}")
    @Operation(summary = "当前时段未分配的考生")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<CandidateResponse>> candidateNotArranged(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        List<Candidate> list = this.candidateService.getNotArrangedCandidateList(periodId);
        return RestResponse.success(CandidateResponse.of(list));
    }

    @PostMapping("/candidate/arrange")
    @Operation(summary = "手动安排考生")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<List<CandidateResponse>> candidateArrange(@RequestBody CandidateArrangeRequest request) {
        this.candidateService.setCandidateListSiteAndRoom(request.getCandidateIdList(),
                request.getPeriodId(),
                request.getSiteId(),
                request.getRoomId());
        return RestResponse.success();
    }

    @PostMapping("/candidate/arrange/cancel")
    @Operation(summary = "手动安排考生 取消")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = String.class))))
    public RestResponse<String> candidateArrangeCancel(@RequestBody CandidateArrangeCancelRequest request) {
        this.candidateService.cancelCandidateListSiteAndRoom(request.getCandidateIdList(), request.getPeriodId(), request.getRoomId());
        return RestResponse.success();
    }

    @PostMapping("/action/add")
    @Operation(summary = "日志添加")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> actionAdd(@RequestBody ActionAddRequest request) {
        Action action = new Action();
        action.setProjectId(new ObjectId(request.getProjectId()));
        action.setPeriodId(new ObjectId(request.getPeriodId()));
        action.setSiteId(new ObjectId(request.getSiteId()));
        action.setRoomId(new ObjectId(request.getRoomId()));
        BeanUtils.copyProperties(request, action);
        this.actionService.add(action);

        return RestResponse.success();
    }

    @GetMapping("/state/{periodId}")
    @Operation(summary = "状态检测")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = MonitorRoomResponse.class))))
    public RestResponse<List<MonitorRoomResponse>> state(@PathVariable String periodId) {
        List<MonitorRoom> list = this.monitorService.getMonitorRoomList(periodId);
        if (list.isEmpty()) {
            return RestResponse.success(new ArrayList<>());
        }
        List<MonitorTest> roomTestList = this.monitorService.getRoomTest(list.get(0).getProjectId().toString());
        return RestResponse.success(MonitorRoomResponse.of(list, roomTestList));
    }

    @PostMapping("/force/pass")
    @Operation(summary = "强制通过")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public RestResponse<String> forcePass(@RequestBody RoomStateForcePassRequest request) {
        monitorService.setTestPassed(request.getProjectId(), request.getRoomIdList());
        return RestResponse.success();
    }

    @GetMapping("/attachment/{periodId}")
    @Operation(summary = "附件状态")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoomAttachmentResponse.class))))
    public RestResponse<List<RoomAttachmentResponse>> attachment(@PathVariable String periodId) {
        List<PeriodRoom> periodRoomList = this.periodRoomService.getRoomListByPeriodId(periodId);
        List<Site> siteList = this.periodRoomService.getSiteListByPeriodId(periodId);
        List<Room> roomList = this.roomService.getByIdList(periodRoomList
                .stream()
                .map(PeriodRoom::getRoomId)
                .toList());
        return RestResponse.success(RoomAttachmentResponse.of(periodRoomList, siteList, roomList));
    }

    @PostMapping("/index/change")
    @Operation(summary = "房间 index 修改")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoomAttachmentResponse.class))))
    public RestResponse<String> roomIndexChange(@RequestBody RoomIndexChangeRequest request) {
        this.periodRoomService.roomIndexChange(request.getPeriodId(), request.getSiteId(), request.getRoomId(), request.getRoomIndex());
        return RestResponse.success();
    }

    @GetMapping("/record/list/{periodId}")
    @Operation(summary = "录像")
    @ApiResponse(content = @Content(array = @ArraySchema(schema = @Schema(implementation = RoomRecordResponse.class))))
    public RestResponse<List<RoomRecordResponse>> monitorVideo(@PathVariable String periodId) {
        List<PeriodRoom> list = this.periodRoomService.getRoomListByPeriodId(periodId);
        List<ObjectId> siteIdList = list
                .stream()
                .map(PeriodRoom::getSiteId)
                .toList();
        List<ObjectId> roomIdList = list
                .stream()
                .map(PeriodRoom::getRoomId)
                .toList();
        List<Room> roomList = this.roomService.getByIdList(roomIdList);
        List<Site> siteList = this.siteService.getByIdList(siteIdList);
        return RestResponse.success(RoomRecordResponse.of(list, siteList, roomList));
    }

    @GetMapping("/question/excel/{periodId}")
    @Operation(summary = "下载时段中各科目的试题")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void excelQuestion(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("科目试题信息_%s", periodId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        long now = new Date().getTime();
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + now + ".xlsx");

        Period period = this.periodService.getById(periodId);
        int max = period.getSubjectList()
                .stream()
                .mapToInt(Subject::getQuestionCount)
                .max()
                .orElse(0);

        List<Part> partList = this.partService.getByPeriodId(period.get_id().toString());
        Set<String> questionIds = new HashSet<>();
        for (Part part : partList) {
            for (PartQuestion partQuestion : part.getQuestionList()) {
                questionIds.add(partQuestion.getQuestionId().toString());
            }
        }
        List<Question> questionList = this.questionService.getByIds(questionIds);

        EasyExcel.write(response.getOutputStream())
                .sheet("试题详情")
                .head(SubjectQuestionItemExporter.head(max))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(SubjectQuestionItemExporter.data(period, partList, questionList));
    }

    @GetMapping("/room/candidate/statistics/{periodId}")
    @Operation(summary = "时段中各考室的考生统计")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void excelStatistics(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("各房间考生到场信息_%s", periodId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<MonitorRoom> monitorRoomList = this.monitorService.getMonitorRoomList(periodId);
        List<PeriodRoom> periodRoomList = this.periodRoomService.getRoomListByPeriodIdSortBySiteId(periodId);
        List<ObjectId> siteIds = periodRoomList
                .stream()
                .map(PeriodRoom::getSiteId)
                .toList();

        List<ObjectId> roomIds = periodRoomList
                .stream()
                .map(PeriodRoom::getRoomId)
                .toList();

        List<Site> siteList = this.siteService.getByIdList(siteIds);
        List<Room> roomList = this.roomService.getByIdList(roomIds);

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        EasyExcel.write(response.getOutputStream())
                .sheet("考生到场统计")
                .head(CandidateStatisticsItemExporter.head())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(CandidateStatisticsItemExporter.data(monitorRoomList, periodRoomList, siteList, roomList));
    }

    @GetMapping("/excel/room/{periodId}")
    @Operation(summary = "时段中所有考点excel")
    @ApiResponse(content = @Content(schema = @Schema(implementation = String.class)))
    public void excelRoomList(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(String.format("时段中所有考点信息_%s", periodId), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<RoomInfoItem> list = this.periodRoomService.getRoomInfoData(periodId);

        //表头水平居中
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容水平居中
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteFont font = new WriteFont();
        font.setFontHeightInPoints((short) 15); // 设置字体大小为15
        contentStyle.setWriteFont(font);

        EasyExcel.write(response.getOutputStream())
                .sheet("房间信息")
                .head(RoomInfoItemExporter.head())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headStyle, contentStyle))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(RoomInfoItemExporter.data(list));
    }

    @GetMapping("/paper/size/{periodId}")
    @Operation(summary = "时段中试卷尺寸")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Integer.class)))
    public RestResponse<Integer> paperSize(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String periodId) {
        String path = String.format("paper/%s.ikp", periodId);
        return RestResponse.success(this.ossService.getFileSize(ossBucketExam, path));
    }

    @GetMapping("/recent/list/{companyId}")
    @Operation(summary = "最近完成的period列表")
    @ApiResponse(content = @Content(schema = @Schema(implementation = Integer.class)))
    public RestResponse<List<PeriodSimpleResponse>> recentList(@PathVariable @Length(min = 24, max = 24, message = "ID长度是24位") String companyId) {
        List<Period> periodList = this.periodService.recent(companyId);
        return RestResponse.success(PeriodSimpleResponse.of(periodList));
    }
}
