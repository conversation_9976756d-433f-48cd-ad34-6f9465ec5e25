package com.iguokao.supernova.exam.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.iguokao.supernova.common.converter.ObjectIdSerializer;
import com.iguokao.supernova.common.document.BaseDocument;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
@CompoundIndex(def = "{'agentList.loginKey' : 1}", name = "agentList_loginKey_index")
public class Site extends BaseDocument {
    @JsonSerialize(using = ObjectIdSerializer.class)
    private ObjectId operatorId;

    @Indexed(name = "name_index")
    private String name;
    private Integer priority = 5;
    private Integer available = 0;
    private Integer capacity = 0;
    private Integer roomCount = 0;
    private String province;

    @Indexed(name = "city_index")
    private String city;
    private String district;
    private String address;
    private String traffic;
    private String note;
    private String manager; // 考点负责人
    private String managerMobile; // 考点负责人手机号
    private String managerEmail; // 负责人邮箱
    private String expressAddress; // 到邮寄地址
    private String itContract;
    private String itMobile;
    private String price; // 参考价格
    private Boolean locked = false;
    private Boolean screen = false; // 电子屏幕

    private List<Agent> agentList = new ArrayList<>();
}